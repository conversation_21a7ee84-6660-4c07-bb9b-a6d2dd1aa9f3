package com.fxiaoke.paas.console.service.metadata

import com.alibaba.fastjson.JSON
import com.facishare.paas.pod.client.DbRouterClient
import com.facishare.paas.pod.dto.RouterInfo
import com.fxiaoke.common.PasswordUtil
import com.fxiaoke.common.http.handler.SyncCallback
import com.fxiaoke.common.http.spring.OkHttpSupport
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.paas.console.mapper.metadata.SqlQueryMapper
import com.fxiaoke.paas.console.service.ReadThreadLocal
import com.github.autoconf.ConfigFactory
import com.github.autoconf.api.IConfig
import com.github.autoconf.helper.ConfigHelper
import com.github.jedis.support.JedisCmd
import com.github.mybatis.util.InjectSchemaUtil
import com.github.shiro.support.ShiroCasRealm
import com.google.common.base.Joiner
import com.google.common.base.Splitter
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import groovy.sql.Sql
import groovy.util.logging.Slf4j
import okhttp3.*
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang.math.NumberUtils
import org.apache.commons.lang3.BooleanUtils
import org.apache.commons.lang3.ObjectUtils
import org.apache.curator.shaded.com.google.common.util.concurrent.Striped
import org.elasticsearch.common.Strings
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

import javax.annotation.PostConstruct
import javax.annotation.Resource
import java.sql.DriverManager
import java.sql.ResultSetMetaData
import java.sql.SQLException
import java.text.NumberFormat
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.Lock
import java.util.regex.Pattern
import java.util.stream.Collector
import java.util.stream.Collectors

/**
 * <AUTHOR> Created on 2018/3/20.
 */
@Service
@Slf4j
class SqlQueryService {
//  tenant_id\s?=\s?[\S]+
  private static final Pattern tenantIdPattern = Pattern.compile("tenant_id\\s?=\\s?'(\\d+)'", Pattern.CASE_INSENSITIVE)
  private String explainUrl
  private String explainUrl2
  private String explainUrl3
  private String dbClassName = "org.postgresql.Driver"
  private String biDbClassName = "com.microsoft.sqlserver.jdbc.SQLServerDriver"
  private String explainSql
  private String explainSql2
  private static final String MY_PROFILE = ConfigHelper.getProcessInfo().getProfile();

  private static String REDIS_HASH_KEY = "paas_console_query_limit"
  private final static String DECREMENT = "local exists = redis.call('EXISTS', KEYS[1])\n" +
          "if exists == 1 then\n" +
          "    local value = redis." + "call" + "('HGET', KEYS[1], ARGV[1])\n" +
          "    if value == false or value == nil then\n" +
          "        return nil\n" +
          "    else\n" +
          "        local newValue = tonumber(value) - 1\n" +
          "        redis.call('HSET', KEYS[1], ARGV[1], newValue)\n" +
          "        return value\n" +
          "    end\n" +
          "else\n" +
          "    return nil\n" + "end";

  private final static String PERMISSION_LIMIT_PREFIX = "PaasConsoleSqlQueryQuota";

  private final static int PERMISSION_LIMIT_PREFIX_LENGTH = PERMISSION_LIMIT_PREFIX.length();

  private Set<String> blackUserIds;


  private Map<Integer, List<String>> roleLimits;

  private static Striped<Lock> USER_CACHE_LOCK;

  private int USER_CACHE_LOCK_TIMEOUT = 4000

  private static int DEFAULT_LIMIT = 10

  @Autowired
  private DbRouterClient dbRouterClient

  @Autowired
  SqlQueryMapper sqlQueryMapper

  @Resource(name = "redisCache")
  private JedisCmd jedis

  @Resource(name = "casRealm")
  private ShiroCasRealm cas

  @Resource(name = "httpSupport")
  private OkHttpSupport okHttpSupport

  public static ThreadLocal<Lock> threadLock = new ThreadLocal<>();

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("db-paas-console,paas-console-base", { config ->
      explainUrl = config.get("explainUrl")
      explainUrl2 = config.get("explainUrl2", "https://explain.dalibo.com/new.json")
      explainUrl3 = config.get("explainUrl3", "https://explain.dalibo.com/plan")
      if (explainUrl3.endsWith('/')) {
        explainUrl3 = explainUrl3.substring(0, explainUrl3.length() - 1)
      }
      explainSql = config.get("explainSql", "EXPLAIN (analyze,verbose,buffers) %s")
      explainSql2 = config.get("explainSql", "EXPLAIN (analyze,verbose,buffers,format json) %s")

      // 限制sql查询访问次数
      blackUserIds = Sets.newHashSet(Splitter.on(',').omitEmptyStrings().trimResults().split(config.get("query.black.userIds", "")))
      roleLimits = parseMapFromConfig(config, "role.query.limit");
      USER_CACHE_LOCK = Striped.lazyWeakLock(config.getInt("cache-missing-lock-size", 256));
    })
  }

  private static <T> Map<Integer, T> parseMapFromConfig(IConfig config, String key) {
    String data = config.get(key);
    if (Strings.isNullOrEmpty(data)) {
      return Maps.newHashMap();
    }
    try {
      return JSON.parseObject(data, Map.class);
    } catch (Exception e) {
      log.error("config name {} key {} parseMapError", config.getName(), key, e);
    }

    return new HashMap<>();
  }

  /**
   * 执行sql查询(CRM)
   * @param sql
   * @return
   */
  @Transactional
  List<Map<String, Object>> sqlQuery(String sql, String tenantId, boolean readOnly, boolean enableNestloop) {
    try {
      if (!sql.toLowerCase().contains("limit")) {
        if (sql.endsWith(";")) {
          sql = sql.substring(0, sql.length() - 1)
        }
        sql = sql.concat(" limit 100")
      }
      sql = injectSchema(sql, tenantId, "CRM")
      ReadThreadLocal.booleanThreadLocal.set(readOnly)
      if (enableNestloop) {
        sqlQueryMapper.setTenantId(tenantId).setOption("set local enable_nestloop = on")
      } else {
        sqlQueryMapper.setTenantId(tenantId).setOption("set local enable_nestloop = off")
      }
      return sqlQueryMapper.setTenantId(tenantId).sqlQuery(sql)
    } catch (Exception e) {
      throw e
    }
  }

  /**
   * 执行sql查询（BI）
   * @return
   */
  List<Map<String, Object>> sqlQueryForOther(String sql, String module, String resourceType, String tenantId, boolean readOnly) {
    if (!sql.toLowerCase().contains("limit")) {
      if (sql.endsWith(";")) {
        sql = sql.substring(0, sql.length() - 1)
      }
      sql = sql.concat(" limit 100")
    }
    if (module == 'BI' && !"clickhouse".equals(resourceType)) {
      sql = injectSchema(sql, tenantId, "BI")
    }

    boolean usePgbouncer = true
    log.info(">>>>> profile = {}", ConfigHelper.getProcessInfo().getProfile())
    if ("ksc-ksc-prod".equals(ConfigHelper.getProcessInfo().getProfile()) && resourceType.trim() != "clickhouse") {
      usePgbouncer = false
    }

    RouterInfo routerInfo = getRouterInfo(tenantId, module, resourceType, usePgbouncer);
    String jdbcUrl
    if (readOnly && routerInfo.slaveUrl != null) {
      jdbcUrl = routerInfo.slaveUrl
    } else {
      jdbcUrl = routerInfo.jdbcUrl
    }
    ReadThreadLocal.booleanThreadLocal.set(false)
//    切换从库
//    jdbcUrl = jdbcUrl.replace("172.17.57.", "172.17.55.")
    List<Map<String, Object>> resultList = Lists.newArrayList()
    try {
      JdbcConnection connection = new JdbcConnection(jdbcUrl, routerInfo.getUserName(), routerInfo.getPassWord())
      connection.setAutoCommit(false)
      connection.query(sql, { resultSet ->
        if (resultSet != null) {
          //获得结果集结构信息,元数据
          ResultSetMetaData md = resultSet.getMetaData()
          //获得列数
          int columnCount = md.getColumnCount()
          while (resultSet.next()) {
            Map<String, Object> rowData = Maps.newHashMap()
            for (int i = 1; i <= columnCount; i++) {
              Object value = resultSet.getObject(i);
              if (value != null) {
                rowData.put(md.getColumnName(i), getValue(resultSet.getObject(i), md.getColumnTypeName(i)))
              }
            }
            resultList.add(rowData)
          }
        }
      })
    } catch (Exception e) {
      throw e
    }
    return resultList
  }

  String handleSql(String sql, String tenantId,  String resourceType) {
    if (!sql.toLowerCase().contains("limit")) {
      if (sql.endsWith(";")) {
        sql = sql.substring(0, sql.length() - 1)
      }
      sql = sql.concat(" limit 100")
    }
    if (!"clickhouse".equals(resourceType)) {
      sql = injectSchema(sql, tenantId, "BI")
    }
  }

  RouterInfo getRouterInfo(String tenantId, String module, String resourceType, boolean usePgbouncer) {
    RouterInfo routerInfo;
    if ("BI".equals(module.trim())) {
      if ("clickhouse".equals(resourceType.trim())) {
        routerInfo = dbRouterClient.queryRouterInfo(tenantId.trim(), module.trim(), "fs-bi-stat", resourceType.trim(), usePgbouncer)
      } else {
        routerInfo = dbRouterClient.queryRouterInfo(tenantId.trim(), module.trim(), "paas-console", resourceType.trim(), usePgbouncer)
      }
    } else if ("ERPSD".equals(module.trim())) {
      routerInfo = dbRouterClient.queryRouterInfo(tenantId.trim(), module.trim(), "fs-erp-sync-data", resourceType.trim(), usePgbouncer)
    }
    return routerInfo;
  }

  def injectSchema(String sql, String tenantId, String podBiz) {
    def info = dbRouterClient.queryRouterInfo(tenantId.trim(), podBiz, "paas-console", "postgresql")
    if (BooleanUtils.isTrue(info.getStandalone())) {
      sql = InjectSchemaUtil.injectSchema(sql, "postgresql", "sch_" + tenantId.trim())
    }
    return sql
  }

  /**
   * 判断字段类型是不是数组
   * @param result
   * @param type
   */
  def getValue(Object result, String type) {
    if (type.contains("_")) {
      return String.valueOf(result)
    } else {
      return result
    }
  }

  /**
   *  分析查询
   * @return
   */
  def explainQuery(String sql, String tenantId, boolean enableNestloop) throws SQLException {
    List<String> resultList = Lists.newArrayList()

    def info = dbRouterClient.queryRouterInfo(tenantId.trim(), "CRM", "paas-console", "postgresql", true)
    log.info(">>>>> dbInfo tenantId = {},url={},userName={}", tenantId, info.getJdbcUrl(), info.getUserName())
    def conn = Sql.newInstance(info.jdbcUrl, info.userName, info.passWord)
    try {
      conn.connection.autoCommit = false
      if (!enableNestloop) {
        conn.execute("set local enable_nestloop = off")
      } else {
        conn.execute("set local enable_nestloop = on")
      }
      conn.eachRow(String.format(explainSql, injectSchema(sql, tenantId, "CRM"))) {
        resultList.add(it.getString(1))
      }
    } catch (Exception e) {
      log.error("query failed.", e)
    } finally {
      conn.close()
    }
    log.info("result:\n{}", Joiner.on('\n').join(resultList))
    String result = Joiner.on('\n').join(resultList)
    String resultUrl = null;
    if (checkProfile()) {
      try {
        resultUrl = getExplainUrl(resultList, sql, tenantId)
      } catch (Exception e) {
        log.debug("getExplainUrl failed, ", e)
      }
    }
    return ["title": "analyze for " + tenantId, "plan": result, "query": sql, "is_public": "1", "url": explainUrl, "resultUrl": resultUrl]
  }

  def explainQuery2(String sql, String tenantId, boolean enableNestloop) throws SQLException {
    List<String> resultList = Lists.newArrayList()
    def info = dbRouterClient.queryRouterInfo(tenantId.trim(), "CRM", "paas-console", "postgresql", true)
    log.info(">>>>> dbInfo tenantId = {},url={},userName={}", tenantId, info.getJdbcUrl(), info.getUserName())
    def conn = Sql.newInstance(info.jdbcUrl, info.userName, info.passWord)
    try {
      conn.connection.autoCommit = false
      if (!enableNestloop) {
        conn.execute("set local enable_nestloop = off")
      } else {
        conn.execute("set local enable_nestloop = on")
      }
      conn.eachRow(String.format(explainSql, injectSchema(sql, tenantId, "CRM"))) {
        resultList.add(it.getString(1))
      }
    } catch (Exception e) {
      log.error("query failed.", e)
    } finally {
      conn.close()
    }
    log.info("result:\n{}", Joiner.on('\n').join(resultList))
    return getExplainUrl2(resultList, sql, tenantId)
  }

  /**
   * bi分析
   */
  def explainOtherQuery(String tenantId, String module, String resourceType, String sql, boolean enableNestloop) {
    List<String> resultList = Lists.newArrayList()
    RouterInfo info = getRouterInfo(tenantId, module, resourceType, true)
    def conn = Sql.newInstance(info.jdbcUrl, info.userName, info.passWord)
    try {
      conn.connection.autoCommit = false
      if (!enableNestloop) {
        conn.execute("set local enable_nestloop = off")
      } else {
        conn.execute("set local enable_nestloop = on")
      }
      if ("BI".equals(module)) {
        sql = injectSchema(sql, tenantId, "BI")
      }
      conn.eachRow(String.format(explainSql, sql)) {
        resultList.add(it.getString(1))
      }
      conn.commit();
    } catch (Exception e) {
      conn.rollback()
      log.error("explain failed.", e)
    } finally {
      conn.close()
    }
    log.info("result:\n{}", Joiner.on('\n').join(resultList))
    String result = Joiner.on('\n').join(resultList)
    String resultUrl = null;
    try {
      resultUrl =  getExplainUrl(resultList, sql, tenantId)
    } catch (Exception e) {
      log.debug("getExplainUrl failed.", e)
    }
    return ["title": "analyze for " + tenantId, "plan": result, "query": sql, "is_public": "1", "url": explainUrl, "resultUrl": resultUrl]
  }

  /**
   * bi分析
   */
  def explainOtherQuery2(String tenantId, String module, String resourceType, String sql, boolean enableNestloop) {
    List<String> resultList = Lists.newArrayList()
    RouterInfo info =getRouterInfo(tenantId, module, resourceType, true)
    def conn = Sql.newInstance(info.jdbcUrl, info.userName, info.passWord)
    try {
      conn.connection.autoCommit = false
      if (!enableNestloop) {
        conn.execute("set local enable_nestloop = off")
      } else {
        conn.execute("set local enable_nestloop = on")
      }
      if ("BI".equals(module)) {
        sql = injectSchema(sql, tenantId, "BI")
      }
      conn.eachRow(String.format(explainSql, sql)) {
        resultList.add(it.getString(1))
      }
      conn.commit();
    } catch (Exception e) {
      conn.rollback()
      log.error("explain failed.", e)
    } finally {
      conn.close()
    }
    log.info("result:\n{}", Joiner.on('\n').join(resultList))
    return getExplainUrl2(resultList, sql, tenantId)
  }

  /**
   * 获取分析结果路径
   * @param resultList
   * @return
   */
  def getExplainUrl(List<String> resultList, String sql, String tenantId) {
    /**
     * 模拟发送post请求
     */
    String result = Joiner.on('\n').join(resultList)
    OkHttpClient client = new OkHttpClient()
    FormBody.Builder builder = new FormBody.Builder()
    builder.add("title", "analyze for $tenantId")
    builder.add("plan", result)
    builder.add("query", sql)
    builder.add("is_public", "1")
    RequestBody formBody = builder.build()
    Request request = new Request.Builder().url(explainUrl).post(formBody).addHeader("Content-Type", "application/x-www-form-urlencoded").build()

    //响应请求（即调用远程接口）并接收返回值
    return okHttpSupport.syncExecute(request, new SyncCallback() {
      @Override
      Object response(Response response) throws Exception {
        if (response.code() != 200) {
          log.error(">>>>> getExplainUrl error:{}", response.message())
        }
        return response.request().url()
      }
    })

  }

  /**
   * 获取分析结果路径
   * @param resultList
   * @return
   */
  def getExplainUrl2(List<String> resultList, String sql, String tenantId) {
    /**
     * 模拟发送post请求
     */
    String result = Joiner.on('\n').join(resultList)
    OkHttpClient client = new OkHttpClient()
    FormBody.Builder builder = new FormBody.Builder()
    builder.add("title", "analyze for $tenantId")
    builder.add("plan", result)
    builder.add("query", sql)
    builder.add("is_public", "1")
    RequestBody formBody = builder.build()
    Request request = new Request.Builder().url(explainUrl2).post(formBody).addHeader("Content-Type", "application/x-www-form-urlencoded").build()

    //响应请求（即调用远程接口）并接收返回值
    String id = okHttpSupport.syncExecute(request, new SyncCallback() {
      @Override
      Object response(Response response) throws Exception {
        if (response.code() != 200) {
          log.error(">>>>> getExplainUrl error:{}", response.message())
        } else {
          return JSON.parseObject(response.body().string()).getString('id')
        }
        request ""
      }
    })

    return "${explainUrl3}/${id}#plan"
  }

  /**
   * 查询pg正在执行的sql
   * @param tenantId
   */
  def getSql(String tenantId) {
    sqlQueryMapper.setTenantId(tenantId).getSql()
  }

  /**
   * 查看持有锁和等待锁
   * @param tenantId
   */
  def getLock(String tenantId) {
    sqlQueryMapper.setTenantId(tenantId).getLock()
  }

  /**
   * 获取当前连接信息
   * @param tenantId
   */
  def getLink(String tenantId) {
    sqlQueryMapper.setTenantId(tenantId).getLink()
  }

  /**
   * 正则匹配sql中的tenantId
   * @param sql
   * @return
   */
  static def tenantIdPattern(String sql) {
    def m = tenantIdPattern.matcher(sql)
    String tenantId = null
    if (m.find()) {
      def temp = m.group(1).trim()
      tenantId = temp
    }
    return tenantId
  }

  /**
   * 创建bi数据库连接
   * @param jdbcUrl
   * @param userName
   * @param password
   */
  def getBiConnection(String jdbcUrl, String username, String password) {
    Properties prop = new Properties()
    prop.put("user", username)
    if (password != null) {
      prop.put("password", password)
    }
    try {
      Class.forName(dbClassName)
      DriverManager.setLoginTimeout(15)
      log.info("getBiConnection is success")
      return DriverManager.getConnection(jdbcUrl, prop)
    } catch (Exception e) {
      log.error("connect bi failed.", e)
    }
  }

  /**
   * 创建sqlServer数据库连接
   * @param jdbcUrl
   * @param username
   * @param password
   * @return
   */
  def getSqlServerConnection(String jdbcUrl, String username, String password) {
    Properties prop = new Properties()
    prop.put("user", username)
    if (password != null) {
      prop.put("password", password)
    }
    try {
      Class.forName(dbClassName)
      DriverManager.setLoginTimeout(15)
      return DriverManager.getConnection(jdbcUrl, prop)
    } catch (Exception e) {
      log.error("connect bi failed.", e)
    }
  }

  boolean checkUpperLimit() {
    Integer limit = 0;
    String userId = String.valueOf(cas.getCurrentUser().getId())
    List<String> permission = Lists.newArrayList(cas.getCurrentUser().getPermissions())
    permission.removeIf { it -> !it.startsWith(PERMISSION_LIMIT_PREFIX) }
    if (CollectionUtils.isNotEmpty(permission)) {
      limit = getQueryLimit(permission)
    }
    if(limit == 0) {
      limit = getQueryLimit(userId)
    }
    if (limit == -1) {
      return true;
    }
    try {
      threadLock.set(USER_CACHE_LOCK.get(userId));
      if (!threadLock.get().tryLock(USER_CACHE_LOCK_TIMEOUT, TimeUnit.MILLISECONDS)) {
        threadLock.set(null)
      }
      String value = jedis.hget(REDIS_HASH_KEY, userId)
      if (value == null) {
        jedis.hset(REDIS_HASH_KEY, userId, "1");
        return true;
      }
      Integer times = Integer.valueOf(value)
      if (times >= limit) {
        return false;
      }
      jedis.hset(REDIS_HASH_KEY, userId, String.valueOf(times + 1))
      return true;
    } catch (Exception ignore) {
      log.warn("checkUpperLimit fail ", ignore);
      return true;
    }
  }

  def unlock(Lock sqlCacheLock) {
    if (Objects.isNull(sqlCacheLock)) {
      return;
    }

    try {
      sqlCacheLock.unlock();
    } catch (Exception e) {
      log.warn("unlock fail ", e);
    }
  }

  def getQueryLimit(String userId) {
    if (CollectionUtils.isNotEmpty(blackUserIds) && (blackUserIds.contains(userId) || blackUserIds.contains("*"))) {
      return -1;
    }
    // 默认值
    int limit = DEFAULT_LIMIT;
    if(ObjectUtils.isEmpty(roleLimits)) {
      return limit;
    }
    roleLimits.forEach({ k, v ->
      if (CollectionUtils.isEmpty(v)) {
        return
      }
      if ((v.contains(userId) || v.contains("*"))) {
        limit = limit < k ? k : limit;
      }
    })
    return limit;
  }

  static int getQueryLimit(List<String> permissions) {
    List<String> limits = permissions.stream().map {it -> it.substring(PERMISSION_LIMIT_PREFIX_LENGTH)}.collect(Collectors.toList())
    if (limits.contains("Black") || limits.contains("limitless")) {
      return -1;
    }
    int limit = 0;
    limits.forEach({ it ->
      if(NumberUtils.isNumber(it) && Integer.parseInt(it) > limit) {
        limit = Integer.parseInt(it)
      }
    })
    return limit;
  }

  def decrementCount() {
    try {
      String userId = String.valueOf(cas.getCurrentUser().getId())
      jedis.eval(DECREMENT, Lists.newArrayList(REDIS_HASH_KEY), Lists.newArrayList(userId))
    } catch (Exception ignore) {
      log.warn("decrementCount fail ", ignore);
    }
  }

  @Scheduled(cron = " 0 0 23 * * *")
  void clearLimitRecord() {
    jedis.del(REDIS_HASH_KEY);
  }

  void clearLimitRecord(String[] userIds) {
    if(userIds.length == 0) {
      return;
    }
    jedis.hdel(REDIS_HASH_KEY, userIds);
  }

  static boolean checkProfile() {
    if(MY_PROFILE.equals("fstest") || MY_PROFILE.equals("foneshare")) {
      return true;
    }
    return false;
  }
}
