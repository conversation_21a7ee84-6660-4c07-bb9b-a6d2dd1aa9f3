package com.fxiaoke.paas.console.util.metadata

import groovy.util.logging.Slf4j

import java.text.ParseException
import java.text.SimpleDateFormat

@Slf4j
class MetadataFieldUtil {
  private static final String INDEX_NAME = "fs-paas-field-%s"

  static def getIndexName() {
    return String.format(INDEX_NAME, getYesterdayByCalendar())
  }

   static String getYesterdayByCalendar(){
    Calendar calendar = Calendar.getInstance()
    calendar.add(Calendar.DATE,-1)
    Date time = calendar.getTime()
    String yesterday = new SimpleDateFormat("yyyy.MM.dd").format(time)
    return yesterday
  }

  static void main(String[] args) {
    String index = getIndexName()
    println(index)
    log.info(index)
  }
}
