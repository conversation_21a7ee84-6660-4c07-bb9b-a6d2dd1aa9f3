<#assign breadcrumbContent>
<section class="content-header">
<nav class="navbar navbar-default container-fluid" role="navigation" style="z-index: 5">
  <div>
    <ul class="nav navbar-nav" id="navbar">

    <#if active_nav=="workflow">
        <li class="active"><a href="${CONTEXT_PATH}/workflow/tenant/source/flow/"><strong>流程查询</strong></a></li>
        <li><a href="${CONTEXT_PATH}/workflow/instance/"><strong>流程实例查询</strong></a></li>
        <li><a href="${CONTEXT_PATH}/workflow/task/"><strong>Task查询</strong></a></li>
        <li><a href="${CONTEXT_PATH}/workflow/tenant/metadata/describe/query/"><strong>ObjectId查询</strong></a></li>
        <#--<li><a href="${CONTEXT_PATH}/workflow/statistics/"><strong>流程统计</strong></a></li>-->
    </#if>

        <#if active_nav=="workflow_instance">
            <li><a href="${CONTEXT_PATH}/workflow/tenant/source/flow/"><strong>流程查询</strong></a></li>
            <li  class="active"><a href="${CONTEXT_PATH}/workflow/instance/"><strong>流程实例查询</strong></a></li>
            <li><a href="${CONTEXT_PATH}/workflow/task/"><strong>Task查询</strong></a></li>
            <li><a href="${CONTEXT_PATH}/workflow/tenant/metadata/describe/query/"><strong>ObjectId查询</strong></a></li>
            <#--<li><a href="${CONTEXT_PATH}/workflow/statistics/"><strong>流程统计</strong></a></li>-->
        </#if>

        <#if active_nav=="workflow_instance_task">
            <li><a href="${CONTEXT_PATH}/workflow/tenant/source/flow/"><strong>流程查询</strong></a></li>
            <li><a href="${CONTEXT_PATH}/workflow/instance/"><strong>流程实例查询</strong></a></li>
            <li class="active"><a href="${CONTEXT_PATH}/workflow/task/"><strong>Task查询</strong></a></li>
            <li><a href="${CONTEXT_PATH}/workflow/tenant/metadata/describe/query/"><strong>ObjectId查询</strong></a></li>
            <#--<li><a href="${CONTEXT_PATH}/workflow/statistics/"><strong>流程统计</strong></a></li>-->
        </#if>

    <#if active_nav=="CustomerId_Query">
        <li><a href="${CONTEXT_PATH}/workflow/tenant/source/flow/"><strong>流程查询</strong></a></li>
        <li><a href="${CONTEXT_PATH}/workflow/instance/"><strong>流程实例查询</strong></a></li>
        <li><a href="${CONTEXT_PATH}/workflow/task/"><strong>Task查询</strong></a></li>
        <li class="active"><a href="${CONTEXT_PATH}/workflow/tenant/metadata/describe/query/"><strong>ObjectId查询</strong></a></li>
        <#--<li><a href="${CONTEXT_PATH}/workflow/statistics/"><strong>流程统计</strong></a></li>-->
    </#if>

    <#--<#if active_nav=="statistics">-->
        <#--<li><a href="${CONTEXT_PATH}/workflow/tenant/source/flow/"><strong>流程查询</strong></a></li>-->
        <#--<li><a href="${CONTEXT_PATH}/workflow/instance/"><strong>流程实例查询</strong></a></li>-->
        <#--<li><a href="${CONTEXT_PATH}/workflow/task/"><strong>Task查询</strong></a></li>-->
        <#--<li><a href="${CONTEXT_PATH}/workflow/tenant/customer/query/"><strong>CustomerId查询</strong></a></li>-->
        <#--<li class="active"><a href="${CONTEXT_PATH}/workflow/statistics/"><strong>流程统计</strong></a></li>-->
    <#--</#if>-->

</ul>
  </div>
</nav>
</section>
</#assign>
