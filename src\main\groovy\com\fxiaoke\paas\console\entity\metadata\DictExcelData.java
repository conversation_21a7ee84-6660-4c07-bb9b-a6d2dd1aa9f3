package com.fxiaoke.paas.console.entity.metadata;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/20 18:46
 */
@Data
@Builder
@ColumnWidth(32)
@HeadStyle(shrinkToFit = BooleanEnum.TRUE)
public class DictExcelData implements Serializable {
  @ExcelProperty("对象apiName")
  @ContentStyle(dataFormat = '@')
  String describeApiName;
  @ExcelProperty("对象名称")
  String displayName;
  @ContentStyle(wrapped = BooleanEnum.TRUE)
  @ExcelProperty("字段含义")
  String fieldLabel;
  @ExcelProperty("字段apiName")
  String fieldApiName;
  @ColumnWidth(24)
  @ExcelProperty("对象字段类型")
  String type;
  @ColumnWidth(12)
  @ExcelProperty("表字段名")
  String storeSlotName;
  @ExcelProperty("表名")
  @ContentStyle(dataFormat = '@')
  String storeTableName;
  @ColumnWidth(20)
  @ExcelProperty("表字段类型")
  String columnType;
  @ColumnWidth(24)
  @ExcelProperty("字段关联关系")
  String fieldRef;
  @ColumnWidth(24)
  @ExcelProperty("是否选项字段")
  String isOption;
  @ColumnWidth(12)
  @ExcelProperty("是否必填")
  String required;
  @ColumnWidth(128)
  @ContentStyle(wrapped = BooleanEnum.TRUE)
  @ExcelProperty("选项值")
  String options;
  @ColumnWidth(12)
  @ExcelProperty("字段长度")
  String fieldLength;
}
