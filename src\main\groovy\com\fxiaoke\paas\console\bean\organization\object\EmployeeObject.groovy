package com.fxiaoke.paas.console.bean.organization.object

import groovy.transform.ToString
import groovy.transform.builder.Builder
import lombok.Getter
import lombok.Setter

import javax.persistence.Transient

@Getter
@Setter
@ToString
@Builder
class EmployeeObject implements Serializable {

  String userId
  String nickname
  String name
  String phone
  String email
  String position
  String tenantId
  String supervisorId
  String title
  String picAddr
  String description
  String status
  String createTime
  String lastModifiedBy
  String lastModifiedTime
  Integer isDeleted
  String department
  Long version
  String createdBy
  String package_
  String objectDescribeId
  String objectDescribedApiName
  String recordType
  String extendObjDataId
  String id
  @Transient
  List<String> departmentName
  @Transient
  String enterpriseId
}
