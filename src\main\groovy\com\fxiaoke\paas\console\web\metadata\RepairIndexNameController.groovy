package com.fxiaoke.paas.console.web.metadata

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.facishare.paas.metadata.dao.pg.entity.metadata.Field
import com.facishare.paas.metadata.support.ESUtil
import com.fxiaoke.paas.common.service.SchemaHelper
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.mapper.metadata.DataMapper
import com.fxiaoke.paas.console.mapper.metadata.DescribeMapper
import com.fxiaoke.paas.console.mapper.metadata.FieldMapper
import com.fxiaoke.paas.console.service.OKHttpService
import com.fxiaoke.paas.console.service.metadata.DescribeService
import com.fxiaoke.paas.console.util.HttpClientUtil
import com.github.autoconf.ConfigFactory
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.PostConstruct
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 * @date 2022/7/28 18:04
 */
@Controller
@Slf4j
@RequestMapping("/metadata-repair/repairIndexName")
class RepairIndexNameController {

  @Autowired
  private FieldMapper fieldMapper

  @Autowired
  private DescribeMapper describeMapper

  @Autowired
  private DescribeService describeService

  @Autowired
  private DataMapper dataMapper

  @Autowired
  private OKHttpService httpService

  @Autowired
  private SchemaHelper schemaHelper

  private String metadataForTestUrl;
  private String metadataForRefreshUrl;

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-console-url", { iConfig ->
      this.metadataForTestUrl = iConfig.get("metadata-for-test-url")
      this.metadataForRefreshUrl = iConfig.get("metadata-for-refresh-url")

    })
  }

  @RequestMapping(path = "/")
  def repairIndexName(){
    "metadata/repairIndexName"
  }

  @PostMapping(path = "/repair", produces = "application/json;charset=utf-8")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 修复index_name重复")
  def repair(String tenantId, String fieldId){
    Map<String, Object> map = Maps.newHashMap()
    if (StringUtils.isAnyBlank(tenantId, fieldId)) {
      return ["code": 400, "error": "tenantId和fieldId不能为空"]
    }
    tenantId = tenantId.trim();
    fieldId = fieldId.trim();
    try {
      Field field = fieldMapper.setTenantId(tenantId).findFieldById(tenantId, fieldId)
      JSONObject jsonObject = new JSONObject()
      jsonObject.put("tenantIds", Lists.newArrayList(tenantId))
      if (field == null) {
        return ["code": 500, "error": "这个字段不存在"]
      }
      String indexName;
      if (ESUtil.retainApiNameMap.containsKey(field.getApiName())) {
        indexName = ESUtil.retainApiNameMap.get(field.getApiName())
      } else {
        String indexType = ESUtil.getIndexType(field, field.getDescribeApiName());
        Integer indexTypeLength = indexType.length()
        indexName = indexType + "_" + (1 + fieldMapper.setTenantId(tenantId)
                .findMaxIndexNameSuf(indexTypeLength + 2, tenantId, indexType + "\\_[0-9]+", field.getDescribeApiName()))
      }
      StringBuilder updateSql = new StringBuilder().append("UPDATE mt_field SET index_name=\'").append(indexName.trim()).append("\' WHERE tenant_id=\'")
              .append(tenantId).append("\' AND field_id=\'").append(fieldId).append("\'");
      fieldMapper.setTenantId(tenantId).updateIndexNameById(indexName, tenantId, fieldId);
      field = fieldMapper.setTenantId(tenantId).findFieldById(tenantId, fieldId)
      map.put("field", field)
      map.put("updateSql", updateSql)
      String cleanDescribeUrl = metadataForTestUrl + "/paas/metadata/describe/batch/purge/syn"
      String cleanResult = HttpClientUtil.post(cleanDescribeUrl, null, jsonObject, MediaType.APPLICATION_JSON)
      map.put("clean describe result",cleanResult)
      String esSynchronizationUrl = String.format(metadataForRefreshUrl + "/paas/metadata/data/bulk/refresh/single/data/ids?ei=%s&api_name=%s", tenantId, field.getDescribeApiName())
      String esResult = HttpClientUtil.get(esSynchronizationUrl, null);
      map.put("es synchronization result", esResult)
      return ["code": 200, "info": JSON.toJSONString(map)]
    } catch(Exception e) {
      return ["code": 500, "error": e.message]
    }
  }

  @PostMapping(path = "/find", produces = "application/json;charset=utf-8")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 查询对应field和修改锁影响的数据条数")
  def find(String tenantId, String fieldId){

    if (StringUtils.isAnyBlank(tenantId, fieldId)) {
      return ["code": 400, "error": "tenantId和apiName不能为空"]
    }
    try {
      Field field = fieldMapper.setTenantId(tenantId).findFieldById(tenantId, fieldId);
      String tableName = describeService.getStoreTableName(tenantId, field.getDescribeApiName())
      if(StringUtils.isBlank(tableName)){
        tableName = "mt_data"
      }
      Integer affectData = dataMapper.setTenantId(tenantId).countDataByDescribeApiName(tableName, tenantId, field.getDescribeApiName())
      String indexType = ESUtil.getIndexType(field, field.getDescribeApiName());
      Map<String, Object> map = Maps.newHashMap()
      map.put("field", field)
      map.put("countAffectData", affectData)
      map.put("indexType", indexType)
      return ["code": 200, "info": JSON.toJSONString(map)]
    } catch (Exception e){
      return ["code": 500, "error": e.message]
    }
  }
}
