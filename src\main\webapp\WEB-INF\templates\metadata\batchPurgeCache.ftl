<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>批量清除缓存</h1>
        <ol class="breadcrumb">
            <li><a href="${ctx}"><i class="fa fa-dashboard"></i>主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>批量清除缓存</a></li>
        </ol>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info">
                    <div class="box-header with-border">
                    </div>
                    <form class="form-horizontal" action="" method="post" id="batchPurgeForm" role="form" data-toggle="validator">
                        <div class="box-body">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">请填写租户id和describeApiNames</label>
                                <div class="col-sm-10">
                                    <table id="tenantApiTable" class="table table-bordered">
                                        <thead>
                                        <tr>
                                            <th>Tenant ID</th>
                                            <th>API Names</th>
                                            <th>操作</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <!-- 初始行 -->
                                        <tr>
                                            <td><input type="text" class="form-control tenantId" placeholder="tenantId(必填)" required></td>
                                            <td><input type="text" class="form-control describeApiNames" placeholder="apiname(必填)" required></td>
                                            <td><button type="button" class="btn btn-sm btn-success addRow">添加更多</button></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-sm-offset-5">
                                <button type="button" id="batchPurgeCache" class="btn btn-primary">批量清理</button>
                                <button type="button" id="clearForm" class="btn btn-primary">清空</button>
                                <input id="cancel_btn" class="btn" type="button" value="返回" onclick="history.back()"/>
                            </div>
                            <div class="box-body">
                                <h4>Result <i class="fa fa-hand-o-down"></i></h4>
                                <pre id="dataInfo" style="min-width: 300px"></pre>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script>
        $(document).ready(function() {
            var rowTemplate = '<tr>' +
                '<td><input type="text" class="form-control tenantId" placeholder="企业ID" required></td>' +
                '<td><input type="text" class="form-control describeApiNames" placeholder="API名称，用逗号分隔" required></td>' +
                '<td><button type="button" class="btn btn-sm btn-danger removeRow">移除</button></td>' +
                '</tr>';

            // 添加新行到表格的最底部
            $('#tenantApiTable').on('click', '.addRow', function() {
                var newRow = $(rowTemplate); // 假设 rowTemplate 是表格行的 HTML 模板
                $('#tenantApiTable').append(newRow); // 将新行添加到表格的最底部
                newRow.find('.removeRow').off('click').on('click', function() {
                    $(this).closest('tr').remove(); // 移除当前点击的行
                });
            });

            // 为清空按钮绑定点击事件
            $('#clearForm').click(function (){
                // 清空表单中的输入字段
                $('.tenantId').val('');
                $('.describeApiNames').val('');

                // 移除所有行，除了初始行
                $('#tenantApiTable tbody tr').filter(function() {
                    return $(this).find('.tenantId').length > 0; // 保留至少一个输入框
                }).remove();

                // 清空结果展示区域
                $('#dataInfo').text('');

                // 重新启用批量清理按钮
                $('#batchPurgeCache').prop('disabled', false).text('批量清理');

            })

            // 处理批量清除操作
            $('#batchPurgeCache').click(function() {
                var tenantApiData = [];
                var isFormValid = true;

                // 验证并收集表单数据
                $('#tenantApiTable tbody tr').each(function() {
                    var tenantId = $(this).find('.tenantId').val().trim();
                    var apiNamesInput = $(this).find('.describeApiNames').val().trim();
                    if (!tenantId || !apiNamesInput) {
                        isFormValid = false;
                        return false;
                    }
                    var describeApiNames = apiNamesInput.split(',').map(function(name) {
                        return name.trim();
                    });
                    tenantApiData.push({ tenantId: tenantId, describeApiNames: describeApiNames });
                });

                if (!isFormValid) {
                    alert('请确保填写了所有必填项。');
                    return;
                }

                // 发送 AJAX 请求
                $.ajax({
                    type: "POST",
                    url: "${CONTEXT_PATH}/metadata/batch/purge/batchPurgeCache",
                    contentType: "application/json",
                    data: JSON.stringify(tenantApiData),
                    beforeSend: function() {
                        $('#batchPurgeCache').text('正在清理...').prop('disabled', true);
                    },
                    success: function(response) {
                        // 操作完成，使按钮可用
                        $('#batchPurgeCache').text('批量清理').prop('disabled', false);

                        // 直接将返回的结果展示到 #dataInfo 元素中
                        $('#dataInfo').text(response);
                    },
                    error: function(xhr, status, error) {
                        // 请求失败时的异常处理
                        console.error("请求失败：" + error);
                        $('#batchPurgeCache').text('批量清理').prop('disabled', false);
                        $('#dataInfo').text("请求失败，请稍后重试或联系管理员。");
                    }
                });
            });
        });
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />