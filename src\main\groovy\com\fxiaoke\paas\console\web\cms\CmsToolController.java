package com.fxiaoke.paas.console.web.cms;


import com.github.autoconf.ConfigFactory;
import groovy.util.logging.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/cms")
@Slf4j
public class CmsToolController {

  @PostMapping(path = "/queryNotAllowConfigs", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public String queryByTransactionId(@RequestBody Set<String> configNames) {
    for (String name : configNames) {
      ConfigFactory.getConfig(name, listener -> {
        Map<String, String> all = listener.getAll();

        System.out.println(listener.getName());
      });
    }
    return "ok";
  }
}
