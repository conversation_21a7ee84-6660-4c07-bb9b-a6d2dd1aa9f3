package com.fxiaoke.paas.console.entity.datarights

import groovy.transform.ToString
import groovy.transform.builder.Builder
import lombok.Data

/**
 * <AUTHOR>
 * @date 2019/4/17 下午2:56
 *
 */
@Builder
@ToString
class DtAuth {
  String id
  String tenantId
  String pkg
  String objectDescribeApiName
  String objectId
  String owner
  Long lastModifiedTime
  List<String> readPermissionUsers
  List<String> writePermissionUsers
  List<String> participants
  List<String> sharedUsersDeptsRoles
  List<String> relateRules
  List<String> salesWaiters
  List<String> followers
  String dataAuthCode
}
