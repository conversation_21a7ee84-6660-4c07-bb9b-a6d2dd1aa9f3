package com.fxiaoke.paas.console.service.metadata

import com.facishare.paas.pod.client.DbRouterClient
import com.facishare.paas.pod.dto.RouterInfo
import com.fxiaoke.common.Pair
import com.fxiaoke.common.PasswordUtil
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.release.FsGrayRelease
import com.fxiaoke.release.FsGrayReleaseBiz
import com.github.autoconf.ConfigFactory
import com.github.mybatis.util.InjectSchemaUtil
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.BooleanUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * <AUTHOR>
 * @date 2019/8/1 11:53 AM
 */
@Slf4j
@Service
class JdbcService {
  @Autowired
  private DbRouterClient dbRouterClient;
  private String username
  private String password
  private optionJdbcUrl
  private optionUsr
  private optionPwd

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-metadata", { config ->
      username = config.get("USER_NAME")
      try {
        password = config.get("USER_PASSWORD")
        password = PasswordUtil.decode(password)
      } catch (Exception e) {
        log.error("cannot decode password, pwd: {}, ", password, e)
      }
    })

    ConfigFactory.getConfig('fs-metadata-option-db') {
      optionJdbcUrl = it.get('masterUrl')
      optionUsr = it.get('username')
      optionPwd = it.get('password')
      if (it.getBool('encrypt.pwd')) {
        optionPwd = PasswordUtil.decode(optionPwd)
      }
    }
  }

  JdbcConnection connection(String db) {
    String url = db.startsWith("jdbc:") ? db : "jdbc:postgresql://" + db
    return new JdbcConnection(url, username, password)
  }

  JdbcConnection connection(String db,String dbUserName,String dbPassword) {
    String url = db.startsWith("jdbc:") ? db : "jdbc:postgresql://" + db
    return new JdbcConnection(url, dbUserName, dbPassword)
  }


  Pair<JdbcConnection, String> conn(String tenantId) {
    Pair<String, String> dbInfo = getDbUrl(tenantId);
    return Pair.build(connection(dbInfo.first), dbInfo.second)
  }

  private Pair<String, String> getDbUrl(String tenantId) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, "CRM", "fs-metadata-service", "postgresql", true)
    String servers = routerInfo.getJdbcUrl()
    Boolean isStandalone = routerInfo.getStandalone()
    String schema = "public"
    if (BooleanUtils.isTrue(isStandalone)) {
      schema = "sch_" + tenantId;
    }
    return Pair.build(servers, schema)
  }

  Pair<JdbcConnection, String> conn(String tenantId, String sql) {
    Pair<JdbcConnection, String> p = conn(tenantId)
    String schema = p.second
    if (!"public".equals(schema)) {
      sql = InjectSchemaUtil.injectSchema(sql, "postgresql", schema)
    }
    return Pair.build(p.first, sql)
  }

  JdbcConnection connectOptionDb() {
    String url = optionJdbcUrl.startsWith("jdbc:") ? optionJdbcUrl : "jdbc:postgresql://" + optionJdbcUrl
    new JdbcConnection(url, optionUsr, optionPwd)
  }
}
