<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fxiaoke.paas.console.license.mapper.ProductLicenseMapper">
  <select id="queryProductBought" resultType="java.lang.String">
    select distinct tenant_id from product_license where product_version = #{productVersion} and del_flag = false
    <if test="expired != null">
      and expired_time > #{expired}
    </if>
  </select>

  <select id="queryProductBoughtV2" resultType="com.fxiaoke.paas.console.entity.license.ProductLicenseEntity">
    select tenant_id,start_time,expired_time from product_license where product_version = #{productVersion} and del_flag = false
    <if test="expired != null">
      and expired_time > #{expired}
    </if>
  </select>

  <select id="queryProductLicenseByOrderNumber" resultType="com.fxiaoke.paas.console.entity.license.ProductLicenseEntity">
    SELECT * FROM product_license WHERE tenant_id = #{tenantId} AND del_flag IS FALSE
    <if test="orderNumbers != null and orderNumbers.size() > 0 ">
      AND order_number IN
      <foreach collection="orderNumbers" item="orderNumber" index="index" open="(" separator="," close=")">
        #{orderNumber}
      </foreach>
    </if>
  </select>
</mapper>
