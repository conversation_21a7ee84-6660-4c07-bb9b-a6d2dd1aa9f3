function msToTime(s) {
    // Pad to 2 or 3 digits, default is 2
    function pad(n, z) {
        z = z || 2;
        return ('00' + n).slice(-z);
    }

    var ms = s % 1000;
    s = (s - ms) / 1000;
    var secs = s % 60;
    s = (s - secs) / 60;
    var mins = s % 60;
    var hrs = (s - mins) / 60;
    return pad(hrs) + ':' + pad(mins) + ':' + pad(secs) + '.' + pad(ms, 3);
}

function displayStatus(data) {
    if (data === 'fail') return "<span class='label label-danger'>fail</span>";
    else if (data === 'success') return "<span class='label label-success'>success</span>";
    else if (data === 'warning') return "<span class='label label-warning'>warning</span>";
    else if (data === "running") return "<span class='label label-primary'>running</span>";
    else if (data === "stop") return "<span class='label label-danger'>stop</span>";
    else return "<span class='label label-info'>" + data + "</span>";
}

var bootstrapDom;
var height;
var pageType;
if ($(window).width() < 768) {
    bootstrapDom = "<''<'col-sm-12'f>>" +
        "<''<'col-sm-12'l>>" +
        "<''<'col-sm-12'tr>>" +
        "<''<'col-sm-6'i>>" +
        "<''<p>>";
    height = 0;
    pageType = "simple_numbers";
} else {
    bootstrapDom = "<''<'col-sm-6'l><'col-sm-6'f>>" +
        "<''<'col-sm-12'tr>>" +
        "<''<'col-sm-5'i><'col-sm-7'p>>";
    $("[id^='fix-breadcrumb']").addClass("hide");
    height = $("#navtop").outerHeight();
    pageType = "full_numbers";
    $(function () {
        $("[data-toggle='tooltip']").tooltip()
    });
}
$.extend(true, $.fn.dataTable.defaults, {
    mark: true
});

if (!String.prototype.startsWith) {
    String.prototype.startsWith = function (searchString, position) {
        return this.substr(position || 0, searchString.length) === searchString;
    };
}
$(document).on("click", "ul.nav li.parent > a > span.icon", function () {
    $(this).find('em:first').toggleClass("glyphicon-minus");
});
$(".sidebar span.icon").find('em:first').addClass("glyphicon-plus");

$("ul.treeview-menu a").each(function () {
    if (window.location.href.startsWith(this.href)) {
        $(this).parent("li").addClass("active treeview");
        $(this).parents().parents().parents("li").addClass("active");
    }
});
