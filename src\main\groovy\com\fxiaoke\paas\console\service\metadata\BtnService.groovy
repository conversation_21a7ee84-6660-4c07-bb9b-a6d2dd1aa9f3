package com.fxiaoke.paas.console.service.metadata

import com.fxiaoke.paas.console.mapper.metadata.BtnMapper
import com.fxiaoke.paas.console.util.DateFormatUtil
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * Created on 2018/3/19.
 */
@Service
@Slf4j
class BtnService {

  @Autowired
  BtnMapper btnMapper

  /**
   *  根据企业ID查询按钮
   * @param tenantId
   */
  def findBtnByTenantId(String tenantId) {
    btnMapper.setTenantId(tenantId).findBtnByTenantId(tenantId)
  }

  /**
   * 根据按钮id查询按钮详情
   * @param tenantId
   * @param id
   * @return
   */
  def findBtnByBtnId(String tenantId, String id) {
    Map<String, Object> btnMap = btnMapper.setTenantId(tenantId).findBtnByBtnId(id, tenantId)
    btnMap["create_time"] = DateFormatUtil.formatLong(btnMap.get("create_time") as Long)
    btnMap["last_modified_time"] = DateFormatUtil.formatLong(btnMap.get("last_modified_time") as Long)
    btnMap
  }

  /**
   * 根据企业ID查询后动作
   * @param tenantId
   */
  def findActByTenantId(String tenantId) {
    btnMapper.setTenantId(tenantId).findActByTenantId(tenantId)
  }

  /**
   * 根据id查询后动作详情
   * @param tenantId
   * @param id
   */
  def findActByActId(String tenantId, String id) {
    Map<String, Object> actMap = btnMapper.setTenantId(tenantId).findActByActId(id, tenantId)
    actMap["create_time"] = DateFormatUtil.formatLong(actMap.get("create_time") as Long)
    actMap
  }

  /**
   * 根据企业ID查询自定义函数
   * @param tenantId
   */
  def findFunByTenantId(String tenantId) {
    btnMapper.setTenantId(tenantId).findFunByTenantId(tenantId)
  }

  /**
   * 根据id查询自定义函数详情
   * @param tenantId
   * @param id
   */
  def findFunByFunId(String tenantId, String id) {
    Map<String, Object> funMap = btnMapper.setTenantId(tenantId).findFunByFunId(id)
    funMap["create_time"] = DateFormatUtil.formatLong(funMap.get("create_time") as Long)
    funMap
  }


}
