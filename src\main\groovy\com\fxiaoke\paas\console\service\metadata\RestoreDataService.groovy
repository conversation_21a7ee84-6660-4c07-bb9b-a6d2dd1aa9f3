package com.fxiaoke.paas.console.service.metadata

import com.alibaba.excel.util.StringUtils
import com.fxiaoke.api.IdGenerator
import com.fxiaoke.paas.console.bean.metadata.RestoreField
import com.fxiaoke.paas.console.entity.metadata.MtDescribe
import com.fxiaoke.paas.console.entity.metadata.MtUnique
import com.fxiaoke.paas.console.mapper.metadata.DescribeMapper
import com.fxiaoke.paas.console.mapper.metadata.FieldMapper
import com.fxiaoke.paas.console.mapper.metadata.MtDataMapper
import com.fxiaoke.paas.console.mapper.metadata.MtRelationMapper
import com.fxiaoke.paas.console.mapper.metadata.MtUniqueMapper
import com.google.common.collect.Lists
import groovy.util.logging.Slf4j
import org.apache.commons.collections.CollectionUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

import java.util.function.Function
import java.util.stream.Collectors

@Service
@Slf4j
class RestoreDataService {
  @Autowired
  private DescribeMapper describeMapper

  @Autowired
  private MtDataMapper mtDataMapper

  @Autowired
  private FieldMapper fieldMapper

  @Autowired
  private MtUniqueMapper mtUniqueMapper

  @Autowired
  private MtRelationMapper mtRelationMapper

  MtDescribe findDescribe(String tenantId, String describeApiName){
    return describeMapper.setTenantId(tenantId).findDescribeByTenantIdAndApiName(tenantId,describeApiName)
  }

  Map<String, Object> findMtData(String tenantId, String dataId, String describeApiName){
    return mtDataMapper.setTenantId(tenantId).dataInfo(dataId,tenantId, describeApiName)
  }

  List<RestoreField> findRestoreFieldList(String tenantId, String describeId){
    return fieldMapper.setTenantId(tenantId).findRestoreFieldByDescribeId(tenantId,describeId)
  }

  List<String> findRestoreIdList(String tenantId, String describeApiName, Long startTime, Long endTime){
    return mtDataMapper.setTenantId(tenantId).findRestoreIdList(tenantId,describeApiName,startTime,endTime)
  }



  @Transactional(rollbackFor = Exception.class)
  void restoreData(String tenantId,String describeApiName,List<RestoreField> restoreFieldList,String dataId,String lifeStatus,String fieldNum) throws Exception{
    restoreMtUnique(tenantId, describeApiName, restoreFieldList, dataId)
    restoreMtRelation(tenantId, describeApiName, dataId)
    restoreMtData(tenantId, dataId, lifeStatus, fieldNum)
  }

  /**
   * 验证和恢复mt_unique数据
   * @param tenantId
   * @param describeApiName
   * @param restoreFieldList 需要恢复的unique数据
   * @param dataId
   * @return
   */
  boolean restoreMtUnique(String tenantId,String describeApiName,List<RestoreField> restoreFieldList,String dataId) throws Exception{
    List<MtUnique> insertList = Lists.newArrayList()
    List<MtUnique> mtUniqueList = mtUniqueMapper.setTenantId(tenantId).findByTenantIdAndDescribeApiName(tenantId,describeApiName)
    Map<String,List<MtUnique>> map = mtUniqueList.stream().collect(Collectors.groupingBy((Function){ MtUnique mtUnique -> return mtUnique.getFieldName()}))
    for (RestoreField restoreField:restoreFieldList){
      if (StringUtils.isEmpty(restoreField.getFieldValue())){
        continue
      }
      if (CollectionUtils.isNotEmpty(map.get(restoreField.getApiName()))){
        if (map.get(restoreField.getApiName()).stream().anyMatch({v -> v.getValue().equals(restoreField.getFieldValue())})){
          throw new RuntimeException("UNIQUE数据不可重复")
        }
      }
      insertList.add(new MtUnique(IdGenerator.get(),tenantId,dataId,describeApiName,restoreField.getApiName(),restoreField.getFieldValue()))
    }
    mtUniqueMapper.setTenantId(tenantId).batchInsert(insertList)
  }

  void restoreMtRelation(String tenantId,String describeApiName,String dataId) throws Exception{
    mtRelationMapper.setTenantId(tenantId).updateIsDeletedBySourceApiNameAndSourceDataId(tenantId,describeApiName,dataId)
  }

  void restoreMtData(String tenantId,String dataId,String lifeStatus,String fieldNum) throws Exception{
    mtDataMapper.setTenantId(tenantId).restoreData(tenantId,dataId,fieldNum,lifeStatus)
  }

}
