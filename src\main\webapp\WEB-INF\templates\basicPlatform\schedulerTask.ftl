<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<style>
    li {
        list-style: none;
    }

    .box {
        background-color: white;
    }

    .btn-box {
        width: 500px;
        display: flex;
        justify-content: space-around;
    }

    .content {
        width: 100%;
        padding: 20px;
    }

    label {
        padding: 5px;
    }

    pre {
        width: 100%;
        height: 75%;
    }

    .form {
        padding: 30px;
        width: 100%;
    }

    .formBox {
        display: flex;
        justify-content: space-evenly;
        padding: 20px;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;
    }

    .formBox input, .formBox textarea {
        line-height: 28px;
        min-width: 200px;
    }

    .formBox textarea {
        min-height: 60px;
        resize: vertical;
    }

    .form button {
        height: 30px;
        width: auto;
    }

    .btn-group {
        display: flex;
        gap: 10px;
    }

    .btn-group .btn {
        margin: 0;
    }

    .alert {
        margin-top: 20px;
    }
    
    /* 顶部操作说明样式 */
    .operation-description-top {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 8px 12px;
        margin-bottom: 15px;
        color: #6c757d;
        font-size: 12px;
        line-height: 1.3;
    }
    
    .operation-description-top small {
        color: #6c757d;
        font-size: 12px;
    }
    
    /* 选项卡样式 */
    .nav-tabs {
        border-bottom: 2px solid #ddd;
        margin-bottom: 20px;
    }
    
    .nav-tabs > li > a {
        border-radius: 4px 4px 0 0;
        margin-right: 2px;
        border: 1px solid transparent;
        color: #555;
        font-weight: 500;
    }
    
    .nav-tabs > li.active > a,
    .nav-tabs > li.active > a:focus,
    .nav-tabs > li.active > a:hover {
        border: 1px solid #ddd;
        border-bottom-color: transparent;
        background-color: #fff;
        color: #337ab7;
    }
    
    .tab-content {
        padding: 20px 0;
    }
    
    .tab-content-wrapper {
        padding: 30px;
        background-color: #f9f9f9;
        border-radius: 4px;
        border: 1px solid #ddd;
        min-height: 400px;
    }
    
    .query-method-section,
    .status-section {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #fff;
        border-radius: 4px;
        border: 1px solid #e7e7e7;
    }
    
    .query-method-section h4,
    .status-section h4 {
        margin-top: 0;
        margin-bottom: 15px;
        color: #333;
        font-size: 16px;
    }
    
    .radio-group {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
    }
    
    .radio-inline {
        margin-right: 15px;
        font-weight: normal;
    }
    
    .radio-inline input[type="radio"] {
        margin-right: 5px;
    }
    
    .input-section {
        margin-bottom: 20px;
    }
    
    .input-group {
        margin-bottom: 15px;
    }
    
    .input-group.hide {
        display: none;
    }
    
    .input-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #333;
    }
    
    .input-group input,
    .input-group textarea {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 14px;
    }
    
    .input-group textarea {
        min-height: 80px;
        resize: vertical;
    }
    
    /* 可拖拽调整大小的textarea样式 */
    .resizable-textarea {
        min-height: 80px;
        max-height: 300px;
        min-width: 300px;
        max-width: 1200px;
        resize: both;
        overflow: auto;
        transition: height 0.2s ease, width 0.2s ease;
        width: 100%;
        box-sizing: border-box;
    }
    
    .resizable-textarea:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .action-section {
        text-align: center;
        padding: 30px 0 20px 0;
        border-top: 1px solid #e7e7e7;
        margin-top: 20px;
    }
    
    .action-section .btn {
        padding: 12px 40px;
        font-size: 16px;
        font-weight: 500;
        line-height: 1.2;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: auto;
        min-height: 44px;
    }
    
    /* 输入区域样式优化 */
    .input-section {
        margin-bottom: 30px;
        padding: 20px;
        background-color: #fff;
        border-radius: 4px;
        border: 1px solid #e7e7e7;
        overflow: hidden;
    }
    
    .input-group {
        margin-bottom: 20px;
        position: relative;
    }
    
    .input-group:last-child {
        margin-bottom: 0;
    }
    
    /* 确保textarea可以正常拉伸 */
    .input-group .resizable-textarea {
        display: block;
        position: relative;
    }
    
    /* 必填字段样式 */
    .required {
        color: #dc3545;
        font-weight: bold;
    }
    
    .input-group label {
        margin-bottom: 8px;
    }
</style>
<#assign breadcrumbContent>
    <section class="content-header">
        <div class="operation-description-top">
            <small><strong>操作说明：</strong>支持通过ID、企业ID或API名称进行批量取消、停用启用、删除计划任务操作</small>
        </div>
        <h1>批量操作计划任务</h1>
        <ol class="breadcrumb">
            <li><a href="${ctx}"><i class="fa fa-dashboard"></i>任务主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>计划任务管理</a></li>
        </ol>
        <div id="warning" class="alert alert-warning alert-dismissible hide" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
            <strong>警告!</strong> 请输入tenantId.
        </div>
        <div id="warningInfo" class="alert alert-warning hide">
            <span id="closeInfo" href="#" class="close">&times;</span>
            <strong id="ajaxInfo"></strong>
        </div>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="box">
            <div class="form">
                
                <!-- 选项卡导航 -->
                <ul class="nav nav-tabs" role="tablist">
                    <li role="presentation" class="active">
                        <a href="#cancelTab" aria-controls="cancelTab" role="tab" data-toggle="tab">
                            <i class="fa fa-stop"></i> 批量取消
                        </a>
                    </li>
                    <li role="presentation">
                        <a href="#toggleTab" aria-controls="toggleTab" role="tab" data-toggle="tab">
                            <i class="fa fa-toggle-on"></i> 批量停用启用
                        </a>
                    </li>
                    <li role="presentation">
                        <a href="#deleteTab" aria-controls="deleteTab" role="tab" data-toggle="tab">
                            <i class="fa fa-trash"></i> 批量删除
                        </a>
                    </li>
                </ul>
                
                <!-- 选项卡内容 -->
                <div class="tab-content">
                    <!-- 批量取消选项卡 -->
                    <div role="tabpanel" class="tab-pane active" id="cancelTab">
                        <div class="tab-content-wrapper">
                            <div class="query-method-section">
                                <h4>操作方式：</h4>
                                <div class="radio-group">
                                    <label class="radio-inline">
                                        <input type="radio" name="cancelQueryMethod" value="ids" checked> 按任务ID操作
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="cancelQueryMethod" value="tenantId"> 按企业ID操作
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="cancelQueryMethod" value="apiName"> 按API名称操作
                                    </label>
                                </div>
                            </div>
                            
                            <div class="input-section">
                                <div id="cancelIdsInput" class="input-group">
                                    <label for="cancelTenantIdForIds">企业ID：<span class="required">*</span></label>
                                    <input type="text" id="cancelTenantIdForIds" placeholder="请输入企业ei" class="form-control" required>
                                    <label for="cancelTaskIds">任务ID：<span class="required">*</span></label>
                                    <textarea id="cancelTaskIds" placeholder="请输入任务ID，多个用逗号分隔" class="form-control resizable-textarea" required></textarea>
                                </div>
                                <div id="cancelTenantIdInput" class="input-group hide">
                                    <label for="cancelTenantId">企业ID：<span class="required">*</span></label>
                                    <input type="text" id="cancelTenantId" placeholder="请输入企业ei" class="form-control" required>
                                </div>
                                <div id="cancelApiNameInput" class="input-group hide">
                                    <label for="cancelApiNames">API名称：<span class="required">*</span></label>
                                    <input type="text" id="cancelApiNames" placeholder="请输入计划任务apiName" class="form-control" required>
                                </div>
                            </div>

                            <div class="action-section">
                                <button type="button" class="btn btn-warning" id="cancelBtn">
                                    <i class="fa fa-stop"></i> 执行批量取消
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 批量停用启用选项卡 -->
                    <div role="tabpanel" class="tab-pane" id="toggleTab">
                        <div class="tab-content-wrapper">
                            <div class="query-method-section">
                                <h4>查询方式：</h4>
                                <div class="radio-group">
                                    <label class="radio-inline">
                                        <input type="radio" name="toggleQueryMethod" value="ids" checked> 按任务ID操作
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="toggleQueryMethod" value="tenantId"> 按企业ID操作
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="toggleQueryMethod" value="apiName"> 按API名称操作
                                    </label>
                                </div>
                            </div>

                            <div class="input-section">
                                <div id="toggleIdsInput" class="input-group">
                                    <label for="toggleTenantIdForIds">企业ID：<span class="required">*</span></label>
                                    <input type="text" id="toggleTenantIdForIds" placeholder="请输入企业ei" class="form-control" required>
                                    <label for="toggleTaskIds">任务ID：<span class="required">*</span></label>
                                    <textarea id="toggleTaskIds" placeholder="请输入任务ID，多个用逗号分隔" class="form-control resizable-textarea" required></textarea>
                                </div>
                                <div id="toggleTenantIdInput" class="input-group hide">
                                    <label for="toggleTenantId">企业ID：<span class="required">*</span></label>
                                    <input type="text" id="toggleTenantId" placeholder="请输入企业ei或ea" class="form-control" required>
                                </div>
                                <div id="toggleApiNameInput" class="input-group hide">
                                    <label for="toggleApiNames">API名称：<span class="required">*</span></label>
                                    <input type="text" id="toggleApiNames" placeholder="请输入计划任务apiName" class="form-control" required>
                                </div>
                            </div>

                            <div class="status-section">
                                <h4>目标状态：</h4>
                                <div class="radio-group">
                                    <label class="radio-inline">
                                        <input type="radio" name="targetStatus" value="1" checked> 启用
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="targetStatus" value="0"> 停用
                                    </label>
                                </div>
                            </div>

                            <div class="action-section">
                                <button type="button" class="btn btn-info" id="toggleBtn">
                                    <i class="fa fa-toggle-on"></i> 执行状态切换
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 批量删除选项卡 -->
                    <div role="tabpanel" class="tab-pane" id="deleteTab">
                        <div class="tab-content-wrapper">
                            <div class="query-method-section">
                                <h4>查询方式：</h4>
                                <div class="radio-group">
                                    <label class="radio-inline">
                                        <input type="radio" name="deleteQueryMethod" value="ids" checked> 按任务ID操作
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="deleteQueryMethod" value="tenantId"> 按企业ID操作
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="deleteQueryMethod" value="apiName"> 按API名称操作
                                    </label>
                                </div>
                            </div>

                            <div class="input-section">
                                <div id="deleteIdsInput" class="input-group">
                                    <label for="deleteTenantIdForIds">企业ID：<span class="required">*</span></label>
                                    <input type="text" id="deleteTenantIdForIds" placeholder="请输入企业ei" class="form-control" required>
                                    <label for="deleteTaskIds">任务ID：<span class="required">*</span></label>
                                    <textarea id="deleteTaskIds" placeholder="请输入任务ID，多个用逗号分隔" class="form-control resizable-textarea" required></textarea>
                                </div>
                                <div id="deleteTenantIdInput" class="input-group hide">
                                    <label for="deleteTenantId">企业ID：<span class="required">*</span></label>
                                    <input type="text" id="deleteTenantId" placeholder="请输入企业ei" class="form-control" required>
                                </div>
                                <div id="deleteApiNameInput" class="input-group hide">
                                    <label for="deleteApiNames">API名称：<span class="required">*</span></label>
                                    <input type="text" id="deleteApiNames" placeholder="请输入计划任务apiName" class="form-control" required>
                                </div>
                            </div>

                            <div class="action-section">
                                <button type="button" class="btn btn-danger" id="deleteBtn">
                                    <i class="fa fa-trash"></i> 执行批量删除
                                </button>
                            </div>
                        </div>
                    </div>
                                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/js/highcharts.js"></script>
    <script src="https://img.hcharts.cn/highcharts/modules/data.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>

    <script type="application/javascript">
        $(document).ready(function () {
            // 初始化选项卡功能
            initTabFunctionality();

            // 批量取消计划任务
            $('#cancelBtn').on('click', function () {
                executeBatchCancel();
            });

            // 批量停用启用计划任务
            $('#toggleBtn').on('click', function () {
                executeBatchToggle();
            });

            // 批量删除计划任务
            $('#deleteBtn').on('click', function () {
                executeBatchDelete();
            });

            // 关闭警告信息
            $('#closeInfo').on('click', function() {
                $('#warningInfo').addClass('hide')
            });

            // 初始化选项卡功能
            function initTabFunctionality() {
                // 批量取消选项卡的查询方式切换
                $('input[name="cancelQueryMethod"]').on('change', function() {
                    toggleCancelInputs($(this).val());
                });

                // 批量停用启用选项卡的查询方式切换
                $('input[name="toggleQueryMethod"]').on('change', function() {
                    toggleToggleInputs($(this).val());
                });

                // 批量删除选项卡的查询方式切换
                $('input[name="deleteQueryMethod"]').on('change', function() {
                    toggleDeleteInputs($(this).val());
                });
            }

            // 切换批量取消的输入框显示
            function toggleCancelInputs(method) {
                $('#cancelIdsInput, #cancelTenantIdInput, #cancelApiNameInput').addClass('hide');
                if (method === 'ids') {
                    $('#cancelIdsInput').removeClass('hide');
                } else if (method === 'tenantId') {
                    $('#cancelTenantIdInput').removeClass('hide');
                } else if (method === 'apiName') {
                    $('#cancelApiNameInput').removeClass('hide');
                }
            }
            
            // 切换批量停用启用的输入框显示
            function toggleToggleInputs(method) {
                $('#toggleIdsInput, #toggleTenantIdInput, #toggleApiNameInput').addClass('hide');
                if (method === 'ids') {
                    $('#toggleIdsInput').removeClass('hide');
                } else if (method === 'tenantId') {
                    $('#toggleTenantIdInput').removeClass('hide');
                } else if (method === 'apiName') {
                    $('#toggleApiNameInput').removeClass('hide');
                }
            }
            
            // 切换批量删除的输入框显示
            function toggleDeleteInputs(method) {
                $('#deleteIdsInput, #deleteTenantIdInput, #deleteApiNameInput').addClass('hide');
                if (method === 'ids') {
                    $('#deleteIdsInput').removeClass('hide');
                } else if (method === 'tenantId') {
                    $('#deleteTenantIdInput').removeClass('hide');
                } else if (method === 'apiName') {
                    $('#deleteApiNameInput').removeClass('hide');
                }
            }
            
            // 执行批量取消
            function executeBatchCancel() {
                var method = $('input[name="cancelQueryMethod"]:checked').val();
                var data = getInputData('cancel', method);
                
                if (!data) return;
                
                if (confirm('确定要批量取消这些计划任务吗？')) {
                    $.ajax({
                        url: "${CONTEXT_PATH}/basicPlatform/schedulerTask/batchCancel?method=" + method,
                        contentType: "application/json",
                        data: JSON.stringify(data),
                        dataType: "json",
                        type: "POST",
                        success: function (result) {
                            if (result != null && result.code === 200) {
                                alert('批量取消操作执行成功！');
                            } else {
                                alert(result.message || '操作失败');
                            }
                        },
                        error: function (err) {
                            alert(err.responseText || '操作失败');
                        }
                    });
                }
            }
            
            // 执行批量停用启用
            function executeBatchToggle() {
                var method = $('input[name="toggleQueryMethod"]:checked').val();
                var status = $('input[name="targetStatus"]:checked').val();
                var data = getInputData('toggle', method);
                
                if (!data) return;
                
                //data.status = parseInt(status);
                
                if (confirm('确定要批量' + (status === '1' ? '启用' : '停用') + '这些计划任务吗？')) {
                    $.ajax({
                        url: "${CONTEXT_PATH}/basicPlatform/schedulerTask/batchToggleStatus?method=" + method + "&status=" + status,
                        contentType: "application/json",
                        data: JSON.stringify(data),
                        dataType: "json",
                        type: "POST",
                        success: function (result) {
                            if (result != null && result.code === 200) {
                                alert('批量状态切换操作执行成功！');
                            } else {
                                alert(result.message || '操作失败');
                            }
                        },
                        error: function (err) {
                            alert(err.responseText || '操作失败');
                        }
                    });
                }
            }
            
            // 执行批量删除
            function executeBatchDelete() {
                var method = $('input[name="deleteQueryMethod"]:checked').val();
                var data = getInputData('delete', method);
                
                if (!data) return;
                
                if (confirm('确定要批量删除这些计划任务吗？此操作不可恢复！')) {
                    $.ajax({
                        url: "${CONTEXT_PATH}/basicPlatform/schedulerTask/batchDelete?method=" + method,
                        contentType: "application/json",
                        data: JSON.stringify(data),
                        dataType: "json",
                        type: "POST",
                        success: function (result) {
                            if (result != null && result.code === 200) {
                                alert('批量删除操作执行成功！');
                            } else {
                                alert(result.message || '操作失败');
                            }
                        },
                        error: function (err) {
                            alert(err.responseText || '操作失败');
                        }
                    });
                }
            }
            
            // 获取输入数据
            function getInputData(operation, method) {
                var data = {};
                
                if (method === 'ids') {
                    // by ids: 需要企业ID和任务ID
                    var tenantId = $('#' + operation + 'TenantIdForIds').val().trim();
                    if (!tenantId) {
                        alert('请输入企业ID');
                        return null;
                    }
                    
                    var ids = $('#' + operation + 'TaskIds').val().trim();
                    if (!ids) {
                        alert('请输入任务ID');
                        return null;
                    }
                    
                    //data.tenantId = tenantId;
                    data.taskIds = ids.split(",").map(function(id) { return id.trim(); });
                    
                } else if (method === 'tenantId') {
                    // by tenantId: 只需要企业ID
                    var tenantId = $('#' + operation + 'TenantId').val().trim();
                    if (!tenantId) {
                        alert('请输入企业ID');
                        return null;
                    }
                    data.tenantId = tenantId;
                    
                } else if (method === 'apiName') {
                    // by apiName: 只需要API名称
                    var apiName = $('#' + operation + 'ApiNames').val().trim();
                    if (!apiName) {
                        alert('请输入API名称');
                        return null;
                    }
                    data.apiName = apiName;
                }
                
                return data;
            }
        });
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
