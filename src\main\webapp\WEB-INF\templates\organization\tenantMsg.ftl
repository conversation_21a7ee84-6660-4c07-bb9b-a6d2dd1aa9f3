<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>企业详细信息</h1>


        <div id="warningInfo" class="alert alert-warning hide">
            <span id="closeInfo" href="#" class="close">&times;</span>
            <strong id="ajaxInfo"></strong>
        </div>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info">
                    <div class="box-header with-border">
                        <form class="form-inline" method="post" id="findForm" role="form" data-toggle="validator">
                            <div class="form-group">
                                <label for="tenantId">EI/EA</label>
                                <input type="text" style="border-radius:5px;" name="tenantId" class="form-control" id="tenantId" value="${tenant_id!}"
                                       placeholder="EI/EA" required>
                            </div>
                            <button type="button" id="findSub" class="btn btn-primary">统计概况</button>
                            <button type="button" id="findTenantMsg" class="btn btn-primary">查询详细信息</button>
                            <button type="button" id="findTenantDb" class="btn btn-primary">查询企业数据库负载</button>
                        </form>
                    </div>
                    <button id="collapse-btn">折叠</button>
                    <button id="expand-btn">展开</button>
                    <div id="json"></div>


                    <div class="box-footer clearfix">
                        <div class="form-group">
                        </div>
                        <div class="box box-info" id="showDataChar">
                            <#--加载动态loding-->
                            <div class="spinner hide" id="loading">
                                <div class="double-bounce1"></div>
                                <div class="double-bounce2"></div>
                            </div>
                            <div class="box-header" align="right">
                                <#--<span id="dateShow" class="btn btn-info hide">根据最后修改时间统计</span>-->
                            </div>
                            <#--块儿总数展示-->
                            <div class="hide" id="dataShowDiv">
                                <#--<section class="content">-->
                                <section class="content" style="min-height:140px">
                                    <!-- Small boxes (Stat box) -->
                                    <div class="row">
                                        <div class="col-lg-3 col-xs-6">
                                            <!-- small box -->
                                            <div class="small-box bg-aqua">
                                                <div class="inner">
                                                    <h3 id="describeSum"></h3>
                                                    <p>有数据对象数</p>
                                                </div>
                                                <div class="icon">
                                                    <i class="ion ion-bag"></i>
                                                </div>
                                                <span class="small-box-footer">No more info <i class="fa fa-circle-o-notch"></i></span>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-xs-6">
                                            <!-- small box -->
                                            <div class="small-box bg-red">
                                                <div class="inner">
                                                    <h3 id="noDataDescribeSum"></h3>
                                                    <p>无数据对象数</p>
                                                </div>
                                                <div class="icon">
                                                    <i class="ion ion-pie-graph"></i>
                                                </div>
                                                <span class="small-box-footer">No more info <i class="fa fa-circle-o-notch"></i></span>
                                            </div>
                                        </div>
                                        <!-- ./col &ndash;&gt;-->
                                        <!-- ./col -->
                                        <div class="col-lg-3 col-xs-6">
                                            <!-- small box -->
                                            <div class="small-box bg-green">
                                                <div class="inner">
                                                    <#--<h3>53<sup style="font-size: 20px">%</sup></h3>-->
                                                    <h3 id="dataSum"></h3>
                                                    <p>数据总数</p>
                                                </div>
                                                <div class="icon">
                                                    <i class="ion ion-stats-bars"></i>
                                                </div>
                                                <span class="small-box-footer">No more info <i class="fa fa-circle-o-notch"></i></span>
                                            </div>
                                        </div>
                                        <!-- ./col -->
                                        <div class="col-lg-3 col-xs-6">
                                            <!-- small box -->
                                            <div class="small-box bg-yellow">
                                                <div class="inner">
                                                    <h3 id="fieldSum"></h3>
                                                    <p>字段总数</p>
                                                </div>
                                                <div class="icon">
                                                    <i class="ion ion-ios-infinite"></i>
                                                </div>
                                                <span id="fieldInfo" style="cursor: pointer;" class="small-box-footer">More info <i
                                                            class="fa fa-arrow-circle-right"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <#--</section>-->
                            </div>
                            <div class="box-body" id="container" style="min-width:400px;height:500px;">
                                <#--展示图-->
                            </div>
                        </div>
                    </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
<#--提示信息模态框-->
    <div class="modal fade" id="hintModal" tabindex="-1" role="dialog" aria-labelledby="sqlModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                                class="sr-only">Close</span></button>
                    <h4 class="modal-title" id="stopModalLabel">提示</h4>
                </div>
                <div class="modal-body">
                    <h4>请输入必要参数</h4>
                </div>
                <div class="modal-footer">
                    <a type="button" class="btn btn-default" data-dismiss="modal">返回</a>
                </div>
            </div>
        </div>
    </div>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script src="//static.foneshare.cn/oss/js/highcharts.js"></script>
    <script src="//static.foneshare.cn/oss/my97datepicker/WdatePicker.js"></script>
    <script>
        function getTree() {
            var tenantId = $('#tenantId').val()
            if (tenantId == "") {
                alert("企业ID不可为空!");
                return false;
            }

            $.ajax({
                type: 'Get',
                url: '${CONTEXT_PATH}/organization/tenantMsg',
                data: {
                    tenantId: tenantId,
                },
                contentType: "application/json", //必须有
                dataType: 'json',
                async: false,
                success: function (data) {
                    var result = eval(data)
                    $("#json").JSONView(result);
                    $("#json-collapsed").JSONView(result, {collapsed: true, nl2br: true});

                    $('#collapse-btn').on('click', function () {
                        $('#json').JSONView('collapse');
                    });
                    $('#expand-btn').on('click', function () {
                        $('#json').JSONView('expand');
                    });

                },
                error: function (error) {
                    alert('网络异常');
                }

            })


        };


        $('#findTenantMsg').on("click", function () {
            getTree();
        });

        $('#findTenantDb').on("click", function () {
            var tenantId = $('#tenantId').val()
            if (tenantId == "") {
                alert("企业ID不可为空!");
                return false;
            }

            $.ajax({
                type: 'Get',
                url: '${CONTEXT_PATH}/organization/tenantDb',
                data: {
                    tenantId: tenantId,
                },
                contentType: "application/json", //必须有
                dataType: 'json',
                async: false,
                success: function (data) {
                    window.open(data)
                },
                error: function (error) {
                    alert('未查到企业路由');
                }

            })
        });

        $(document).ready(function () {
            if ($('#tenantId').val() !== "") {
                $("#findSub").trigger('click');
            }

//         按时间查询
            $('#dateShow').on('click', function () {
                $('#dataSelectModal').modal('show')
            });

//        快速选择时间
            $('#dateShow72,#dateShow24,#dateShow5Day,#dateShowOneMonth,#dateShowThreeMonth,#dateShowOneYear').on('click', function () {
                var thisValue = this.value;
                if (thisValue.indexOf("m") !== -1) {
                    $('#startTime').val("now()-" + this.value);
                    $('#dateShow').html("最近" + parseInt(this.value) + "个月");
                } else if (thisValue.indexOf("y") !== -1) {
                    $('#startTime').val("now()-" + this.value);
                    $('#dateShow').html("最近" + parseInt(this.value) + "年");
                } else if (thisValue.indexOf("d") !== -1) {
                    $('#startTime').val("now()-" + this.value);
                    $('#dateShow').html("最近" + parseInt(this.value) + "天");
                } else {
                    $('#startTime').val("now()-" + this.value);
                    //parseInt() 取出字符串中的数字（以数字开头）
                    $('#dateShow').html("最近" + parseInt(this.value) + "小时");
                }
                //        自动点击
                $("#findSub").trigger('click');
            });

//        apply选择
            $('#dateShowApply').on('click', function () {
                var startTime = $('#startTime').val();
                var endTime = $('#endTime').val();
                if (startTime.indexOf("now()") > -1 && endTime.indexOf("now()") > -1) {
                    $('#dateShow').html("最近" + parseInt(startTime.replace("now()-", '')) + "小时");
                } else {
                    $('#dateShow').html(startTime + " TO " + endTime);
                }
//            自动点击
                $("#findSub").trigger('click');
            });

            /**
             * 检测input变化
             */
            $('#tenantId').bind("input propertychange", function () {
                var tenantIdValue = $('#tenantId').val();

                if (tenantIdValue === null || tenantIdValue === "") {
                    $('#findSub').attr("disabled", "disabled");
                } else {
                    $('#findSub').removeAttr("disabled");
                }
            });

        });

        //    查询
        $('#findSub').on('click', function () {
            $('#loading').removeClass('hide');
            var tenantId = $('#tenantId').val();
            var startTime = $('#startTime').val();
            var endTime = $('#endTime').val();
            var dataObject = {
                tenantId: tenantId,
                ea: tenantId,
                startTime: startTime,
                endTime: endTime,
                describeApiName: ""
            };
            if (tenantId !== "") {
                $('#container').html('');
                $('#dateShow').addClass('hide');
                $('#dataShowDiv').addClass('hide');
                ajaxFindData(dataObject);
            } else {
                $('#hintModal').modal("show");
            }
        });

        //    ajax请求数据
        function ajaxFindData(dataObject) {
            $.ajax({
                url: "${CONTEXT_PATH}/metadata/stat/find",
                contentType: "application/json",
                data: JSON.stringify(dataObject),
                dataType: "json",
                type: "POST",
                traditional: true,
                success: function (data) {
                    $('#warningInfo').addClass('hide');
                    if (data.code === 200) {
                        $('#loading').addClass('hide');
                        var xNameNum = data.xNameNum;
                        var xName = data.xName;

                        /**
                         * 板块信息
                         */
                        var sumMap = data.sumMap;
                        $('#describeSum').html(sumMap.describeSum);
                        $('#noDataDescribeSum').html(sumMap.noDataDescribeSum);
                        $('#dataSum').html(sumMap.dataSum);
                        $('#fieldSum').html(sumMap.fieldSum);

                        $('#dateShow').removeClass('hide');
                        $('#dataShowDiv').removeClass('hide');
                        showMap(xNameNum, xName);
                    } else {
                        $('#loading').addClass('hide');
                        console.log(data.errorInfo);
                        $('#ajaxInfo').html(data.error);
                        $('#warningInfo').removeClass('hide');
                        $('#startTime').val("");
                    }
                }
            });
        }

        //    字段总数点击事件
        $('#fieldInfo').on('click', function () {
            var tenantId = $('#tenantId').val();
            window.location.href = "${ctx}/metadata/stat/field?id=" + tenantId;
        });


        //    展示示意图
        function showMap(dataList, xName) {
            $('#container').highcharts({
                chart: {
//              柱状图
                    type: 'column'
                },
                title: {
                    text: "对象数据概况统计图"
                },
                subtitle: {
//                数据来源:
                    text: ''
                },
                xAxis: {
                    categories: xName,
                    crosshair: true,
                    title: {
                        text: 'describe_api_name'
                    }
                },
                yAxis: {
                    min: 0,
                    tickInterval: 2, // 刻度值
                    title: {
                        text: '对象数据量'
                    }
                },
                credits: {
                    enabled: false, // 禁用版权信息
                    href: 'https://www.fxiaoke.com/',
                    text: 'Copyright:纷享销客',
                    style: {
                        cursor: 'pointer',
                        color: '#909090',
                        fontSize: '10px'
                    }
                },
//      自定义提示内容------------将柱状图信息合并
                tooltip: {
                    shared: true,
                    useHTML: true,
                    borderRadius: 10,             // 边框圆角
                },
                plotOptions: {
                    column: {
                        borderWidth: 0,
                        cursor: 'pointer',
                        point: {
                            events: {
                                click: function () {
                                    console.log(this.category);
                                    var tId = $('#tenantId').val();
                                    var dName = this.category;
                                    window.location.href = "${ctx}/metadata/describe/?tenant_id=" + tId + "&describe_api_name=" + dName;
                                }
                            }
                        }
                    }
                },
                series: [{
                    name: '数量',
                    data: dataList
                }
                ]
            });
        };

        /**
         * 信息提示栏关闭
         */
        $('#closeInfo').on('click', function () {
            $('#warningInfo').addClass('hide');
        });

    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
<#include "dataSelectModal.ftl" />
