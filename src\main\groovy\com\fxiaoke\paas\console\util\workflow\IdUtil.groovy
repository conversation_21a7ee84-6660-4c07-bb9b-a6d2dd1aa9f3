package com.fxiaoke.paas.console.util.workflow

import com.fxiaoke.paas.console.exception.workflow.WorkflowAdminException
import groovy.util.logging.Slf4j
import org.bson.types.ObjectId

/**
 * Created by yangxw on 2018/3/8.
 */
@Slf4j
class IdUtil {

    /**
     * 把一个String对象转换成一个ObjectId对象
     * @param id
     * @return
     */
    def static  generateId(String id) throws WorkflowAdminException {

        ObjectId objectId;
        try {
            objectId = new ObjectId(id);
        } catch (Exception e) {
            log.info("id = {} isn't a valid id", id);
            throw new WorkflowAdminException(3, "id = " + id + " 不是一个有效的id", e);
        }
        return objectId;
    }

    /**
     * 验证id是否是一个有效的数据库的id
     * @param id
     * @throws WorkflowAdminException
     */
    def static  verifyId(String id) throws WorkflowAdminException {

        if (!ObjectId.isValid(id)) {
            log.info("id = {} isn't a valid id", id);
            throw new WorkflowAdminException(3, "id = " + id + " 不是一个有效的id");
        }

    }

}
