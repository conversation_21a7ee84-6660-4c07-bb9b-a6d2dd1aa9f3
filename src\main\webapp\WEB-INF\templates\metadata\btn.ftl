<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
<link type="text/css" href="//static.foneshare.cn/oss/datatables-checkboxes/dataTables.checkboxes.css" rel="stylesheet"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
    <h1 id="h1">按钮/后动作/自定义函数</h1>
    <ol class="breadcrumb">
        <li><a href="../"><i class="fa fa-dashboard"></i>主页</a></li>
        <li><a href=""><i class="fa fa-dashboard"></i>查询</a></li>
    </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title"></h3>
                </div>
                <form class="form-horizontal" action="" method="post" id="myForm" role="form"
                      data-toggle="validator">
                    <div class="box-body">
                        <div class="form-group">
                            <label for="biz" class="col-sm-1 control-label">企业ID</label>
                            <div class="col-sm-2" style="padding-right: 3%">
                                <input type="text" style="border-radius:5px;" class="form-control short-input" id="tenantId" name="tenantId"
                                       value="" placeholder="企业ID" required>
                                <div id="nameMsg" class="help-block with-errors"></div>
                            </div>
                            <div class="col-sm-4">
                                <button type="button" id="findBtnSub" class="btn btn-default" disabled>查询按钮</button>
                                <button type="button" id="findActSub" class="btn btn-primary" disabled>查询后动作</button>
                                <button type="button" id="findFunSub" class="btn btn-info" disabled>查询自定义函数</button>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="box box-info">
                    <div class="box-body hide" id="btnBody">
                        <table id="datatableBtn" class="table table-hover table-bordered" cellpadding="0" width="100%">
                            <thead>
                            <tr>
                                <th>ApiName</th>
                                <th>Label</th>
                                <th>DescribeApiName</th>
                                <th>IsActive</th>
                                <th>IsDeleted</th>
                                <th>LastModifiedTime</th>
                                <th>详情</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="box-body hide" id="actBody">
                        <table id="datatableAct" class="table table-hover table-bordered" cellpadding="0" width="100%">
                            <thead>
                            <tr>
                                <th>Label</th>
                                <th>DescribeApiName</th>
                                <th>ActionType</th>
                                <th>CreateTime</th>
                                <th>详情</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="box-body hide" id="funBody">
                        <table id="datatableFun" class="table table-hover table-bordered" cellpadding="0" width="100%">
                            <thead>
                            <tr>
                                <th>ApiName</th>
                                <th>BindingObjectApiName</th>
                                <th>FunctionName</th>
                                <th>CreateTime</th>
                                <th>详情</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<#--详情-->
<div class="modal fade" id="theInfoModal" tabindex="-1" role="dialog" aria-labelledby="infoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                        class="sr-only">Close</span></button>
                <h4 class="modal-title" id="stopModalLabel"></h4>
            </div>
            <div class="modal-body">
                <pre id="theInfo" style="height: 400px">
                </pre>
            </div>
            <div class="modal-footer">
                <a type="button" class="btn btn-default" data-dismiss="modal">返回</a>
            </div>
        </div>
    </div>
</div>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script>
    $(document).ready(function () {
//        按钮表
        var tableBtn = $("#datatableBtn").DataTable({
//            "deferRender": true,
            "processing": true,
            "ajax": "",
            "columnDefs": [
                {
                    "targets": 6,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return "<button value='" + data + "' onclick='theBtnInfo(value)' class='btn btn-info btn-xs'>详情</button>";
                        }
                        return data;
                    }
                }
            ],
            "language": {
                "url": "${ctx}/static/js/datatables-zh_CN.json"
            },
            "mark": true,
            "order": [[5, 'desc']]
        });
//        后动作表
        var tableAct = $("#datatableAct").DataTable({
//            "deferRender": true,
            "processing": true,
            "ajax": "",
            "columnDefs": [
                {
                    "targets": 4,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return "<button value='" + data + "' onclick='theActInfo(value)' class='btn btn-info btn-xs'>详情</button>";
                        }
                        return data;
                    }
                }
            ],
            "language": {
                "url": "${ctx}/static/js/datatables-zh_CN.json"
            },
            "mark": true,
            "order": [[3, 'desc']]
        });
//        自定义函数表
        var tableFun = $("#datatableFun").DataTable({
//            "deferRender": true,
            "processing": true,
            "ajax": "",
            "columnDefs": [
                {
                    "targets": 4,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return "<button value='" + data + "' onclick='theFunInfo(value)' class='btn btn-info btn-xs'>详情</button>";
                        }
                        return data;
                    }
                }
            ],
            "language": {
                "url": "${ctx}/static/js/datatables-zh_CN.json"
            },
            "mark": true,
            "order": [[3, 'desc']]
        });

//        查询按钮
        $('#findBtnSub').on('click', function () {
            var tenantId = $('#tenantId').val();
            if (tenantId !== null && tenantId !== "") {
                tableBtn.ajax.url("${ctx}/metadata/btn/find-btn?id=" + tenantId).load();
                $('#actBody').addClass('hide');
                $('#funBody').addClass('hide');
                $('#btnBody').removeClass('hide');
            }
            $('#h1').html("查询按钮")
        });

//        查询后动作
        $('#findActSub').on('click', function () {
            var tenantId = $('#tenantId').val();
            if (tenantId !== null && tenantId !== "") {
                tableAct.ajax.url("${ctx}/metadata/btn/find-act?id=" + tenantId).load();
                $('#funBody').addClass('hide');
                $('#btnBody').addClass('hide');
                $('#actBody').removeClass('hide');
            }
            $('#h1').html("查询后动作")
        });

//        查询自定义函数
        $('#findFunSub').on('click', function () {
            var tenantId = $('#tenantId').val();
            if (tenantId !== null && tenantId !== "") {
                tableFun.ajax.url("${ctx}/metadata/btn/find-fun?id=" + tenantId).load();
                $('#actBody').addClass('hide');
                $('#btnBody').addClass('hide');
                $('#funBody').removeClass('hide');
            }
            $('#h1').html("查询自定义函数")
        });


        /**
         * 检测input变化并改变按钮状态
         */
        $('#tenantId').bind("input propertychange", function () {
            var tenantIdValue = $('#tenantId').val();
            if (tenantIdValue === null || tenantIdValue === "") {
                $('#findBtnSub,#findFunSub,#findActSub').attr("disabled", "disabled");
            } else {
                $('#findBtnSub,#findActSub,#findFunSub').removeAttr("disabled");
            }
        });

    });
    //    按钮详情
    function theBtnInfo(btnId) {
        var tenantId = $('#tenantId').val();
        var infoUrl = "${ctx}/metadata/btn/btn-info";
        $('#stopModalLabel').html("按钮详情");
        getInfo(btnId, tenantId, infoUrl);
    }
    //    后动作详情
    function theActInfo(actId) {
        var tenantId = $('#tenantId').val();
        var infoUrl = "${ctx}/metadata/btn/act-info";
        $('#stopModalLabel').html("后动作详情");
        getInfo(actId, tenantId, infoUrl);
    }
    //    自定义函数详情
    function theFunInfo(funId) {
        var tenantId = $('#tenantId').val();
        var infoUrl = "${ctx}/metadata/btn/fun-info";
        $('#stopModalLabel').html("自定义函数详情");
        getInfo(funId, tenantId, infoUrl);
    }

    //    ajax
    function getInfo(id, tenantId, infoUrl) {
        $.getJSON(infoUrl, {
            id: id,
            tenantId: tenantId
        }, function (theInfo) {
            $('#theInfo').JSONView(theInfo.infoMap, {
                collapsed: false,
                nl2br: true,
                recursive_collapser: true
            });
            $('#theInfoModal').modal('show');
        });
    }
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
