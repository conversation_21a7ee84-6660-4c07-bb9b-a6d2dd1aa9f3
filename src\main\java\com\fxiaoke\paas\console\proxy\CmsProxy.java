package com.fxiaoke.paas.console.proxy;

import com.github.autoconf.ConfigFactory;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CmsProxy {


  @Getter
  private String pgbouncerPaasMaster;
  @Getter
  private String pgbouncerPaasSlave;
  @Getter
  private String pgbouncerBiMaster;
  @Getter
  private String pgbouncerBiSlave;
  @Getter
  private static List<String> noValidatePaasTable;
  @Getter
  private static List<String> noValidateBiTable;

  @PostConstruct
  public void init() {

    ConfigFactory.getConfig("variables_endpoint", listener -> {
      pgbouncerPaasMaster = listener.get("pgbouncer_paas_master");
      pgbouncerPaasSlave = listener.get("pgbouncer_paas_slave");
      pgbouncerBiMaster = listener.get("pgbouncer_bi_master");
      pgbouncerBiSlave = listener.get("pgbouncer_bi_master");
    });

    ConfigFactory.getConfig("paas-hamster", iConfig -> {
      noValidatePaasTable = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(iConfig.get("noValidatePaasTable",""));
      noValidateBiTable = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(iConfig.get("noValidateBiTable",""));
    });
  }


}
