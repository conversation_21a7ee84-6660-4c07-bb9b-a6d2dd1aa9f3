package com.fxiaoke.paas.console.mapper.metadata

import com.github.mybatis.mapper.IBatchMapper
import com.github.mybatis.mapper.ICrudMapper
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * Created on 2018/3/19.
 */
interface BtnMapper extends ICrudMapper, IBatchMapper, ITenant<BtnMapper> {

  /**
   * 根据企业ID查询按钮
   * @param tenantId
   * @return
   */
  @Select("SELECT * FROM mt_udef_button WHERE tenant_id = #{tenantId}")
  List<Map<String, Object>> findBtnByTenantId(@Param("tenantId") String tenantId)

  /**
   * 根据id查询按钮详情
   * @param btnId
   * @return
   */
  @Select("SELECT * FROM mt_udef_button WHERE id = #{btnId} AND tenant_id = #{tenantId}")
  Map<String, Object> findBtnByBtnId(@Param("btnId") String btnId, @Param("tenantId") String tenantId)

  /**
   * 根据企业ID查询后动作
   * @param tenantId
   * @return
   */
  @Select("SELECT * FROM mt_udef_action WHERE tenant_id = #{tenantId}")
  List<Map<String, Object>> findActByTenantId(@Param("tenantId") String tenantId)

  /**
   * 根据id查询后动作详情
   * @param actId
   * @return
   */
  @Select("SELECT * FROM mt_udef_action WHERE id = #{actId} AND tenant_id = #{tenantId}")
  Map<String, Object> findActByActId(@Param("actId") String actId, @Param("tenantId") String tenantId)

  /**
   * 根据企业ID查询自定义函数
   * @param tenantId
   * @return
   */
  @Select("SELECT * FROM mt_udef_function WHERE tenant_id = #{tenantId} AND is_current = true")
  List<Map<String, Object>> findFunByTenantId(@Param("tenantId") String tenantId)

  /**
   * 根据id查询自定义函数详情
   * @param funId
   * @return
   */
  @Select("SELECT * FROM mt_udef_function WHERE id = #{funId}")
  Map<String, Object> findFunByFunId(@Param("funId") String funId)


}
