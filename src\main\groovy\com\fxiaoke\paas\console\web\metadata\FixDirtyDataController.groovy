package com.fxiaoke.paas.console.web.metadata

import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.service.metadata.FixDirtyDataService
import lombok.extern.slf4j.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody

@Controller
@Slf4j
@RequestMapping("/metadata-operate/fix-dirty-data")
class FixDirtyDataController {

  @Autowired
  FixDirtyDataService fixDirtyDataService
  @RequestMapping("")
  def page(){
    "/metadata/fixDirtyData";
  }

  @RequestMapping("/fix")
  @ResponseBody
  @SystemControllerLog(description = "修复脏数据")
  def fixDirtyData(String tenantId, String describeApiName, String fieldApiNames, String dirtyValue, String id){
    def result = fixDirtyDataService.fixDirtyData(tenantId, describeApiName, fieldApiNames, dirtyValue, id);
    return ["code": 200, "info": result]
  }
}
