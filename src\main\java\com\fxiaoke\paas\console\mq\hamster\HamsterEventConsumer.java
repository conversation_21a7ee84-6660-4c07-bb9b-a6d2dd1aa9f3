package com.fxiaoke.paas.console.mq.hamster;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.hamster.constant.HamsterConstant;
import com.facishare.hamster.entity.DbSource;
import com.facishare.hamster.listener.HamsterEventListener;
import com.facishare.hamster.module.HamsterEvent;
import com.facishare.hamster.module.Module;
import com.facishare.hamster.pojo.HamsterTaskPojo;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.jdbc.JdbcConnection;
import com.fxiaoke.paas.console.mq.hamster.validation.TableDataCountService;
import com.fxiaoke.paas.console.mq.hamster.validation.TableDataReport;
import com.fxiaoke.paas.console.service.hamster.HamsterTaskService;
import com.fxiaoke.paas.console.service.metadata.JdbcService;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.SQLException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class HamsterEventConsumer extends HamsterEventListener {

  @Autowired
  private TableDataCountService tableDataCountService;
  @Autowired
  private HamsterTaskService hamsterTaskService;

  @Autowired
  private DbRouterClient dbRouterClient;

  @Autowired
  private JdbcService jdbcService;

  String selectBizAccount = "select id, value112 as ei from sch_1.biz_account where tenant_id = ? and value112 is not null and value112 = ?;";

  String updateBizAccount = "update sch_1.biz_account set value266 = ? where tenant_id = ? and id = ?;";


  public HamsterEventConsumer() {
    super("paas-console-consumer", Lists.newArrayList(Module.DataValidation, Module.RouteSwitchValidation, Module.PaasBiCopier));
  }

  @Override
  protected boolean execute(String action, HamsterEvent hamsterEvent) {

    if (Module.DataValidation.name().equals(action)) {
      try {
        Collection<TableDataReport> calculate = tableDataCountService.calculate(hamsterEvent.getHamsterTask().getTenantId(), hamsterEvent.getHamsterTask(), false);
        List<TableDataReport> error = calculate.stream().filter(x -> !x.areEqual()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(error)) {
          sendHamsterEventError(action, hamsterEvent, JSON.toJSONString(error));
        } else {
          sendHamsterEventSuccess(action, hamsterEvent, JSON.toJSONString(calculate));
        }
        return false;

      } catch (Exception e) {

        sendHamsterEventError(action, hamsterEvent, e.getMessage());
        return false;

      } finally {

      }
    } else if (Module.RouteSwitchValidation.name().equals(action)) {
      IChangeableConfig config = ConfigFactory.getConfig(HamsterConstant.fsPaasCopierUrl);
      HamsterTaskPojo task = hamsterEvent.getHamsterTask();
      JSONArray jsonArray = JSONArray.parseArray(config.getString());

      if (jsonArray == null) {
        log.error("RouteSwitchValidation copierUrl is null");
        return false;
      }

      List<DbSource> result = Lists.newArrayList();

      int size = jsonArray.size();
      for (int i = 0; i < size; i++) {
        JSONObject jsonObject = jsonArray.getJSONObject(i);
        List<String> cmsTenantIds = (List<String>) jsonObject.get("tenantIds");
        DbSource dbSource = new DbSource();
        dbSource.setTenantIds(cmsTenantIds);
        dbSource.setOldUrl(jsonObject.getString("old"));
        dbSource.setNewUrl(jsonObject.getString("new"));
        dbSource.setBiNewUrl(jsonObject.getString("bi-new"));
        dbSource.setBiOldUrl(jsonObject.getString("bi-old"));
        result.add(dbSource);
      }

      String tenantId = hamsterEvent.getHamsterTask().getTenantId();

      Optional<DbSource> currentDbSource = result.stream().filter(dbSource -> dbSource.getTenantIds().contains(tenantId)).findFirst();
      if (!currentDbSource.isPresent()) {
        log.error("RouteSwitchValidation 没有对应的配置,old={},new={},oldBi={},newBi={}", task.getPaasPgbouncerOld(), task.getPaasPgbouncerNewMaster(), task.getBiPgbouncerOld(), task.getBiPgbouncerNewMaster());
        sendHamsterEventError(action, hamsterEvent, "没有从配置中找到企业 tenantId=" + tenantId);
      }

      RouterInfo routerInfoPaas = dbRouterClient.queryRouterInfo(tenantId, "CRM", "application", "postgresql");
      RouterInfo routerInfoBi = dbRouterClient.queryRouterInfo(tenantId, "BI", "application", "postgresql");
      String paasUrl = HamsterConstant.DB_HOST_HEAD + routerInfoPaas.getMasterProxyUrl() + task.getJdbcNewPaasMaster().substring(task
                                                                                                                                 .getJdbcNewPaasMaster()
                                                                                                                                 .indexOf("/"));
      String biUrl = HamsterConstant.DB_HOST_HEAD + routerInfoBi.getMasterProxyUrl() + task.getJdbcNewBiMaster().substring(task
                                                                                                                           .getJdbcNewBiMaster()
                                                                                                                           .indexOf("/"));

      //赋权
      try {
        JdbcConnection connectionNewPaas = tableDataCountService.getConnection(tenantId, "PAAS", true, task, false);
        connectionNewPaas.executeUpdate("grant select,insert,delete,update,trigger,references on table public.fd_id_generator to fs_pgdb_b_u_metadata;");
      } catch (Exception e) {
        log.error("grant fd_id_generator error", e);
      }

      //将fs客户对象设为schema

      try {
        RouterInfo routerInfo = dbRouterClient.queryRouterInfo("1", "CRM", "fs-paas-data-auth", "postgresql", true);
        JdbcConnection connection = jdbcService.connection(routerInfo.getJdbcUrl(), routerInfo.getUserName(), routerInfo.getPassWord());

        List<Map<String, String>> standaloneEntityList = Lists.newArrayList();
        try {
          connection.prepareQuery(selectBizAccount, preparer -> {
            preparer.setString(1, "1");
            preparer.setString(2, hamsterEvent.getHamsterTask().getTenantId());
          }, rs -> {
            while (rs.next()) {
              Map<String, String> standaloneEntity = Maps.newHashMap();
              standaloneEntity.put("id", rs.getString("id"));
              standaloneEntity.put("ei", rs.getString("ei"));
              standaloneEntity.put("schemaType", rs.getString("independentDB"));
              standaloneEntityList.add(standaloneEntity);
            }
          });
        } catch (Exception e) {
          log.error("getAccountByTenantId error", e);
        }

        try {
          connection.prepareUpdateBatch(updateBizAccount, preparer -> {
            for (Map<String, String> item : standaloneEntityList) {
              try {
                preparer.setString(1, item.get("schemaType"));
                preparer.setString(2, item.get("ei"));
                preparer.setString(3, item.get("id"));
                preparer.addBatch();
              } catch (SQLException e) {
                log.error("updateAccountStandalone update sql error, tenantId: {}, id: {}", item.get("ei"), item.get("id"), e);
              }
            }
          });
        } catch (Exception e) {
          log.error("updateAccountStandalone error", e);
        }
      } catch (Exception e) {
        log.error("updateAccount error", e);
      }

      if (paasUrl.equals(currentDbSource.get().getNewUrl()) && biUrl.equals(currentDbSource.get().getBiNewUrl())) {
        sendHamsterEventSuccess(action, hamsterEvent, "当前paas路由" + routerInfoPaas.getJdbcUrl() + "当前bi路由" + routerInfoBi.getJdbcUrl());
      } else {
        sendHamsterEventError(action, hamsterEvent,
                              "当前paas路由" + routerInfoPaas.getJdbcUrl() + "当前bi路由" + routerInfoBi.getJdbcUrl() + "paasPgbouncer" + paasUrl +
                                "biPgbouncer" + biUrl);
      }
    } else if (Module.PaasBiCopier.name().equals(action)) {
      try {
        String tenantId = hamsterEvent.getHamsterTask().getTenantId();
        String result = "\n Dump:" + hamsterTaskService.paasBiCopierDump(tenantId);
        result += "\n " + "Recheck:" + hamsterTaskService.paasBiCopierRecheck(tenantId);
        sendHamsterEventSuccess(action, hamsterEvent, "paas-bi-copier: " + result);
      } catch (Exception e) {
        sendHamsterEventError(action, hamsterEvent, e.getMessage());
      }

    }
    return false;
  }

  @Override
  protected boolean rollback(String action, HamsterEvent event) {
    return true;
  }

  @Override
  protected boolean pause(String action, HamsterEvent event) {
    return true;
  }

  @Override
  protected boolean resume(String action, HamsterEvent event) {
    return true;
  }

}
