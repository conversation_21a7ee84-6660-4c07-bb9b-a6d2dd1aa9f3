package com.fxiaoke.paas.console.web.metadata

import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.bean.metadata.DbResource
import com.fxiaoke.paas.console.service.metadata.DescribeService
import com.fxiaoke.paas.console.service.metadata.StatService
import com.fxiaoke.paas.console.util.DateFormatUtil
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.Resource
import java.sql.SQLException
import java.text.SimpleDateFormat
import java.util.function.Function
import java.util.stream.Collectors

/**
 * <AUTHOR> Created on 2018/3/5.
 */
@Controller
@Slf4j
@RequestMapping("/metadata/describe")
class DescribeController {
  @Resource
  private DescribeService describeService
  @Autowired
  StatService statService

  /**
   * 跳转查询describe页面
   * @return
   */
  @RequestMapping(value = "")
  def describe(@RequestParam(defaultValue = "") String tenant_id,
               @RequestParam(defaultValue = "") String describe_api_name, ModelMap model) {
    model.put("tenant_id", tenant_id)
    model.put("describe_api_name", describe_api_name)
    "metadata/describe"
  }

  /**
   * 获取describe列表
   * @param tenantId
   * @param apiName
   * @return
   */
  @RequestMapping(value = "/list", method = RequestMethod.GET)
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 获取describe列表")
  def findDescribe(@RequestParam String tenantId, @RequestParam(required = false) String apiName, @RequestParam(required = false) String displayName) {
    def json = []
    try {
      List<Map<String, String>> describeList = describeService.loadDescribeFromPg(tenantId.trim(), apiName, displayName)
      json = describeList.collect { describe ->
        [describe.get("describe_id"), describe.get("tenant_id"), describe.get("describe_api_name"), describeService.getStoreTableName(describe.get("tenant_id"), describe.get("describe_api_name")), getEsIndexName(tenantId, apiName), describe.get("display_name"), describe.get("last_modified_time") ? DateFormatUtil.formatLong(describe.get("last_modified_time") as Long) : "", ""]
      }

    } catch (Exception e) {
      log.error("findDescribe is error: ", e)
    }
    return ["data": json]
  }

  String getEsIndexName(String tenantId, String objectApiName) {
    return "data_search_" + Math.abs(Objects.toString(tenantId + objectApiName).hashCode() % 101)
  }

  /**
   * 获取describe详情
   * @param tenantId
   * @param describeId
   * @return
   */
  @RequestMapping(value = "/info", method = RequestMethod.GET)
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 获取describe详情")
  def describeInfo(@RequestParam String tenantId, @RequestParam String describeId) {
    Map<String, String> describe = describeService.describeInfo(tenantId.trim(), describeId.trim())
    ["describeInfo": describe]
  }

  /**
   * 获取指定对象的数据量趋势
   * @param tenant_id
   * @param describe_api_name
   */
  @RequestMapping(value = "ajax-chart")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 获取指定对象的数据量趋势")
  def ajaxChart(@RequestParam String tenant_id, @RequestParam String describe_api_name) {
    List<Map<String, Object>> chartMapList = statService.findDescribeDataLine(tenant_id.trim(), describe_api_name.trim())
    if (chartMapList == null) {
      return ["mapList": []]
    }
    List<Object> modifiedTimeList = Lists.newLinkedList()
    SimpleDateFormat dtf = new SimpleDateFormat("yyyy-MM-dd")
    chartMapList.each { map ->
      modifiedTimeList.add(dtf.format(map.get("last_modified_time")))
    }

//        分组
    Map<String, Object> chart = modifiedTimeList.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))

    /**
     * map根据key排序
     */
    List<Map.Entry<String, Integer>> list = new ArrayList<Map.Entry<String, Integer>>(chart.entrySet())
    Collections.sort(list, new Comparator<Map.Entry<String, Integer>>() {
      int compare(Map.Entry<String, Integer> o1, Map.Entry<String, Integer> o2) {
        return (o1.getKey()).toString().compareTo(o2.getKey())
      }
    })

    List<List<Object>> mapList = Lists.newLinkedList()
    list.each { it ->
      List<Object> objectList = Lists.newLinkedList()
      objectList.add(dtf.parse(it.key))
      objectList.add(it.value)
      mapList.add(objectList)
    }
    return ["mapList": mapList]
  }


}
