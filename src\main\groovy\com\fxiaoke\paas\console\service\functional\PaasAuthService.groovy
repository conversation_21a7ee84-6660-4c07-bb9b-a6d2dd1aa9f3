package com.fxiaoke.paas.console.service.functional

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.fxiaoke.common.http.handler.SyncCallback
import com.fxiaoke.common.http.spring.OkHttpSupport
import com.fxiaoke.paas.console.mapper.metadata.SqlQueryMapper
import com.github.autoconf.ConfigFactory
import groovy.util.logging.Slf4j
import okhttp3.MediaType
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import javax.annotation.Resource

/**
 * <AUTHOR> @date 2020/9/8
 */
@Service
@Slf4j
class PaasAuthService {

  @Autowired
  SqlQueryMapper sqlQueryMapper

  @Resource(name = "httpSupport")
  OkHttpSupport okHttpSupport
  private final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8")

  private String addFuncUrl
  private String addFuncAccessUrl
  private String addMenuUrl

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-console-auth", { config ->
      addFuncUrl = config.get("add-system-func-url", "http://fs-paas-auth-provider:80/fs-paas-auth/init/system-db/func")
      addFuncAccessUrl = config.get("add-system-funcAccess-url", "http://fs-paas-auth-provider:80/fs-paas-auth/init/system-db/func-access")
      addMenuUrl = config.get("add-system-addMenuUrl-url", "http://fs-paas-auth-provider:80/fs-paas-auth/init/system-db/menu")
    })
  }

  def synchronized addFunc(JSONArray funcArray) {
    def result = sqlQueryMapper.setTenantId("-100").sqlQuery("SELECT max(version) AS version FROM fc_func")
    def version = result[0]["version"] as int

    for (i in 0..<funcArray.size()) {
      funcArray.getJSONObject(i).put("isGray", 0)
      funcArray.getJSONObject(i).put("version", version + 1)
    }

    requestPaasAuthServer(addFuncUrl, funcArray)
  }

  def synchronized addFuncAccess(JSONArray funcAccessArray) {
    def result = sqlQueryMapper.setTenantId("-100").sqlQuery("SELECT max(version) AS version FROM fc_func_access WHERE version!=1000000")
    def version = result[0]["version"] as int

    for (i in 0..<funcAccessArray.size()) {
      funcAccessArray.getJSONObject(i).put("isGray", 0)
      String appId = funcAccessArray.getJSONObject(i).getString("appId")
      //如果是CRM管理员则version为1000000
      if ("CRM" != appId || "00000000000000000000000000000006" == funcAccessArray.getJSONObject(i).getString("roleCode")) {
        funcAccessArray.getJSONObject(i).put("version", 1000000)
      } else {
        funcAccessArray.getJSONObject(i).put("version", version + 1)
      }
    }

    requestPaasAuthServer(addFuncAccessUrl, funcAccessArray)
  }

  def synchronized addMenu(JSONArray menuArray) {
    def result = sqlQueryMapper.setTenantId("-100").sqlQuery("SELECT max(version) AS version FROM fc_menu")
    def version = result[0]["version"] as int

    for (i in 0..<menuArray.size()) {
      menuArray.getJSONObject(i).put("isGray", 0)
      menuArray.getJSONObject(i).put("version", version + 1)
    }

    requestPaasAuthServer(addMenuUrl, menuArray)
  }

  Object requestPaasAuthServer(String url, JSONArray data) {
    Request request = new Request.Builder().url(url).post(RequestBody.create(JSON_TYPE, data.toJSONString())).build()
    return okHttpSupport.syncExecute(request, new SyncCallback() {
      @Override
      Object response(Response response) throws Exception {
        if (response == null || response.body() == null) {
          throw new Exception("request fs-paas-auth server error")
        }
        JSONObject jsonObject = JSONObject.parseObject(response.body().string())
        if (jsonObject.getInteger("errCode") != 0) {
          log.error("request fs-paas-auth failed,errCode:{},errMessage:{}", jsonObject.getInteger("errCode"), jsonObject.getString("errMessage"))
          throw new Exception(jsonObject.getString("errMessage"))
        }
        return jsonObject.getString("errMessage")
      }
    });
  }
}
