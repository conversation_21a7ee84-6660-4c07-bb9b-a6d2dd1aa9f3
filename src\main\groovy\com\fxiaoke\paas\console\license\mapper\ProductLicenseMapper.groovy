package com.fxiaoke.paas.console.license.mapper


import com.fxiaoke.paas.console.entity.license.ProductLicenseEntity
import org.apache.ibatis.annotations.Param

/**
 * <AUTHOR>
 */
interface ProductLicenseMapper {

  List<String> queryProductBought(@Param("productVersion") String productVersion, @Param("expired") Long now);

  List<ProductLicenseEntity> queryProductBoughtV2(@Param("productVersion") String productVersion, @Param("expired") Long now);

  List<ProductLicenseEntity> queryProductLicenseByOrderNumber(@Param("tenantId") String tenantId,
                                                              @Param("orderNumbers") Set<String> orderNumbers);
}
