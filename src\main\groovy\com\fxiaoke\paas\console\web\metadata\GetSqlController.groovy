package com.fxiaoke.paas.console.web.metadata

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.paas.console.service.OKHttpService
import com.github.autoconf.ConfigFactory
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils

import org.springframework.beans.factory.annotation.Autowired

import org.springframework.stereotype.Controller

import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody


import javax.annotation.PostConstruct



/**
 * <AUTHOR> @date 2019/9/16 4:34 PM
 */
@Controller
@Slf4j
@RequestMapping("/metadata/getSql")
class GetSqlController {
  @Autowired
  OKHttpService okHttpService

  private String metadataStaticUrl

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-console-url", { iConfig ->
      this.metadataStaticUrl = iConfig.get("metadata-static-url")
    })
  }

  @RequestMapping(path = "/page")
  def getSql(){
     "metadata/getSql"
  }



  @PostMapping(path = "/getSearchSql", produces = "application/json;charset=utf-8")
  @ResponseBody
  def getSearchSql(String tenantId, String apiName, String userId, String templateJson, boolean isAuth) {
    if (StringUtils.isAnyBlank(tenantId, apiName)) {
      return ["code": 200, "info": "{\"message\":\"tenantId和apiName不能为空\"}"]
    }
    JSONObject jsonObject = new JSONObject()
    jsonObject.put("tenantId", tenantId)
    jsonObject.put("objectDescribeAPIName", apiName)
    jsonObject.put("userId", userId)
    jsonObject.put("templateJson", templateJson)
    String jsonResult
    if (isAuth) {
      jsonResult = okHttpService.postJSON(this.metadataStaticUrl + "/paas/metadata/data/find/by/template/auth", jsonObject)
    } else {
      jsonResult = okHttpService.postJSON(this.metadataStaticUrl + "/paas/metadata/data/find/by/template/sql", jsonObject)
    }
    Map<String, String> map = Maps.newHashMap()
    map.put("sql", JSONObject.parseObject(jsonResult).getString("result"))
    return ["code": 200, "info": map]

  }
}