package com.fxiaoke.paas.console.web

import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.entity.log.SpecialTable
import com.fxiaoke.paas.console.mapper.log.SpecialTableMapper
import com.fxiaoke.paas.console.service.metadata.SpecialTableService
import com.github.shiro.support.ShiroCasRealm
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.servlet.mvc.support.RedirectAttributes

import javax.annotation.Resource
import javax.validation.Valid
import java.text.SimpleDateFormat

/**
 * 专表数据统计管理
 * <AUTHOR>
 * Created on 2018/7/5.
 */
@Controller
@RequestMapping(value = "/table/special")
@Slf4j
class SpecialTableController {

  @Autowired
  SpecialTableService specialTableService
  @Autowired
  SpecialTableMapper specialTableMapper

  @Resource(name = "casRealm")
  private ShiroCasRealm cas

  /**
   * 跳转专表数据统计管理
   * @return
   */
  @RequestMapping(value = "/created")
  def created() {
    "metadata/special-table"
  }

  /**
   * 创建专表
   * @param storeTable
   * @param r
   * @return
   */
  @SystemControllerLog(description = "元数据 -- 专表创建")
  @RequestMapping(value = "/created", method = RequestMethod.POST)
  def create(@Valid SpecialTable specialTable, RedirectAttributes r) {
    try {
      specialTable.operator = cas.getCurrentUser().getDisplayName()
      specialTable.createdTime = new Date()
      specialTable.status = "using"
      if (specialTableService.insert(specialTable) == 0) {
        r.addFlashAttribute("error", "创建失败，可能专表名已经存在")
        return "redirect:/table/special/created"
      }
      r.addFlashAttribute("success", "创建成功，专表名=" + specialTable.storeTableName)
    } catch (Exception e) {
      log.error("创建异常，store_table_name={},describeApiName={},error:", specialTable.storeTableName, specialTable.describeApiName, e)
      r.addFlashAttribute("error", "创建失败：" + e.printStackTrace())
    }
    "redirect:/table/special/created"
  }

  /**
   * 查询专表数据
   */
  @SystemControllerLog(description = "元数据 -- 专表查询")
  @RequestMapping(value = "/get-store-table")
  @ResponseBody
  def getDesignedTable() {
    List<SpecialTable> storeTableList = specialTableMapper.findAll()
//    List<SpecialTable> storeTableList = Lists.newArrayList()
    def fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    def json = storeTableList.collect { it ->
      [it.id, it.describeApiName, it.label, it.storeTableName, it.status, it.team, fmt.format(it.createdTime), fmt.format(it.modifyTime), it.operator, it.id]
    }
    return ["data": json]
  }

  /**
   * 根据id查询
   * @param id
   * @return
   */
  @RequestMapping(value = "/findById")
  @ResponseBody
  def findById(@RequestParam String id) {
    return ["storeTable", specialTableMapper.findById(id as Long)]
  }

  /**
   * 编辑
   * @param storeTable
   * @param r
   * @return
   */
  @SystemControllerLog(description = "元数据 -- 编辑专表")
  @RequestMapping(value = "/edit")
  def edit(@Valid SpecialTable specialTable, RedirectAttributes r) {
    try {
      specialTable.operator = cas.getCurrentUser().getDisplayName()
      specialTableMapper.update(specialTable)
      r.addFlashAttribute("success", "编辑成功，id=" + specialTable.id)
    } catch (Exception e) {
      log.error("编辑失败，id = {},error:", specialTable.id, e)
      r.addFlashAttribute("error", "编辑失败，id=" + specialTable.id)
    }
    "redirect:/table/special/created"
  }

  /**
   * 建表sql获取
   * @param id
   * @return
   */
  @RequestMapping(value = "/getSql")
  @ResponseBody
  def getSql(@RequestParam String id) {
    return ["sql", specialTableService.getCreateSpecialTableSql(id)]
  }


}
