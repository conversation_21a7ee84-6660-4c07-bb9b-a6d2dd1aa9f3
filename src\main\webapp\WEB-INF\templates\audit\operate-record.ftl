<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
  <h1>操作记录</h1>
  <ol class="breadcrumb">
    <li><a href="../"><i class="fa fa-dashboard"></i>任务主页</a></li>
    <li><a href=""><i class="fa fa-dashboard"></i>审计日志</a></li>
  </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <div class="box-header with-border">
          <h3 class="box-title"></h3>
        </div>
        <div class="box-body">
          <table id="datatable" class="table table-hover table-bordered" cellpadding="0" width="100%">
            <thead>
            <tr>
              <th>ID</th>
              <th>操作时间</th>
              <th>操作人</th>
              <th>操作描述</th>
            <#--<th>状态</th>-->
              <th>参数</th>
            </tr>
            </thead>
          </table>
        </div>
        <div class="box-footer clearfix">
          <div class="clearfix">
            <div class="hide">
              <div class="form-group">
                <div class="col-sm-4">
                  <input type="text" class="form-control" style="border-radius:5px;" id="logId" placeholder="ID(必填)" required>
                </div>
              </div>
              <button class="btn btn-danger" id="deleteLog">删除</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<#--参数详情-->
<div class="modal fade" id="theAuditLogModal" tabindex="-1" role="dialog" aria-labelledby="infoModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                class="sr-only">Close</span></button>
        <h4 class="modal-title" id="stopModalLabel"></h4>
      </div>
      <div class="modal-body">
                <pre id="auditLogInfo" style="height: 400px">
                </pre>
      </div>
      <div class="modal-footer">
        <a type="button" class="btn btn-default" data-dismiss="modal">返回</a>
      </div>
    </div>
  </div>
</div>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script>
  $(document).ready(function () {
    var table = $("#datatable").DataTable({
      "processing": true,
      "displayLength": 25,
      "ajax": "${ctx}/audit/log",
      "language": {
        "url": "${ctx}/static/js/datatables-zh_CN.json"
      },
      "autoWidth": false,
      "columnDefs": [
        {"width": "55%", "targets": 4}
      ],
      "stateSave": true,
      "mark": true,
      // "scrollX": true,
      "order": [[1, 'desc']]
    });

  });

  $('#deleteLog').on('click', function () {
    var id = $('#logId').val();
    $.getJSON("${ctx}/audit/delete?id=" + id);
  });


</script>
</#assign>
<#include "../layout/layout-main.ftl" />
