<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">
  <bean id="dbRouterClient" class="com.facishare.paas.pod.client.DbRouterClient"/>
  <bean id="consoleTenantPolicy" class="com.fxiaoke.paas.console.service.ConsoleTenantPolicy"/>
  <!--审计日志数据源-->
  <bean id="logDatasource" class="com.github.mybatis.spring.DynamicDataSource">
    <property name="configName" value="db-paas-console-audit-log"/>
  </bean>

  <bean id="logSqlSessionFactory" class="com.github.mybatis.spring.SessionFactoryBean">
    <property name="dataSource" ref="logDatasource"/>
    <property name="typeAliasesPackage" value="com.fxiaoke.paas.console.entity.log"/>
    <property name="configLocation" value="classpath:mybatis/mybatis-config.xml"/>
  </bean>

  <!-- 扫描mapper并让它们自动连接 -->
  <bean id="logScanner" class="com.github.mybatis.spring.ScannerConfigurer">
    <property name="basePackage" value="com.fxiaoke.paas.console.mapper.log,com.fxiaoke.paas.console.mapper.hamster"/>
    <property name="sqlSessionFactoryBeanName" value="logSqlSessionFactory"/>
  </bean>


  <!--定义事务管理器-->
  <bean id="transactionManager"
        class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
    <property name="dataSource" ref="datasource"/>
  </bean>

  <tx:annotation-driven/>

  <!---->
  <!--<bean id="myBatisRoutePolicy" class="com.facishare.paas.pod.mybatis.MyBatisRoutePolicy">-->
  <!--<property name="biz" value="CRM"/>-->
  <!--<property name="application" value="paas-console"/>-->
  <!--<property name="dialect" value="postgresql"/>-->
  <!--</bean>-->

  <bean id="licenseSource" class="com.github.mybatis.spring.DynamicDataSource">
    <property name="configName" value="fs-paas-license-db"/>
    <property name="sectionNames" value="common,license-provider"/>
  </bean>
  <bean id="pgLocalSqlSessionFactory" class="com.github.mybatis.spring.SessionFactoryBean">
    <property name="dataSource" ref="licenseSource"/>
    <property name="typeAliasesPackage" value="com.fxiaoke.paas.console.entity"/>
    <property name="configLocation" value="classpath:mybatis/mybatis-config.xml"/>
    <property name="mapperLocations" value="classpath:mapper/*.xml"/>
  </bean>
  <bean id="pgLocalScanner" class="com.github.mybatis.spring.ScannerConfigurer">
    <property name="basePackage" value="com.fxiaoke.paas.console.license.mapper"/>
    <property name="sqlSessionFactoryBeanName" value="pgLocalSqlSessionFactory"/>
  </bean>

  <bean id="datasource" class="com.github.mybatis.spring.DynamicDataSource">
    <property name="configName" value="db-paas-console"/>
    <property name="tenantPolicy" ref="consoleTenantPolicy"/>
  </bean>
  <!-- 定义 SqlSessionFactory -->
  <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
    <property name="dataSource" ref="datasource"/>
    <property name="typeAliasesPackage" value="com.fxiaoke.paas.console.entity"/>
    <property name="configLocation" value="classpath:mybatis/mybatis-config.xml"/>
    <property name="mapperLocations" value="classpath:mapper/*.xml"/>
  </bean>
  <!-- 扫描mapper并让它们自动连接 -->
  <bean id="scanner" class="com.github.mybatis.spring.ScannerConfigurer">
    <property name="basePackage" value="com.fxiaoke.paas.console.mapper"/>
    <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
  </bean>




</beans>
