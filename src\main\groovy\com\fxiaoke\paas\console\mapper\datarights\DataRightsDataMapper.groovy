package com.fxiaoke.paas.console.mapper.datarights

import com.fxiaoke.paas.console.entity.datarights.DepartmentUser
import com.fxiaoke.paas.console.entity.datarights.DtAuth
import com.fxiaoke.paas.console.entity.datarights.EntityShareGroup
import com.fxiaoke.paas.console.entity.datarights.EntityShareReceive
import com.fxiaoke.paas.console.entity.datarights.GroupUser
import com.fxiaoke.paas.console.entity.datarights.Principal
import com.fxiaoke.paas.console.entity.datarights.RoleMember
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * @date 2019/3/29 下午4:40
 *
 */
interface DataRightsDataMapper extends ITenant<DataRightsDataMapper> {

  String PRINCIPAL = "1"
  String JOINT_FOLLOWER = "2"
  String SERVICE_PERSONNEL = "3"
  String ORDINARY_MEMBER = "4"
/**
 * 查询相关团队角色数据
 * @param tenantId 企业ID
 * @param objectId 数据ID
 * @param roleType 角色类型
 * @return
 */
  List<Principal> queryTeamMember(@Param("tenantId") String tenantId,
                                  @Param("objectId") String objectId,
                                  @Param("roleType") String roleType)

  List<Map<String, String>> queryDataRelation(@Param("schema") String schema,
                                              @Param("tenantId") String tenantId,
                                              @Param("apiName") String apiName, @Param("dataIds") Collection<String> dataIds)

  /**
   * 查询数据归属部门(通表)
   * @param tenantId
   * @param objectId
   * @return
   */
  Map<String, String> queryDataOwnDepartment(@Param("tenantId") String tenantId,
                                             @Param("objectId") String objectId)

/**
 * 查询数据归属部门(专表)
 * @param tenantId
 * @param objectId
 * @param storeTableName
 * @param tenantIdName
 * @param objectIdName
 * @return
 */
  Map<String, String> queryDataOwnDepartmentSpecial(
          @Param("tenantId") String tenantId,
          @Param("objectId") String objectId,
          @Param("storeTableName") String storeTableName,
          @Param("objectIdName") String objectIdName)
  /**
   * 查询数据归属部门(专表预置对象)
   * @param tenantId
   * @param objectId
   * @return
   */
  Map<String, String> queryDataOwnDepartmentSpecialBiz(@Param("tenantId") String tenantId,
                                                       @Param("objectId") String objectId,
                                                       @Param("storeTableName") String storeTableName)

  /**
   * 查询基于来源的共享规则
   * @param tenantId 企业ID
   * @param objectId 数据ID
   * @param ids 角色数据ID
   * @return
   */
  List<Map<String, Object>> queryEntityShare(@Param("tenantId") String tenantId,
                                             @Param("objectId") String objectId,
                                             @Param("ids") Set<String> ids)

  /**
   * 查询分组下用户数据
   * @param tenantId 企业ID
   * @param groupId 分组ID
   * @return
   */
  GroupUser queryGroupUserByGroupId(@Param("tenantId") String tenantId,
                                    @Param("groupId") String groupId)

  /**
   * 查询部门下所有成员
   * @param tenantId
   * @param deptId
   * @return
   */
  List<DepartmentUser> queryDepartmentUserByDeptId(@Param("tenantId") String tenantId,
                                                   @Param("deptId") String deptId)

/**
 * 通过ApiName查询基于条件的共享规则规则组
 * @param tenantId
 * @param apiName
 * @return
 */
  List<EntityShareGroup> queryEntityShareGroupByApiName(@Param("tenantId") String tenantId,
                                                        @Param("apiName") String apiName)

  /**
   * 通过ApiName查询基于条件共享规则目标方
   * @param tenantId
   * @param apiName
   * @return
   */
  List<EntityShareReceive> queryEntityShareReceiveByApiName(@Param("tenantId") String tenantId,
                                                            @Param("apiName") String apiName)

  /**
   * 查询数据下当前角色类型的所有用户信息
   * @param tenantId
   * @param roleType
   * @param objectId
   * @return
   */
  List<RoleMember> queryRoleMemberByRoleTypeAndObjectId(@Param("tenantId") String tenantId,
                                                        @Param("roleType") String roleType,
                                                        @Param("objectId") String objectId)

  DtAuth queryDtAuthByObjectId(
          @Param("authTable") String authTable,
          @Param("table") String table,
          @Param("objectId") String objectId,
          @Param("tenantId") String tenantId)

  List<Map<String, Object>> queryEntityShareGroupByRuleId(@Param("tenantId") String tenantId, @Param("ruleIds") Set<String> ruleIds)

  List<Map<String, Object>> queryReceiveShareGroupByRuleId(@Param("tenantId") String tenantId, @Param("ruleIds") Set<String> ruleIds)


  Map<String, Object> queryRoleOrDeptOrUserOrGroupByTypeAndId(@Param("tenantId") String tenantId, @Param("id") String id, @Param("type") String type)


  @Select("select * from \${storeTableName} where tenant_id=#{tenantId} and object_describe_api_name=#{apiName} and id=#{id}")
  Map<String, Object> queryData(
          @Param("tenantId") String tenantId, @Param("apiName") String apiName, @Param("storeTableName") String storeTableName, @Param("id") String id)
}