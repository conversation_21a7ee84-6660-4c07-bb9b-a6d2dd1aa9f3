package com.fxiaoke.paas.console.web.i18n

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.paas.console.service.OKHttpService
import com.github.autoconf.ConfigFactory
import com.google.common.base.Splitter
import com.google.common.collect.Sets
import org.apache.commons.collections.CollectionUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

import javax.annotation.PostConstruct

/**
 * <AUTHOR>
 * @date 2020/7/13 11:13
 *
 */
@RestController
@RequestMapping(path = "i18n/migrate")
class I18nController {
  @Autowired
  private OKHttpService okHttpService
  private refreshUrl

  @PostConstruct
  void init() {

    ConfigFactory.getConfig("fs-paas-console-datarights", { iConfig ->
      this.refreshUrl = iConfig.get("HISTORY_DATA_AUTH_CODE_REFRESH_URL")
    })

  }

  @RequestMapping(path = "migrate/start")
  def migrateStart(String tenantIds) {
    String migrateUrl = this.refreshUrl + "/api/v1/inner/i18n-migrate/migrate-by-tenants?token=746afb412ddd0a5e0f1f68833c42da1f"
    Set<String> tenantIdSet = Sets.newHashSet(Splitter.on(",").omitEmptyStrings().trimResults().split(tenantIds))
    if (CollectionUtils.isNotEmpty(tenantIdSet)) {
      okHttpService.postJSON(migrateUrl, new JSONObject().fluentPut("tenantIds", tenantIdSet))
    }

    return ["status": 200, "msg": "success"]
  }

}
