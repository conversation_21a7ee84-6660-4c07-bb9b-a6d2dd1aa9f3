package com.fxiaoke.paas.console.web.metadata

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData
import com.facishare.uc.api.service.EnterpriseEditionService
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.service.metadata.StatService

import com.fxiaoke.paas.console.util.DateFormatUtil
import com.github.shiro.support.ShiroCasRealm
import com.google.common.base.Strings
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.*

import javax.annotation.Resource

/**
 * 统计
 * <AUTHOR>
 * Created on 2018/3/5.
 */
@Controller
@Slf4j
@RequestMapping("/metadata/stat")
class StatController {

  @Resource(name = "casRealm")
  private ShiroCasRealm cas

  @Autowired
  StatService statService
  @Autowired
  private EnterpriseEditionService enterpriseEditionService
  /**
   * 查询统计概况
   * @return
   */
  @RequestMapping(value = "/find")
  def Statistics() {
    "metadata/stat"
  }

  /**
   * 这个URL有俩个对应的操作接口，不同的接口处理方式不同，所以有点繁琐。
   * 情况1：tanantId和ea分别代表ei和ea
   * 情况2：tanantId可能是ea也可能是ei
   * @param tenantId
   */
  @RequestMapping(value = "/find", method = RequestMethod.POST)
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 查询数据统计信息")
  def findStatistics(@RequestBody String dataObject) {
    JSONObject jsonObject = JSONObject.parseObject(dataObject)
    if (Strings.isNullOrEmpty(jsonObject.getString("tenantId")) && Strings.isNullOrEmpty(jsonObject.getString("ea"))) {
      return ["code": 400, "error": "执行失败"]
    }
    String tenantId = jsonObject.getString("tenantId")
    if (StringUtils.isNotBlank(tenantId)) {
      tenantId = tenantId.trim()
    }
    //如果是情况2，先认为tanantId传递的是ea，用ea查一次组织架构。如果是情况1，这里查不到东西tenantId是""或ei
    BatchGetSimpleEnterpriseDataArg batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(
            enterpriseIds: null,
            enterpriseAccounts: Arrays.asList(tenantId)
    )
    BatchGetSimpleEnterpriseDataResult batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
    List<SimpleEnterpriseData> enterpriseList = batchGetSimpleEnterpriseDataResult.simpleEnterpriseList
    //如果查到，用查到的ei替换tenantId，必须要加上simpleEnterpriseData.enterpriseAccount.equals(tenantId)判断，因为这个接口在线上貌似有点问题
    if (CollectionUtils.isNotEmpty(enterpriseList)) {
      SimpleEnterpriseData simpleEnterpriseData = enterpriseList.get(0)
      if (simpleEnterpriseData != null && simpleEnterpriseData.enterpriseId != 0 && simpleEnterpriseData.enterpriseAccount.equals(tenantId)) {
        tenantId = simpleEnterpriseData.enterpriseId
      }
    }
    if (CollectionUtils.isEmpty(enterpriseList) && StringUtils.isNotBlank(tenantId)) {
      batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(
              enterpriseIds: Arrays.asList(Integer.parseInt(tenantId)),
              enterpriseAccounts: null
      )
      batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
      enterpriseList = batchGetSimpleEnterpriseDataResult.simpleEnterpriseList
      if (CollectionUtils.isNotEmpty(enterpriseList)) {
        SimpleEnterpriseData simpleEnterpriseData = enterpriseList.get(0)
        if (simpleEnterpriseData != null && simpleEnterpriseData.enterpriseId != 0 && simpleEnterpriseData.enterpriseAccount.equals(tenantId)) {
          tenantId = simpleEnterpriseData.enterpriseId
        }
      }
    }

    //如果是情况2，这里不会执行，因为tanantId不可能是blank。如果是情况1，说明tenantId是""，那就用ea查ei
    if (StringUtils.isBlank(tenantId)&&CollectionUtils.isEmpty(enterpriseList)) {
      batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(
              enterpriseIds: null,
              enterpriseAccounts: Arrays.asList(jsonObject.getString("ea").trim())
      )

      batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
      enterpriseList = batchGetSimpleEnterpriseDataResult.simpleEnterpriseList
      if (CollectionUtils.isNotEmpty(enterpriseList)) {
        SimpleEnterpriseData simpleEnterpriseData = enterpriseList.get(0)
        if (simpleEnterpriseData != null && simpleEnterpriseData.enterpriseId != 0 && simpleEnterpriseData.enterpriseAccount.equals(jsonObject.getString("ea").trim())) {
          tenantId = simpleEnterpriseData.enterpriseId
        }
      }
    }

//    定义横坐标
    List<String> xName = Lists.newArrayList()
//    定义纵坐标
    List<Integer> xNameNum = Lists.newArrayList()
    try {
//            List<Map> describeApiNames = statService.findDescribeByTenantId(tenantId, jsonObject.getString("startTime"), jsonObject.getString("endTime"))
      List<Map> describeApiNames = tenantCountService.findDescribeByTenantId(tenantId)
      log.info("describeApiNames is {}",describeApiNames)
      if (describeApiNames.isEmpty()) {
//        如果数据为空
        return ["code": 400, "error": "新对象数据为空"]
      }

      /**
       * 获取对应list
       */
      describeApiNames.each { describeApiName ->
        xName.add(describeApiName.get("object_describe_api_name") as String)
        xNameNum.add(describeApiName.get("count") as Integer)
      }
//      数据总数
      Integer dataSum = tenantCountService.getTenantCount(tenantId)
//      有数据对象数
      Integer describeSum = tenantCountService.getHaveDataApiName(tenantId)
//      无数据对象
      Integer noDataDescribeSum = statService.findDescribeSumNum(tenantId) - describeSum
//      查询field数量
      List<String> describeIds = statService.findDescribeIds(tenantId, xName)
      Integer fieldSum = 0
      if (!describeIds.isEmpty()) {
        fieldSum = statService.findFieldSum(tenantId, describeIds)
      }
      Map<String, String> sumMap = Maps.newHashMap()
      sumMap["dataSum"] = dataSum as String
      sumMap["describeSum"] = describeSum as String
      sumMap["fieldSum"] = fieldSum as String
      sumMap["noDataDescribeSum"] = ""
      return ["code": 200, "xName": xName, "xNameNum": xNameNum, "sumMap": sumMap]
    } catch (Exception e) {
      log.error("数据量统计概况异常：", e)
      return ["code": 400, "error": "统计异常", "errorInfo": e]
    }

  }

  /**
   * 获取当前tenantId下的字段（field）列表
   * @param dataObject
   */
  @RequestMapping(value = "/field")
//  @SystemControllerLog(description = "元数据 -- 获取当前tenantId下的字段（field）列表")
  def findField(@RequestParam String id, ModelMap modal) {
    List<Map> describeApiNames = tenantCountService.findDescribeByTenantId(id)

    if (describeApiNames.isEmpty()) {
//        如果数据为空
      modal.put("data", JSON.toJSONString([]))
      return "metadata/field-list"
    }
    List<String> xName = Lists.newArrayList()
    describeApiNames.each { describeApiName ->
      xName.add(describeApiName.get("object_describe_api_name") as String)
    }
//    查询所有有效field
    List<Map<String, Object>> fieldMapList = statService.findField(id, xName)
    def json = fieldMapList.collect { fieldMap ->
      [fieldMap.get("field_id"), fieldMap.get("tenant_id"), fieldMap.get("describe_id"), fieldMap.get("api_name"), fieldMap.get("type"), fieldMap.get("field_label"),
       fieldMap.get("create_time") != null ? DateFormatUtil.formatLong(fieldMap.get("create_time") as Long) : fieldMap.get("create_time"),
       fieldMap.get("describe_id")]
    }
    modal.put("data", JSON.toJSONString(json))
    return "metadata/field-list"
  }

  /**
   * List<Map>降序排序
   * @param list
   */
  @SuppressWarnings("unchecked")
  private static void sortListMapMethod(List list) {
    Collections.sort(list, new Comparator() {
      @Override
      int compare(Object o1, Object o2) {
        Map map1 = (Map) o1
        Map map2 = (Map) o2
        if (map1.get("count") > map2.get("count")) {
          return -1
        } else if (map1.get("count") == map2.get("count")) {
          return 0
        } else {
          return 1
        }
      }
    })
  }


}
