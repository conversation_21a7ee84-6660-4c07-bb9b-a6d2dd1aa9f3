package com.fxiaoke.paas.console.service.datarights

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.facishare.paas.org.pojo.DeptInfo
import com.facishare.paas.org.pojo.GroupPojo
import com.facishare.paas.org.pojo.OrgContext
import com.facishare.paas.org.pojo.UserPojo
import com.facishare.paas.org.service.DeptUserService
import com.facishare.paas.org.service.GroupMemService
import com.facishare.paas.org.service.para.BatchQueryDeptUserMapByDeptIdArg
import com.fxiaoke.paas.console.bean.datarights.BasicQueryArg
import com.fxiaoke.paas.console.bean.datarights.DataRightsContext
import com.fxiaoke.paas.console.bean.datarights.DeptsArg
import com.fxiaoke.paas.console.bean.datarights.InitCacheArg
import com.fxiaoke.paas.console.bean.datarights.LeaderArg
import com.fxiaoke.paas.console.bean.datarights.ShareArg
import com.fxiaoke.paas.console.bean.datarights.ShareCacheArg
import com.fxiaoke.paas.console.bean.datarights.UserPojoObject
import com.fxiaoke.paas.console.service.OrgDubboService
import com.fxiaoke.paas.console.util.HttpClientUtil
import com.fxiaoke.paas.console.util.datarights.IdAssembleUtils
import com.github.autoconf.ConfigFactory
import com.github.mybatis.datatables.DataResponse
import com.google.common.base.CharMatcher
import com.google.common.base.Splitter
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import groovy.util.logging.Slf4j
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import javax.ws.rs.core.MediaType
import java.text.SimpleDateFormat

/**
 * Created by yangxw on 2018/3/15.
 */
@Service("dataRightsAdminService")
@Slf4j
class DataRightsAdminService {

  @Autowired
  private OrgDubboService orgDubboService

  @Autowired
  DeptUserService deptUserService

  @Autowired
  GroupMemService groupMemService

  private String basicQueryUrl;
  private String userLeaderQueryUrl;
  private String userDeptQueryUrl;
  private String shareCacheQueryUrl;
  private String shareQueryUrl;
  private String shareCacheInitUrl;
  private String userDetpCacheInitUrl;
  private String userLeaderCacheInitUrl;

  @PostConstruct
  void init() {
    ConfigFactory.getConfig "paas-console-remotecall", { config ->
      basicQueryUrl = config.get("basicQueryUrl")
      userLeaderQueryUrl= config.get("userLeaderQueryUrl")
      userDeptQueryUrl = config.get("userDeptQueryUrl")
      shareCacheQueryUrl = config.get("shareCacheQueryUrl")
      shareQueryUrl = config.get("shareQueryUrl")
      shareCacheInitUrl = config.get("shareCacheInitUrl")
      userDetpCacheInitUrl = config.get("userDetpCacheInitUrl")
      userLeaderCacheInitUrl = config.get("userLeaderCacheInitUrl")
    }
  }


  def basicQuery(BasicQueryArg basicQueryArg) {

    DataResponse<JSONObject> response = new DataResponse()
    JSONObject jsonObject = HttpClientUtil.post(basicQueryUrl, null, basicQueryArg, MediaType.APPLICATION_JSON)
    if (jsonObject == null || jsonObject.size() == 0) {
      log.error("basicQuery jsonObject == null BasicQueryArg={}", basicQueryArg)
      response.data = Lists.newArrayList()
      response.error = "调用接口出错"
      return response
    }
    jsonObject = jsonObject.get("result")
    JSONObject jsonTotal = jsonObject.get("page")
    response.setRecordsFiltered(jsonTotal.getIntValue("total"))

    JSONArray jsonArray = jsonObject.get("content")
    if (CollectionUtils.isEmpty(jsonArray)) {
      response.data = new ArrayList<>();
      return response
    }
    Iterator it = jsonArray.iterator()
    Set<String> userSet = new HashSet<>()
    while (it.hasNext()) {
      JSONObject content = it.next()
      userSet.add(content.getString("creator"))
      userSet.add(content.getString("modifier"))
    }
    OrgContext orgContext = new OrgContext()
    DataRightsContext context = basicQueryArg.context
    orgContext.tenantId = context.tenantId
    orgContext.appId = context.appId
    orgContext.userId = context.userId
    Map<String, UserPojo> userPojoMap = orgDubboService.batchQueryUserPojoMapByUserId(orgContext, userSet, null)
    String name
    Iterator it2 = jsonArray.iterator()
    while (it2.hasNext()) {
      JSONObject content = it2.next()
      if (content.get("creator") != null && userPojoMap.get(content.get("creator")) != null && userPojoMap.get(content.get("creator")).getNickname() != null) {
        name = userPojoMap.get(content.get("creator")).getNickname()
      } else {
        name = ""
      }
      content.put("createName", name)
      if (content.get("modifier") != null && userPojoMap.get(content.get("modifier")) != null && userPojoMap.get(content.get("modifier")).getNickname() != null) {
        name = userPojoMap.get(content.get("modifier")).getNickname()
      } else {
        name = ""
      }
      content.put("modifierName", name)
      if (StringUtils.isNotBlank(content.getString("createTime"))) {
        content.put("createTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(content.getLongValue("createTime"))))
      }
      if (StringUtils.isNotBlank(content.getString("modifyTime"))) {
        content.put("modifyTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(content.getLongValue("modifyTime"))))
      }
    }
    response.setData(jsonArray)
    return response
  }

  def shareCacheQuery(ShareCacheArg shareCacheArg) {
    DataResponse<JSONObject> response = new DataResponse()
    JSONObject jsonObject = HttpClientUtil.post(shareCacheQueryUrl, null, shareCacheArg, MediaType.APPLICATION_JSON)
    if (jsonObject == null || jsonObject.size() == 0) {
      log.error("basicQuery jsonObject == null BasicQueryArg={}", shareCacheArg)
      response.data = Lists.newArrayList()
      response.error = "调用接口出错"
      return response
    }
    jsonObject = jsonObject.get("result")
    JSONObject jsonTotal = jsonObject.get("page")
    response.setRecordsFiltered(jsonTotal.getIntValue("total"))

    JSONArray jsonArray = jsonObject.get("content")
    if (CollectionUtils.isEmpty(jsonArray)) {
      response.data = new ArrayList<>();
      return response
    }
    Iterator iterator = jsonArray.iterator()
    while (iterator.hasNext()) {
      jsonObject = iterator.next()
      switch (jsonObject.getIntValue("receiveType")) {
        case 0:
          jsonObject.put("receiveType", "用户")
          break
        case 1:
          jsonObject.put("receiveType", "用户组")
          break
        case 2:
          jsonObject.put("receiveType", "部门")
          break
        case 4:
          jsonObject.put("receiveType", "角色")
          break
        default:
          jsonObject.put("receiveType", "不是 ：用户/用户组/部门/角色")
          break
      }
      jsonObject.put("receiveUserName", "")
      jsonObject.put("shareUserName", "")
    }

    OrgContext orgContext = new OrgContext()
    DataRightsContext context = shareCacheArg.context
    orgContext.tenantId = context.tenantId
    orgContext.appId = context.appId
    orgContext.userId = context.userId
    Map<String, UserPojo> userPojoMap = orgDubboService.batchQueryUserPojoMapByUserId(orgContext, Sets.newHashSet(IdAssembleUtils.getShareCache(jsonArray, "用户")), null)
    Map<String, DeptInfo> shareDeptMap = orgDubboService.getDeptIdAndDeptInfoMapByDeptIdList(orgContext, IdAssembleUtils.getShareCache(jsonArray, "部门"))
    Map<String, GroupPojo> shareGroupMap = orgDubboService.getGroupMapByGroupIdList(orgContext, IdAssembleUtils.getShareCache(jsonArray, "用户组"))

    String name
    Iterator it = jsonArray.iterator()
    JSONObject content
    while (it.hasNext()) {
      content = it.next()
      if (content.get("shareUser") != null && userPojoMap.get(content.getString("shareUser")) != null && userPojoMap.get(content.getString("shareUser")).getNickname() != null) {
        name = userPojoMap.get(content.getString("shareUser")).getNickname()
      } else {
        name = ""
      }
      content.put("shareUserName", name)
      switch (content.get("receiveType")) {
        case "用户":
          if (content.get("receiveUser") != null && userPojoMap.get(content.getString("receiveUser")) != null && userPojoMap.get(content.getString("receiveUser")).getNickname() != null) {
            name = userPojoMap.get(content.getString("receiveUser")).getNickname()
          } else {
            name = ""
          }
          content.put("receiveUserName", name)
          break
        case "用户组":
          if (content.get("receiveUser") != null && shareGroupMap.get(content.getString("receiveUser")) != null && shareGroupMap.get(content.getString("receiveUser")).getName() != null) {
            name = shareGroupMap.get(content.getString("receiveUser")).getName()
          } else {
            name = ""
          }
          content.put("receiveUserName", name)
          break
        case "部门":
          if (content.get("receiveUser") != null && shareDeptMap.get(content.getString("receiveUser")) != null && shareDeptMap.get(content.getString("receiveUser")).getDeptName() != null) {
            name = shareDeptMap.get(content.getString("receiveUser")).getDeptName()
          } else {
            name = ""
          }
          content.put("receiveUserName", name)
          break
        default:
          content.put("receiveUserName", "角色类或无接受对象，暂时无法解析")
          break
      }
    }
    response.setData(jsonArray)
    return response
  }

  def shareQuery(ShareArg shareArg) {
    DataResponse<JSONObject> response = new DataResponse()
    JSONObject jsonObject = HttpClientUtil.post(shareQueryUrl, null, shareArg, MediaType.APPLICATION_JSON)
    if (jsonObject == null || jsonObject.size() == 0) {
      log.error("basicQuery jsonObject == null BasicQueryArg={}", shareArg)
      response.data = Lists.newArrayList()
      response.error = "调用接口出错"
      return response
    }
    jsonObject = jsonObject.get("result")
    JSONObject jsonTotal = jsonObject.get("page")
    response.setRecordsFiltered(jsonTotal.getIntValue("total"))

    JSONArray jsonArray = jsonObject.get("content")
    if (CollectionUtils.isEmpty(jsonArray)) {
      response.data = new ArrayList<>();
      return response
    }
    Iterator iterator = jsonArray.iterator()
    while (iterator.hasNext()) {
      jsonObject = iterator.next()
      switch (jsonObject.getIntValue("receiveType")) {
        case 0:
          jsonObject.put("receiveType", "用户")
          break
        case 1:
          jsonObject.put("receiveType", "用户组")
          break
        case 2:
          jsonObject.put("receiveType", "部门")
          break
        case 4:
          jsonObject.put("receiveType", "角色")
          jsonObject.put("receiveName", "暂无法解析")
          break
        default:
          jsonObject.put("receiveType", "不是 ：用户/用户组/部门")
          break
      }
      switch (jsonObject.getIntValue("shareType")) {
        case 0:
          jsonObject.put("shareType", "用户")
          break
        case 1:
          jsonObject.put("shareType", "用户组")
          break
        case 2:
          jsonObject.put("shareType", "部门")
          break
        case 4:
          jsonObject.put("shareType", "角色")
          jsonObject.put("shareName", "暂无法解析")
          break
        default:
          jsonObject.put("shareType", "不是 ：用户/用户组/部门")
          break
      }
    }

    OrgContext orgContext = new OrgContext()
    DataRightsContext context = shareArg.context
    orgContext.tenantId = context.tenantId
    orgContext.appId = context.appId
    orgContext.userId = context.userId
    Map<String, UserPojo> userPojoMap = orgDubboService.batchQueryUserPojoMapByUserId(orgContext, Sets.newHashSet(IdAssembleUtils.getShare(jsonArray, "用户")), null)
    Map<String, DeptInfo> shareDeptMap = orgDubboService.getDeptIdAndDeptInfoMapByDeptIdList(orgContext, IdAssembleUtils.getShare(jsonArray, "部门"))
    Map<String, GroupPojo> shareGroupMap = orgDubboService.getGroupMapByGroupIdList(orgContext, IdAssembleUtils.getShare(jsonArray, "用户组"))

    String name
    Iterator it = jsonArray.iterator()
    JSONObject content
    while (it.hasNext()) {
      content = it.next()
      if (content.get("creator") != null && userPojoMap.get(content.getString("creator")) != null && userPojoMap.get(content.getString("creator")).getNickname() != null) {
        name = userPojoMap.get(content.getString("creator")).getNickname()
      } else {
        name = ""
      }
      content.put("createName", name)

      if (content.get("modifier") != null && userPojoMap.get(content.getString("modifier")) != null && userPojoMap.get(content.getString("modifier")).getNickname() != null) {
        name = userPojoMap.get(content.getString("modifier")).getNickname()
      } else {
        name = ""
      }
      content.put("modifierName", name)

      switch (content.get("shareType")) {
        case "用户":
          if (content.get("shareId") != null && userPojoMap.get(content.getString("shareId")) != null && userPojoMap.get(content.getString("shareId")).getNickname() != null) {
            name = userPojoMap.get(content.getString("shareId")).getNickname()
          } else {
            name = ""
          }
          content.put("shareName", name)
          break
        case "用户组":
          if (content.get("shareId") != null && shareGroupMap.get(content.getString("shareId")) != null && shareGroupMap.get(content.getString("shareId")).getName() != null) {
            name = shareGroupMap.get(content.getString("shareId")).getName()
          } else {
            name = ""
          }
          content.put("shareName", name)
          break
        case "部门":
          if (content.get("shareId") != null && shareDeptMap.get(content.getString("shareId")) != null && shareDeptMap.get(content.getString("shareId")).getDeptName() != null) {
            name = shareDeptMap.get(content.getString("shareId")).getDeptName()
          } else {
            name = ""
          }
          content.put("shareName", name)
          break
        default:
          content.put("shareName", "")
          break
      }

      switch (content.get("receiveType")) {
        case "用户":
          if (content.get("receiveId") != null && userPojoMap.get(content.getString("receiveId")) != null && userPojoMap.get(content.getString("receiveId")).getNickname() != null) {
            name = userPojoMap.get(content.getString("receiveId")).getNickname()
          } else {
            name = ""
          }
          content.put("receiveName", name)
          break
        case "用户组":
          if (content.get("receiveId") != null && shareGroupMap.get(content.getString("receiveId")) != null && shareGroupMap.get(content.getString("receiveId")).getName() != null) {
            name = shareGroupMap.get(content.getString("receiveId")).getName()
          } else {
            name = ""
          }
          content.put("receiveName", name)
          break
        case "部门":
          if (content.get("receiveId") != null && shareDeptMap.get(content.getString("receiveId")) != null && shareDeptMap.get(content.getString("receiveId")).getDeptName() != null) {
            name = shareDeptMap.get(content.getString("receiveId")).getDeptName()
          } else {
            name = ""
          }
          content.put("receiveName", name)
          break
        default:
          content.put("receiveName", "")
          break
      }
      if (StringUtils.isNotBlank(content.getString("createTime"))) {
        content.put("createTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(content.getLongValue("createTime"))))
      }
      if (StringUtils.isNotBlank(content.getString("modifyTime"))) {
        content.put("modifyTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(content.getLongValue("modifyTime"))))
      }
      if (StringUtils.isNotBlank(content.getString("status"))) {
        switch (content.getIntValue("status")) {
          case 0:
            content.put("status", "<a style='color: red'>停用</a>")
            break
          case 1:
            content.put("status", "<a style='color: green'>启用</a>")
        }
      }
      if (StringUtils.isNotBlank(content.getString("permission"))) {
        switch (content.getIntValue("permission")) {
          case 1:
            content.put("permission", "<a style='color: blue'>只读</a>")
            break
          case 2:
            content.put("permission", "<a style='color: green'>读写</a>")
        }
      }
    }
    response.setData(jsonArray)
    return response
  }

  def leaderQuery(LeaderArg leaderArg) {
    DataResponse<JSONObject> response = new DataResponse()
    JSONObject jsonObject = HttpClientUtil.post(userLeaderQueryUrl, null, leaderArg, MediaType.APPLICATION_JSON)
    if (jsonObject == null || jsonObject.size() == 0) {
      log.error("basicQuery jsonObject == null BasicQueryArg={}", leaderArg)
      response.data = Lists.newArrayList()
      response.error = "调用接口出错"
      return response
    }
    jsonObject = jsonObject.get("result")
    JSONObject jsonTotal = jsonObject.get("page")
    response.setRecordsFiltered(jsonTotal.getIntValue("total"))

    JSONArray jsonArray = jsonObject.get("content")
    if (CollectionUtils.isEmpty(jsonArray)) {
      response.data = new ArrayList<>();
      return response
    }
    Iterator it = jsonArray.iterator()
    Set<String> idSet = new HashSet<>()
    while (it.hasNext()) {
      jsonObject = it.next()
      if (StringUtils.isNotBlank(jsonObject.getString("userId"))) {
        idSet.add(jsonObject.getString("userId"))
      }
      if (StringUtils.isNotBlank(jsonObject.getString("leaderId"))) {
        idSet.add(jsonObject.getString("leaderId"))
      }
    }
    OrgContext orgContext = new OrgContext()
    DataRightsContext context = leaderArg.context
    orgContext.tenantId = context.tenantId
    orgContext.appId = context.appId
    orgContext.userId = context.userId
    Map<String, UserPojo> userPojoMap = orgDubboService.batchQueryUserPojoMapByUserId(orgContext, idSet, null)

    Iterator it2 = jsonArray.iterator()
    String name
    while (it2.hasNext()) {
      jsonObject = it2.next()
      if (jsonObject.get("userId") != null && userPojoMap.get(jsonObject.getString("userId")) != null && userPojoMap.get(jsonObject.getString("userId")).getNickname() != null) {
        name = userPojoMap.get(jsonObject.getString("userId")).getNickname()
      } else {
        name = ""
      }
      jsonObject.put("userName", name)
      if (jsonObject.get("leaderId") != null && userPojoMap.get(jsonObject.getString("leaderId")) != null && userPojoMap.get(jsonObject.getString("leaderId")).getNickname() != null) {
        name = userPojoMap.get(jsonObject.getString("leaderId")).getNickname()
      } else {
        name = ""
      }
      jsonObject.put("leaderName", name)
      if (jsonObject.get("relationType") != null) {
        switch (jsonObject.getIntValue("relationType")) {
          case 0:
            jsonObject.put("relationType", "直接上级")
            break
          case 1:
            jsonObject.put("relationType", "间接上级")
            break
        }
      }
    }
    response.data = jsonArray
    return response
  }

  def deptsQuery(DeptsArg deptsArg) {
    DataResponse<JSONObject> response = new DataResponse<>()
    JSONObject jsonObject = HttpClientUtil.post(userDeptQueryUrl, null, deptsArg, MediaType.APPLICATION_JSON)
    if (jsonObject == null || jsonObject.size() == 0) {
      log.error("basicQuery jsonObject == null BasicQueryArg={}", deptsArg)
      response.data = Lists.newArrayList()
      response.error = "调用接口出错"
      return response
    }
    jsonObject = jsonObject.get("result")
    JSONObject jsonTotal = jsonObject.get("page")
    response.setRecordsFiltered(jsonTotal.getIntValue("total"))

    JSONArray jsonArray = jsonObject.get("content")
    if (CollectionUtils.isEmpty(jsonArray)) {
      response.data = new ArrayList<>();
      return response
    }
    Iterator it = jsonArray.iterator()
    Set<String> deptIdSet = new HashSet<>()
    Set<String> userIdSet = new HashSet<>()
    while (it.hasNext()) {
      jsonObject = it.next()
      if (jsonObject.get("userId") != null) {
        userIdSet.add(jsonObject.getString("userId"))
      }
      if (jsonObject.get("deptId") != null) {
        deptIdSet.add(jsonObject.getString("deptId"))
      }
    }

    OrgContext orgContext = new OrgContext()
    DataRightsContext context = deptsArg.context
    orgContext.tenantId = context.tenantId
    orgContext.appId = context.appId
    orgContext.userId = context.userId
    Map<String, UserPojo> userPojoMap = orgDubboService.batchQueryUserPojoMapByUserId(orgContext, userIdSet, null)
    Map<String, DeptInfo> deptMap = orgDubboService.getDeptIdAndDeptInfoMapByDeptIdList(orgContext, Lists.newArrayList(deptIdSet))

    Iterator it2 = jsonArray.iterator()
    String name
    while (it2.hasNext()) {
      jsonObject = it2.next()
      if (jsonObject.get("userId") != null && userPojoMap.get(jsonObject.getString("userId")) && userPojoMap.get(jsonObject.getString("userId")).getNickname() != null) {
        name = userPojoMap.get(jsonObject.getString("userId")).getNickname()
      } else {
        name = ""
      }
      jsonObject.put("userName", name)
      if (jsonObject.get("deptId") != null && deptMap.get(jsonObject.getString("deptId")) && deptMap.get(jsonObject.getString("deptId")).getDeptName() != null) {
        name = deptMap.get(jsonObject.getString("deptId")).getDeptName()
      } else {
        name = ""
      }
      jsonObject.put("deptName", name)
      if (jsonObject.get("relationType") != null) {
        switch (jsonObject.getIntValue("relationType")) {
          case 0:
            jsonObject.put("relationType", "直属部门")
            break
          case 1:
            jsonObject.put("relationType", "部门负责人")
            break
          case 2:
            jsonObject.put("relationType", "部门直接负责人")
            break
        }
      }
    }
    response.data = jsonArray
    return response
  }

  def initCache(InitCacheArg initCacheArg, String initType) {
    DataResponse<JSONObject> response = new DataResponse<>()
    String url
    switch (initType) {
      case "企业共享规则缓存初始化": url = shareCacheInitUrl
        break
      case "企业部门用户缓存初始化": url = userDetpCacheInitUrl
        break
      case "企业用户汇报对象缓存初始化": url = userLeaderCacheInitUrl
        break
      default: return response
        break
    }
    JSONObject jsonObject = HttpClientUtil.post(url, null, initCacheArg, MediaType.APPLICATION_JSON)
    List<JSONObject> result = new ArrayList<>()
    result.add(jsonObject)
    response.data = result
    return response
  }

  def batchQueryUserPojoByDeptId(List<String> depts, DataRightsContext context) {
    DataResponse<UserPojoObject> response = new DataResponse<>()
    BatchQueryDeptUserMapByDeptIdArg arg = new BatchQueryDeptUserMapByDeptIdArg()
    if (CollectionUtils.isEmpty(depts)) {
      return response
    }
    arg.setDeptIds(Sets.newHashSet(depts))
    OrgContext orgContext = new OrgContext()
    orgContext.tenantId = context.tenantId
    orgContext.appId = context.appId
    orgContext.userId = context.userId
    Map<String, List<UserPojo>> userPojoMap = deptUserService.batchQueryUserPojoByDeptId(orgContext, arg)
    List<UserPojo> userPojos
    List<UserPojoObject> userPojoObjectList = new ArrayList<>()
    UserPojoObject userPojoObject
    if (depts.get(0) != null && userPojoMap.get(depts.get(0)) != null) {
      userPojos = userPojoMap.get(depts.get(0))
      for (UserPojo userPojo : userPojos) {
        if (userPojo != null) {
          userPojoObject = new UserPojoObject()
          if (StringUtils.isNotBlank(userPojo.getTenantId())) {
            userPojoObject.setTenantId(userPojo.getTenantId())
          }
          if (StringUtils.isNotBlank(userPojo.getId())) {
            userPojoObject.setId(userPojo.getId())
          }
          if (StringUtils.isNotBlank(userPojo.getName())) {
            userPojoObject.setName(userPojo.getName())
          }
          if (userPojo.getStatus() != null) {
            switch (userPojo.getStatus()) {
              case 0: userPojoObject.setStatus("<a style='color: green'>启用</a>")
                break
              case 1: userPojoObject.setStatus("<a style='color: red'>停用</a>")
                break
              default: userPojoObject.setStatus("未定义的状态")
            }
          }
          if (userPojo.getCreateTime() != null) {
            userPojoObject.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(userPojo.getCreateTime())))
          }
          if (userPojo.getModifyTime() != null) {
            userPojoObject.setModifyTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(userPojo.getModifyTime())))
          }
          userPojoObjectList.add(userPojoObject)
        }
      }
    }
    response.data = userPojoObjectList
    return response
  }

  def batchQueryGroupPojo(String groupId, DataRightsContext context) {
    DataResponse<UserPojoObject> response = new DataResponse<>()
    if (StringUtils.isBlank(groupId)) {
      return response
    }
    OrgContext orgContext = new OrgContext()
    orgContext.tenantId = context.tenantId
    orgContext.appId = context.appId
    orgContext.userId = context.userId
    List<String> groupPojoIds = groupMemService.queryGroupMem(orgContext, groupId, null)
    if (CollectionUtils.isNotEmpty(groupPojoIds)) {
      Map<String, UserPojo> userPojoMap = orgDubboService.batchQueryUserPojoMapByUserId(orgContext, Sets.newHashSet(groupPojoIds), null)
      List<UserPojoObject> groupList = new ArrayList<>()
      UserPojoObject userPojoObject
      for (String id : groupPojoIds) {
        userPojoObject = new UserPojoObject()
        userPojoObject.setId(id)
        if (userPojoMap.get(id) != null) {
          if (userPojoMap.get(id).getName() != null) {
            userPojoObject.setName(userPojoMap.get(id).getName())
          }
          if (userPojoMap.get(id).getStatus() != null) {
            switch (userPojoMap.get(id).getStatus()) {
              case 0: userPojoObject.setStatus("<a style='color: green'>启用</a>")
                break
              case 1: userPojoObject.setStatus("<a style='color: red'>停用</a>")
                break
              default: userPojoObject.setStatus("未定义状态")
                break
            }
          }
          if (userPojoMap.get(id).getTenantId() != null) {
            userPojoObject.setTenantId(userPojoMap.get(id).getTenantId())
          }
          if (userPojoMap.get(id).getCreateTime() != null) {
            userPojoObject.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(userPojoMap.get(id).getCreateTime())))
          }
          if (userPojoMap.get(id).getModifyTime() != null) {
            userPojoObject.setModifyTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(userPojoMap.get(id).getModifyTime())))
          }
          groupList.add(userPojoObject)
        }
      }
    }
    return response
  }

}
