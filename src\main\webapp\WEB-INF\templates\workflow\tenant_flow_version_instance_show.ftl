<#assign title="实例实例查询">
<#assign active_nav="workflow_instance">
<#assign headContent>
<style>

    pre {
        outline: 1px solid #ccc;
        padding: 5px;
        margin: 5px;
        width: 80%;
        height: 750px;
        margin-left: 10%;
    }

    .string {
        color: green;
    }

    .number {
        color: darkorange;
    }

    .boolean {
        color: blue;
    }

    .null {
        color: magenta;
    }

    .key {
        color: red;
    }


</style>
</#assign>
<#assign bodyContent>
    <#assign breadcrumbContent>
    <section class="content-header">
        <h1>流程定义</h1>
        <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i>展示</a></li>
        </ol>
    </section>
    </#assign>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title"></h3>
                </div>
                <form class="form-horizontal" action="${ctx}/biz/add" method="post" id="myForm" role="form"
                      data-toggle="validator">
                    <div class="container-fluid">
                        <div id="workflow_instance_show">
                            <legend>Instance</legend>
         <pre id="instance_result">
         </pre>
                        </div>
                    </div>
            </div>
        </div>
        </form>
    </div>
    </div>
    </div>
</section>
</#assign>
<#assign scriptContent>
<script>
    $(document).ready(function () {

        var instanceJson =  ${(instanceJson)!""};
        $("#instance_result").html(syntaxHighlight(instanceJson));

        function syntaxHighlight(json) {
            if (typeof json != 'string') {
                json = JSON.stringify(json, undefined, 2);
            }
            json = json.replace(/&/g, '&').replace(/</g, '<').replace(/>/g, '>');
            return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
                var cls = 'number';
                if (/^"/.test(match)) {
                    if (/:$/.test(match)) {
                        cls = 'key';
                    } else {
                        cls = 'string';
                    }
                } else if (/true|false/.test(match)) {
                    cls = 'boolean';
                } else if (/null/.test(match)) {
                    cls = 'null';
                }
                return '<span class="' + cls + '">' + match + '</span>';
            });
        }


    });
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
