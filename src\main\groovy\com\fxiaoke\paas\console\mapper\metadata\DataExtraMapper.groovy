package com.fxiaoke.paas.console.mapper.metadata;

import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

public interface DataExtraMapper extends ICrudMapper, IBatchMapper, ITenant<DataExtraMapper> {
  @Delete("delete from mt_data_extra where tenant_id=#{tenantId} and id in (select id from mt_data_extra where tenant_id=#{tenantId} and describe_api_name=#{describeApiName} and field_api_name in (select api_name from mt_field where tenant_id=#{tenantId} and describe_api_name=#{describeApiName} and api_name not in (\${blackFieldApiNames}) and status='deleted' and type in ('select_one','html_rich_text','select_many')) limit 100)")
  public int recycleDataExtra(@Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName, @Param("blackFieldApiNames") String blackFieldApiNames);
}
