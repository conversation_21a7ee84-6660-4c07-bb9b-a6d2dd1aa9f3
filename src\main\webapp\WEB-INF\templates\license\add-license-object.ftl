<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>添加主版本产品对象</h1>
        <ol class="breadcrumb">
            <li><a href=".."><i class="fa fa-dashboard"></i>任务主页</a></li>
            <li><a href="${ctx}/license-admin/add-application-object"><i class="fa fa-dashboard"></i>添加应用产品对象</a></li>
        </ol>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info">
                    <div class="box-header with-border">
                        <ul class="nav nav-tabs" id="myTab">
                            <li role="presentation"><a href="#createDesigned" data-toggle="tab">添加CRM对象</a></li>
                            <li role="presentation" class="active"><a href="#findDesigned" data-toggle="tab">添加应用对象</a></li>
                        </ul>
                    </div>
                    <div class="box-body">
                        <div class="tab-content">
                            <div class="tab-pane fade" id="createDesigned">
                                <form class="form-horizontal" action="${ctx}/license-admin/add-CRM-object" method="post" id="crmForm">
                                    <div class="box-body">
                                        <div class="form-group">
                                            <label for="tenantId" class="col-sm-2 control-label" style="width: 120px">productVersions</label>
                                            <div class="col-sm-6">
                                                <input type="text" style="border-radius:5px;width:1500px;" class="form-control" id="productVersions" name="productVersions" placeholder="必填" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="host" class="col-sm-1 control-label" style="width: 120px">apiNames</label>
                                            <div class="col-sm-4">
                                                <input type="text" style="border-radius:5px;width:1500px" class="form-control" id="apiNamesC" name="apiNamesC" placeholder="必填" required/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="box-footer clearfix">
                                        <div class="form-group">
                                            <div class="col-sm-offset-2 col-sm-2">
                                                <button type="button" onclick="addCrmObject('0')" class="btn btn-primary">提交</button>
                                                <button type="button" class="btn btn-default" onclick="history.back()">返回</button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="tab-pane fade in active" id="findDesigned">
                                <form class="form-horizontal" action="${ctx}/license-admin/add-application-object" method="post" id="applicationForm">
                                    <div class="box-body">
                                        <div class="form-group">
                                            <label for="host" class="col-sm-1 control-label" style="width: 120px">moduleCode</label>
                                            <div class="col-sm-4">
                                                <input type="text" style="border-radius:5px;width:1500px" class="form-control" id="moduleCode" name="moduleCode" placeholder="必填" required/>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="host" class="col-sm-1 control-label" style="width: 120px">apiNames</label>
                                            <div class="col-sm-4">
                                                <input type="text" style="border-radius:5px;width:1500px" class="form-control" id="apiNamesA" name="apiNamesA" placeholder="必填" required/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="box-footer clearfix">
                                        <div class="form-group">
                                            <div class="col-sm-offset-2 col-sm-2">
                                                <button type="button" onclick="addAPPObject('3')" class="btn btn-primary">提交</button>
                                                <button type="button" class="btn btn-default" onclick="history.back()">返回</button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script type="js"></script>
    <script type="application/javascript">
        function addCrmObject(type) {
            var apiNamesC = $('#apiNamesC').val()
            var productVersions = $('#productVersions').val()
            var dataObject = {
                apiNames: apiNamesC,
                productVersions: productVersions,
                check: "no",
                type: type
            }

            $.ajax({
                url: '${ctx}/license-admin/add-CRM-object', // 请求的URL地址
                contentType: "application/json", //必须有
                data: JSON.stringify(dataObject),// 这次请求要携带的数据
                dataType: 'text',
                type: 'POST', //请求的方式
                traditional: true,
                success: function (data) {//请求成功之后的回调函数
                    if (data == "success") {
                        alert("添加成功")
                        window.location.reload()
                    } else {
                        alert("添加失败")
                    }
                }
            })
        }
        function addAPPObject(type) {
            var apiNamesC = $('#apiNamesA').val()
            var productVersions = $('#moduleCode').val()
            var dataObject = {
                apiNames: apiNamesC,
                productVersions: productVersions,
                check: "no",
                type: type
            }

            $.ajax({
                url: '${ctx}/license-admin/add-CRM-object', // 请求的URL地址
                contentType: "application/json", //必须有
                data: JSON.stringify(dataObject),// 这次请求要携带的数据
                dataType: 'text',
                type: 'POST', //请求的方式
                traditional: true,
                success: function (data) {//请求成功之后的回调函数
                    if (data == "success") {
                        alert("添加成功")
                        window.location.reload()
                    } else {
                        alert("添加失败")
                    }
                }
            })
        }
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
