<#assign title="数据共享查询">
<#assign active_nav="data_permission_share">
<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
<style>
    .doSearch {
        width: 8%;
        margin-left: 20px
    }

    .back {
        height: 15px;
        line-height: 15px;
        padding: 0 20px;
        border: 5px solid #3e84e9;
        background-color: #3e84e9;
        border-radius: 3px;
        color: #fff !important;
        text-align: center;
        text-decoration: none;
        cursor: pointer;
        font-family: inherit;
        vertical-align: middle;
        margin-left: 10px
    }

    #dataPermission_table {
        margin-top: 10px;
        width: 97%;
        margin-left: 1.5%;
    }

    input {
        margin-left: 10px;
    }

    #dataTable2 th {
        vertical-align: middle;
        align-items: center;
    }

    #dataTable2 td {
        vertical-align: middle;
        align-items: center
    }

    .table > thead:first-child > tr:first-child > th {
        text-align: center;
        vertical-align: middle;
    }

    .table > tbody > tr > td {
        text-align: center;
    }

    input {
        width: 10%;
        height: 34px;
        line-height: 34px;
        box-sizing: border-box;
        border-radius: 4px;
        border: 1px solid #c8cccf;
        color: #6a6f77;
        -web-kit-appearance: none;
        -moz-appearance: none;
        outline: 0;
        text-decoration: none;
    }
</style>
</#assign>
<#assign bodyContent>
    <#assign breadcrumbContent>
    <section class="content-header">
        <h1>数据共享查询</h1>
        <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i>查询</a></li>
        </ol>
    </section>
    </#assign>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title"></h3>
                </div>
                <form class="form-horizontal" action="${ctx}/biz/add" method="post" id="myForm" role="form"
                      data-toggle="validator">
                    <div class="container-fluid">
                        <div id="permission_share_cache">
                            <input placeholder="企业_ID"/>
                            <input placeholder="App_ID"/>
                            <input placeholder="共享规则_ID"/>
                            <button type="button" class="doSearch btn btn-primary">Send</button>
                            <button type="button" class=" btn btn-primary" onclick="window.history.go(-1)" \>返回</button>
                        </div>
                        <div id="dataPermission_table">
                            <table class="table table-striped table-bordered table-condensed dataTable no-footer"
                                   id="dataTable2">
                                <thead>
                                <tr>
                                    <th>企业ID</th>
                                    <th>App_ID</th>
                                    <th>对象实体名称</th>
                                    <th>共享者类型</th>
                                    <th>共享者ID</th>
                                    <th>共享者名称</th>
                                    <th>接收者类型</th>
                                    <th>接收者ID</th>
                                    <th>接收者名称</th>
                                    <th>权限</th>
                                    <th>状态</th>
                                    <th>创建人ID</th>
                                    <th>创建人姓名</th>
                                    <th>创建时间</th>
                                    <th>修改人ID</th>
                                    <th>修改人姓名</th>
                                    <th>修改时间</th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
            </div>
            </form>
        </div>
    </div>
    </div>
</section>
</#assign>
<#assign scriptContent>
<script>
    $(document).ready(function () {
        var bootstrapDom = "<'row'<'col-sm-6'l>>" +
                "<'row'<'col-sm-12'tr>>" +
                "<'row'<'col-sm-5'i><'col-sm-7'p>>";
        var table = $("#dataTable2").DataTable({
            "dom": bootstrapDom,
            "processing": false,
            "serverSide": true,
            "search": {
                "regex": true
            },
            "order": [[2, 'desc']],
            "ajax": "${CONTEXT_PATH}/share/query?dataObject="+ encodeURIComponent(JSON.stringify(getDataObject())),
            "columnDefs": [
                { "width": "5%", "targets": 0 },
                { "width": "5%", "targets": 1 },
                { "width": "5%", "targets": 2},
                { "width": "5%", "targets": 3 },
                { "width": "5%", "targets": 4 },
                { "width": "5%", "targets": 5 },
                { "width": "5%", "targets": 6 },
                { "width": "5%", "targets": 7 },
                { "width": "5%", "targets": 8 },
                { "width": "5%", "targets": 9 },
                { "width": "5%", "targets": 10 },
                { "width": "5%", "targets": 11 },
                { "width": "5%", "targets": 12 },
                { "width": "10%", "targets": 13 },
                { "width": "5%", "targets": 14 },
                { "width": "5%", "targets": 15 },
                { "width": "10%", "targets": 16 },
            ],
            columns: [
                {data: "tenantId"},
                {data: "appId"},
                {data: "entityId"},
                {data: "shareType"},
                {data: "shareId"},
                {data: "shareName"},
                {data: "receiveType"},
                {data: "receiveId"},
                {data: "receiveName"},
                {data: "permission"},
                {data: "status"},
                {data: "creator"},
                {data: "createName"},
                {data: "createTime"},
                {data: "modifier"},
                {data: "modifierName"},
                {data: "modifyTime"},
            ],
            "iDisplayLength": 25,
            "sPaginationType": "full_numbers",
            "language": {
                "processing": "加载中...",
                "lengthMenu": "显示 _MENU_ 项结果",
                "InfoEmpty": "显示第 0 至 0 项结果，共 0 项",
                "zeroRecords": "没有匹配结果",
                "emptyTable": "没有数据",
                "info": "",
                "infoEmpty": "",
                "infoFiltered": "",
                "infoPostFix": "",
                "search": "搜索:",
                "url": "",
                "paginate": {
                    "first": "首页",
                    "previous": "上页",
                    "next": "下页",
                    "last": "末页"
                }
            }
        });


        $(".doSearch").on("click", function () {
            var tenantId = $("#permission_share_cache input").eq(0).val();
            var AppId = $("#permission_share_cache input").eq(1).val();
            var ids = $("#permission_share_cache input").eq(2).val();
            if (tenantId == "") {
                alert("企业ID不可为空!");
                return false;
            }
            if (AppId == "") {
                alert("AppId不可为空");
                return false;
            }
            var dataObject = {
                "tenantId":tenantId,
                "AppId":AppId,
                "ids":ids
            }
            table.ajax.url("${CONTEXT_PATH}/share/query?dataObject=" + encodeURIComponent(JSON.stringify(dataObject))).load();
        });
    });
    function getDataObject() {
        var dataObject = {
            "tenantId":"${(tenantId)!""}",
            "AppId":"${(AppId)!""}",
            "ids":"${(entityShareId)!""}",
        }
        return dataObject;
    }
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
