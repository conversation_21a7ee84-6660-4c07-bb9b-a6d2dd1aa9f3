package com.fxiaoke.paas.console.entity.metadata

import com.alibaba.fastjson.annotation.JSONField
import lombok.Data

import javax.persistence.Column
import javax.persistence.Id
import javax.persistence.Table

@Data
@Table(name = "mt_unique")
class MtUnique {

  @JSONField(name = "_id")
  @Id
  @Column(name = "unique_id")
  String uniqueId;
  @JSONField(name = "tenant_id")
  @Id
  String tenantId;
  @JSONField(name = "data_id")
  String dataId;
  @JSONField(name = "describe_api_name")
  String describeApiName;
  @JSONField(name = "field_name")
  String fieldName;
  @JSONField(name = "value")
  String value;

  MtUnique() {
  }

  MtUnique(String uniqueId, String tenantId, String dataId, String describeApiName, String fieldName, String value) {
    this.uniqueId = uniqueId
    this.tenantId = tenantId
    this.dataId = dataId
    this.describeApiName = describeApiName
    this.fieldName = fieldName
    this.value = value
  }
}
