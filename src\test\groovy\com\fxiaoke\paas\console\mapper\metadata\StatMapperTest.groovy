package com.fxiaoke.paas.console.mapper.metadata

import com.google.common.base.CharMatcher
import com.google.common.base.Splitter
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * <AUTHOR>
 * Created on 2018/3/6.
 */
@ContextConfiguration(locations = ["classpath:mapperContext.xml"])
@Slf4j
class StatMapperTest extends Specification {

    @Autowired
    StatMapper statMapper

    def "findTenantDataNum"() {
        given:
        def tenantId = "2"
        expect:
        statMapper.setTenantId(tenantId).findTenantDataNum(tenantId)
    }

    def "findField"() {
        expect:
        statMapper.setTenantId("74255").findFieldByTableName("sales_clue_pool")
    }

    def "findDescribeApiName"() {
        given:
        def tenantId = '2'
        expect:
        statMapper.setTenantId(tenantId).findDescribeApiName(tenantId).size()
    }

    def "findDescribeByTenantId"() {
        given:
        def tenantId = '2'
        expect:
        statMapper.setTenantId(tenantId).findDescribeByTenantId(tenantId).size()
    }

    def "findDataNumByTableAndTenantId"() {
        given:
        def table = "return_order"
        def tenantId = "2"
        def describeApiName = ""
        expect:
        statMapper.setTenantId(tenantId).findDataNum(table, tenantId, describeApiName)
    }

    def "batchFindDesNum"() {
        given:
        def tenantId = "2"
        def str = "quote,price_book"
        def describeApiName = "QuoteObj,PriceBookObj,"
        List<String> list = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(describeApiName)

        expect:
        statMapper.setTenantId(tenantId).batchFindDesNum(tenantId, list)
    }

    def "findDataNum"() {
        given:
        def tenantId = "2"
        def str = "quote"
        def desTableName = "QuoteObj"
        expect:
        statMapper.setTenantId(tenantId).findDataNum(str, tenantId, desTableName)

    }

    def "findFieldSum"() {
        given:
        def describeIds = ['5a682400bab09c601fb9f9dc', '5a682409bab09c601fb9f9e9']
        expect:
        statMapper.setTenantId("2").findFieldSum(describeIds)
    }

    def "findFieldNumByDescribeIds"() {
        given:
        def ids = ['597fd94dbab09ca40c202649', '5a4d8fb8bab09cdc3fbca9f9', '5a532057830bdbfcc9cbad2d']
        expect:
        statMapper.setTenantId("2").findFieldNumByDescribeIds(ids)
    }

    def "findDataNumByDescribeId"() {
        given:
        def describeId = "5a4f33cf830bdbdfae9c905c"
        Calendar calendar = Calendar.getInstance()
        calendar.set(Calendar.MONTH, -Integer.valueOf(3))
        def threeMonth = calendar.getTimeInMillis()
        expect:
        calendar.getTime()
        calendar.getTimeInMillis()
        statMapper.setTenantId("2").findDataNumByDescribeId(describeId, threeMonth)
    }

    def "a"() {
        given:
        int a = 40
        int b = 40
        for (int i = 1; i <= 5; i++) {
            a += b++
        }
        expect:
        a
    }


}
