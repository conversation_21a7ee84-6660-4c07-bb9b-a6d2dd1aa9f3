package com.fxiaoke.paas.console.web.basicPlatform


import com.fxiaoke.paas.console.bean.calculate.TriggerCalculateAndUpdate
import com.fxiaoke.paas.console.service.basicPlatform.CalculateService
import com.google.common.collect.Lists
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.servlet.ModelAndView

@RestController
@RequestMapping("/basicPlatform/calculate")
class calculateController {

    @Autowired
    CalculateService calculateService;



    @PostMapping(path = "triggerCalculateAndUpdate", produces = "application/json;charset=utf-8")
    def triggerCalculateAndUpdate(@RequestBody TriggerCalculateAndUpdate arg, String tenantId) {
        if (!tenantId) {
            return ["code": -100, "message": "请输入企业id"]
        }
        try {
            return calculateService.triggerCalculateAndUpdate(tenantId, arg.getDataIds(), arg.getFieldApiNames(), arg.getObjectApiName())
        } catch (RuntimeException e) {
            return ["code": -100, "message": e.getMessage()]
        }
    }

    @PostMapping(path = "triggerCalculate", produces = "application/json;charset=utf-8")
    def triggerCalculate(@RequestBody Map arg, String tenantId) {
        if (!tenantId) {
            return ["code": -100, "message": "请输入企业id"]
        }
        try {
            return calculateService.triggerCalculate(tenantId, arg.get("objectApiName"), arg.get("fieldApiName"), arg.get("dataId"))
        } catch (RuntimeException e) {
            return ["code": -100, "message": e.getMessage()]
        }

    }

    @PostMapping(path = "submitCalculate")
    def submitCalculate(@RequestBody Map arg, String tenantId) {
        if (!tenantId) {
            return ["code": -100, "message": "请输入企业id"]
        }
        List objectApiNames = arg.get("objectApiNames")
        List fieldApiNames = arg.get("fieldApiNames")
        if (objectApiNames == null || objectApiNames.size() < 1) {
            return ["code": -100, "message": "对象apiNames为空"]
        }
        try {
            def result = calculateService.submitCalculate(tenantId, objectApiNames, fieldApiNames)
            return ["code": 200, "data": result, "message": "已提交计算任务"]
        } catch (RuntimeException e) {
            return ["code": -100, "message": e.getMessage()]
        }
    }


    @PostMapping(path = "metadataChangeLog")
    def metadataChangeLog(@RequestBody Map arg, String tenantId) {
        if (!tenantId) {
            return ["code": -100, "message": "请输入企业id"]
        }
        try {
            List<Map> result = calculateService.selectByClickHouse(arg.get("id") as String, tenantId, "`biz_log`.`paas_metadata_changes_dist`", "objectId")
            List logs = calculateService.filterLogsByField(arg.get("fieldApiName"), Lists.newArrayList(result))
            return ["code": 200, "data": logs, "message": "成功"]
        } catch (RuntimeException e) {
            return ["code": -100, "message": e.getMessage()]
        }

    }

    @PostMapping(path = "fieldChangeLog")
    def getFieldChangeLog(@RequestBody Map arg, String tenantId) {
        String objectApiName = arg.get("objectApiName")
        String fieldApiName = arg.get("fieldApiName")
        if (StringUtils.isAnyBlank(tenantId, objectApiName, fieldApiName)) {
            return ["code": -100, "message": "参数错误"]
        }
        try {
            List<Map> result = calculateService.getFieldChangeLog(tenantId, objectApiName, fieldApiName)
            return ["code": 200, "data": result]
        } catch (RuntimeException e) {
            return ["code": -100, "message": e.getMessage()]
        }

    }


    @RequestMapping(value = "/page")
    ModelAndView consoleLog() {
        return new ModelAndView("basicPlatform/calculateTool");
    }


}
