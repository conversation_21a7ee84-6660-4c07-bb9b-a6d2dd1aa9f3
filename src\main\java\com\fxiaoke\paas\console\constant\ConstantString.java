package com.fxiaoke.paas.console.constant;

import com.alibaba.excel.EasyExcel;
import com.fxiaoke.paas.console.entity.metadata.DictExcelData;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.nio.file.Paths;

/**
 * <AUTHOR>
 */
public class ConstantString {
  public final static String DEFAULT_PLAN_ID = "default_plan_id";

  public static void main(String[] args) {
    EasyExcel.write(Paths.get("/tmp/a.xlsx").toFile(), DictExcelData.class).sheet("1").doWrite(Lists.newArrayList(DictExcelData.builder().fieldLength("111").fieldLabel("label").fieldApiName("name").options("11111111111").build()));

  }

  @Getter
  @AllArgsConstructor
  public enum HamsterPermission{
    HAMSTER_ADMIN("hamster-admin","管理员"),

    HAMSTER_EDIT("hamster-edit","编辑"),

    HAMSTER_DELETE("hamster-delete","删除"),

    HAMSTER_READ("hamster-read","查看");

    private final String code;

    private final String description;
  }


}
