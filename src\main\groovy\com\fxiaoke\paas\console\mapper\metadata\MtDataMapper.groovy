package com.fxiaoke.paas.console.mapper.metadata

import com.facishare.paas.metadata.api.UniqueIndex
import com.fxiaoke.paas.console.entity.metadata.MtUnique
import com.github.mybatis.mapper.IBatchMapper
import com.github.mybatis.mapper.ICrudMapper
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

/**
 * <AUTHOR>
 * Created on 2018/3/19.
 */
interface MtDataMapper extends ICrudMapper, IBatchMapper, ITenant<MtDataMapper> {

  /**
   * 根据tenantId和apiName获取mtData列表
   * 只取1000条
   * @param tenantId
   * @param apiName
   * @return
   */
  @Select("SELECT id,tenant_id,object_describe_api_name,object_describe_id,package,create_time,last_modified_time,name,record_type FROM mt_data WHERE tenant_id = #{tenantId} AND object_describe_api_name = #{apiName} ORDER BY last_modified_time DESC LIMIT 1000")
  List<Map<String, Object>> findDataList(@Param("tenantId") String tenantId, @Param("apiName") String apiName)

  /**
   * 根据id和tenantId获取通表数据
   * @param id
   * @param tenantId
   * @return
   */
  @Select("SELECT * FROM mt_data WHERE id=#{id} AND tenant_id=#{tenantId} and object_describe_api_name=#{describeApiName}")
  Map<String, Object> dataInfo(@Param("id") String id, @Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName)


  List<Map<String, Object>> getDataByName(@Param("storeTableName") String storeTableName, @Param("tenantId") String tenantId, @Param("name") String name, @Param("apiName") String apiName)

  @Update("update mt_data set \${fieldNum} = #{lifeStatus}, is_deleted = 0 where id = #{dataId} and tenant_id= #{tenantId} and is_deleted ='-1' and \${fieldNum} = 'invalid'")
  int restoreData(@Param("tenantId") String tenantId, @Param("dataId") String dataId, @Param("fieldNum") String fieldNum, @Param("lifeStatus") String lifeStatus)

  List<String> findRestoreIdList(@Param("tenantId") String tenantId, @Param("apiName") String apiName, @Param("startTime") Long startTime, @Param("endTime") Long endTime)

  @Select("select count(*) from \${storeTableName} where tenant_id=#{tenantId} and object_describe_api_name=#{describeApiName} and \${fieldNum} is not null and is_deleted >=0")
  int countUniqueData(@Param("tenantId") String tenantId, @Param("storeTableName") String storeTableName, @Param("describeApiName") String describeApiName, @Param("fieldNum") String fieldNum)

  @Select("SELECT #{tenantId} as tenant_id, #{apiName} as field_name, #{describeApiName} as describe_api_name, d.ID as data_id, d.\${fieldNum} as value FROM \${storeTableName} d left join mt_unique u on d.tenant_id = u.tenant_id and d.id = u.data_id and d.object_describe_api_name = u.describe_api_name and u.field_name = #{apiName} WHERE d.tenant_id = #{tenantId} AND u.value is null AND d.tenant_id = #{tenantId} and d.object_describe_api_name = #{describeApiName} and d.\${fieldNum} is not null  AND d.is_deleted >= 0 limit 100")
  List<MtUnique> getUnique(@Param("tenantId") String tenantId, @Param("storeTableName") String storeTableName, @Param("describeApiName") String describeApiName, @Param("apiName") String apiName, @Param("fieldNum") String fieldNum)

  List<Map<String,String>> getUniqueData(@Param("tenantId") String tenantId, @Param("storeTableName") String storeTableName, @Param("describeApiName") String describeApiName, @Param("fieldNum") String fieldNum,@Param("idName") String idName, @Param("id") String id)

  @Select("SELECT indexname,indexdef,tablename FROM pg_indexes where schemaname=#{schemaName} AND tablename = #{tableName} AND indexdef ILIKE 'CREATE UNIQUE INDEX%'")
  List<Map> getUniqueIndex(@Param("schemaName") String schemaName, @Param("tableName") String tableName)

}
