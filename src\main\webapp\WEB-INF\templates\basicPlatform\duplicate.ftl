<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<style>
    li {
        list-style: none;
    }

    .box {
        background-color: white;
    }

    .btn-box {
        width: 500px;
        display: flex;
        justify-content: space-around;
    }

    #singleObjForm {
        display: flex;
        justify-content: space-around;
    }

    .singleObjCalculate form div {
        height: 50px;
    }

    .content {
        width: 100%;
        padding: 20px;
    }

    label {
        padding: 5px;
    }

    pre {
        width: 100%;
        height: 75%;
    }

    /*.control-group {*/
    /*    display: flex*/
    /*}*/

    .control-group:last-child {
        width: auto;
    }

    .form {
        padding: 30px;
        width: 100%;
    }

    .formBox {
        display: flex;
        justify-content: space-evenly;
        padding: 20px;
    }

    .formBox input {
        line-height: 28px;
    }

    .form button {
        height: 30px;
        width: auto;
    }
</style>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>查重工具</h1>
        <ol class="breadcrumb">
            <li><a href="${ctx}"><i class="fa fa-dashboard"></i>任务主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>对象查询</a></li>
        </ol>
        <div id="warning" class="alert alert-warning alert-dismissible hide" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
            <strong>警告!</strong> 请输入tenantId.
        </div>
        <div id="warningInfo" class="alert alert-warning hide">
            <span id="closeInfo" href="#" class="close">&times;</span>
            <strong id="ajaxInfo"></strong>
        </div>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="box">
            <div class="tabbable">
                <ul class="btn-box nav nav-tabs">
                    <li class="active">
                        <a href="#multiRuleRefresh" data-toggle="tab">刷描述</a>
                    </li>
                </ul>
            </div>

            <div class="tab-content">
                <div class="singleObjCalculate tab-pane active" id="multiRuleRefresh">
                    <div class="form-inline formBox">
                        <div id="radioGroup">
                            刷库类型：
                            <input type="radio" id="custom" name="refreshType" value="custom"/>
                            <label for="custom">普通对象</label>
                            <input type="radio" id="system" name="refreshType" value="system" checked/>
                            <label for="system">三大对象</label>
                        </div>
                        <input type="text" id="inputTenantId" name="tenantId"
                               placeholder="请输入企业ei或ea,使用','隔开">
                        <button class="btn" id="multiRuleRefreshBtn">提交</button>
                    </div>
                    <div class="content">
                            <pre id="contentInfo">
                            </pre>
                    </div>
                </div>

            </div>

        </div>
    </section>



</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/js/highcharts.js"></script>
    <script src="https://img.hcharts.cn/highcharts/modules/data.js"></script>
<#--高亮显示-->
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<#--表单验证插件-->
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script type="application/javascript">
        $(document).ready(function () {
            $('#multiRuleRefreshBtn').on('click', function () {
                $('#contentInfo').html("数据加载中，请稍等...")
                var refreshType = $('#radioGroup input[name="refreshType"]:checked').val();
                var tenantIdList = $('#inputTenantId').val().split(",")
                var arg = {
                    "refreshType": refreshType,
                    "tenantIdList": tenantIdList
                };
                $.ajax({
                    url: "${CONTEXT_PATH}/basicPlatform/duplicated/duplicatedRefresh",
                    contentType: "application/json",
                    data: JSON.stringify(arg),
                    dataType: "json",
                    type: "POST",
                    traditional: true,
                    success: function (data) {
                        result = eval(data)
                        if (result != null && result.code === 200) {
                            $('#contentInfo').JSONView(result.data, {
                                collapsed: false,
                                nl2br: true,
                                recursive_collapser: true
                            })
                        } else {
                            console.log(result)
                            $('#contentInfo').JSONView(result.data, {
                                collapsed: false,
                                nl2br: true,
                                recursive_collapser: true
                            })
                            alert(result.message)
                        }
                    },
                    error: function (err) {
                        $('#contentInfo').html("")
                        alert(err.responseText)
                    }
                });
            })

        });

    </script>
</#assign>
<#include "../layout/layout-main.ftl" />


