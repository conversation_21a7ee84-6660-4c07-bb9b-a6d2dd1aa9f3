package com.fxiaoke.paas.console.web.basicPlatform

import com.fxiaoke.paas.console.service.basicPlatform.DuplicatedRefreshService
import com.google.common.collect.Maps
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.servlet.ModelAndView

@RestController
@RequestMapping("/basicPlatform/duplicated")
class DuplicatedRefreshController {


    @Autowired
    private DuplicatedRefreshService duplicatedRefreshService;

    @PostMapping("/duplicatedRefresh")
    def duplicatedRefresh(@RequestBody Map arg) {
        String refreshType = arg.get("refreshType")
        List<String> tenantIdList = arg.get("tenantIdList")
        if (StringUtils.isEmpty(refreshType) || CollectionUtils.isEmpty(tenantIdList)) {
            return ["code": -100, "message": "参数为空，请检查参数"]
        }
        Map<String, Object> failTenant = Maps.newHashMap();
        tenantIdList = duplicatedRefreshService.checkRefreshTenant(refreshType, tenantIdList, failTenant)
        if (CollectionUtils.isEmpty(tenantIdList)) {
            return ["code": -100, "message": "没有可以刷的企业", "data": ["tenant": [], "failTenant": failTenant]]
        }
        try {
            def result = duplicatedRefreshService.refreshTenant(refreshType, tenantIdList, failTenant)
            return ["code": 200, "message": "刷描述成功", "data": ["tenantInfo": result, "failTenant": failTenant]]
        } catch (RuntimeException e) {
            return ["code": -100, "message": e, "data": ["tenant": [], "failTenant": failTenant]]
        }
    }

    @RequestMapping(value = "/page")
    ModelAndView consoleLog() {
        return new ModelAndView("basicPlatform/duplicate");
    }

}
