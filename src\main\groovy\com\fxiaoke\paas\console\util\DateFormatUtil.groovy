package com.fxiaoke.paas.console.util

import groovy.util.logging.Slf4j

import java.text.ParseException
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.util.regex.Pattern

/**
 *  Created by dong<PERSON><PERSON><PERSON> on 2018/3/26.
 */
@Slf4j
class DateFormatUtil {
  private static final SimpleDateFormat dtf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
  private static final Pattern pattern = Pattern.compile('\\b(delete|truncate|update|insert|drop|rename|alter)\\b', Pattern.CASE_INSENSITIVE | Pattern.MULTILINE)

  /**
   * long型转字符串型
   * @param date
   * @return
   */
  static formatLong(Long date) {
    return dtf.format(date)
  }

  /**
   * 字符串型转long型
   * @param date
   * @return
   */
  static formatString(String date) {
    return dtf.parse(date)
  }

  /**
   * 转换开始和结束时间（用于es操作）
   * @param start
   * @param end
   * @return
   */
  static DateTime parseDate(String start, String end) {
    DateTime time = new DateTime()
    String newStart = getTime(start)
    String newEnd = getTime(end)
    Date s = formatString(newStart)
    Date e = formatString(newEnd)
    if (s.before(e)) {
      time.setStart(newStart)
      time.setEnd(newEnd)
    } else {
      time = null
    }
    return time
  }

  /**
   * 获取指定格式的时间
   * @param time
   * @return
   */
  static String getTime(String time) {
    String newTime = null
    if (time.contains("now()")) {
      if (time.contains("-")) {
        newTime = getNewTime(time)
      } else {
        newTime = formatLong(System.currentTimeMillis())
      }
    } else {
      if (isValidDate(time)) {
        newTime = time
      }
    }
    return newTime
  }

  /**
   * 判断日期格式是否符合指定规范
   * @return
   */
  static boolean isValidDate(String time) {
    boolean verify = true
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    try {
//      设置lenient为false. 否则SimpleDateFormat会宽松验证日期，比如2007-02-29会被接受，并转换成2007-03-01
      format.setLenient(false)
      format.parse(time)
    } catch (ParseException e) {
      log.error("日期格式不符合，time=${time},error:", e)
      verify = false
    }
    return verify
  }

  static void main(String[] args) {
    println(checkSql("select is_deleted is_delete abc del"))
    println(checkSql("DELETE 1"))
    println(checkSql("select 2\nTRUNCATE 1"))
    getStartOfDay(new Date())
  }

  /**
   * 获取前n m/h/d
   * @return
   */
  static def getNewTime(String time) {
    Calendar calendar = Calendar.getInstance()
    int s = Integer.valueOf(time.indexOf("-"))
    int index = Integer.valueOf(time.substring(s, s + 2)) + 1
    String newTime = null
    if (time.contains("m")) {
      calendar.add(Calendar.MINUTE, index)
      newTime = formatLong(calendar.getTimeInMillis())
    } else if (time.contains("h")) {
      calendar.add(Calendar.HOUR, index)
      newTime = formatLong(calendar.getTimeInMillis())
    } else if (time.contains("d")) {
      calendar.add(Calendar.DATE, index)
      newTime = formatLong(calendar.getTimeInMillis())
    }
    return newTime
  }

  /**
   * 确定日期格式
   * @param simpleType
   * @return
   */
  static SimpleDateFormat getSimpleDateFormat(String simpleType) {
    SimpleDateFormat sdf
    switch (simpleType) {
      case "yyyy-MM-dd":
        sdf = new SimpleDateFormat("yyyy-MM-dd")
        break
      case "yyyy-MM-dd HH":
        sdf = new SimpleDateFormat("yyyy-MM-dd HH")
        break
      case "yyyy-MM-dd HH:mm:ss":
        sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        break
      default:
        sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    }
    return sdf

  }

  /**
   * 获取一天中开始时间
   * @param date
   * @return
   */
  static Long getStartOfDay(Date date) {
    LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault())
    LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN)
    return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant()).getTime()
  }

//  构造存放指定时间的类型
  static class DateTime {
    String start
    String end

    String getStart() {
      return start
    }

    void setStart(String start) {
      this.start = start
    }

    String getEnd() {
      return end
    }

    void setEnd(String end) {
      this.end = end
    }
  }

  /**
   * 检测过滤sql
   */
  static boolean checkSql(String sql) {
    def matcher = pattern.matcher(sql)
    int offset = 0
    while (matcher.find(offset)) {
      def word = matcher.group(1)
      if ('delete'.equalsIgnoreCase(word)) {
        def start = matcher.start()
        if (start > 3 && 'is_'.equalsIgnoreCase(sql.substring(start - 3, start))) {
          offset += word.length()
        } else {
          return false
        }
      } else {
        return false
      }
    }
    return true
  }
}
