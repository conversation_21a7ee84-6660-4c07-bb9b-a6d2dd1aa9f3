package com.fxiaoke.paas.console.mapper.log

import com.fxiaoke.paas.console.entity.log.SpecialTable
import com.github.mybatis.mapper.IBatchMapper
import com.github.mybatis.mapper.ICrudMapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * Created on 2018/7/24.
 */
interface SpecialTableMapper extends ICrudMapper<SpecialTable>, IBatchMapper<SpecialTable> {

  @Select("SELECT * FROM paas_special_table WHERE store_table_name = #{storeTableName}")
  SpecialTable findByStoreName(@Param("storeTableName") String storeTableName)

}