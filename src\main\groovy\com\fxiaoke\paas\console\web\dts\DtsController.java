package com.fxiaoke.paas.console.web.dts;


import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.fxiaoke.paas.console.bean.dts.DtsCheckDataArg;
import com.fxiaoke.paas.console.bean.dts.DtsConfigArg;
import com.fxiaoke.paas.console.bean.dts.DtsQueryResult;
import com.fxiaoke.paas.console.service.dts.DtsService;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/dts")
@Slf4j
public class DtsController {

    @Autowired
    private DtsService dtsService;

    @RequestMapping(value = "/get/config")
    public String generateDtsConfig() {
        return "dts/config_data";
    }

    @RequestMapping(value = "/check/data")
    public String checkDtsData() {
        return "dts/check_data";
    }

    @PostMapping(path = "/check/data/do")
    @ResponseBody
    public DtsQueryResult doCheckData(@RequestBody DtsCheckDataArg arg) {
        try {
            String result = dtsService.checkDtsData(arg);
            return DtsQueryResult.builder().code(0).data(result).build();
        } catch (Exception e) {
            return DtsQueryResult.builder().code(1).data(e.getMessage()).build();
        }
    }

    @PostMapping(path = "/config/data")
    @ResponseBody
    public DtsQueryResult configData(@RequestBody DtsConfigArg arg) {
        try {
            dtsService.generateDtsConfig(arg);
            return DtsQueryResult.builder().code(0).data(arg.getConfig()).message(arg.getJdbcUrl()).build();
        } catch (Exception e) {
            return DtsQueryResult.builder().code(1).data(e.getMessage()).build();
        }
    }

    @PostMapping(path = "/check/jdbc")
    @ResponseBody
    public DtsQueryResult checkJdbcUrl(@RequestBody DtsConfigArg arg) {
        try {
            String result = dtsService.checkJdbcUrl(arg);
            return DtsQueryResult.builder().code(0).data(result).build();
        } catch (Exception e) {
            return DtsQueryResult.builder().code(1).message(e.getMessage()).build();
        }
    }

    @RequestMapping(path = "/get/topic")
    @ResponseBody
    public DtsQueryResult getDtsTopic(@RequestParam("ei") String ei) {
        try {
            String result = dtsService.getDtsTopicByEi(ei);
            return DtsQueryResult.builder().code(0).data(result).build();
        } catch (Exception e) {
            return DtsQueryResult.builder().code(1).data(e.getMessage()).build();
        }
    }

    @RequestMapping(path = "/get/egress/script")
    @ResponseBody
    public DtsQueryResult generateEgressScript(@RequestBody Map<String, String> params) {
        try {
            String result = dtsService.generateDtsEgress(params.get("ei"),params.get("port"),params.get("dbInfo"));
            return DtsQueryResult.builder().code(0).data(result).build();
        } catch (Exception e) {
            return DtsQueryResult.builder().code(1).data(e.getMessage()).build();
        }
    }
}
