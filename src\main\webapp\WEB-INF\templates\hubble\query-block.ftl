<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
      <h1>查询全局搜索刷新任务</h1>
      <ol class="breadcrumb">
        <li><a href="../"><i class="fa fa-dashboard"></i>主页</a></li>
        <li><a href=""><i class="fa fa-dashboard"></i>查询任务</a></li>
      </ol>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
      <div class="row">
        <div class="col-md-12">
          <div class="box box-info row">
            <div class="box-header with-border" style="padding: 0px">
            </div>
            <form class="form-horizontal" action="" onsubmit="return false;" method="post" id="myForm" role="form" data-toggle="validator">
              <div class="box-body col-xs-6" style="margin-right: -127px">
                <div class="form-group">
                  <label for="tenantId" class="col-sm-2 control-label">租户ID</label>
                  <div class="col-sm-6">
                    <input type="text" style="border-radius:5px;" class="form-control" id="tenantId"
                           name="tenantId" value="" placeholder="">
                  </div>
                </div>
                <div class="form-group">
                  <label for="topNum" class="col-sm-2 control-label">Top数</label>
                  <div class="col-sm-6">
                    <input type="text" style="border-radius:5px;" class="form-control" id="topNum"
                           name="topNum" value="10" placeholder="">
                  </div>
                </div>
                <div class="form-group">
                  <label for="moudel" class="col-sm-2 control-label">任务状态</label>
                  <div class="col-sm-6">
                    <select id="taskStatus" name="taskStatus" class="selectpicker show-tick form-control" title="Nothing selected" data-live-search="false">
                      <option value="1" selected="selected">等待</option>
                      <option value="2">执行</option>
                      <option value="4">失败</option>
                    </select>
                    <div class="help-block with-errors"></div>
                  </div>
                </div>
                <div class="col-sm-offset-3 col-sm-2">
                  <button type="button" id="queryBut" class="btn btn-primary">查询</button>
                </div>
              </div>
            <#--result展示-->
              <div class="box-body col-xs-6">
                <h4>Result <i class="fa fa-hand-o-down"></i></h4>
                <pre id="queryResult" style="">
                        </pre>
              </div>
            </form>
          </div>
        </div>
      </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script>
      $(document).ready(function () {

        $('#queryBut').on('click', function () {

          $('#queryResult').html('');
          $('#queryBut').attr("disabled", "disabled");

          var taskStatus = $('#taskStatus').val();
          var topNum = $('#topNum').val();
          var tenantId = $('#tenantId').val();

          $.post("${CONTEXT_PATH}/hubble/query-block", {
            status: taskStatus,
            topNum: topNum,
            tenantId: tenantId
          }, function (result) {
            $('#queryBut').removeAttr("disabled");
            if (result.code === 200) {
              info = result.info
            } else {
              info = result
            }
            $('#queryResult').JSONView(info, {
              collapsed: false,
              nl2br: true,
              recursive_collapser: true
            });
          });
        });

      });
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
