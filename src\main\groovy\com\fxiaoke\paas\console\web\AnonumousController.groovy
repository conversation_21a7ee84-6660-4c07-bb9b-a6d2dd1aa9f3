package com.fxiaoke.paas.console.web

import com.fxiaoke.paas.console.entity.anonymous.GetStoreTable
import com.fxiaoke.paas.console.entity.anonymous.GetStoreTablePojo
import com.fxiaoke.paas.console.mapper.log.SpecialTableMapper
import com.fxiaoke.paas.console.entity.log.SpecialTable
import com.google.common.base.Strings
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.util.CollectionUtils
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController


@RestController
@RequestMapping(value = "/anonymous")
@Slf4j
class AnonumousController {
    @Autowired
    SpecialTableMapper specialTableMapper;

    @GetMapping("/get-store-table")
    def getStoreTable() {
        List<SpecialTable> all = specialTableMapper.findAll();
        def result = GetStoreTablePojo.builder().build();
        if (CollectionUtils.isEmpty(all)) {
            result.setObjectInfo(new HashMap<String, GetStoreTable>());
            return result;
        }
        Map<String, GetStoreTable> storeTableMap = new HashMap<String, GetStoreTable>();
        for (SpecialTable specialTable : all) {
            if (Strings.isNullOrEmpty(specialTable.getStatus()) || "deprecated".equals(specialTable.getStatus())) {
                continue;
            }
            def objectGroup = GetStoreTable.builder()
                    .objectApiName(specialTable.getDescribeApiName())
                    .label(specialTable.getLabel())
                    .team(specialTable.getTeam())
                    .build();
            storeTableMap.put(objectGroup.objectApiName, objectGroup);
        }
        result.setObjectInfo(storeTableMap);
        return result;
    }
}
