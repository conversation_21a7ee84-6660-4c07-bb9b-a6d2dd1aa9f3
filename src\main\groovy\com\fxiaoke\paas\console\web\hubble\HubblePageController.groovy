package com.fxiaoke.paas.console.web.hubble

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.fxiaoke.common.http.spring.OkHttpSupport
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.service.OKHttpService
import com.github.autoconf.ConfigFactory
import com.google.common.base.Splitter
import com.google.common.collect.Maps
import org.elasticsearch.common.Strings
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.PostConstruct

/**
 * <AUTHOR>
 * @date 2020/5/22 10:50
 */
@Controller
@RequestMapping(path = "/hubble")
class HubblePageController {
  @Autowired
  private OKHttpService okHttpService
  private String hubbleUrl
  private String hubbleConfigUrl

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-console-url", { iConfig ->
      this.hubbleConfigUrl = iConfig.get("hubble-config-url")
      this.hubbleUrl = iConfig.get("hubble-url")
    })
  }

  @RequestMapping(path = "/hubble_query_refresh/page")
  def page() {
    return "hubble/hubble_query_refresh"
  }

  @PostMapping(path = "/config/refresh", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  def refresh(String tenantId, String apiName) {
    okHttpService.getForm(String.format(hubbleConfigUrl + "/config/refresh?tenantId=%s&apiName=%s&force=true", tenantId, apiName))
    return ["errCode": 0, "result": "success"]
  }

  @PostMapping(path = "/config/refresh_tenant_id", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  def refreshTenantId(String tenantId) {
    okHttpService.getForm(String.format(hubbleConfigUrl + "/config/refresh_tenant_id?tenantId=%s", tenantId))
    return ["errCode": 0, "result": "success"]
  }

  @PostMapping(path = "/query", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  def query(String tenantId, String userId, String keyword, String searchApiNames, String size) {
    JSONObject param = new JSONObject()
    param.put("tenantId", tenantId)
    param.put("userId", userId)
    param.put("keyword", keyword)
    param.put("searchApiNames", Splitter.on(",").omitEmptyStrings().trimResults().splitToList(searchApiNames))
    param.put("size", size)
    String result = okHttpService.postJSON(this.hubbleUrl + "/hubble/query", param)
    return ["errCode": 0, "result": JSONObject.parseObject(result)]

  }


  @PostMapping(path = "/test/render", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  def render(String tenantId, String apiName, String id) {
    String json = okHttpService.getForm(String.format(this.hubbleConfigUrl + "/test/render?tenantId=%s&apiName=%s&id=%s", tenantId, apiName, id))
    return ["errCode": 0, "result": JSONObject.parseObject(json)]
  }

  @PostMapping(path = "/test/describe", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  def describe(String tenantId, String apiName) {
    String json = okHttpService.getForm(String.format(this.hubbleConfigUrl + "/test/describe?tenantId=%s&apiName=%s", tenantId, apiName))
    return ["errCode": 0, "result": JSONObject.parseObject(json)]
  }

  /**
   * 查询搜索任务阻塞情况
   * @return
   */
  @RequestMapping(value = "/query/block")
  def list() {
    return "hubble/query-block"
  }

  @PostMapping(path = "/query-block", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @SystemControllerLog(description = "全局搜索 -- 查询刷新任务")
  @ResponseBody
  def queryBlock(@RequestParam Integer status, @RequestParam Integer topNum, @RequestParam String tenantId) {
    Map<String, Object> body = Maps.newHashMap()
    body.put("taskStatus", status)
    body.put("topNum", topNum == null ? 10 : topNum)
    body.put("tenantId", tenantId)

    return ["code": 200, "info": JSON.toJSONString(okHttpService.post(this.hubbleConfigUrl + "/config/query/tasks", JSONObject.toJSONString(body), Strings.isNullOrEmpty(tenantId) ? Map.class : List.class))]
  }


}
