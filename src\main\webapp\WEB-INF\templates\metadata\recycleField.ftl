<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>快速回收已删除字段</h1>
        <ol class="breadcrumb">
            <li><a href="../"><i class="fa fa-dashboard"></i>主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>快速回收已删除字段</a></li>
        </ol>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info row">
                    <div class="box-header with-border" style="padding: 0px">
                    </div>
                    <form class="form-horizontal" action="" onsubmit="return false;" method="post" id="myForm" role="form" data-toggle="validator">
                        <div class="box-body col-xs-6" style="margin-right: -127px">

                            <div class="form-group">
                                <label for="tenantId" class="col-sm-2 control-label">企业ID</label>
                                <div class="col-sm-6">
                                    <input type="text" style="border-radius:5px;" class="form-control" id="tenantId" name="tenantId" value="" placeholder="必填" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="describeApiName" class="col-sm-2 control-label">对象apiName</label>
                                <div class="col-sm-6">
                                    <input type="text" style="border-radius:5px;" class="form-control" id="describeApiName" name="describeApiName" value="" placeholder="必填" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="fieldApiNames" class="col-sm-2 control-label">黑名单字段</label>
                                <div class="col-sm-6">
                                    <input type="text" style="border-radius:5px;" class="form-control" id="fieldApiNames" name="fieldApiNames" value="" placeholder="多个apiName,分隔">
                                </div>
                            </div>
                            <div class="col-sm-offset-3" style="float: left">
                                <button type="button" id="query" class="btn btn-info">查询</button>
                                <button type="button" id="recycleField" class="btn btn-primary" disabled>回收字段</button>
                            </div>
                        </div>

                </div>
                <#--result展示-->
                <div class="box-body col-xs-6">
                    <h4>Result <i class="fa fa-hand-o-down"></i></h4>
                    <pre id="queryResult">
            </pre>
                    <#--loading。。。-->
                    <div class="spinner hide" id="loading">
                        <div class="spinner-container container1">
                            <div class="circle1"></div>
                            <div class="circle2"></div>
                            <div class="circle3"></div>
                            <div class="circle4"></div>
                        </div>
                        <div class="spinner-container container2">
                            <div class="circle1"></div>
                            <div class="circle2"></div>
                            <div class="circle3"></div>
                            <div class="circle4"></div>
                        </div>
                        <div class="spinner-container container3">
                            <div class="circle1"></div>
                            <div class="circle2"></div>
                            <div class="circle3"></div>
                            <div class="circle4"></div>
                        </div>
                    </div>
                </div>
                </form>
            </div>
        </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#query').on('click', function () {
                $('#queryResult').html('');
                $('#loading').removeClass('hide');
                $('#query').attr("disabled", "disabled");

                var tenantId = $('#tenantId').val();
                var describeApiName = $('#describeApiName').val();
                //TODO: 指向操作接口
                $.post("${CONTEXT_PATH}/metadata-operate/field-recycle/query", {
                    describeApiName: describeApiName,
                    tenantId: tenantId
                }, function (result) {
                    $('#query').removeAttr("disabled");
                    $('#loading').addClass('hide');

                    if (result.code === 200) {
                        info = result.info
                    } else {
                        info = result
                    }
                    $('#queryResult').JSONView(info, {
                        collapsed: false,
                        nl2br: true,
                        recursive_collapser: true
                    });
                });
            });
            $('#recycleField').on('click', function () {
                $('#queryResult').html('');
                $('#loading').removeClass('hide');
                $('#recycleField').attr("disabled", "disabled");
                var tenantId = $('#tenantId').val();
                var describeApiName = $('#describeApiName').val();
                var fieldApiNames = $('#fieldApiNames').val();
                //TODO: 指向操作接口
                $.post("${CONTEXT_PATH}/metadata-operate/field-recycle/recycle", {
                    describeApiName: describeApiName,
                    tenantId: tenantId,
                    fieldApiNames: fieldApiNames,
                }, function (result) {
                    $('#recycleField').removeAttr("disabled");
                    $('#loading').addClass('hide');

                    if (result.code === 200) {
                        info = result.info
                    } else {
                        info = result
                    }
                    $('#queryResult').JSONView(info, {
                        collapsed: false,
                        nl2br: true,
                        recursive_collapser: true
                    });
                });
            });
        })
        /**
         * 表单验证
         */
        var required1 = false, required2 = false;

        $('#tenantId').bind("input propertychange", function () {
            var tenantIdValue = $('#tenantId').val();
            if (tenantIdValue === null || tenantIdValue === "") {
                required1 = false;
            } else {
                required1 = true;
            }
        });

        $('#describeApiName').bind("input propertychange", function () {
            var apiNameValue = $('#describeApiName').val();
            if (apiNameValue === null || apiNameValue === "") {
                required2 = false;
            } else {
                required2 = true;
            }
        });

        $('#tenantId,#describeApiName,#fieldApiName').bind("input propertychange", function () {
            if (required1 && required2) {
                $('#recycleField').removeAttr("disabled");
                $('#query').removeAttr("disabled");
            } else {
                $('#switchFieldType').attr("disabled", "disabled");
                $('#query').attr("disabled", "disabled");
            }
        });
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />