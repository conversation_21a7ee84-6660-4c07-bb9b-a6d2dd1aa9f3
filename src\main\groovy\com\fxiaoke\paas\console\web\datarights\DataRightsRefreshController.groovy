package com.fxiaoke.paas.console.web.datarights

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.facishare.paas.pod.client.DbRouterClient
import com.facishare.paas.pod.dto.RouterInfo
import com.facishare.paas.pod.util.DialectUtil
import com.fxiaoke.common.Pair
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.entity.datarights.SimpleTenantIdAndObjectIdEntity
import com.fxiaoke.paas.console.service.OKHttpService
import com.fxiaoke.paas.console.service.metadata.JdbcService
import com.fxiaoke.paas.console.util.datarights.ResponseResult
import com.github.autoconf.ConfigFactory
import com.google.common.base.Splitter
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import com.google.common.util.concurrent.RateLimiter
import groovy.util.logging.Slf4j
import lombok.Cleanup
import org.apache.commons.lang3.StringUtils
import org.apache.ibatis.annotations.Param
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.bind.annotation.RestController

import javax.annotation.PostConstruct
import java.util.stream.Collectors


/**
 * <AUTHOR>
 * @date 2019/4/10 下午5:15
 */
@RestController
@Slf4j
@RequestMapping(path = "/datarights_refresh")
class DataRightsRefreshController {
  @Autowired
  private OKHttpService okHttpService
  @Autowired
  private JdbcService jdbcService
  @Autowired
  private DbRouterClient dbRouterClient

  private String PRODUCT_REFRESH_URL
  private String DATARIGHTS_REFRESH_URL
  private String HISTORY_DATA_AUTH_CODE_REFRESH_START_BY_TENANT_URL
  private String HISTORY_DATA_AUTH_CODE_REFRESH_START_BY_POD_URL
  private String HISTORY_DATA_AUTH_CODE_REFRESH_STOP_URL
  private String COUNT_THE_AMOUNT_OF_DATA_API_URL
  private String HISTORY_DATA_AUTH_CODE_REFRESH_START_BY_POD_URL_AND_APINAME
  private String HISTORY_DATA_AUTH_CODE_REFRESH_START_BY_POD_URL_AND_APINAME_AND_TENANTID
  private String DATA_AUTH_QUERY_REFRESH_ERROR_LOGS
  private String HISTORY_DATA_AUTH_CODE_REFRESH_START_BY_POD_URL_AND_APINAME_AND_TENANTID_PART
  private String DATA_AUTH_CODE_REFRESH_ACCOUNTOBJ_CUS
  private String DATA_AUTH_ID_REFRESH_FROM_DATA_AUTH_TO_DATA_AUTH_TARGET

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-console-datarights", { iconfig ->
      this.PRODUCT_REFRESH_URL = iconfig.get("DATA_AUTH_REFRESH_URL") + "/worker/init_tenant_entity_auth"
      this.DATARIGHTS_REFRESH_URL = iconfig.get("DATA_AUTH_REFRESH_URL") + "/worker/init_data_async"
      this.HISTORY_DATA_AUTH_CODE_REFRESH_START_BY_TENANT_URL = iconfig.get("HISTORY_DATA_AUTH_CODE_REFRESH_URL") + "/api/v1/inner/data-auth/start-by-tenant?token=746afb412ddd0a5e0f1f68833c42da1f"
      this.HISTORY_DATA_AUTH_CODE_REFRESH_START_BY_POD_URL = iconfig.get("HISTORY_DATA_AUTH_CODE_REFRESH_URL") + "/api/v1/inner/data-auth/start-by-pod-url?token=746afb412ddd0a5e0f1f68833c42da1f"
      this.HISTORY_DATA_AUTH_CODE_REFRESH_STOP_URL = iconfig.get("HISTORY_DATA_AUTH_CODE_REFRESH_URL") + "/api/v1/inner/data-auth/switch/off?token=746afb412ddd0a5e0f1f68833c42da1f"
      this.COUNT_THE_AMOUNT_OF_DATA_API_URL = iconfig.get("HISTORY_DATA_AUTH_CODE_REFRESH_URL") + "/api/v1/inner/data-auth/calculate-by-tenant?token=746afb412ddd0a5e0f1f68833c42da1f"
      this.HISTORY_DATA_AUTH_CODE_REFRESH_START_BY_POD_URL_AND_APINAME = iconfig.get("HISTORY_DATA_AUTH_CODE_REFRESH_URL") + "/api/v1/inner/data-auth/null-data-auth-code-by-pod-url-and-apiname?token=746afb412ddd0a5e0f1f68833c42da1f"
      this.HISTORY_DATA_AUTH_CODE_REFRESH_START_BY_POD_URL_AND_APINAME_AND_TENANTID = iconfig.get("HISTORY_DATA_AUTH_CODE_REFRESH_URL") + "/api/v1/inner/data-auth/null-data-auth-code-by-pod-url-and-apiname-and-tenantId?token=746afb412ddd0a5e0f1f68833c42da1f"
      this.DATA_AUTH_QUERY_REFRESH_ERROR_LOGS = iconfig.get("HISTORY_DATA_AUTH_CODE_REFRESH_URL") + "/api/v1/inner/data-auth/check-by-tenants?token=746afb412ddd0a5e0f1f68833c42da1f"
      this.HISTORY_DATA_AUTH_CODE_REFRESH_START_BY_POD_URL_AND_APINAME_AND_TENANTID_PART = iconfig.get("HISTORY_DATA_AUTH_CODE_REFRESH_URL") + "/api/v1/inner/data-auth/null-data-auth-code-by-pod-url-and-apiname-part?token=746afb412ddd0a5e0f1f68833c42da1f"
      this.DATA_AUTH_CODE_REFRESH_ACCOUNTOBJ_CUS = iconfig.get("HISTORY_DATA_AUTH_CODE_REFRESH_URL") + "/api/v1/inner/data-auth/null-data-auth-code-by-pod-url-and-accountobj-cus?token=746afb412ddd0a5e0f1f68833c42da1f"
      this.DATA_AUTH_ID_REFRESH_FROM_DATA_AUTH_TO_DATA_AUTH_TARGET = iconfig.get("HISTORY_DATA_AUTH_CODE_REFRESH_URL") + "/api/v1/inner/auth-id-migrate/migrate-by-tenants?token=746afb412ddd0a5e0f1f68833c42da1f"
    })
  }


  @RequestMapping(path = "countAmountForGrep", produces = "application/json;charset=utf-8")
  @SystemControllerLog(description = "数据权限 2.0 -- 统计待刷数据量")
  @ResponseBody
  JSONObject countAmountForGrep(String tenantIds, @RequestParam(required = false) String apiName) {
    JSONObject args = new JSONObject()
    args.put("tenantIds", Splitter.on(",").trimResults().omitEmptyStrings().split(tenantIds))
    args.put("describeApiName", apiName)
    String result = okHttpService.postJSON(COUNT_THE_AMOUNT_OF_DATA_API_URL, args)
    if (StringUtils.isNotBlank(result)) {
      return ResponseResult.success(result)
    }
    return ResponseResult.failed("查询失败")
  }

  @RequestMapping(path = "stopRefresh", produces = "application/json;charset=utf-8")
  @SystemControllerLog(description = "数据权限 2.0 -- 停止刷库")
  @ResponseBody
  JSONObject stopRefresh() {
    String result = okHttpService.postForm(HISTORY_DATA_AUTH_CODE_REFRESH_STOP_URL)
    if (result.equalsIgnoreCase("OFF")) {
      return ResponseResult.success("停止成功")
    } else {
      return ResponseResult.failed("停止失败")
    }

  }

  @RequestMapping(path = "startByPod", produces = "application/json;charset=utf-8")
  @SystemControllerLog(description = "数据权限 2.0 -- 根据pod url刷库")
  @ResponseBody
  JSONObject startByPod(String podUrl) {
    if (StringUtils.isBlank(podUrl)) {
      return ResponseResult.failed("Pod url is blank")
    }

    String result = okHttpService.postForm(HISTORY_DATA_AUTH_CODE_REFRESH_START_BY_POD_URL + "&podUrl=" + podUrl)
    if (result.equalsIgnoreCase("FAILED")) {
      return ResponseResult.failed("refresh error")
    }
    return ResponseResult.success("执行成功")
  }


  @RequestMapping(path = "startByTenant", produces = "application/json;charset=utf-8")
  @SystemControllerLog(description = "数据权限 2.0 -- 刷data_auth_code")
  @ResponseBody
  JSONObject startByTenant(String tenantIds, @RequestParam(required = false) String apiName) {

    if (StringUtils.isBlank(tenantIds)) {
      log.warn("start by tenant error, cause :the format of parameters is error. tenantIds is {},apiName is {}", tenantIds, apiName)
      return ResponseResult.failed("参数错误！")
    }
    JSONObject args = new JSONObject()
    args.put("tenantIds", Splitter.on(',').trimResults().omitEmptyStrings().split(tenantIds))
    args.put("describeApiName", apiName)
    String result = okHttpService.postJSON(HISTORY_DATA_AUTH_CODE_REFRESH_START_BY_TENANT_URL, args)
    if (result.equalsIgnoreCase("STARTED")) {
      return ResponseResult.success("执行成功")
    } else {
      return ResponseResult.failed("执行失败")
    }
  }

  @RequestMapping(path = "productRefresh", produces = "application/json;charset=utf-8")
  @SystemControllerLog(description = "数据权限 -- 刷对象权限")
  @ResponseBody
  JSONObject productRefresh(String tenantId, String apiName) {
    JSONObject params = new JSONObject()
    params.put("tenantIds", Arrays.asList(tenantId))
    params.put("objectDescribeApiNames", Arrays.asList(apiName))
    String result = okHttpService.postJSON(PRODUCT_REFRESH_URL, params)
    if ("Thread-worker-initTenantEntityAuth".equals(result.trim())) {
      return ResponseResult.success("执行成功")
    } else {
      return ResponseResult.failed("执行失败")
    }

  }


  @PostMapping(path = "datarightsRefreshBatch", produces = "application/json;charset=utf-8")
  @SystemControllerLog(description = "数据权限(临时) -- 重算数据权限")
  @ResponseBody
  String datarightsRefreshBatch(@RequestParam String apiName, @RequestParam String json) {
    JSONArray jsonArray = JSON.parseArray(json)
    Set<SimpleTenantIdAndObjectIdEntity> simpleTenantIdAndObjectIdEntityHashSet = Sets.newHashSet()
    for (int i = 0; i < jsonArray.size(); i++) {
      JSONObject jsonObject = jsonArray.getJSONObject(i)
      simpleTenantIdAndObjectIdEntityHashSet.add(new SimpleTenantIdAndObjectIdEntity(tenantId: jsonObject.get("tenant_id"), objectId: jsonObject.get("object_id")))
    }

    Map<String, Set<String>> tenantIdAndObjectIdSetMaps = Maps.newHashMap()
    simpleTenantIdAndObjectIdEntityHashSet.forEach({ simpleTenantIdAndObjectIdEntity ->

      Set<String> stringSet = tenantIdAndObjectIdSetMaps.computeIfAbsent(simpleTenantIdAndObjectIdEntity.getTenantId(), { k -> Sets.newHashSet() })
      stringSet.add(simpleTenantIdAndObjectIdEntity.objectId)
    })

    tenantIdAndObjectIdSetMaps.forEach({ tenantId, objectLists ->
      List<String> objectIds = Lists.newArrayList()
      log.info("重算数据权限 tenantId:{},apiName:{},objectLists:{}", tenantId, apiName, objectLists)
      objectLists.forEach({
        object ->
          objectIds.add(object)
          if (objectIds.size() == 100) {
            JSONObject params = new JSONObject()
            params.put("tenantId", tenantId)
            params.put("objectDescribeApiName", apiName)
            params.put("dataIds", objectIds)
            okHttpService.postJSON(DATARIGHTS_REFRESH_URL, params)
            objectIds = Lists.newArrayList()
          }
      })
      if (objectIds.size() > 0) {
        JSONObject params = new JSONObject()
        params.put("tenantId", tenantId)
        params.put("objectDescribeApiName", apiName)
        params.put("dataIds", objectIds)
        okHttpService.postJSON(DATARIGHTS_REFRESH_URL, params)
      }
    }
    )

    return "{\"result\": \"done\", \"errCode\": 0}"
  }

  @PostMapping(path = "datarightsRefresh", produces = "application/json;charset=utf-8")
  @SystemControllerLog(description = "数据权限 -- 重算数据权限")
  @ResponseBody
  String datarightsRefresh(@RequestParam String tenantId, @RequestParam String apiName, @RequestParam String objectId) {
    Set<String> objectLists = Sets.newHashSet(Splitter.on(",").omitEmptyStrings().trimResults().splitToList(objectId.replace("\n", "")))
    List<String> objectIds = Lists.newArrayList()
    log.info("重算数据权限 tenantId:{},apiName:{},objectLists:{}", tenantId, apiName, objectLists)
    objectLists.forEach({
      object ->
        objectIds.add(object)
        if (objectIds.size() == 100) {
          JSONObject params = new JSONObject()
          params.put("tenantId", tenantId)
          params.put("objectDescribeApiName", apiName)
          params.put("dataIds", objectIds)
          okHttpService.postJSON(DATARIGHTS_REFRESH_URL, params)
          objectIds = Lists.newArrayList()
        }
    })
    if (objectIds.size() > 0) {
      JSONObject params = new JSONObject()
      params.put("tenantId", tenantId)
      params.put("objectDescribeApiName", apiName)
      params.put("dataIds", objectIds)
      okHttpService.postJSON(DATARIGHTS_REFRESH_URL, params)
    }
    return "{\"result\": \"done\", \"errCode\": 0}"
  }

  @RequestMapping(path = "startByPodUrlAndApiName", produces = "application/json;charset=utf-8")
  @SystemControllerLog(description = "数据权限 2.0 -- 根据pod url刷库 指定ApiName刷库:")
  @ResponseBody
  JSONObject startByPodUrlAndApiName(String podUrl, String apiName) {
    if (StringUtils.isBlank(podUrl)) {
      return ResponseResult.failed("Pod url is blank")
    }

    String result = okHttpService.postForm(HISTORY_DATA_AUTH_CODE_REFRESH_START_BY_POD_URL_AND_APINAME + "&podUrl=" + podUrl + "&apiName=" + apiName)
    if (result.equalsIgnoreCase("FAILED")) {
      return ResponseResult.failed("refresh error")
    }
    return ResponseResult.success("执行成功")
  }

  @RequestMapping(path = "startByPodUrlAccountObj", produces = "application/json;charset=utf-8")
  @SystemControllerLog(description = "数据权限 2.0 -- 根据pod url  刷AccountObj data_auth_code:")
  @ResponseBody
  JSONObject startByPodUrlAndApiNameAndTenantIdAccountObj(String podUrl) {
    if (StringUtils.isBlank(podUrl)) {
      return ResponseResult.failed("Pod url is blank")
    }
    String result = okHttpService.postForm(HISTORY_DATA_AUTH_CODE_REFRESH_START_BY_POD_URL_AND_APINAME_AND_TENANTID_PART + "&podUrl=" + podUrl + "&apiName=AccountObj")
    if (result.equalsIgnoreCase("FAILED")) {
      return ResponseResult.failed("refresh error")
    }
    return ResponseResult.success("执行成功")
  }


  @RequestMapping(path = "startByPodUrlAccountObjCus", produces = "application/json;charset=utf-8")
  @SystemControllerLog(description = "数据权限 2.0 -- 根据pod url  刷AccountObj Cus data_auth_code:")
  @ResponseBody
  JSONObject startByPodUrlAccountObjCus(String podUrl) {
    if (StringUtils.isBlank(podUrl)) {
      return ResponseResult.failed("Pod url is blank")
    }
    String result = okHttpService.postForm(DATA_AUTH_CODE_REFRESH_ACCOUNTOBJ_CUS + "&podUrl=" + podUrl)
    if (result.equalsIgnoreCase("FAILED")) {
      return ResponseResult.failed("refresh error")
    }
    return ResponseResult.success("执行成功")
  }


  @RequestMapping(path = "startByPodUrlAndApiNameAndTenantId", produces = "application/json;charset=utf-8")
  @SystemControllerLog(description = "数据权限 2.0 -- 根据pod url 指定ApiName 指定企业ID 刷库:")
  @ResponseBody
  JSONObject startByPodUrlAndApiNameAndTenantId(String podUrl, String apiName, String tenantId) {
    if (StringUtils.isBlank(podUrl)) {
      return ResponseResult.failed("Pod url is blank")
    }

    String result = okHttpService.postForm(HISTORY_DATA_AUTH_CODE_REFRESH_START_BY_POD_URL_AND_APINAME_AND_TENANTID + "&podUrl=" + podUrl + "&apiName=" + apiName + "&tenantId=" + tenantId)
    if (result.equalsIgnoreCase("FAILED")) {
      return ResponseResult.failed("refresh error")
    }
    return ResponseResult.success("执行成功")
  }

  @RequestMapping(path = "queryRefreshErrorLog", produces = "application/json;charset=utf-8")
  @SystemControllerLog(description = "数据权限 2.0 -- 统计待刷数据量")
  @ResponseBody
  JSONObject queryRefreshErrorLog(String tenantIds, @RequestParam(required = false) String apiName) {
    JSONObject args = new JSONObject()
    args.put("tenantIds", Splitter.on(",").trimResults().omitEmptyStrings().split(tenantIds))
    args.put("describeApiName", apiName)
    String result = okHttpService.postJSON(DATA_AUTH_QUERY_REFRESH_ERROR_LOGS, args)
    if (StringUtils.isNotBlank(result)) {
      return ResponseResult.success(result)
    }
    return ResponseResult.failed("查询失败")
  }

  @RequestMapping(path = "refresh_data_auth_target", produces = "application/json")
  @SystemControllerLog(description = "数据权限 -- 同步dataAuthId data_auth->data_auth_target")
  @ResponseBody
  def refreshDataAuthTarget(String tenantIds) {
    JSONObject jsonObject = new JSONObject().fluentPut("tenantIds", Sets.newHashSet(Splitter.on(",").trimResults().omitEmptyStrings().splitToList(tenantIds)))
    String result = okHttpService.postJSON(DATA_AUTH_ID_REFRESH_FROM_DATA_AUTH_TO_DATA_AUTH_TARGET, jsonObject)
    if (StringUtils.isNotBlank(result)) {
      return ResponseResult.success(result)
    }
    return ResponseResult.failed("执行失败")

  }

  @RequestMapping(path = "refreshByRuleIds", produces = "application/json")
  @SystemControllerLog(description = "数据权限 -- 根据ruleIds刷数据权限")
  @ResponseBody
  def refreshByRuleIds(String tenantId, String ruleIds) {
    try {
      Pair<JdbcConnection, String> pair = jdbcService.conn(tenantId, "select rule_sql,entity_id from rule_rule_group where id =any('{" + ruleIds + "}')")
      String querySql = pair.second
      @Cleanup JdbcConnection jdbcConnection = pair.first
      jdbcConnection.cursor(querySql, { result ->
        while (result.next()) {
          String ruleSql = result.getString(1)
          String entityId = result.getString(2)
          executeRuleSqlAndTriggerDataAuth(tenantId, entityId, ruleSql)
        }
      })
    } catch (Exception e) {
      log.error("refresh by rule id error! tenantId:{} ruleIds:{} ", tenantId, ruleIds, e)
      return ResponseResult.failed("执行失败")
    }
    return ResponseResult.success("执行成功")

  }

  private void executeRuleSqlAndTriggerDataAuth(String tenantId, String entityId, String sql) {
    try {
      RateLimiter rateLimiter = RateLimiter.create(50)
      Set<String> ids = Sets.newHashSet()
      Pair<JdbcConnection, String> pair = jdbcService.conn(tenantId, sql)
      @Cleanup JdbcConnection jdbcConnection = pair.first
      jdbcConnection.cursor(sql, { result ->
        while (result.next()) {
          ids.add(result.getString("_id"))
          if (ids.size() >= 50) {
            rateLimiter.acquire(ids.size())
            JSONObject params = new JSONObject()
            params.put("tenantId", tenantId)
            params.put("objectDescribeApiName", entityId)
            params.put("dataIds", ids)
            okHttpService.postJSON(DATARIGHTS_REFRESH_URL, params)
            ids.clear()
          }
        }
      })
      if (ids.size() != 0) {
        rateLimiter.acquire(ids.size())
        JSONObject params = new JSONObject()
        params.put("tenantId", tenantId)
        params.put("objectDescribeApiName", entityId)
        params.put("dataIds", ids)
        okHttpService.postJSON(DATARIGHTS_REFRESH_URL, params)
        ids.clear()
      }
    } catch (Exception e) {
      log.error("executeRuleSqlAndTriggerDataAuth error! tenantId:{} sql:{} ", tenantId, sql, e)
    }

  }

}
