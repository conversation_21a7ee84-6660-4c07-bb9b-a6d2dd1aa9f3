<#assign title="权限计算任务">
<#assign active_nav="data_auth_task">
<#assign headContent>
        <style>
          .doSearch {
            width: 8%;
            margin-left: 20px
          }

          input {
            margin-left: 10px;
          }

          #dataTable2 th {
            vertical-align: middle;
            align-items: center;
          }

          #dataTable2 td {
            vertical-align: middle;
            align-items: center
          }

          .table > thead:first-child > tr:first-child > th {
            text-align: center;
            vertical-align: middle;
          }

          .table > tbody > tr > td {
            text-align: center;
          }

          input {
            width: 10%;
            height: 34px;
            line-height: 34px;
            box-sizing: border-box;
            border-radius: 4px;
            border: 1px solid #c8cccf;
            color: #6a6f77;
            -web-kit-appearance: none;
            -moz-appearance: none;
            outline: 0;
            text-decoration: none;
          }

        </style>
</#assign>
<#assign bodyContent>
    <#assign breadcrumbContent>
    <section class="content-header">
      <h1>权限计算任务</h1>
      <ol class="breadcrumb">
        <li><a href="#"><i class="fa fa-dashboard"></i>查询</a></li>
      </ol>
    </section>
    </#assign>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <form class="form-horizontal" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="permission_basic">
              <pre style="font-size:22px;font-weight:bold">权限计算任务报告:</pre>
              <button type="button" class="report btn btn-primary">Send</button>
            </div>
            <br/>
          </div>
        </form>
      </div>
    </div>
  </div>
  <div id="json">
  </div>
</section>
</#assign>
<#assign scriptContent>

<script>
  function fetchResult() {
    $.ajax({
      type: 'POST',
      url: '${CONTEXT_PATH}/datarights/task/report',
      contentType: "application/json;charset=utf-8",
      dataType: 'json',
      async: false,
      success: function (data) {
        $("#json").JSONView(eval(data));
      },
      error: function (error) {
        alert('网络异常');
      }

    });
  };
  $(".report").on("click", function () {
    fetchResult();
  });
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
