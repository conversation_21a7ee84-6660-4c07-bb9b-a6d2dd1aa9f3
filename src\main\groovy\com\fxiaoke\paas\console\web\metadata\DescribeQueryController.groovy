package com.fxiaoke.paas.console.web.metadata

import com.alibaba.fastjson.JSON
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.service.metadata.DataService
import com.fxiaoke.paas.console.service.metadata.DescribeService
import com.fxiaoke.paas.console.service.metadata.FieldService
import com.fxiaoke.paas.console.service.metadata.LayoutService
import com.google.common.collect.ImmutableMap
import groovy.util.logging.Slf4j
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.Resource

/**
 * <AUTHOR>
 * @date 2018/3/8
 */
@Controller
@Slf4j
@RequestMapping("/metadata/describe-query")
class DescribeQueryController {
  @Resource
  private FieldService fieldService
  @Resource
  private DescribeService describeService
  @Resource
  private LayoutService layoutService
  @Resource
  private DataService dataService

  /**
   * 跳转describe下的查询页面
   * @param tenantId
   * @param describeId
   * @param modelMap
   * @return
   */
  @RequestMapping(value = "")
  def describeQuery(@RequestParam String tenantId, @RequestParam String describeId, ModelMap modelMap) {
    Map<String, String> names = describeService.apiNameAndDisplayName(tenantId, describeId)
    modelMap.put("names", names)
//    log.info("----------" + names as String)
    "metadata/describeQuery"
  }

  /**
   * 查询当前describe下的field详情
   * @param tenantId
   * @param describeId
   * @return
   */
  @RequestMapping(value = "/field")
  @ResponseBody
//  @SystemControllerLog(description = "元数据 -- 查询describe下的field详情")
  def field(@RequestParam String tenantId, @RequestParam String describeId) {
    List<Map<String, Object>> fieldList = fieldService.fieldListByDescribeId(tenantId, describeId)
    ["fieldList": JSON.toJSONString(fieldList)]
  }

  /**
   * 查询当前describe下的layout
   * @param tenantId
   * @param apiName
   * @return
   */
  @RequestMapping(value = "/layout")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 查询describe下的layout")
  def layout(@RequestParam String tenantId, @RequestParam String apiName) {
    ["layoutList": layoutService.layoutList(tenantId, apiName)]
  }

  /**
   * 查询当前describe所对应的api_name下的数据列表
   * @param tenantId
   * @param describeId
   * @return
   */
  @RequestMapping(value = "/data")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 查询describe所对应的api_name下的数据列表")
  def data(@RequestParam String tenantId, @RequestParam String describeId) {
    ["dataResult": dataService.dataList(tenantId, describeId)]
  }
}
