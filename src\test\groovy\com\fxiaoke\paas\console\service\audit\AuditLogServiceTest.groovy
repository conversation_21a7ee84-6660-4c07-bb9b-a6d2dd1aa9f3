package com.fxiaoke.paas.console.service.audit

import com.fxiaoke.paas.console.entity.log.AuditLog
import com.fxiaoke.paas.console.service.metadata.AuditLogService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * <AUTHOR>
 * Created on 2018/5/18.
 */
@ContextConfiguration(value = ["classpath:mapperContext.xml"])
class AuditLogServiceTest extends Specification {

  @Autowired
  AuditLogService auditLogService

  def "insertAuditLog"() {
    given:
    def auditLog = AuditLog.builder()
            .executor("dong")
            .description("测试时间")
            .argument("kabulaka")
            .executeTime(new Date())
            .build()

    expect:
    println "==============" + auditLogService.insertAuditLog(auditLog)

  }

  def "findLog"() {
    given:
    def num = 1000
    expect:
    println "============" + auditLogService.findLog(num)
  }


  def "deleteAuditLog"() {
    expect:
    println "===========" + auditLogService.deleteAuditLog()
  }

  def "findMaxExecuteTime"() {
    expect:
    println "=============" + auditLogService.findMaxExecuteTime()
  }

  def "findNewLogNum"() {
    given:
    def time = "2018-05-18 10:39:05.082"
    expect:
    println "===============" + auditLogService.findNewAuditLog(time)
  }

}

