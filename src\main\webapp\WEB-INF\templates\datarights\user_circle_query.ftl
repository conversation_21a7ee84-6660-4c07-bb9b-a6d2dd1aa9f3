<#assign title="我的圈子&权限重刷">
<#assign active_nav="user_circle_query">
<#assign headContent>
        <style>
          .doSearch {
            width: 6%;
            margin-left: 20px
          }

          .queryDataAuth {
            width: 6%;
          }

          input {
            margin-left: 10px;
          }

          #dataTable2 th {
            vertical-align: middle;
            align-items: center;
          }

          #dataTable2 td {
            vertical-align: middle;
            align-items: center
          }

          .table > thead:first-child > tr:first-child > th {
            text-align: center;
            vertical-align: middle;
          }

          .table > tbody > tr > td {
            text-align: center;
          }

          input {
            width: 10%;
            height: 34px;
            line-height: 34px;
            box-sizing: border-box;
            border-radius: 4px;
            border: 1px solid #c8cccf;
            color: #6a6f77;
            -web-kit-appearance: none;
            -moz-appearance: none;
            outline: 0;
            text-decoration: none;
          }

        </style>
</#assign>
<#assign bodyContent>
    <#assign breadcrumbContent>
    <section class="content-header">
      <h1>员工圈子查询（员工所属的部门、用户组、角色等查询&权限重算</h1>
      <ol class="breadcrumb">
        <li><a href="#"><i class="fa fa-dashboard"></i>查询</a></li>
      </ol>
    </section>
    </#assign>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <form class="form-horizontal" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="permission_productRefresh">
              <pre style="font-size:15px;font-weight:bold">刷对象权限:</pre>
              <input placeholder="企业_ID"/>
              <input placeholder="ApiName"/>
              <button type="button" class="productRefresh btn btn-primary">开始</button>
            </div>
          </div>
        </form>
        <form class="form-horizontal" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="data_auth_id_refresh">
              <pre style="font-size:15px;font-weight:bold">Data_Auth_Id同步:</pre>
              <input placeholder="企业_ID"/>
              <button type="button" class="dataAuthIdRefresh btn btn-primary">执行</button>
            </div>
          </div>
        </form>
        <form class="form-horizontal" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="refresh_by_rule_id">
              <pre style="font-size:15px;font-weight:bold">根据ruleId触发权限计算</pre>
              <input placeholder="tenantId"/>
              <button type="button" class="refreshByRuleId btn btn-primary">执行</button>
              </br>
              <textarea cols="80" rows="5" placeholder="ruleIds(逗号隔开)"></textarea>
            </div>
          </div>
        </form>
        <form class="form-horizontal" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="permission_datarightsRefresh">
              <pre style="font-size:15px;font-weight:bold">重算数据权限:</pre>
              <input placeholder="TenantId"/>
              <input placeholder="ApiName"/>
              <button type="button" class="datarightsRefresh btn btn-primary">开始</button>
              </br>
              <textarea cols="80" rows="5" placeholder="DataIds(逗号隔开)"></textarea>
            </div>
          </div>
        </form>
        <form class="form-horizontal" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="permission_basic">
              <pre style="font-size:15px;font-weight:bold">我的圈子:</pre>
              <input placeholder="TenantId" id="tenantId"/>
              <input placeholder="UserId/OuterUserId" id="userId"/>
              <input placeholder="DescribeApiName" id="describeApiName"/>
              <input placeholder="OuterTenantId" id="outerTenantId"/>
              <br/>
              是否启用引用字段:
              <input name="check" type="checkbox" value="true" id="check" checked="checked"/>
              <button type="button" class="doSearch btn btn-primary">Send</button>
              <button type="button" class="queryDataAuth btn btn-primary">查询权限</button>
            </div>
            <br/>
            <textarea cols="80" rows="5" placeholder="DataIds(逗号隔开)" id="dataIds"></textarea>
          </div>
        </form>
        <form class="form-horizontal" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="data_auth_msg">
              <pre style="font-size:15px;font-weight:bold">查询UserId对数据是否有读写权限</pre>
              <input placeholder="企业_ID"/>
              <input placeholder="User_ID"/>
              <input placeholder="ApiName"/>
              <input placeholder="OuterTenantId"/>
              <input placeholder="OuterUserId"/>
              <input placeholder="OuterAppId"/>
              <button type="button" class="getDataAuthMsg btn btn-primary">Send</button>
              <br/>
              <textarea cols="80" rows="5" placeholder="数据_ID(逗号隔开)"></textarea>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
  <div id="json">
  </div>
</section>
</#assign>
<#assign scriptContent>

<script>
  function fetchResult() {
    var tenantId = $("#tenantId").val();
    var userId = $("#userId").val();
    var describeApiName = $("#describeApiName").val();
    var outerTenantId = $('#outerTenantId').val();
    var dataIds = $("#dataIds").val();
    var check = $("input[name = 'check']:checked").val();
    if (tenantId == "") {
      alert("企业ID不可为空!");
      return false;
    }
    if (userId == "") {
      alert("员工ID不可为空");
      return false;
    }
    $.ajax({
      type: 'POST',
      url: '${CONTEXT_PATH}/user/circle/query',
      data: JSON.stringify(
              {
                "tenantId": tenantId,
                "userId": userId,
                "describeApiName": describeApiName,
                "dataIds": dataIds,
                "check": check,
                "outerTenantId": outerTenantId
              }
      ),
      contentType: "application/json;charset=utf-8",
      dataType: 'json',
      async: false,
      success: function (data) {
        $("#json").JSONView(eval(data));
      },
      error: function (error) {
        alert('网络异常');
      }

    });
  };

  $(".productRefresh").on("click", function () {
    var tenantId = $("#permission_productRefresh input").eq(0).val();
    var apiName = $("#permission_productRefresh input").eq(1).val();
    if (tenantId == "") {
      alert("企业ID不可为空!");
      return false;
    }
    if (apiName == "") {
      alert("ApiName不可为空");
      return false;
    }
    $.ajax({
      type: 'GET',
      url: '${CONTEXT_PATH}/datarights_refresh/productRefresh',
      data: {
        tenantId: tenantId,
        apiName: apiName
      },
      contentType: "application/json", //必须有
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        if (result.status == 200) {
          alert("执行成功")
        } else {
          alert(result.msg)
        }

      },
      error: function (error) {
        alert('网络异常');
      }

    })

  });

  $(".refreshByRuleId").on("click", function () {
    var tenantId = $("#refresh_by_rule_id input").eq(0).val();
    var ruleIds = $("#refresh_by_rule_id textarea").eq(0).val();
    if (tenantId == "") {
      alert("企业ID不可为空!");
      return false;
    }
    if (ruleIds == "") {
      alert("ruleIds不可为空");
      return false;
    }
    $.ajax({
      type: 'POST',
      url: '${CONTEXT_PATH}/datarights_refresh/refreshByRuleIds',
      data: {
        tenantId: tenantId,
        ruleIds: ruleIds,
      },
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        if (result.status == 200) {
          alert(result.msg)
        } else {
          alert(result.msg)
        }

      },
      error: function (error) {
        alert('网络异常');
      }

    })

  });


  $(".datarightsRefresh").on("click", function () {
    var tenantId = $("#permission_datarightsRefresh input").eq(0).val();
    var apiName = $("#permission_datarightsRefresh input").eq(1).val();
    var objectId = $("#permission_datarightsRefresh textarea").eq(0).val();
    if (tenantId == "") {
      alert("企业ID不可为空!");
      return false;
    }
    if (apiName == "") {
      alert("ApiName不可为空");
      return false;
    }
    if (objectId == "") {
      alert("objectId不可为空");
      return false;
    }
    $.ajax({
      type: 'POST',
      url: '${CONTEXT_PATH}/datarights_refresh/datarightsRefresh',
      data: {
        tenantId: tenantId,
        apiName: apiName,
        objectId: objectId
      },
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        if (result.errCode == 0) {
          alert("执行成功")
        } else {
          alert(result.errMessage)
        }

      },
      error: function (error) {
        alert('网络异常');
      }

    })

  });


  $(".dataAuthIdRefresh").on("click", function () {
    var tenantIds = $("#data_auth_id_refresh input").eq(0).val();
    if (tenantIds == "") {
      alert("企业ID不可为空!");
      return false;
    }
    $.ajax({
      type: 'POST',
      url: '${CONTEXT_PATH}/datarights_refresh/refresh_data_auth_target',
      data: {
        tenantIds: tenantIds
      },
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        if (result.status == 200) {
          alert(result.msg)
        } else {
          alert(result.msg)
        }

      },
      error: function (error) {
        alert('网络异常');
      }

    })

  });


  function getDataAuth() {
    var tenantId = $("#data_auth_msg input").eq(0).val();
    var userId = $("#data_auth_msg input").eq(1).val();
    var apiName = $("#data_auth_msg input").eq(2).val();
    var outerTenantId = $("#data_auth_msg input").eq(3).val();
    var outerUserId = $("#data_auth_msg input").eq(4).val();
    var outerAppId = $("#data_auth_msg input").eq(5).val();
    var objectIds = $("#data_auth_msg textarea").eq(0).val();
    if (tenantId == "") {
      alert("企业ID不可为空!");
      return false;
    }
    if (userId == "") {
      alert("用户ID不可为空!");
      return false;
    }
    if (objectIds == "") {
      alert("数据ID不可为空");
      return false;
    }
    if (apiName == "") {
      alert("apiName不可为空");
      return false;
    }
    $.ajax({

      type: 'Get',
      url: '${CONTEXT_PATH}/datarights/getDataAuthMsg',
      data: {
        tenantId: tenantId,
        userId: userId,
        objectIds: objectIds,
        apiName: apiName,
        outerTenantId: outerTenantId,
        outerUserId: outerUserId,
        outerAppId: outerAppId
      },
      contentType: "application/json", //必须有
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        $("#json").JSONView(result);
        $("#json-collapsed").JSONView(result, {collapsed: true, nl2br: true});

        $('#collapse-btn').on('click', function () {
          $('#json').JSONView('collapse');
        });
        $('#expand-btn').on('click', function () {
          $('#json').JSONView('expand');
        });

      },
      error: function (error) {
        alert('网络异常');
      }

    })


  };

  function queryDataAuth() {
    var tenantId = $("#tenantId").val();
    var userId = $("#userId").val();
    var describeApiName = $("#describeApiName").val();
    var dataId = $("#dataIds").val();
    var outerTenantId = $("#outerTenantId").val();

    if (tenantId == "") {
      alert("TenantId不可为空!");
      return false;
    }
    if (userId == "") {
      alert("UserId不可为空!");
      return false;
    }
    if (describeApiName == "") {
      alert("DescribeApiName不可为空");
      return false;
    }
    if (dataId == "") {
      alert("DataIds不可为空");
      return false;
    }
    $.ajax({

      type: 'GET',
      url: '${CONTEXT_PATH}/datarights/queryDataAuth',
      data: {
        tenantId: tenantId,
        userId: userId,
        describeApiName: describeApiName,
        dataId: dataId,
        outerTenantId: outerTenantId
      },
      contentType: "application/json", //必须有
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        $("#json").JSONView(result);
        $("#json-collapsed").JSONView(result, {collapsed: true, nl2br: true});

        $('#collapse-btn').on('click', function () {
          $('#json').JSONView('collapse');
        });
        $('#expand-btn').on('click', function () {
          $('#json').JSONView('expand');
        });
      },
      error: function (error) {
        alert('网络异常');
      }

    })

  };

  $(".doSearch").on("click", function () {
    fetchResult();
  });
  $("#userId").on('keypress', function (e) {
    if (e.which == 13) {
      fetchResult();
    }
  });
  $(".getDataAuthMsg").on("click", function () {
    getDataAuth();
  });
  $(".queryDataAuth").on("click", function () {
    queryDataAuth();
  });
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
