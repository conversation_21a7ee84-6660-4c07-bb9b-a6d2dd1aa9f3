package com.fxiaoke.paas.console.mapper.datarights

import com.fxiaoke.paas.console.entity.datarights.DepartmentMsg
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Param

/**
 * <AUTHOR>
 * @date 2019/4/1 下午2:07
 *
 */
interface DataRightsDepartmentMapper extends ITenant<DataRightsDepartmentMapper>{

 /**
  * 获取部门的：负责人、成员、上级部门、下级部门
  * @param tenantId
  * @param deptId
  * @return
  */
 DepartmentMsg queryDepartmentMsgByDeptId(@Param("tenantId")String tenantId,
                                          @Param("deptId") String deptId)



}