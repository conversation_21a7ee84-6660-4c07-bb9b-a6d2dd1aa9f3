package com.fxiaoke.paas.console.util.datarights

import com.fxiaoke.paas.console.bean.datarights.*
import org.apache.commons.lang3.StringUtils
/**
 * Created by yangxw on 2018/3/15.
 */
class DataRightsArgUtil {
//基础数据查询参数
  def static getBasicQueryArg(Map<String, Object> param) {
    DataRightsContext context = new DataRightsContext();
    context.setTenantId(param.get("tenantId").toString());
    context.setAppId(param.get("AppId").toString());
    context.setUserId("1000");
    BasicQueryArg basicQueryArg = new BasicQueryArg();
    if (StringUtils.isNotBlank((String) param.get("entityId"))) {
      List<String> entitys = new ArrayList();
      entitys.add((String) param.get("entityId"));
      basicQueryArg.setEntitys(entitys);
    }

    //分页参数
    Page page = new Page();
    int start = Integer.parseInt((String) param.get("start"))
    int length = Integer.parseInt((String) param.get("length"))
    page.currentPage = start / length + 1;
    page.setPageSize(length);
    basicQueryArg.setPage(page);
    basicQueryArg.setContext(context);
    return basicQueryArg;
  }

  //汇报人缓存
  def static getLeaderArg(Map<String, Object> param) {
    LeaderArg leaderArg = new LeaderArg();
    DataRightsContext context = new DataRightsContext();
    context.setTenantId((String) param.get("tenantId"));
    context.setAppId((String) param.get("AppId"));
    context.setUserId("1000");
    leaderArg.setContext(context);

    //分页参数
    Page page = new Page();
    int start = Integer.parseInt((String) param.get("start"))
    int length = Integer.parseInt((String) param.get("length"))
    page.currentPage = start / length + 1;
    page.setPageSize(length);
    leaderArg.setPage(page);

    if (StringUtils.isNotBlank((String) param.get("relationType"))) {
      leaderArg.setRelationType(Integer.valueOf((String) param.get("relationType")));
    }
    if (StringUtils.isNotBlank((String) param.get("users"))) {
      List<String> list = new ArrayList<>();
      list.add((String) param.get("users"));
      leaderArg.setUsers(list);
    }
    if (StringUtils.isNotBlank((String) param.get("leaders"))) {
      List<String> list = new ArrayList<>();
      list.add((String) param.get("leaders"));
      leaderArg.setLeaders(list);
    }
    return leaderArg;
  }

  //部门负责人缓存
  def static getDeptsArg(Map<String, Object> param) {
    DeptsArg deptsArg = new DeptsArg();
    DataRightsContext context = new DataRightsContext();
    context.setTenantId((String) param.get("tenantId"));
    context.setAppId((String) param.get("AppId"));
    context.setUserId("1000");
    deptsArg.setContext(context);

    //分页参数
    Page page = new Page();
    int start = Integer.parseInt((String) param.get("start"))
    int length = Integer.parseInt((String) param.get("length"))
    page.currentPage = start / length + 1;
    page.setPageSize(length);
    deptsArg.setPage(page);

    if (StringUtils.isNotBlank((String) param.get("relationType"))) {
      deptsArg.setRelationType(Integer.valueOf((String) param.get("relationType")));
    }

    if (StringUtils.isNotBlank((String) param.get("users"))) {
      List<String> list = new ArrayList<>();
      list.add((String) param.get("users"));
      deptsArg.setUsers(list);
    }
    if (StringUtils.isNotBlank((String) param.get("depts"))) {
      List<String> list = new ArrayList<>();
      list.add((String) param.get("depts"));
      deptsArg.setDepts(list);
    }
    return deptsArg;
  }

  //共享规则缓存
  def static getShareCacheArg(Map<String, Object> param) {
    ShareCacheArg shareCacheArg = new ShareCacheArg()
    DataRightsContext context = new DataRightsContext()
    context.setTenantId((String) param.get("tenantId"))
    context.setAppId((String) param.get("AppId"))
    context.setUserId("1000")
    shareCacheArg.setContext(context)
    //分页参数
    Page page = new Page();
    int start = Integer.parseInt((String) param.get("start"))
    int length = Integer.parseInt((String) param.get("length"))
    page.currentPage = start / length + 1;
    shareCacheArg.setPage(page);


    if (StringUtils.isNotBlank((String) param.get("entityId"))) {
      shareCacheArg.setEntityId((String) param.get("entityId"));
    }
    if (StringUtils.isNotBlank((String) param.get("entityShareId"))) {
      shareCacheArg.setEntityShareId((String) param.get("entityShareId"));
    }
    if (StringUtils.isNotBlank((String) param.get("shareUser"))) {
      shareCacheArg.setShareUser((String) param.get("shareUser"));
    }
    if (StringUtils.isNotBlank((String) param.get("receiveUser"))) {
      shareCacheArg.setReceiveUser((String) param.get("receiveUser"));
    }

    return shareCacheArg;
  }

  //共享规则数据
  def static getShareArg(Map<String, Object> param) {
    ShareArg shareArg = new ShareArg();
    DataRightsContext context = new DataRightsContext();
    context.setTenantId((String) param.get("tenantId"));
    context.setAppId((String) param.get("AppId"));
    context.setUserId("1000");
    shareArg.setContext(context);

    //分页参数
    Page page = new Page();
    int start = Integer.parseInt((String) param.get("start"))
    int length = Integer.parseInt((String) param.get("length"))
    page.currentPage = start / length + 1;
    page.setPageSize(length);
    shareArg.setPage(page);

    if (StringUtils.isNotBlank((String) param.get("ids"))) {
      List<String> list = new ArrayList<>();
      list.add((String) param.get("ids"));
      shareArg.setIds(list);
    }
    return shareArg;
  }

  //缓存初始化参数
  def static getInitCachaArg(Map<String, Object> param) {
    InitCacheArg initCacheArg = new InitCacheArg();
    DataRightsContext context = new DataRightsContext();
    context.setTenantId((String) param.get("tenantId"));
    if (StringUtils.isNotBlank((String) param.get("AppId"))) {
      context.setAppId((String) param.get("AppId"));
    }
//        else {
//            context.setAppId("CRM");
//        }
    context.setUserId("1000");
    initCacheArg.setContext(context);

    if (StringUtils.isNotBlank((String) param.get("AppId"))) {
      initCacheArg.setAppId((String) param.get("AppId"));
    }
    if (StringUtils.isNotBlank((String) param.get("currentPage"))) {
      initCacheArg.setCurrentPage(Integer.parseInt(param.get("currentPage").toString()));
    }

    if (StringUtils.isNotBlank((String) param.get("tenants"))) {
      String[] tenantsString = ((String) param.get("tenants")).split(",");
      List<String> tenants = new ArrayList<>();
      for (String tenant : tenantsString) {
        if (StringUtils.isNotBlank(tenant) && !tenants.contains(tenant)) {
          tenants.add(tenant);
        }
      }
      initCacheArg.setTenants(tenants);
    }
    return initCacheArg;
  }
}
