package com.fxiaoke.paas.console.mapper.metadata

import com.fxiaoke.paas.console.entity.metadata.MtUnique
import com.github.mybatis.mapper.IBatchMapper
import com.github.mybatis.mapper.ICrudMapper
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

interface MtUniqueMapper extends ICrudMapper<MtUnique>, IBatchMapper<MtUnique>, ITenant<MtUniqueMapper> {

  @Select("select * from mt_unique where tenant_id = #{tenantId} and describe_api_name = #{describeApiName}")
  List<MtUnique> findByTenantIdAndDescribeApiName(@Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName)

  @Select("select count(*) from mt_unique where tenant_id = #{tenantId} and describe_api_name = #{describeApiName} and field_name = #{apiName}")
  Integer countByTenantIdDescribeApiNameAndApiName(@Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName, @Param("apiName") String apiName)

  @Delete("delete from mt_unique where tenant_id=#{tenantId} and unique_id in (select unique_id from mt_unique where tenant_id=#{tenantId} and describe_api_name=#{describeApiName} and field_name=#{fieldName} limit 100)")
  int deleteUnique(@Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName, @Param("fieldName") String fieldName)

  @Delete("delete FROM mt_unique where tenant_id=#{tenantId} and unique_id in (select unique_id from mt_unique where tenant_id=#{tenantId} and describe_api_name=#{describeApiName} and field_name in (select api_name from mt_field where tenant_id =#{tenantId} and describe_api_name =#{describeApiName} and api_name not in (\${blackFieldApiNames}) and status='deleted' and is_unique=true) limit 100)")
  int recycleUnique(@Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName, @Param("blackFieldApiNames") String blackFieldApiNames)

}