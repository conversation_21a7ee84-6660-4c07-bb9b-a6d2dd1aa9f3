package com.fxiaoke.template.exception;

/**
 *
 * <AUTHOR>
 * @date 2017/8/9
 */
public class DbxException extends RuntimeException {
  private String code;

  public DbxException(String code, String message) {
    super(message);
    this.code = code;
  }

  private DbxException(String code, String message, Throwable e) {
    super(message, e);
    this.code = code;
  }

  public static DbxException asDbxException(String code, String message) {
    return new DbxException(code, message);
  }

  public static DbxException asDbxException(String code, String message, Throwable e) {
    return new DbxException(code, message, e);
  }

  public static DbxException asConsumerLaterException(String code, String message) {
    return new ConsumerLaterException(code, message);
  }

  public static DbxException wrap(String message, Throwable e) {
    return asDbxException("-1", message, e);
  }
}
