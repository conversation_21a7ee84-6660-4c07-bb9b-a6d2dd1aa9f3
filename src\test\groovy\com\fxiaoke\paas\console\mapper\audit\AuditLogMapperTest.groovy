package com.fxiaoke.paas.console.mapper.audit

import com.fxiaoke.paas.console.mapper.log.AuditLogMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import java.text.SimpleDateFormat

/**
 * <AUTHOR>
 * Created on 2018/5/18.
 */
@ContextConfiguration(value = ["classpath:mapperContext.xml"])
class AuditLogMapperTest extends Specification {

  @Autowired
  AuditLogMapper auditLogMapper

  def "findNewLogNum"() {
    given:
    def time = '2018-05-18 16:16:45.568'
    Date date = new Date()
    SimpleDateFormat dtf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    expect:
    auditLogMapper.findNewLogNum(dtf.parse(time))
  }

}
