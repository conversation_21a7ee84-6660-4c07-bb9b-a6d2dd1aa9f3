<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fxiaoke.paas.console.mapper.datarights.DataRightsDepartmentMapper">

<select id="queryDepartmentMsgByDeptId" resultMap="departmentMsgByDeptIdResultMap" parameterType="java.util.Map">
SELECT
  a.tenant_id as tenantId,
  a.dept_id as deptId,
  a.name as name,
  a.manager_id as leader,
  b.dept_id as subDeptId,
  b.name as subDeptName,
  c.dept_id as supDeptId,
  c.name as supDeptId,
  org_employee_user.user_id as userId,
  org_employee_user.name as userName,
  org_employee_user.position as position
FROM org_dept as a
  left join org_dept as b on a.tenant_id=b.tenant_id and a.dept_id=b.parent_id
left join org_dept as c on a.parent_id=c.dept_id and a.tenant_id=c.tenant_id
left join org_dept_user on org_dept_user.tenant_id=a.tenant_id and org_dept_user.dept_id=a.dept_id
 left join org_employee_user on org_employee_user.tenant_id=a.tenant_id and org_dept_user.user_id=org_employee_user.user_id
where a.tenant_id=#{tenantId} and a.dept_id=#{deptId}
</select>

  <resultMap id="departmentMsgByDeptIdResultMap" type="com.fxiaoke.paas.console.entity.datarights.DepartmentMsg">
  <id property="deptId" column="deptId"></id>
    <result property="tenantId" column="tenantId"></result>
    <result property="name" column="name"></result>
    <result property="leader" column="leader"></result>
   <association property="superiorDepartment" javaType="com.fxiaoke.paas.console.entity.datarights.Department">
     <id column="supDeptId" property="deptId"></id>
     <result column="supDeptName" property="name"></result>
   </association>
    <collection property="subordinateDepartment" ofType="com.fxiaoke.paas.console.entity.datarights.Department">
      <id column="subDeptId" property="deptId"></id>
      <result column="subDeptName" property="name"></result>
    </collection>
    <collection property="users" ofType="com.fxiaoke.paas.console.entity.datarights.Principal">
      <id column="userId" property="userId"></id>
      <result column="userName" property="name"></result>
      <result column="position" property="position"></result>
    </collection>
  </resultMap>
</mapper>