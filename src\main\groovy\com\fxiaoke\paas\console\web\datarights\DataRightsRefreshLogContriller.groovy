package com.fxiaoke.paas.console.web.datarights


import com.alibaba.fastjson.JSONObject
import com.fxiaoke.paas.console.entity.datarights.DataAuthErrorLog
import com.fxiaoke.paas.console.service.OKHttpService
import com.github.autoconf.ConfigFactory

import groovy.util.logging.Slf4j

import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.bind.annotation.RestController

import javax.annotation.PostConstruct


/**
 * <AUTHOR>
 * @date 2019/7/4 下午3:53
 */
@RestController
@Slf4j
@RequestMapping(path = "/datarights_refresh")
class DataRightsRefreshLogContriller {
  private String DATA_AUTH_REFRESH_GET_ERROR_LOG_API_URL
  private String DATA_AUTH_REFRESH_GET_ERROR_LOG_COUNT_API_URL
  private String DATA_AUTH_REFRESH_ERROR_RETRY_API_URL
  @Autowired
  OKHttpService okHttpService

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-console-datarights", { iconfig ->
      this.DATA_AUTH_REFRESH_GET_ERROR_LOG_API_URL =
              iconfig.get("HISTORY_DATA_AUTH_CODE_REFRESH_URL") + "/api/v1/inner/data-auth/log/error-log?token=746afb412ddd0a5e0f1f68833c42da1f"
      this.DATA_AUTH_REFRESH_GET_ERROR_LOG_COUNT_API_URL =
              iconfig.get("HISTORY_DATA_AUTH_CODE_REFRESH_URL") + "/api/v1/inner/data-auth/log/error-log-count?token=746afb412ddd0a5e0f1f68833c42da1f"
      this.DATA_AUTH_REFRESH_ERROR_RETRY_API_URL =
              iconfig.get("HISTORY_DATA_AUTH_CODE_REFRESH_URL") + "/api/v1/inner/data-auth/start-error-refresh?token=746afb412ddd0a5e0f1f68833c42da1f"
    })
  }

  @GetMapping(path = "getErrorLog", produces = "application/json;charset=utf-8")
  @ResponseBody
  String getErrorLog(
          @RequestParam(value = "start", defaultValue = "0", required = false) int start,
          @RequestParam(value = "length", defaultValue = "100", required = false) int length) {
    JSONObject args = new JSONObject()
    args.put("limit", length)
    args.put("offSet", start)
    String logList = okHttpService.postJSON(DATA_AUTH_REFRESH_GET_ERROR_LOG_API_URL, args)
    List<DataAuthErrorLog> dataAuthErrorLogList = JSONObject.parseArray(logList, DataAuthErrorLog.class)
    JSONObject response = new JSONObject()
    response.put("data", dataAuthErrorLogList)
    return response.toJSONString()
  }

  @GetMapping(path = "refresh-error", produces = "application/json;charset=utf-8")
  @ResponseBody
  String refreshError() {
    okHttpService.postForm(DATA_AUTH_REFRESH_ERROR_RETRY_API_URL)
    return "{\"msg\":\"启动成功\"}"

  }

}
