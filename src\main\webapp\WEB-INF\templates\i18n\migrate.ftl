<#assign title="同步（中文）词条到国际化平台">
<#assign active_nav="data_permission_basic">
<#assign headContent>
<style>
  .productRefresh {
    width: 8%;
    margin-left: 20px
  }

  input {
    margin-left: 10px;
  }

  #dataTable2 th {
    vertical-align: middle;
    align-items: center;
  }

  #dataTable2 td {
    vertical-align: middle;
    align-items: center
  }

  .table > thead:first-child > tr:first-child > th {
    text-align: center;
    vertical-align: middle;
  }

  .table > tbody > tr > td {
    text-align: center;
  }

  input {
    width: 10%;
    height: 34px;
    line-height: 34px;
    box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid #c8cccf;
    color: #6a6f77;
    -web-kit-appearance: none;
    -moz-appearance: none;
    outline: 0;
    text-decoration: none;
  }

</style>
</#assign>
<#assign bodyContent>
    <#assign breadcrumbContent>
    <section class="content-header">
      <h1>同步（中文）词条到国际化平台</h1>
    </section>
    </#assign>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <div class="box-header with-border">
          <h3 class="box-title"></h3>
        </div>
        <form class="form-horizontal" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="migrate">
              <pre style="font-size:22px;font-weight:bold">同步预设对象的name、预设字段的label、object_reference或master_detail类型字段的target_related_list_label</pre>
              <input placeholder="企业_ID（,分割）"/>
              <button type="button" class="migrate-start btn btn-primary">开始</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>
</#assign>
<#assign scriptContent>
<script>

  $(".migrate-start").on("click", function () {
    var tenantIds = $("#migrate input").eq(0).val();
    if (tenantIds == "") {
      alert("tenantId不可为空");
      return false;
    }
    $.ajax({
      type: 'GET',
      url: '${CONTEXT_PATH}/i18n/migrate/migrate/start',
      data: {
        tenantIds: tenantIds
      },
      contentType: "application/json", //必须有
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        if (result.status == 200) {
          alert("执行成功")
        } else {
          alert(result.msg)
        }

      },
      error: function (error) {
        alert('网络异常');
      }

    })

  });

</script>
</#assign>
<#include "../layout/layout-main.ftl" />
