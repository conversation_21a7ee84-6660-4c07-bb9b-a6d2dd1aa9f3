# PAAS-Console 技术分析报告

## 项目概述

PAAS-Console 是一个面向技术人员的数据查询/刷库工具，基于 Spring MVC + MyBatis 架构构建的企业级 Java Web 应用。该项目主要为技术运维人员提供跨数据库的数据查询、元数据管理、数据迁移、系统监控等功能。

## 对外提供的核心功能

### 1. 数据查询与分析
- **多数据库查询**: 支持PostgreSQL、MySQL、SQL Server、ClickHouse的统一查询接口
- **元数据查询**: 提供数据库表结构、字段信息、索引信息的查询功能
- **SQL执行器**: 支持自定义SQL查询，提供查询结果导出(Excel)功能
- **数据统计分析**: 基于ClickHouse的大数据量统计分析功能

### 2. 数据迁移与同步(Hamster模块)
- **Schema迁移**: 数据库表结构迁移和同步
- **数据迁移**: 支持跨数据库的数据迁移任务管理
- **迁移验证**: 提供数据迁移前后的数据一致性验证
- **任务监控**: 迁移任务的状态监控和日志查看

### 3. DTS数据传输服务
- **数据库初始化**: 支持多种数据库的初始化操作
- **数据同步**: 企业数据、配置数据的同步功能
- **SQL执行**: 支持查询SQL和变更SQL的远程执行

### 4. 企业组织架构管理
- **部门管理**: 部门信息查询、组织树状图展示
- **员工信息**: 员工信息查询和管理
- **企业信息**: 企业基本信息和路由信息查询

### 5. 许可证管理
- **产品许可证查询**: 企业产品授权信息查询
- **模块权限验证**: 功能模块的权限验证接口
- **许可证同步**: 定时同步许可证信息

### 6. 系统监控与运维
- **配置管理**: 系统配置项的查询和管理
- **性能监控**: JVM监控和应用性能指标
- **健康检查**: 应用和数据库连接状态检查

## 第三方数据源操作方式

### 1. 多租户数据库路由
- **路由策略**: 通过`DbRouterClient`实现动态数据源路由
- **租户隔离**: 基于Schema的租户数据隔离(`ConsoleTenantPolicy`)
- **连接管理**: 使用HikariCP连接池管理多数据源连接

### 2. 数据库访问实现
```groovy
// 多租户数据源路由示例
RouterInfo routerInfo = dbRouterClient.queryRouterInfo(
    tenantId, "CRM", "paas-console", DialectUtil.POSTGRESQL, usePgBouncer
)
// 动态切换数据源
TenantContext.builder()
    .username(routerInfo.userName)
    .password(routerInfo.passWord)
    .url(routerInfo.jdbcUrl)
    .schema(routerInfo.standalone ? "sch_" + tenantId : "public")
    .build()
```

### 3. 外部服务集成
- **Dubbo服务调用**: 通过Dubbo调用企业信息服务(`fs-uc-api`)
- **HTTP API调用**: 使用OkHttp客户端调用外部REST API
- **MongoDB操作**: 工作流数据的MongoDB存储和查询
- **Redis缓存**: 数据权限和会话信息的缓存操作

## 技术架构与模块分层

### 核心技术栈
- **开发语言**: Java 8+ / Groovy
- **Web框架**: Spring MVC 4.x
- **持久层**: MyBatis 3.x + 动态数据源
- **构建工具**: Maven 3.x
- **模板引擎**: FreeMarker 2.3.23

### 分层架构
```
├── Web层 (Controller) - HTTP请求处理
│   ├── /metadata/* - 元数据管理接口
│   ├── /datarights/* - 数据权限接口
│   ├── /hamster/* - 数据迁移接口
│   ├── /dts/* - 数据传输接口
│   ├── /organization/* - 组织架构接口
│   └── /license/* - 许可证管理接口
├── Service层 - 业务逻辑处理
│   ├── 数据查询服务 (多数据库支持)
│   ├── 数据迁移服务 (Hamster)
│   ├── 外部服务代理 (Dubbo/HTTP)
│   └── 缓存管理服务 (Redis)
├── Mapper层 - 数据访问
│   ├── 动态数据源路由
│   ├── MyBatis Mapper接口
│   └── 多租户SQL拦截器
└── Entity/Bean层 - 数据模型
    ├── 数据库实体类
    └── API传输对象(DTO)
```

## 中间件集成与用途

### 1. Redis缓存系统
- **数据权限缓存**: 缓存用户数据权限信息，提高权限验证性能
- **审计日志缓存**: 缓存未读的审计日志消息
- **会话管理**: 用户会话信息的缓存存储
- **配置**: `paas-console-redis`配置项，基于Jedis客户端

### 2. XXL-Job分布式任务调度
- **许可证同步**: `LicenseSyncJobHandler` - 定时同步企业许可证信息
- **日志清理**: `DelAuditLogService` - 定时清理过期的审计日志
- **任务监控**: 提供任务执行状态监控和日志管理
- **配置**: 支持集群部署和故障转移

### 3. MongoDB文档数据库
- **工作流数据**: 存储工作流实例、任务、历史数据
- **复杂查询**: 支持工作流的复杂条件查询和聚合操作
- **配置**: `mongo-support-workflow`配置项
- **连接管理**: 支持连接池和读写分离

### 4. Dubbo微服务调用
- **企业信息服务**: 调用`fs-uc-api`获取企业基本信息
- **组织架构服务**: 获取部门、员工等组织架构数据
- **注册中心**: 使用Zookeeper作为服务注册中心
- **服务治理**: 支持服务监控、负载均衡、故障转移

### 5. ClickHouse大数据分析
- **大数据统计**: 处理海量数据的统计分析查询
- **实时查询**: 支持实时数据查询和报表生成
- **高性能**: 列式存储，查询性能优异
- **版本**: 使用0.3.2-patch11版本的JDBC驱动

### 6. Elasticsearch搜索引擎(间接支持)
- **全文搜索**: 通过模板页面支持ES数据的全文搜索
- **数据分析**: 提供数据分析和可视化能力
- **集成方式**: 通过HTTP API调用ES服务

## 数据库支持与多租户架构

### 支持的数据库类型
- **PostgreSQL**: 主要数据库，支持Schema级别的租户隔离
- **MySQL**: 兼容支持，版本5.1.47，用于传统业务系统
- **SQL Server**: 企业级数据库支持，用于大型企业客户
- **ClickHouse**: 专用于大数据分析和实时查询
- **MongoDB**: 文档数据库，主要用于工作流和非结构化数据

### 多租户数据隔离实现
```groovy
// 租户策略实现
@Component
class ConsoleTenantPolicy implements TenantPolicy {
    @Override
    TenantContext get(String tenantId, boolean readOnly) {
        // 查询租户路由信息
        RouterInfo routerInfo = dbRouterClient.queryRouterInfo(
            tenantId, "CRM", "paas-console", DialectUtil.POSTGRESQL, usePgBouncer
        )

        // 构建租户上下文
        return TenantContext.builder()
            .username(routerInfo.userName)
            .password(routerInfo.passWord)
            .url(routerInfo.jdbcUrl)
            .schema(routerInfo.standalone ? "sch_" + tenantId : "public")
            .build()
    }
}
```

### 动态数据源配置
- **路由客户端**: `DbRouterClient`负责查询租户的数据库路由信息
- **连接池管理**: 使用HikariCP管理多数据源连接池
- **读写分离**: 支持主从数据库的读写分离
- **故障转移**: 自动切换到备用数据源

## 开发与测试

### 开发环境搭建
1. **环境要求**
   - JDK 8+
   - Maven 3.x
   - IDE支持Groovy (推荐IntelliJ IDEA)

2. **项目启动**
   ```bash
   # 编译项目
   mvn clean compile

   # 运行测试
   mvn test

   # 打包部署
   mvn clean package
   ```

3. **配置文件**
   - `application.properties`: 基础配置
   - 多环境配置通过配置中心管理

### API测试工具
项目提供了完整的HTTP测试文件(位于`/http`目录):

- **CmsController.http**: 配置管理API测试
  ```http
  POST http://localhost:8080/cms/queryNotAllowConfigs
  Content-Type: application/json

  {"configNames": ["fs-gray-metadata"]}
  ```

- **DataCountController.http**: 数据统计API测试
- **bizconfQuery.http**: 业务配置查询测试
- **paas.http**: 通用平台API测试

### 测试框架
- **单元测试**: 使用Spock框架(基于Groovy)
- **集成测试**: MyBatis Mapper测试
- **API测试**: HTTP Client文件支持

### 部署方式
- **打包格式**: WAR包，支持Tomcat/Jetty容器
- **配置管理**: 基于配置中心的动态配置
- **监控集成**: JVM监控、应用性能监控
- **日志系统**: Logback日志框架，支持日志轮转

## 核心依赖库

### 外部依赖
- **FastJSON**: JSON数据序列化/反序列化
- **EasyExcel**: Excel文件读写处理
- **Apache HttpClient**: HTTP客户端通信
- **FreeMarker**: Web页面模板引擎

### 内部服务依赖
- **fs-metadata-provider**: 元数据服务提供者
- **fs-paas-org-provider**: 组织架构服务
- **fs-paas-license-api**: 许可证管理API
- **hamster-client**: 数据迁移客户端
- **paas-common**: 公共组件库

## 技术特点

1. **多租户架构**: 完善的多租户数据隔离，支持企业级SaaS部署
2. **多数据库支持**: 统一接口操作PostgreSQL、MySQL、SQL Server、ClickHouse等
3. **高性能**: 连接池管理、缓存机制、读写分离优化查询性能
4. **模块化设计**: 清晰的分层架构，便于功能扩展和维护
5. **丰富的集成**: 支持多种中间件和外部服务集成

## 其他功能特性

### 安全与权限
- 基于角色的访问控制和数据权限管理
- 数据脱敏功能和数据库密码加密存储
- 完整的操作审计日志记录

### 系统监控
- JVM性能监控和应用健康检查
- 基于Logback的日志管理系统
- 支持日志轮转和集中化日志收集

## 总结

PAAS-Console是一个专业的企业级数据管理和运维工具，主要面向技术人员提供跨数据库的数据查询、迁移、监控等功能。项目采用现代化的技术架构，具有良好的扩展性和维护性，特别在多租户数据隔离、多数据库支持、中间件集成等方面有完善的实现。