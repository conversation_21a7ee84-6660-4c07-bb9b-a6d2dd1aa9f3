package com.fxiaoke.paas.console.mapper.metadata

import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * <AUTHOR>
 * Created on 2018/3/19.
 */
@ContextConfiguration(locations = ["classpath:mapperContext.xml"])
@Slf4j
class BtnMapperTest extends Specification {
  @Autowired
  BtnMapper btnMapper

  def "findBtnByTenantId"() {
    given:
    def tenantId = "2"
    expect:
    btnMapper.setTenantId(tenantId).findBtnByTenantId(tenantId)
  }

  def "findActByTenantId"() {
    given:
    def tenantId = "2"
    expect:
    btnMapper.setTenantId(tenantId).findActByTenantId(tenantId)
  }

  def "findFunByTenantId"() {
    given:
    def tenantId = "2"
    expect:
    btnMapper.setTenantId(tenantId).findFunByTenantId(tenantId)
  }

  def "getInfo"() {
    given:
    def tenantId = "2"
    def btnId = "5a8d057c830bdba244bd9b71"
    def actId = "5a700799830bdbfd9870d580"
    def funId = "5aaa5341830bdbbfdd502c69"
    expect:
    btnMapper.setTenantId(tenantId).findBtnByBtnId(btnId)
    btnMapper.setTenantId(tenantId).findActByActId(actId)
    btnMapper.setTenantId(tenantId).findFunByFunId(funId)
  }


}
