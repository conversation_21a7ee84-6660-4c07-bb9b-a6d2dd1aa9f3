<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:cache="http://www.springframework.org/schema/cache"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xmlns="http://www.springframework.org/schema/beans"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
  http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache.xsd">
  <!-- 缓存注解驱动 -->
<bean id="cacheManager" class="org.springframework.cache.guava.GuavaCacheManager"
p:cacheBuilder-ref="guavaCacheBuilder"/>

<bean id="guavaCacheBuilder"
      class="com.google.common.cache.CacheBuilder"
      factory-method="from"
      c:spec="maximumSize=5000,expireAfterAccess=1h,expireAfterWrite=6h"/>
<cache:annotation-driven cache-manager="cacheManager"/>
  </beans>
