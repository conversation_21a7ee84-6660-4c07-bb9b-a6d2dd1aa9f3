package com.fxiaoke.paas.console.service.dts;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.common.Holder;
import com.fxiaoke.common.PasswordUtil;
import com.fxiaoke.common.http.HttpClient;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.k8s.support.util.SystemUtils;
import com.fxiaoke.paas.console.bean.dts.DtsCheckDataArg;
import com.fxiaoke.paas.console.bean.dts.DtsConfigArg;
import com.fxiaoke.paas.console.util.dts.DtsInternUtils;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.admin.ConfigAdminClient;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.StringReader;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2024/7/23 19:21
 */
@Service
@Slf4j
public class DtsService {
    @Resource
    private EnterpriseEditionService enterpriseEditionService;

    @Resource(name = "httpSupport")
    private OkHttpSupport okHttpSupport;
    @Autowired
    private DbRouterClient dbRouterClient;
    private String dtsConfigTemplate = "";
    private String dtsEgressTemplate = "";
    private String checkJdbcHost;
    private String dataxSyncHost;
    private String dataxPusherHost;
    private String checkJdbcUri = "/api/check/jdbc";
    private boolean httpProxyEnable;

    private String mysqlJdbcUrlTemplate;
    private String sqlServerJdbcUrlTemplate;
    private String oracleServerNameJdbcUrlTemplate;
    private String oracleSidJdbcUrlTemplate;
    private String postgresqlJdbcUrlTemplate;
    private final ConfigAdminClient configAdmin = new ConfigAdminClient();
    private Map<String, Properties> dataxSyncMapping = Maps.newHashMap();
    private Map<String, Properties> dataxAgentMapping = Maps.newHashMap();

    @PostConstruct
    private void init() {
        ConfigFactory.getConfig("datax-config-template", config -> dtsConfigTemplate = config.getString());

        ConfigFactory.getConfig("datax-egress-template", config -> dtsEgressTemplate = config.getString());

        ConfigFactory.getConfig("paas-console-dts", config -> {
            //dts服务校验jdbcUrl host
            checkJdbcHost = config.get("dts.check.jdbc.host");
            dataxSyncHost = config.get("dts.datax.sync.host.url");
            dataxPusherHost = config.get("dts.datax.pusher.host");

            //数据库连接模版
            mysqlJdbcUrlTemplate = config.get("dts.mysql.jdbc.url");
            sqlServerJdbcUrlTemplate = config.get("dts.sqlserver.jdbc.url");
            oracleServerNameJdbcUrlTemplate = config.get("dts.oracle.server_name.jdbc.url");
            oracleSidJdbcUrlTemplate = config.get("dts.oracle.sid.jdbc.url");
            postgresqlJdbcUrlTemplate = config.get("dts.postgresql.jdbc.url");
            //http请求是否使用代理
            httpProxyEnable = config.getBool("dts.http.proxy.enable", false);
        });

        //初始化加载datax配置信息
        try{
            loadDataxConfig();
        }catch (Exception e){
            log.error("load datax config fail, ", e);
        }
    }

    /**
     * 查询企业明细
     * @param tenantId
     * @return
     */
    public SimpleEnterpriseData getEnterpriseInfo(String tenantId) {
        BatchGetSimpleEnterpriseDataArg batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(Arrays.asList(Integer.parseInt(tenantId)), null);
        BatchGetSimpleEnterpriseDataResult result = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg);
        List<SimpleEnterpriseData> dataList = result.getSimpleEnterpriseList();
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        return dataList.get(0);
    }

    /**
     * 请求datax的检查jdbcUrl有效性
     * @param jdbcUrl
     * @return
     */
    private String doCheckJdbcUrl(String jdbcUrl) {
        String postUrl = checkJdbcHost + checkJdbcUri;
        String requestBody = JSON.toJSONString(ImmutableMap.of("jdbcUrl", jdbcUrl));
        return HttpClient.defaultClient().postJson(postUrl, requestBody);
    }

    /**
     * 检查jdbcUrl有效性
     * @param arg
     * @return
     */
    public String checkJdbcUrl(DtsConfigArg arg) {
        String jdbcUrl = generateDtsJdbcUrl(arg);
        if (StringUtils.isBlank(jdbcUrl)) {
            return "jdbcUrl is empty";
        }
        return doCheckJdbcUrl(jdbcUrl);
    }

    /**
     * 检查数据
     * @param arg
     * @return
     */
    public String checkDtsData(DtsCheckDataArg arg) {
        String serverType = arg.getServerType();
        if(StringUtils.isBlank(serverType)){
            return "服务类型不能为空";
        }
        //重新加载datax配置信息
        if (StringUtils.equals("3", serverType)) {
            //重新加载配置
            loadDataxConfig();
            return "datax配置加载完毕!";
        }
        String ei = arg.getEi();
        if (StringUtils.isBlank(ei)) {
            throw new IllegalArgumentException("企业ID不能为空!");
        }
        String opType = arg.getOpType();
        if (StringUtils.isAnyBlank(serverType, opType)) {
            throw new IllegalArgumentException("必要参数不能为空!");
        }
        if (StringUtils.equals("1", serverType)) {
            return doQueryDataIdInDatax(arg);
        }
        if (StringUtils.equals("2", serverType)) {
            return doQueryDataIdInAgent(arg);
        }
        return "无法识别的服务类型: " + serverType;
    }

    /**
     * 重新加载datax配置信息
     */
    private void loadDataxConfig(){
        try {
            String profile1 = ConfigHelper.getProcessInfo().getProfile();
            String profile2 = ConfigHelper.getProcessInfo().getProfile();
            if (SystemUtils.getRuntimeEnv() == SystemUtils.RuntimeEnv.FONESHARE) {
                //如果是foneshare环境，则加载fs-paas-datax-pusher-config的foneshare-vip环境; datax-config-mapping的foneshare环境
                profile1 = "foneshare-vip";
                profile2 = "foneshare";
            }
            //加载datax-agent配置
            String content1 = getConfigContent(profile1, "fs-paas-datax-pusher-config");
            if(StringUtils.isNotBlank(content1)){
                dataxAgentMapping = load(content1);
            }

            //加载直连方式的配置
            String content2 = getConfigContent(profile2, "datax-config-mapping");
            if(StringUtils.isNotBlank(content2)){
                dataxSyncMapping = load(content2);
            }
        } catch (Exception e) {
            log.error("load datax config fail, ", e);
            throw new RuntimeException("load datax config fail, ", e);
        }
    }

    private String getConfigContent(String profile, String configName) throws Exception{
        try{
            log.info("load datax config,get config {}/{} content",configName,profile);
            return configAdmin.get("_ALL_", profile, configName,"",true);
        }catch (FileNotFoundException e){
            log.warn("load datax config fail, {}/{} not found",configName,profile);
            return null;
        }catch (Exception e){
            log.error("get config {}/{} content fail, ",configName,profile, e);
            throw e;
        }
    }

    /**
     * 请求客户agent，执行get查询
     * @param ei
     * @param url
     * @param user
     * @param pwd
     * @return
     */
    private String doGetInAgent(String ei, String url, String user, String pwd) {
        String credential = Credentials.basic(user, pwd);
        Map<String, String> headMap = ImmutableMap.of("Authorization", credential);
        Request request = new Request.Builder().headers(Headers.of(headMap)).url(url).get().build();
        OkHttpSupport client = okHttpSupport;
        if (!httpProxyEnable) {
            client = HttpClient.defaultClient().getClient();
        }
        return (String) client.syncExecute(request, new SyncCallback() {
            @Override
            public Object response(Response response) {
                try {
                    ResponseBody resBody = response.body();
                    String res = resBody == null ? "fail: result is empty" : resBody.string();
                    log.info("do get: tenantId: {}, url: {},resCode={}, res={}", ei, url, response.code(), res);
                    return res;
                } catch (Exception e) {
                    log.error("do get fail,tenantId: {}, url: {},error ", ei, url, e);
                }
                return "fail: result is empty";
            }
        });
    }

    /**
     * 请求客户agent，执行post查询
     * @param ei
     * @param url
     * @param user
     * @param pwd
     * @param postBody
     * @return
     */
    private String doPostInAgent(String ei, String url, String user, String pwd, String postBody) {
        RequestBody body = RequestBody.create(postBody.getBytes());
        String credential = Credentials.basic(user, pwd);
        Request request = new Request.Builder().url(url).header("Authorization", credential).post(body).build();
        OkHttpSupport client = okHttpSupport;
        if (!httpProxyEnable) {
            client = HttpClient.defaultClient().getClient();
        }
        return (String) client.syncExecute(request, new SyncCallback() {
            @Override
            public Object response(Response response) {
                try {
                    ResponseBody resBody = response.body();
                    String res = resBody == null ? "fail: result is empty" : resBody.string();
                    log.info("exec sql: tenantId: {}, url: {}, sql: {}, resCode={}, res={}", ei, url, postBody, response.code(), res);
                    return res;
                } catch (Exception e) {
                    log.error("do exec sql fail,tenantId: {}, url: {},sql: {}, error ", ei, url, postBody, e);
                }
                return "fail: result is empty";
            }
        });
    }

    private boolean isGrayEi(String tenantId) {
        if(dataxSyncMapping.containsKey(tenantId)){
            return true;
        }
        for (Map.Entry<String, Properties> entry : dataxSyncMapping.entrySet()) {
            Properties properties = entry.getValue();
            String enableN1 = properties.getProperty("enable_n_1", "false");
            if (BooleanUtils.toBoolean(enableN1)) {
                String n1Eis = properties.getProperty("n_1_eis", "");
                if (StringUtils.isNotBlank(n1Eis) && StringUtils.contains(n1Eis, tenantId)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 访问datax-sync，执行数据查询
     * @param arg
     * @return
     */
    private String doQueryDataIdInDatax(DtsCheckDataArg arg) {
        String ei = arg.getEi();
        if (!isGrayEi(ei)) {
            return "NOT GRAY EI: " + ei;
        }
        String objectApiName = arg.getObjectApiName();
        String table = DtsInternUtils.toTableName(objectApiName);
        String queryData = arg.getQueryData();
        String opType = arg.getOpType();
        Holder<Boolean> displaySourceUrl = new Holder<>();
        displaySourceUrl.set(true);

        String serviceHost = StringUtils.defaultIfBlank(arg.getServiceHost(), dataxSyncHost);
        List<String> queryList = Lists.newArrayList();
        switch (opType) {
            case "query_id":
                queryList.add(serviceHost + "/api/query/id?tenantId="+ei+"&table=" + table + "&id=" + queryData);
                displaySourceUrl.set(false);
                break;
            case "table_count":
                queryList.add(serviceHost + "/api/table/count?tenantId="+ei+"&table=" + table);
                break;
            case "table_info":
                queryList.add(serviceHost + "/api/table/info?tenantId="+ei+"&table=" + table);
                displaySourceUrl.set(false);
                break;
            case "table_refresh":
                queryList.add(serviceHost + "/api/table/refresh?tenantId="+ei+"&table=" + table);
                displaySourceUrl.set(false);
                break;
            case "table_drop":
                queryList.add(serviceHost + "/api/table/drop?tenantId="+ei+"&table=" + table);
                break;
            case "sync_data":
                if(StringUtils.isNotBlank(objectApiName)){
                    queryList.add(serviceHost + "/datax/init/data/by/object?tenantId="+ei+"&apiName=" + objectApiName);
                }else if(StringUtils.isNotBlank(queryData)){
                    Splitter.on(",").splitToList(queryData).forEach(apiName -> queryList.add(serviceHost + "/datax/init/data/by/object?tenantId="+ei+"&apiName=" + apiName));
                }
                break;
            case "sync_data_id":
                if(StringUtils.isNotBlank(queryData)){
                    Splitter.on(",").splitToList(queryData).forEach(id -> queryList.add(serviceHost + "/datax/init/data/by/object/id?tenantId="+ei+"&apiName=" + objectApiName+"&id="+id));
                }
                displaySourceUrl.set(false);
                break;
            case "sync_1n_object_data":
                if(StringUtils.isNotBlank(objectApiName)){
                    queryList.add(serviceHost + "/datax/init/data/by/n/object?ent1="+ei+"&apiName=" + objectApiName);
                }else if(StringUtils.isNotBlank(queryData)){
                    Splitter.on(",").splitToList(queryData).forEach(apiName -> queryList.add(serviceHost + "/datax/init/data/by/n/object?ent1="+ei+"&apiName=" + apiName));
                }
                break;
            case "sync_1n_data_ei_id":
                if(StringUtils.isNotBlank(ei)){
                    queryList.add(serviceHost + "/datax/init/data/n/id?ent1="+ei);
                }else if(StringUtils.isNotBlank(queryData)){
                    Splitter.on(",").splitToList(queryData).forEach(sei -> queryList.add(serviceHost + "/datax/init/data/n/id?ent1="+sei));
                }
                break;
            case "sync_describe":
                if(StringUtils.isNotBlank(objectApiName)){
                    queryList.add(serviceHost + "/datax/init/describe/by/object?tenantId="+ei+"&apiName=" + objectApiName);
                }else if(StringUtils.isNotBlank(queryData)){
                    Splitter.on(",").splitToList(queryData).forEach(apiName -> queryList.add(serviceHost + "/datax/init/describe/by/object?tenantId="+ei+"&apiName=" + apiName));
                }
                break;
            case "sync_city_data":
                queryList.add(serviceHost + "/datax/init/city/data?tenantId="+ei);
                break;
            case "sync_config_table":
                if(StringUtils.isNotBlank(objectApiName) && StringUtils.contains(objectApiName, ".")){
                    String schema = StringUtils.substringBefore(objectApiName, ".");
                    String configTable = StringUtils.substringAfter(objectApiName, ".");
                    queryList.add(serviceHost + "/datax/init/table/by/config?tenantId="+ei+"&schema="+schema+"&table="+configTable);
                }
                break;
            case "sync_config_data":
                if(StringUtils.isNotBlank(objectApiName) && StringUtils.contains(objectApiName, ".")){
                    String schema = StringUtils.substringBefore(objectApiName, ".");
                    String configTable = StringUtils.substringAfter(objectApiName, ".");
                    queryList.add(serviceHost + "/datax/init/data/by/config?tenantId="+ei+"&schema="+schema+"&table="+configTable);
                }
                break;
            case "sync_n_config_data":
                queryList.add(serviceHost + "/datax/init/data/by/n/config?ent1="+ei+"&table="+objectApiName);
                break;
            case "init_all_data":
                queryList.add(serviceHost + "/datax/init/data/id?tenantId="+ei);
                break;
            case "init_all_describe":
                queryList.add(serviceHost + "/datax/init/describe/id?tenantId="+ei);
                break;
            case "init_db":
                queryList.add(serviceHost + "/api/init/db?tenantId="+ei);
                break;

            case "sync_log_object":
                queryList.add(serviceHost + "/init/audit/log/by/object?tenantId="+ei+"&apiName=" + objectApiName);
                break;
            case "sync_log_ei":
                queryList.add(serviceHost + "/init/audit/log/by/id?tenantId="+ei);
                break;
            case "sync_log_group":
                queryList.add(serviceHost + "/init/audit/log/group?tenantId="+ei);
                break;
            case "sync_login_log":
                queryList.add(serviceHost + "/init/login/log/by/id?tenantId="+ei);
                break;
        }
        if (CollectionUtils.isNotEmpty(queryList)) {
            StringBuilder sb = new StringBuilder();
            queryList.forEach(url -> {
                String result = HttpClient.defaultClient().get(url);
                if(displaySourceUrl.get()){
                    sb.append(url).append("\n");
                }
                sb.append(result);
                log.info("query data: tenantId: {}, url: {}, result={}", ei, url, result);
            });
            return sb.toString();
        }
        String postUrl = "";
        switch (opType) {
            case "query_sql":
                postUrl = serviceHost + "/api/query/sql";
                break;
            case "update_sql":
                postUrl = serviceHost + "/api/exec/sql";
                break;
        }
        if (StringUtils.isNotBlank(postUrl)) {
            String postBody = JSON.toJSONString(ImmutableMap.of("tenantId",ei,"sql",queryData));
            String result = HttpClient.defaultClient().postJson(postUrl,postBody);
            log.info("exec sql: tenantId: {}, url: {}, postBody: {}, result={}", ei, postUrl, postBody, result);
            return result;
        }
        return "无法识别的操作类型: " + opType;
    }

    /**
     * 基于文件投递数据的方式，查询数据
     * @param arg
     * @return
     */
    private String doQueryDataIdInAgent(DtsCheckDataArg arg) {
        String ei = arg.getEi();
        String objectApiName = arg.getObjectApiName();
        if (!dataxAgentMapping.containsKey(ei)) {
            return "NOT GRAY EI: " + ei;
        }
        String serverHost = dataxAgentMapping.get(ei).getProperty("upload_host");
        String dataxUser = dataxAgentMapping.get(ei).getProperty("datax_user");
        String dataxPwd = dataxAgentMapping.get(ei).getProperty("datax_pwd");

        String table = DtsInternUtils.toTableName(objectApiName);
        String queryData = arg.getQueryData();
        String opType = arg.getOpType();
        List<String> queryList = Lists.newArrayList();
        Holder<Boolean> displaySourceUrl = new Holder<>();
        displaySourceUrl.set(true);

        switch (opType) {
            case "query_id":
                queryList.add(serverHost + "/api/query/id?table=" + table + "&id=" + queryData);
                displaySourceUrl.set(false);
                break;
            case "table_count":
                queryList.add(serverHost + "/api/table/count?table=" + table);
                break;
            case "table_info":
                queryList.add(serverHost + "/api/table/info?table=" + table);
                displaySourceUrl.set(false);
                break;
            case "table_refresh":
                queryList.add(serverHost + "/api/table/refresh?table=" + table);
                displaySourceUrl.set(false);
            case "table_reload":
                queryList.add(serverHost + "/api/table/refresh?tenantId="+ei+"&table=" + table);
                displaySourceUrl.set(false);
                break;
            case "sync_data":
                if(StringUtils.isNotBlank(objectApiName)){
                    queryList.add(dataxPusherHost + "/datax/init/data/by/object?tenantId="+ei+"&apiName=" + objectApiName);
                }else if(StringUtils.isNotBlank(queryData)){
                    Splitter.on(",").splitToList(queryData).forEach(apiName -> queryList.add(dataxPusherHost + "/datax/init/data/by/object?tenantId="+ei+"&apiName=" + apiName));
                }
                break;
            case "sync_city_data":
                queryList.add(dataxPusherHost + "/datax/init/city/data?tenantId="+ei);
                break;
            case "sync_config_table":
                queryList.add(dataxPusherHost + "/datax/init/table/by/config?tenantId="+ei);
                break;
            case "sync_config_data":
                queryList.add(dataxPusherHost + "/datax/init/data/by/config?tenantId="+ei);
                break;
            case "sync_data_id":
                if(StringUtils.isNotBlank(queryData)){
                    Splitter.on(",").splitToList(queryData).forEach(id -> queryList.add(dataxPusherHost + "/datax/init/data/by/object/id?tenantId="+ei+"&apiName=" + objectApiName+"&id="+id));
                }
                break;
            case "sync_describe":
                if(StringUtils.isNotBlank(objectApiName)){
                    queryList.add(dataxPusherHost + "/datax/init/describe/by/object?tenantId="+ei+"&apiName=" + objectApiName);
                }else if(StringUtils.isNotBlank(queryData)){
                    Splitter.on(",").splitToList(queryData).forEach(apiName -> queryList.add(dataxPusherHost + "/datax/init/describe/by/object?tenantId="+ei+"&apiName=" + apiName));
                }
                break;
            case "init_all_data":
                queryList.add(dataxPusherHost + "/datax/init/data/id?tenantId="+ei);
                break;
            case "init_all_describe":
                queryList.add(dataxPusherHost + "/datax/init/describe/id?tenantId="+ei);
                break;
            case "table_drop":
                queryList.add(serverHost + "/api/table/drop?table=" + table);
                break;
        }
        if (CollectionUtils.isNotEmpty(queryList)) {
            StringBuilder sb = new StringBuilder();
            queryList.forEach(url -> {
                String result = doGetInAgent(ei, url, dataxUser, dataxPwd);
                if(displaySourceUrl.get()){
                    sb.append(url).append("\n");
                }
                sb.append(result);
                log.info("query data: tenantId: {}, url: {}, result={}", ei, url, result);
            });
            return sb.toString();
        }
        String postUrl = "";
        switch (opType) {
            case "query_sql":
                postUrl = serverHost + "/api/query/sql";
                break;
            case "update_sql":
                postUrl = serverHost + "/api/exec/sql";
                break;
        }
        if (StringUtils.isNotBlank(postUrl)) {
            return doPostInAgent(ei, postUrl, dataxUser, dataxPwd, queryData);
        }
        return "无法识别的操作类型: " + opType;
    }

    /**
     * 生成datax配置数据
     * @param arg
     */
    public void generateDtsConfig(DtsConfigArg arg) {
        String ei = arg.getEi();
        if (StringUtils.isBlank(ei)) {
            throw new IllegalArgumentException("企业ID不能为空");
        }
        SimpleEnterpriseData ent = getEnterpriseInfo(ei);
        if (ent == null) {
            throw new IllegalArgumentException("企业信息不存在");
        }
        String ea = ent.getEnterpriseAccount();
        String entName = ent.getEnterpriseName();
        String jdbcUrl = generateDtsJdbcUrl(arg);
        String needCheckJdbcUrl = StringUtils.defaultIfBlank(arg.getCheckJdbcUrl(), "0");
        //生成配置数据的时候，是否需要校验jdbcUrl
        if (StringUtils.equals("1", needCheckJdbcUrl)) {
            String checkResult = doCheckJdbcUrl(jdbcUrl);
            if (!StringUtils.equals(checkResult, "\"ok\"")) {
                arg.setConfig(checkResult);
                return;
            }
        }
        String configInfo = StringUtils.replace(dtsConfigTemplate, "${EA}", ea).replace("${SYNC_RELEVANT_TEAM}", StringUtils.defaultIfBlank(arg.getSyncRelevantTeam(), "false")).replace("${SYNC_ALL_CITY}", StringUtils.defaultIfBlank(arg.getSyncAllCity(),"false")).replace("${DATE_TIME_TO_LONG}", StringUtils.defaultIfBlank(arg.getDateTimeToLong(),"true")).replace("${TOMBSTONE}", StringUtils.defaultIfBlank(arg.getTombstone(),"false")).replace("${ENT_NAME}", entName).replace("${EI}", ei).replace("${JDBC_URL}", jdbcUrl).replace("${DISPATCH_TIME}", arg.getDispatchTime());
        arg.setConfig(configInfo);
        arg.setJdbcUrl(jdbcUrl);
    }

    public String generateDtsEgress(String ei,String port,String dbInfo) {
        if (StringUtils.isAnyBlank(ei,port,dbInfo)) {
            throw new IllegalArgumentException("企业ID / 端口 / DB信息不能为空");
        }
        return StringUtils.replace(dtsEgressTemplate, "${PORT}", port).replace("${EI}", ei).replace("${DB_INFO}", dbInfo);
    }

    /**
     * 基于配置中心加密算法对数据进行加密
     * @param password
     * @return
     */
    private String ecodePassword(String password) {
        try {
            return PasswordUtil.encode(password);
        } catch (Exception e) {
            log.error("cannot decode password {}", password, e);
            return null;
        }
    }

    /**
     * 加载ini配置信息
     * @param iniContent
     * @return
     * @throws Exception
     */
    private Map<String, Properties> load(String iniContent) throws Exception {
        Map<String, Properties> sections = Maps.newHashMap();
        BufferedReader reader = new BufferedReader(new StringReader(iniContent));
        String currentSection = "";
        String line;
        while ((line = reader.readLine()) != null) {
            line = line.trim();
            if (line.isEmpty() || line.startsWith(";")) {
                continue;
            }
            if (line.startsWith("[") && line.endsWith("]")) {
                currentSection = line.substring(1, line.length() - 1);
                sections.put(currentSection, new Properties());
            } else {
                int index = line.indexOf('=');
                if (index > 0) {
                    String key = line.substring(0, index).trim();
                    String value = line.substring(index + 1).trim();
                    sections.get(currentSection).setProperty(key, value);
                }
            }
        }
        return sections;
    }

    /**
     * 生成jdbcUrl
     * @param arg
     * @return
     */
    public String generateDtsJdbcUrl(DtsConfigArg arg) {
        String dbType = arg.getDbType();
        String host = arg.getDbHost();
        String port = arg.getDbPort();
        String dbName = arg.getDbName();
        String user = arg.getDbUser();
        String password = arg.getDbPwd();
        if (StringUtils.isAnyBlank(dbType, host, port, dbName, user, password)) {
            throw new IllegalArgumentException("dbType,host,port,dbName,user,password is empty");
        }
        password = ecodePassword(password);
        if (StringUtils.isEmpty(password)) {
            throw new IllegalArgumentException("密码加密失败");
        }

        if (StringUtils.equals(dbType, "mysql")) {
            //jdbc:mysql://datax:CB539AC21BA642E4287B1E0398116DCEDA559F4D27A67F37B881F4E3FB07BF3C@************/datax?serverTimezone=Asia/Shanghai&rewriteBatchedStatements=true&useSSL=false
            return String.format(mysqlJdbcUrlTemplate, dbType, user, password, host, port, dbName);
//            return "jdbc:" +   dbType + "://"+user+":"+password+"@"+ host + ":" + port + "/" + dbName + "?serverTimezone=Asia/Shanghai&rewriteBatchedStatements=true&useSSL=false";
        }
        if (StringUtils.equals(dbType, "oracle_sn")) {
            //jdbc:oracle:thin:ltcdts/FC368F67AC7A363FE3CF9CB16D63F057174F017BF97F1917@//***********:1521/ltcdtsdb
            return String.format(oracleServerNameJdbcUrlTemplate, user, password, host, port, dbName);
//            return "jdbc:oracle:thin:" +   user + "/" + password + "@//" + host + ":" + port + "/" + dbName;
        }
        if (StringUtils.equals(dbType, "oracle_sid")) {
            //****************************************************
            return String.format(oracleSidJdbcUrlTemplate, user, password, host, port, dbName);
//            return "jdbc:oracle:thin:" +   user + "/" + password + "@" + host + ":" + port + ":" + dbName;
        }
        if (StringUtils.equals(dbType, "sqlserver")) {
            //jdbc:sqlserver://datax_user:E9147C5C74472E5CD04A8D11E6F30D9C0407A0126FABDBBF2468819451F9DDC0@**********:65349;DatabaseName=datax;sslProtocol=TLSv1;encrypt=false
            return String.format(sqlServerJdbcUrlTemplate, dbType, user, password, host, port, dbName);
//            return "jdbc:" +   dbType + "://"+user+":"+password+"@"+ host + ":" + port + ";DatabaseName=" + dbName + ";sslProtocol=TLSv1;encrypt=false";
        }
        if (StringUtils.equals(dbType, "postgresql")) {
            //jdbc:***********************************************************************************************************/datax
            return String.format(postgresqlJdbcUrlTemplate, dbType, user, password, host, port, dbName);
//            return "jdbc:" +   dbType + "://"+user+":"+password+"@"+ host + ":" + port + "/" + dbName;
        }
        return "";
    }

    public String getDtsTopicByEi(String tenantId){
        String result = dbRouterClient.queryJdbcUrl(tenantId, "CRM", "postgresql");
        if(StringUtils.isBlank(result)){
            return tenantId+",无法获取企业的路由信息";
        }
        if(!StringUtils.contains(result,"/")){
            return tenantId+",企业路由格式不正确："+result;
        }
        String dbName = StringUtils.substringAfterLast(result,"/");
        if(StringUtils.length(dbName)<4){
            return tenantId+",企业路由中数据库名格式不正确："+result;
        }
        //去掉result最后3位
        return "buf_"+StringUtils.substring(dbName,0,dbName.length()-3);
    }

}
