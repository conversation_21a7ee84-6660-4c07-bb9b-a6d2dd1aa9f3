<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.paas.console.mapper.metadata.FieldMapper">

  <select id="queryDataIds">
    SELECT id FROM ${table}
    WHERE
    is_deleted >= 0 AND
    tenant_id = #{tenantId} AND
    object_describe_api_name = #{describeApiName}
    <if test="id != null">
      AND id > #{id}
    </if>
    order by id asc limit #{limit}
  </select>

  <update id="clearFieldData">
    update mt_data set
    <foreach collection="columns" item="column" separator=",">
      value${column} = null
    </foreach>
    where tenant_id = #{tenantId} and id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </update>
</mapper>