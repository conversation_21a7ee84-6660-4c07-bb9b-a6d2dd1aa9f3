package com.fxiaoke.template;

import freemarker.template.TemplateMethodModelEx;
import freemarker.template.TemplateModelException;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;

import java.util.List;

public class ShiroFreeSupport implements TemplateMethodModelEx {
  @Override
  public Object exec(List list) throws TemplateModelException {
    Subject subject = SecurityUtils.getSubject();
    if (subject != null) {
      return subject.getPrincipal();
    }
    return null;
  }
}
