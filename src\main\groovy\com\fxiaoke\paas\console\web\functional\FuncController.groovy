package com.fxiaoke.paas.console.web.functional

import com.alibaba.fastjson.JSON
import com.fxiaoke.paas.console.service.functional.FuncService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody

/**
 * <AUTHOR>
 * @date 2020/5/8 16:47
 */


@Controller
@RequestMapping("/func")
class FuncController {
  @Autowired
  private FuncService funcService


  @RequestMapping("/tenant-role-query")
  def tenantRoleQuery() {
    return "functional/tenant-role-query"
  }

  @RequestMapping("/user-role-query")
  def userRoleQuery() {
    return "functional/user-role-query"
  }

  @RequestMapping(path = "/getRoleCodes", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  def getRoleCodes(String tenantId) {
    return ["code": 200, "info": JSON.toJSONString(funcService.queryRoleCodesByTenantId(tenantId))]
  }


  @RequestMapping(path = "/getUsersByRoleCode", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  def getUsersByRoleCode(String tenantId, String roleCode) {
    return ["code": 200, "info": JSON.toJSONString(funcService.queryUsersByTenantIdAndRoleCode(tenantId,roleCode))]
  }

}
