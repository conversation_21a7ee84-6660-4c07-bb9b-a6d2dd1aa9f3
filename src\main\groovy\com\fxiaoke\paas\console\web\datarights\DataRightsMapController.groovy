package com.fxiaoke.paas.console.web.datarights

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.entity.datarights.DepartmentMsg
import com.fxiaoke.paas.console.entity.datarights.DtAuth
import com.fxiaoke.paas.console.entity.datarights.GroupUser
import com.fxiaoke.paas.console.entity.datarights.Personnel
import com.fxiaoke.paas.console.entity.datarights.Principal
import com.fxiaoke.paas.console.entity.datarights.RoleMember
import com.fxiaoke.paas.console.service.datarights.DataRightsDataService
import com.fxiaoke.paas.console.service.OKHttpService
import com.fxiaoke.paas.console.service.datarights.DataRightsStoreTableNameService
import com.fxiaoke.paas.console.util.ApiNameToStoreTableNameUtil
import com.fxiaoke.paas.console.util.PackUtil
import com.github.autoconf.ConfigFactory
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.collections.MapUtils
import org.apache.commons.lang3.ObjectUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping

import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.PostConstruct
import java.util.stream.Collectors


/**
 * <AUTHOR>
 * @date 2019/4/4 下午4:53
 *
 */
@Controller
@RequestMapping(path = "/map")
class DataRightsMapController {
  @Autowired
  private DataRightsDataService dataRightsDataService
  @Autowired
  private DataRightsStoreTableNameService dataRightsStoreTableNameService

  @Autowired
  private OKHttpService okHttpService
  private String GET_DESCRIBE_DETAIL_API_URL

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-console-datarights", { iConfig ->
      this.GET_DESCRIBE_DETAIL_API_URL = iConfig.get("GET_DESCRIBE_DETAIL_API_URL") + "/paas/metadata/describe/findBy/simple.json?tenantId=%s&describeApiName=%s"
    })
  }

  @RequestMapping(path = "/search", produces = "application/json;charset=utf-8")
  @ResponseBody
  @SystemControllerLog(description = "数据权限 -- 权限图谱")
  String search(String tenantId, String objectId, String apiName) {
    JSONObject data = new JSONObject()
    if (StringUtils.isNotBlank(tenantId) && StringUtils.isNotBlank(objectId)) {
      List<JSONObject> jsonObjectList = Lists.newArrayList()
      List<Principal> principals = dataRightsDataService.getTeamMember(tenantId, objectId)
      data.put("tenantId", tenantId)
      data.put("objectId", objectId)

      String jsonString = okHttpService.getForm(String.format(GET_DESCRIBE_DETAIL_API_URL, tenantId, apiName))
      JSONObject jsonObject1 = JSONObject.parseObject(jsonString)
      Integer rolesCode = jsonObject1.get("roles")
      if (rolesCode != null) {
        String binaryString = Integer.toString(rolesCode, 2)
        for (int i = binaryString.length(); i < 4; i++) {
          binaryString = "0" + binaryString
        }
        String relation = ""
        if (binaryString.charAt(0) == '1') {
          relation = relation + "主对象 "
        }
        if (binaryString.charAt(1) == '1') {
          relation = relation + "从对象 "
        }
        if (binaryString.charAt(2) == '1') {
          relation = relation + "被其它对象引用了 "
        }
        if (binaryString.charAt(3) == '1') {
          relation = relation + "引用了其他的的对象 "
        }

        data.put("relation", relation.trim())
      }
      if (CollectionUtils.isEmpty(principals)) {
        data.put("msg", "没有参与人")
        return data.toJSONString()
      }
      String storeTableName = null
      if (StringUtils.isNotBlank(apiName)) {
        storeTableName = dataRightsStoreTableNameService.byDescribeApiName(tenantId, apiName)
      }
      Map<String, String> dataOwnDepartment = null

      if (storeTableName.equalsIgnoreCase("mt_data")) {
        dataOwnDepartment = dataRightsDataService.getDataOwnDepartment(tenantId, objectId)
      } else {
        String newStoreTableName = ApiNameToStoreTableNameUtil.toNewStoreTableName(storeTableName)
        if (newStoreTableName != null) {
          dataOwnDepartment = dataRightsDataService.getDataOwnDepartmentSpecialBiz(tenantId, objectId, newStoreTableName)
        } else {
          dataOwnDepartment = dataRightsDataService.getDataOwnDepartmentSpecial(tenantId, objectId, storeTableName)
        }
      }

      if (dataOwnDepartment != null) {
        DepartmentMsg departmentMsg = dataRightsDataService.getDepartmentMsgByDeptId(tenantId, dataOwnDepartment.get("depid"))
        Map<String, Object> packDepartmentMsg = PackUtil.packDepartmentMsg(departmentMsg)
        data.put("数据归属部门", packDepartmentMsg)
      }
      apiName = dataOwnDepartment != null && StringUtils.isNotBlank(dataOwnDepartment.get("apiname")) ? dataOwnDepartment.get("apiname") : principals.get(0).apiName
      data.put("apiName", apiName)
      DtAuth dtAuth = dataRightsDataService.getDtAuth("dt_auth", apiName, tenantId, objectId)
      DtAuth outDtAuth = dataRightsDataService.getDtAuth("dt_auth_out", apiName, tenantId, objectId)
      data.put("dtAuth", dtAuth)
      data.put("outDtAuth", Objects.isNull(outDtAuth)? DtAuth.builder().build():outDtAuth)
      if (dtAuth != null && CollectionUtils.isNotEmpty(dtAuth.getRelateRules())) {
        Map<String, List<String>> relateRules = dtAuth.getRelateRules().stream().collect(Collectors.groupingBy({ s -> s.split("_")[0] }))
        List<String> scs = relateRules.get("sc")
        List<String> srs = relateRules.get("rc")
        Set<String> scSets = Sets.newHashSet()
        Set<String> srSets = Sets.newHashSet()
        if (CollectionUtils.isNotEmpty(scs)) {
          scs.forEach({ s -> scSets.add(s.split("_")[1]) })
        }
        if (CollectionUtils.isNotEmpty(srs)) {
          srs.forEach({ s -> srSets.add(s.split("_")[1]) })
        }
        List<Map<String, Object>> entityRules = null
        List<Map<String, Object>> receiveRules = null
        if (CollectionUtils.isNotEmpty(srSets)) {
          entityRules = dataRightsDataService.getEntityShareGroupByRuleId(tenantId, srSets)
        }
        if (CollectionUtils.isNotEmpty(scSets)) {
          receiveRules = dataRightsDataService.getReceiveShareGroupByRuleId(tenantId, scSets)
        }
        data.put("基于条件的共享规则组", entityRules != null ? entityRules : Lists.newArrayList())
        data.put("基于来源的共享规则组", receiveRules != null ? receiveRules : Lists.newArrayList())
        List<Map<String, Object>> receiveShare = Lists.newArrayList()
        if (CollectionUtils.isNotEmpty(receiveRules)) {
          receiveRules.forEach({
            rule ->
              Map<String, Object> temp = Maps.newHashMap()
              String receiveType = rule.get("receivetype")
              String receiveId = rule.get("receiveid")
              String shareType = rule.get("sharetype")
              String shareId = rule.get("shareid")
              JSONObject jsonObject = null
              def roles = null
              if (receiveType.equals("4")) {
                jsonObject = dataRightsDataService.getRoleList(tenantId)
                if (jsonObject != null) {
                  roles = jsonObject.get("result")["roles"] as List
                  if (roles != null) {
                    roles.forEach({
                      it ->
                        if (it["roleCode"] == receiveId) {
                          temp.put("目标方", it)
                        }
                    })
                  }
                }
              }


              if (shareType.equals("4")) {
                jsonObject = dataRightsDataService.getRoleList(tenantId)
                if (jsonObject != null) {
                  roles = jsonObject.get("result")["roles"] as List
                  if (roles != null) {
                    roles.forEach({
                      it ->
                        if (it["roleCode"] == shareId) {
                          temp.put("来源方", it)
                        }
                    })
                  }
                }
              }
              if (MapUtils.isNotEmpty(temp)) {
                receiveShare.add(temp)
              }

          })
        }
        data.put("基于来源的共享规则-角色", receiveShare)
      }
      List<Map<String, Object>> temporalRights = dataRightsDataService.getTemporaryRightsByObjectId(tenantId, objectId)
      data.put("临时权限", temporalRights)
      if (CollectionUtils.isNotEmpty(principals)) {
        principals.forEach({
          principal ->
            Map<String, Object> packPrincipal = PackUtil.packPrincipal(principal)
            Personnel personnel = dataRightsDataService.getPersonnelByObjectId(tenantId, principal.getUserId(), objectId)
            if (personnel != null) {
              List<RoleMember> roleMembers = dataRightsDataService.getRoleMemberByRoleTypeAndObjectId(tenantId, objectId, personnel.getRole())
              List<Map<String, Object>> packRoleMembers = Lists.newArrayList()
              roleMembers.forEach({ roleMember ->
                packRoleMembers.add(PackUtil.packRoleMember(roleMember))
              })
              if (CollectionUtils.isNotEmpty(packRoleMembers)) {
                packPrincipal.put("角色成员", packRoleMembers)
              }
              Set<Map<String, String>> groups = personnel.getGroup()
              Map<String, Object> personnelMap = PackUtil.packPersonnel(personnel)
              for (Map<String, String> group : groups) {
                GroupUser groupUser = dataRightsDataService.getGroupUserByGroupId(tenantId, group.get("groupId"))
                if (groupUser != null) {
                  group.put("用户组人员信息", groupUser)
                }
              }
              packPrincipal.put("用户关系数据", personnelMap)
            }
            jsonObjectList.add(packPrincipal)
        })
      }
      data.put("相关团队", jsonObjectList)
    }
    return data.toJSONString()
  }


}
