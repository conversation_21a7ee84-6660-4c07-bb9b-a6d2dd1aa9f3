<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fxiaoke.paas.console.license.mapper.LicenseObjectInfoMapper">

  <resultMap id="LicenseObjectEntity" type="com.fxiaoke.paas.console.entity.license.LicenseObjectEntity">
    <result property="productVersion" column="product_version"/>
    <result property="moduleCode" column="module_code"/>
    <result property="apiName" column="api_name"/>
  </resultMap>


  <select id="queryObjectInfoApiName" resultMap="LicenseObjectEntity">
    select product_version,module_code,api_name from license_object_info where 1=1
    <if test="productVersion != null">
      and product_version = #{productVersion}
    </if>
    <if test="moduleCode != null">
      and module_code = #{moduleCode}
    </if>
  </select>
</mapper>