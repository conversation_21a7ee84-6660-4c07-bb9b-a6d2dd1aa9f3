<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css" xmlns="http://www.w3.org/1999/html" xmlns="http://www.w3.org/1999/html"/>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>数据补迁</h1>
        <ol class="breadcrumb">
            <li><a href="../"><i class="fa fa-dashboard"></i>任务主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>数据补迁接口</a></li>
        </ol>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info">
                    <div class="box-body">
                        <div class="tab-content">
                            <div class="tab-pane fade in active" id="createDesigned">
                                <form class="form-horizontal" action="${ctx}/hamster/supplement-migration-data" method="post" id="myForm" role="form" data-toggle="validator">
                                    <div class="box-body col-xs-6" style="margin-right: -127px">
                                        <div class="form-group">
                                            <label for="biz" class="col-sm-2 control-label">业务</label>
                                            <div class="col-sm-6">
                                                <select id="biz" name="biz" class="selectpicker show-tick form-control" title="Nothing selected" data-live-search="false">
                                                    <option>PAAS</option>
                                                    <option>BI</option>
                                                </select>
                                                <div class="help-block with-errors"></div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="isCompareData" class="col-sm-2 control-label">对比数据</label>
                                            <div class="col-sm-6">
                                                <select id="isCompareData" name="isCompareData" class="selectpicker show-tick form-control" title="Nothing selected" data-live-search="false">
                                                    <option>false</option>
                                                    <option>true</option>
                                                </select>
                                                <div class="help-block with-errors"></div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="tenantId" class="col-sm-2 control-label">租户ID</label>
                                            <div class="col-sm-6">
                                                <input type="text" style="border-radius:5px;" class="form-control" id="tenantId" name="tenantId" value="77996" placeholder="必填" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="tables" class="col-sm-2 control-label">表名</label>
                                            <div class="col-sm-6">
                                                <textarea id="tables" name="tables" class="form-control" style="height: 150px;border-radius:5px;"  placeholder="用逗号隔开，示例：table_name1,table_name2..." required></textarea>
                                                <div class="help-block with-errors"></div>
                                            </div>
                                        </div>
                                        <div class="col-sm-offset-2" style="float:left">
                                            <button type="button" id="supplementBut" class="btn btn-primary">补迁</button>
                                        </div>
                                    </div>
                                    <div class="box-body col-xs-6">
                                        <h4>Result <i class="fa fa-hand-o-down"></i></h4>
                                        <pre id="supplementResult" style=""></pre>
                                        <#--loading。。。-->
                                        <div class="spinner hide" id="loading">
                                            <div class="spinner-container container1">
                                                <div class="circle1"></div>
                                                <div class="circle2"></div>
                                                <div class="circle3"></div>
                                                <div class="circle4"></div>
                                            </div>
                                            <div class="spinner-container container2">
                                                <div class="circle1"></div>
                                                <div class="circle2"></div>
                                                <div class="circle3"></div>
                                                <div class="circle4"></div>
                                            </div>
                                            <div class="spinner-container container3">
                                                <div class="circle1"></div>
                                                <div class="circle2"></div>
                                                <div class="circle3"></div>
                                                <div class="circle4"></div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="box-footer clearfix">
                        <div class="clearfix"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script type="application/javascript">
        $(document).ready(function () {
            $('#supplementBut').on('click', function () {

                $('#supplementResult').html('');
                $('#loading').removeClass('hide');
                $('#supplementBut').attr("disabled", "disabled");
                
                var biz = $('#biz').val()
                var isCompareData = $('#isCompareData').val()
                var tenantId = $('#tenantId').val()
                var tables = $('#tables').val()

                $.post("${CONTEXT_PATH}/hamster/supplement-migration-data", {
                    biz: biz,
                    isCompareData: isCompareData,
                    tenantId: tenantId,
                    tables: tables
                },function (result) {
                    $('#supplementBut').removeAttr("disabled");
                    $('#loading').addClass('hide');

                    if (result.code === 200) {
                        info = result.info
                    } else {
                        info = result
                    }
                    $('#supplementResult').JSONView(info, {
                        collapsed: false,
                        nl2br: true,
                        recursive_collapser: true
                    });
                });
            });
        });

    </script>
</#assign>
<#include "../layout/layout-main.ftl" />