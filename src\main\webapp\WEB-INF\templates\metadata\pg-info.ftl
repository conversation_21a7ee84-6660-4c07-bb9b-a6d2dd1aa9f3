<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
<link type="text/css" href="//static.foneshare.cn/oss/datatables-checkboxes/dataTables.checkboxes.css" rel="stylesheet"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
  <h1 id="h1">PG信息查询</h1>
  <ol class="breadcrumb">
    <li><a href="../"><i class="fa fa-dashboard"></i>主页</a></li>
    <li><a href=""><i class="fa fa-dashboard"></i>查询</a></li>
  </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <div class="box-header with-border">
          <h3 class="box-title"></h3>
        </div>
        <form class="form-horizontal" action="" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="box-body">
            <div class="form-group">
              <label for="biz" class="col-sm-1 control-label">企业ID</label>
              <div class="col-sm-2" style="padding-right: 3%">
                <input type="text" style="border-radius:5px;" class="form-control short-input" id="tenantId" name="tenantId"
                       value="" placeholder="企业ID" required>
                <div id="nameMsg" class="help-block with-errors"></div>
              </div>
              <div class="col-sm-4">
                <button type="button" id="findSqlSub" class="btn btn-default" disabled>查看正在执行的Sql</button>
                <button type="button" id="findLockSub" class="btn btn-danger" disabled>查看持有锁和等待锁</button>
                <button type="button" id="findLinkSub" class="btn btn-info" disabled>查看连接信息</button>
              </div>
            </div>
          </div>
        </form>
        <div class="box box-info">
          <div class="box-body hide" id="sqlBody">
            <table id="datatableSql" class="table table-hover table-bordered" cellpadding="0" width="100%">
              <thead>
              <tr>
                <th>进程ID</th>
                <th>进程开始时间</th>
                <th>经过时间</th>
                <th>执行Sql</th>
              </tr>
              </thead>
            </table>
          </div>
          <div class="box-body hide" id="lockBody">
            <table id="datatableLock" class="table table-hover table-bordered" cellpadding="0" width="100%">
              <thead>
              <tr>
                <th>进程ID</th>
                <th>名称</th>
                <th>应用名称</th>
                <th>开始时间</th>
                <th>状态</th>
                <th>锁状态</th>
                <th>运行时间</th>
                <th>执行语句</th>
              </tr>
              </thead>
            </table>
          </div>
          <div class="box-body hide" id="linkBody">
            <table id="datatableLink" class="table table-hover table-bordered" cellpadding="0" width="100%">
              <thead>
              <tr>
                <th>ID</th>
                <th>名称</th>
                <th>进程ID</th>
                <th>应用ID</th>
                <th>服务名称</th>
                <th>应用名称</th>
                <th>地址</th>
                <th>端口</th>
                <th>后端开始时间</th>
                <th>执行语句</th>
              </tr>
              </thead>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<#--详情-->
<div class="modal fade" id="theInfoModal" tabindex="-1" role="dialog" aria-labelledby="infoModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                class="sr-only">Close</span></button>
        <h4 class="modal-title" id="stopModalLabel"></h4>
      </div>
      <div class="modal-body">
                <pre id="theInfo" style="height: 400px">
                </pre>
      </div>
      <div class="modal-footer">
        <a type="button" class="btn btn-default" data-dismiss="modal">返回</a>
      </div>
    </div>
  </div>
</div>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<script>
  $(document).ready(function () {
//        查看执行sql
    var tableBtn = $("#datatableSql").DataTable({
      "processing": true,
      "ajax": "",
      "columnDefs": [
        {"width": "65%", "targets": 3}
      ],
      "language": {
        "url": "${ctx}/static/js/datatables-zh_CN.json"
      },
      "mark": true
//      "order": [[5, 'desc']]
    });
//        查看锁信息
    var tableAct = $("#datatableLock").DataTable({
      "processing": true,
      "ajax": "",
      "columnDefs": [
        {"width": "35%", "targets": 7}
      ],
      "language": {
        "url": "${ctx}/static/js/datatables-zh_CN.json"
      },
      "mark": true,
      "order": [[3, 'desc']]
    });
//        查看连接表
    var tableFun = $("#datatableLink").DataTable({
//            "deferRender": true,
      "processing": true,
      "ajax": "",
      "columnDefs": [
        { "width": "40%", "targets": 9 }
      ],
      "language": {
        "url": "${ctx}/static/js/datatables-zh_CN.json"
      },
      "mark": true
//      "order": [[3, 'desc']]
    });

//        查询执行sql
    $('#findSqlSub').on('click', function () {
      var tenantId = $('#tenantId').val();
      if (tenantId !== null && tenantId !== "") {
        tableBtn.ajax.url("${ctx}/metadata/pg/get-sql?id=" + tenantId).load();
        $('#linkBody').addClass('hide');
        $('#lockBody').addClass('hide');
        $('#sqlBody').removeClass('hide');
      }
      $('#h1').html("查询正在执行的sql")
    });

//        查询锁信息
    $('#findLockSub').on('click', function () {
      var tenantId = $('#tenantId').val();
      if (tenantId !== null && tenantId !== "") {
        tableAct.ajax.url("${ctx}/metadata/pg/get-lock?id=" + tenantId).load();
        $('#sqlBody').addClass('hide');
        $('#linkBody').addClass('hide');
        $('#lockBody').removeClass('hide');
      }
      $('#h1').html("查询锁信息")
    });

//        查询当前连接
    $('#findLinkSub').on('click', function () {
      var tenantId = $('#tenantId').val();
      if (tenantId !== null && tenantId !== "") {
        tableFun.ajax.url("${ctx}/metadata/pg/get-link?id=" + tenantId).load();
        $('#sqlBody').addClass('hide');
        $('#lockBody').addClass('hide');
        $('#linkBody').removeClass('hide');
      }
      $('#h1').html("查询当前连接")
    });


    /**
     * 检测input变化并改变按钮状态
     */
    $('#tenantId').bind("input propertychange", function () {
      var tenantIdValue = $('#tenantId').val();
      if (tenantIdValue === null || tenantIdValue === "") {
        $('#findSqlSub,#findLockSub,#findLinkSub').attr("disabled", "disabled");
      } else {
        $('#findSqlSub,#findLockSub,#findLinkSub').removeAttr("disabled");
      }
    });

  });

  //查看锁详情
  function theLockInfo(data) {

  }
  //查看连接详情
  function theLinkInfo(data) {

  }
  //    ajax
  function getInfo(id, tenantId, infoUrl) {
    $.getJSON(infoUrl, {
      id: id,
      tenantId: tenantId
    }, function (theInfo) {
      $('#theInfo').JSONView(theInfo.infoMap, {
        collapsed: false,
        nl2br: true,
        recursive_collapser: true
      });
      $('#theInfoModal').modal('show');
    });
  }
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
