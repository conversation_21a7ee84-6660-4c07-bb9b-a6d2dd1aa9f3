<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
  <h1>查询企业员工数</h1>
  <ol class="breadcrumb">
    <li><a href=".."><i class="fa fa-dashboard"></i>任务主页</a></li>
    <li><a href=""><i class="fa fa-dashboard"></i></a></li>
  </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
<#--        <div class="box-header with-border">-->
<#--          <h3 class="box-title"></h3>-->
<#--        </div>-->
        <pre align="left" class="alert-warning hide" id="waringTxt"></pre>
        <form class="form-horizontal" action="${ctx}/license/query-count" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="box-body">
            <div class="form-group">
              <label for="tenantId" class="col-sm-2 control-label">企业EI/EA</label>
              <div class="col-sm-6">
                <input type="text" style="border-radius:5px;" class="form-control" id="tenantId" name="tenantId" value="${tenantId!}" placeholder="必填，多个企业以' , '分割" required>
              </div>
            </div>
          </div>
          <div class="box-footer clearfix">
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-2">
                <button id="searchBtn" type="button" class="btn btn-primary">查询</button>
                <button type="button" class="btn btn-default" onclick="history.back()">返回</button>
              </div>
            </div>
            <div class="clearfix"></div>
          </div>
        </form>
      </div>
      <div class="box box-info">
        <div class="box-body">
          <table id="datatable" class="table table-hover table-bordered" cellspacing="0" width="100%">
            <thead>
            <tr>
              <th>EI</th>
              <th>EA</th>
              <th>produceCode</th>
              <th>maxHeadCount</th>
              <th>quota</th>
              <th>usedQuota</th>
              <th>productType</th>
              <th>productName</th>
            </tr>
            </thead>
          </table>
        </div>
      </div>
    </div>
  </div>
</section>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<script>

  $(document).ready(function () {
    var table = $("#datatable").DataTable({
      "processing": true,
      "ajax": "",
      dom: 'Blfrtip',
      buttons: [
        'copy', 'excel', 'pdf'
      ],
      "columns": [
        { title: 'EI' },
        { title: 'EA' },
        { title: 'produceCode' },
        { title: 'maxHeadCount' },
        { title: 'quota' },
        { title: 'usedQuota' },
        { title: 'productType' },
        { title: 'productName' },
      ],
      "language": {
        "url": "${ctx}/static/js/datatables-zh_CN.json"
      },
      "mark": true,
      "paging": false
    });


    $('#searchBtn').on('click', function () {
      var tenantId = $('#tenantId').val();
      if (!tenantId){
        $('#waringTxt').text("请填写企业EI/EA").removeClass("hide")
        return;
      }
      $.ajax({
        url: '${ctx}/license/query-count',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({tenantId: tenantId}),
        dataType: 'json',
        success: function (data) {
          var table = $('#datatable').DataTable();
          table.clear().rows.add(data.data).draw();
        },
        error: function () {
          console.log('请求失败！');
        }
      });
    });

  })

</script>
</#assign>
<#include "../layout/layout-main.ftl" />
