package com.fxiaoke.paas.console.util.workflow

import com.effektif.mongo.TaskFields
import com.facishare.paas.workflow.kernel.entity.TaskEntity
import com.facishare.paas.workflow.kernel.entity.WorkflowEntity
import com.facishare.paas.workflow.kernel.entity.WorkflowInstanceEntity
import com.fxiaoke.paas.console.bean.workflow.BatchQueryTaskArg
import com.fxiaoke.paas.console.bean.workflow.BatchQueryWorkflowInstanceArg
import com.fxiaoke.paas.console.bean.workflow.OrderArg
import com.fxiaoke.paas.console.bean.workflow.QueryWorkflowArg
import com.fxiaoke.paas.console.exception.workflow.WorkflowAdminException
import com.github.mybatis.datatables.DataResponse
import com.google.common.net.UrlEscapers
import com.mongodb.BasicDBList
import com.mongodb.BasicDBObject
import com.mongodb.MongoClient
import com.mongodb.MongoClientOptions
import com.mongodb.MongoClientURI
import com.mongodb.ReadPreference
import groovy.util.logging.Slf4j
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.bson.types.ObjectId
import org.mongodb.morphia.query.Query

import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * Created by yangxw on 2018/3/5.
 */
@Slf4j
class DaoUtils {

  private static final List<String> workflowEntityFieldList = new ArrayList<>()
  private static final List<String> workflowInstanceEntityFieldList = new ArrayList<>()
  private static final List<String> taskEntityFieldList = new ArrayList<>()
  private static final List<String> workflowEntityAssembledFieldList = new ArrayList<>()
  private static final List<String> workflowInstanceEntityAssembledFieldList = new ArrayList<>()
  private static final List<String> taskEntityAssembledFieldList = new ArrayList<>()
  private static List<String>[][] listArray = new List[2][3]

  static {
    workflowEntityFieldList.add(WorkflowFields.SOURCE_WORKFLOW_ID)
    workflowEntityFieldList.add(WorkflowFields.TENANT_ID)
    workflowEntityFieldList.add(WorkflowFields.APP_ID)
    workflowEntityFieldList.add(WorkflowFields.ENTITY_ID)
    workflowEntityFieldList.add(WorkflowFields.CREATE_TIME)
    workflowEntityFieldList.add(WorkflowFields.NAME)
    workflowEntityFieldList.add(WorkflowFields.TYPE)
    workflowEntityFieldList.add(WorkflowFields.ENABLE)
    workflowEntityFieldList.add(WorkflowFields.DELETED)


    workflowInstanceEntityFieldList.add(WorkflowFields.TENANT_ID)
    workflowInstanceEntityFieldList.add(WorkflowFields.ENTITY_ID)
    workflowInstanceEntityFieldList.add(WorkflowFields.APP_ID)
    workflowInstanceEntityFieldList.add(WorkflowFields.WORKFLOW_ID)
    workflowInstanceEntityFieldList.add(WorkflowFields.OBJECT_ID)
    workflowInstanceEntityFieldList.add(WorkflowFields.APPLICANT_ID)
    workflowInstanceEntityFieldList.add(WorkflowFields.STATE)
    workflowInstanceEntityFieldList.add("start")
    workflowInstanceEntityFieldList.add("end")
    workflowInstanceEntityFieldList.add("duration")


    taskEntityFieldList.add(WorkflowFields.TENANT_ID)
    taskEntityFieldList.add(WorkflowFields.SOURCE_WORKFLOW_ID)
    taskEntityFieldList.add(WorkflowFields.WORKFLOW_ID)
    taskEntityFieldList.add(WorkflowFields.WORKFLOW_INSTANCE_ID)
    taskEntityFieldList.add(WorkflowFields.APP_ID)
    taskEntityFieldList.add(WorkflowFields.ENTITY_ID)
    taskEntityFieldList.add(WorkflowFields.OBJECT_ID)
    taskEntityFieldList.add(WorkflowFields.STATE)
    taskEntityFieldList.add(WorkflowFields.CREATE_TIME)
    taskEntityFieldList.add(WorkflowFields.MODIFY_TIME)
    taskEntityFieldList.add(WorkflowFields.APPLICANT_ID)
    taskEntityFieldList.add(TaskFields.TASK_TYPE)
    taskEntityFieldList.add(TaskFields.ACTION_TYPE)


    taskEntityAssembledFieldList.add("duration")


    listArray[0][0] = workflowEntityFieldList
    listArray[0][1] = workflowInstanceEntityFieldList
    listArray[0][2] = taskEntityFieldList
    listArray[1][0] = workflowEntityAssembledFieldList
    listArray[1][1] = workflowInstanceEntityAssembledFieldList
    listArray[1][2] = taskEntityAssembledFieldList

  }

  def static batchQuery(Query<WorkflowEntity> query, QueryWorkflowArg queryWorkflowArg) {

    verifyQuery(query)
    if (null != queryWorkflowArg) {
      if (StringUtils.isNotBlank(queryWorkflowArg.get_id())) {
        query.criteria(WorkflowFields.ID).equal(new ObjectId(queryWorkflowArg.get_id()))
      }

      if (StringUtils.isNotBlank(queryWorkflowArg.getSourceWorkflowId())) {
        query.criteria(WorkflowFields.SOURCE_WORKFLOW_ID).equal(queryWorkflowArg.getSourceWorkflowId())
      }
      if (StringUtils.isNotBlank(queryWorkflowArg.getTenantId())) {
        query.criteria(WorkflowFields.TENANT_ID).equal(queryWorkflowArg.getTenantId())
      }
      if (StringUtils.isNotBlank(queryWorkflowArg.getEntityId())) {
        query.criteria(WorkflowFields.ENTITY_ID).equal(queryWorkflowArg.getEntityId())
      }
      if (StringUtils.isNotBlank(queryWorkflowArg.getAppId())) {
        query.criteria(WorkflowFields.APP_ID).equal(queryWorkflowArg.getAppId())
      }
      if (StringUtils.isNotBlank(queryWorkflowArg.getCreator())) {
        query.criteria(WorkflowFields.CREATOR).equal(queryWorkflowArg.getCreator())
      }
      if (StringUtils.isNotBlank(queryWorkflowArg.getName())) {
        query.or(
                query.criteria(WorkflowFields.NAME).containsIgnoreCase(queryWorkflowArg.getName()),
                query.criteria(WorkflowFields.SOURCE_WORKFLOW_ID).equal(queryWorkflowArg.getName())
        )
      }
      if (StringUtils.isNotBlank(queryWorkflowArg.getDescription())) {
        query.criteria(WorkflowFields.DESC).containsIgnoreCase(queryWorkflowArg.getDescription())
      }
      if (StringUtils.isNotBlank(queryWorkflowArg.getFlowType())) {
        query.criteria(WorkflowFields.TYPE).equal(queryWorkflowArg.getFlowType())
      }
      if (null != queryWorkflowArg.getEnable()) {
        query.criteria(WorkflowFields.ENABLE).equal(queryWorkflowArg.getEnable())
      }
      query.or(
              query.criteria(WorkflowFields.DELETED).equal(false),
              query.criteria(WorkflowFields.DELETED).doesNotExist()
      )
    }

  }

  def static batchQuery(Query query, List<String> userIdList, String accountField, List<OrderArg> orderArgList, int offset, int limit) {

    verifyQuery(query)
    DataResponse response = new DataResponse<>()
    /**
     * 查询WorkflowFields.ApplicantAccount在userIds中的WorkflowInstanceEntity
     */
    if (isEmptyUserIdList(query, response, userIdList, accountField)) {
      return response
    }
    /**
     * 首先设置TASK总数量
     */
    response.setRecordsFiltered((int) query.countAll())
    /**
     * 设置分页查询参数,并按创建时间降序排序
     */
    setOrder(query, orderArgList)
    query.offset(offset).limit(limit)
    response.setData(query.asList())
    return response

  }

//    def static  assembleMatch(QueryWorkflowArg queryWorkflowArg) {
//
//        DBObject dbObject = new BasicDBObject()
//        if (null != queryWorkflowArg) {
//            if (StringUtils.isNotBlank(queryWorkflowArg.getTenantId())) {
//                dbObject.put(WorkflowFields.TENANT_ID, queryWorkflowArg.getTenantId())
//            }
//            if (StringUtils.isNotBlank(queryWorkflowArg.getFlowType())) {
//                dbObject.put(WorkflowFields.TYPE, queryWorkflowArg.getFlowType())
//            }
//            if (null != queryWorkflowArg.getEnable()) {
//                dbObject.put(WorkflowFields.ENABLE, queryWorkflowArg.getEnable())
//            }
//            if (StringUtils.isNotBlank(queryWorkflowArg.getAppId())) {
//                dbObject.put(WorkflowFields.APP_ID, queryWorkflowArg.getAppId())
//            }
//            if (StringUtils.isNotBlank(queryWorkflowArg.getEntityId())) {
//                dbObject.put(WorkflowFields.ENTITY_ID, queryWorkflowArg.getEntityId())
//            }
//            if (StringUtils.isNotBlank(queryWorkflowArg.getName())) {
//                if (ObjectId.isValid(queryWorkflowArg.getName())) {
//                    dbObject.put(WorkflowFields.SOURCE_WORKFLOW_ID, queryWorkflowArg.getName())
//                } else {
//                    dbObject.put(WorkflowFields.NAME, new BasicDBObject("\$regex", ".*" + queryWorkflowArg.getName() + ".*"))
//                }
//            }
//            dbObject.put("\$or", new BasicDBObject [] {
//                new BasicDBObject(WorkflowFields.DELETED, false) new BasicDBObject(WorkflowFields.DELETED, new BasicDBObject("\$exists", false))
//            })
//
//            List<String> creatorIdList = queryWorkflowArg.getCreatorIdList()
//            if (null != creatorIdList) {
//                BasicDBList dbList = new BasicDBList()
//                if (CollectionUtils.isNotEmpty(creatorIdList)) {
//                    dbList.addAll(creatorIdList)
//                } else {
//                    dbList.add("@##@%")
//                }
//                dbObject.put(WorkflowFields.CREATOR, new BasicDBObject("\$in", dbList))
//            }
//        }
//        return dbObject;
//
//    }

  /**
   * 通过WorkflowInstanceEntity一些基本字段来查询满足要求的WorkflowInstanceEntity
   * @param query
   * @param batchQueryWorkflowInstanceArg 工作流实例批量 查询参数，里面不为空的参数当作查询条件
   */
  def static batchQuery(Query<WorkflowInstanceEntity> query, BatchQueryWorkflowInstanceArg batchQueryWorkflowInstanceArg) {

    if (null == batchQueryWorkflowInstanceArg) {
      return;
    }
    //新加属性：instanceId
    if (StringUtils.isNotBlank(batchQueryWorkflowInstanceArg.getId())) {
      query.criteria("_id").equal(new ObjectId(batchQueryWorkflowInstanceArg.getId()));
    }

    if (StringUtils.isNotBlank(batchQueryWorkflowInstanceArg.getTenantId())) {
      query.criteria(WorkflowFields.TENANT_ID).equal(batchQueryWorkflowInstanceArg.getTenantId());
    }
    if (StringUtils.isNotBlank(batchQueryWorkflowInstanceArg.getAppId())) {
      query.criteria(WorkflowFields.APP_ID).equal(batchQueryWorkflowInstanceArg.getAppId());
    }
    if (StringUtils.isNotBlank(batchQueryWorkflowInstanceArg.getWorkflowId())) {
      query.criteria(WorkflowFields.WORKFLOW_ID).equal(IdUtil.generateId(batchQueryWorkflowInstanceArg.getWorkflowId()));
    }
    if (StringUtils.isNotBlank(batchQueryWorkflowInstanceArg.getObjectId())) {
      query.criteria(WorkflowFields.OBJECT_ID).equal(batchQueryWorkflowInstanceArg.getObjectId());
    }
    if (StringUtils.isNotBlank(batchQueryWorkflowInstanceArg.getEntityId())) {
      query.criteria(WorkflowFields.ENTITY_ID).equal(batchQueryWorkflowInstanceArg.getEntityId());
    }
    if (StringUtils.isNotBlank(batchQueryWorkflowInstanceArg.getApplicantId())) {
      query.criteria(WorkflowFields.APPLICANT_ID).equal(batchQueryWorkflowInstanceArg.getApplicantId());
    }
    if (StringUtils.isNotBlank(batchQueryWorkflowInstanceArg.getState())) {
      query.criteria(WorkflowFields.STATE).equal(batchQueryWorkflowInstanceArg.getState());
    }
    if (null != batchQueryWorkflowInstanceArg.getStartTime()) {
      query.criteria("start").greaterThanOrEq(batchQueryWorkflowInstanceArg.getStartTime());
    }
    if (null != batchQueryWorkflowInstanceArg.getEndTime()) {
      query.criteria("end").lessThan(batchQueryWorkflowInstanceArg.getEndTime());
    }
  }

  /**
   * 通过TaskEntity一些基本字段来查询满足要求的TaskEntity
   * @param query
   * @param batchQueryTaskArg 工作流实例实体，里面不为空的参数当作查询条件
   */
  public static void batchQuery(Query<TaskEntity> query, BatchQueryTaskArg batchQueryTaskArg) {

    if (null != batchQueryTaskArg) {
      //taskId
      if (StringUtils.isNotBlank(batchQueryTaskArg.getTaskId())) {
        query.criteria(WorkflowFields.ID).equal(new ObjectId(batchQueryTaskArg.getTaskId()));
      }

      if (StringUtils.isNotBlank(batchQueryTaskArg.getTenantId())) {
        query.criteria(WorkflowFields.TENANT_ID).equal(batchQueryTaskArg.getTenantId());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getAppId())) {
        query.criteria(WorkflowFields.APP_ID).equal(batchQueryTaskArg.getAppId());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getSourceWorkflowId())) {
        query.criteria(WorkflowFields.SOURCE_WORKFLOW_ID).equal(batchQueryTaskArg.getSourceWorkflowId());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getWorkflowId())) {
        query.criteria(WorkflowFields.WORKFLOW_ID).equal(batchQueryTaskArg.getWorkflowId());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getWorkflowInstanceId())) {
        query.criteria(WorkflowFields.WORKFLOW_INSTANCE_ID).equal(batchQueryTaskArg.getWorkflowInstanceId());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getActionType())) {
        query.criteria(TaskFields.ACTION_TYPE).equal(batchQueryTaskArg.getActionType());
      }
      if (null != batchQueryTaskArg.getRemind()) {
        query.criteria(TaskFields.REMIND).equal(batchQueryTaskArg.getRemind());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getObjectId())) {
        query.criteria(WorkflowFields.OBJECT_ID).equal(batchQueryTaskArg.getObjectId());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getEntityId())) {
        query.criteria(WorkflowFields.OBJECT_TYPE).equal(batchQueryTaskArg.getEntityId());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getTaskType())) {
        query.criteria(TaskFields.TASK_TYPE).equal(batchQueryTaskArg.getTaskType());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getApplicantId())) {
        query.criteria(WorkflowFields.APPLICANT_ID).equal(batchQueryTaskArg.getApplicantId());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getState())) {
        query.criteria(TaskFields.STATE).equal(batchQueryTaskArg.getState());
      }
//            if (StringUtils.isNotBlank(taskEntity.getErrMsg())) {
//                query.criteria(TaskEntity.FIELD_ERR_MSG).containsIgnoreCase(taskEntity.getErrMsg());
//            } else {
//                query.criteria(TaskEntity.FIELD_ERR_MSG).doesNotExist();
//            }
    }
  }


  def static getClient(boolean encryptPwd, String serverAddress) {
    String mongoServer = serverAddress;
    if (encryptPwd) {
      mongoServer = decodeEncryptPwd(mongoServer);
    }
    MongoClientOptions.Builder builder = new MongoClientOptions.Builder();
    builder.socketKeepAlive(true)
            .readPreference(ReadPreference.valueOf("primary"))
            .maxWaitTime(120000)
            .connectionsPerHost(100)
            .connectTimeout(5000)
            .socketTimeout(60000);
    MongoClientURI uri = new MongoClientURI(mongoServer, builder);
    return new MongoClient(uri);
  }


  def static assembleMatch(BatchQueryTaskArg batchQueryTaskArg) {

    BasicDBObject dbObject = new BasicDBObject();
    if (null != batchQueryTaskArg) {
      if (StringUtils.isNotBlank(batchQueryTaskArg.getTenantId())) {
        dbObject.put(WorkflowFields.TENANT_ID, batchQueryTaskArg.getTenantId());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getAppId())) {
        dbObject.put(WorkflowFields.APP_ID, batchQueryTaskArg.getAppId());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getSourceWorkflowId())) {
        dbObject.put(WorkflowFields.SOURCE_WORKFLOW_ID, batchQueryTaskArg.getSourceWorkflowId());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getWorkflowId())) {
        dbObject.put(WorkflowFields.WORKFLOW_ID, batchQueryTaskArg.getWorkflowId());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getWorkflowInstanceId())) {
        dbObject.put(WorkflowFields.WORKFLOW_INSTANCE_ID, batchQueryTaskArg.getWorkflowInstanceId());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getActionType())) {
        dbObject.put(TaskFields.ACTION_TYPE, batchQueryTaskArg.getActionType());
      }
      if (null != batchQueryTaskArg.getRemind()) {
        dbObject.put(TaskFields.REMIND, batchQueryTaskArg.getRemind());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getObjectId())) {
        dbObject.put(WorkflowFields.OBJECT_ID, batchQueryTaskArg.getObjectId());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getEntityId())) {
        dbObject.put(WorkflowFields.OBJECT_TYPE, batchQueryTaskArg.getEntityId());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getTaskType())) {
        dbObject.put(TaskFields.TASK_TYPE, batchQueryTaskArg.getTaskType());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getApplicantId())) {
        dbObject.put(WorkflowFields.APPLICANT_ID, batchQueryTaskArg.getApplicantId());
      }
      if (StringUtils.isNotBlank(batchQueryTaskArg.getState())) {
        dbObject.put("state", batchQueryTaskArg.getState());
      }
      List<String> applicantIdList = batchQueryTaskArg.getApplicantIdList();
      if (null != applicantIdList) {
        BasicDBList dbList = new BasicDBList();
        if (CollectionUtils.isNotEmpty(applicantIdList)) {
          dbList.addAll(applicantIdList);
        } else {
          dbList.add("@##@%");
        }
        dbObject.put(WorkflowFields.APPLICANT_ID, new BasicDBObject("\$in", dbList));
      }
    }
    return dbObject;

  }

  def static assembleSort(Query query, List<OrderArg> orderArgList) {

    BasicDBObject sort = new BasicDBObject();
    verifyQuery(query);
    if (CollectionUtils.isNotEmpty(orderArgList)) {
      List<String> entityFieldList = getEntityFieldList(query.getEntityClass(), false);
      List<String> entityAssembledFieldList = getEntityFieldList(query.getEntityClass(), true);
      for (OrderArg orderArg : orderArgList) {
        if (entityFieldList.contains(orderArg.getField()) || entityAssembledFieldList.contains(orderArg.getField())) {
          int order = "-".equals(orderArg.getOrder()) ? -1 : 1;
          sort.put(orderArg.getField(), order);
        }
      }
    }
    return sort;

  }

  def static isEmptyUserIdList(Query query, DataResponse response, List<String> userIdList, String field) {

    verifyQuery(query)
    if (null == response || StringUtils.isBlank(field)) {
      return true
    }
    if (null != userIdList) {
      if (CollectionUtils.isNotEmpty(userIdList)) {
        query.criteria(field).in(userIdList)
        return false
      } else {
        response.setRecordsFiltered(0)
        response.setData(new ArrayList<>())
        return true
      }
    }
    return false

  }

  def static verifyQuery(Query query) {
    if (null == query) {
      log.info("Parameter query is null")
      throw new WorkflowAdminException(3, "查询变量为空")
    }
  }

  def static setOrder(Query query, List<OrderArg> orderArgList) {
    verifyQuery(query)
    StringBuilder sort = new StringBuilder()
    if (CollectionUtils.isNotEmpty(orderArgList)) {
      List<String> entityFieldList = getEntityFieldList(query.getEntityClass(), false)
      for (OrderArg orderArg : orderArgList) {
        if (entityFieldList.contains(orderArg.field)) {
          sort.append(orderArg.order + orderArg.field + ",")
        }
      }
    }
    if (StringUtils.isNotBlank(sort)) {
      query.order(sort.toString())
    }
  }

  def static List<String> getEntityFieldList(Class type, boolean assembled) {
    int index = 0;
    if (assembled) {
      index = 1;
    }
    if (null != type) {
      if (type == WorkflowEntity.class) {
        return listArray[index][0]
      } else if (type == WorkflowInstanceEntity.class) {
        return listArray[index][1]
      } else if (type == TaskEntity.class) {
        return listArray[index][2]
      }
    }
    return new ArrayList<>()

  }

  def static decodeEncryptPwd(String mongoServer) {
    Pattern pattern = Pattern.compile("mongodb://((.+):(.*)@)");
    Matcher matcher = pattern.matcher(mongoServer);
    String url = null;
    if (matcher.find()) {
      try {
        String pwd = UrlEscapers.urlFormParameterEscaper().escape(com.fxiaoke.common.PasswordUtil.decode(matcher.group(3)));
        url = mongoServer.substring(0, matcher.end(2) + 1) + pwd + mongoServer.substring(
                matcher.end(1) - 1);
      } catch (Exception e) {
        log.error("cannot decode " + matcher.group(3), e);
      }
    }
    return url;
  }

}
