package com.fxiaoke.paas.console.mapper.datarights

import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * @date 2019/4/1 下午6:09
 *
 */
interface TemporaryRightsMapper extends ITenant<TemporaryRightsMapper> {

  /**
   * 获取数据临时权限
   * @param tenantId
   * @param objectId
   * @return
   */
  @Select("select * from dt_temporary_rights where dt_temporary_rights.tenant_id = #{tenantId} and dt_temporary_rights.data_id=#{objectId}")
  List<Map<String, Object>> queryTemporaryRightsByObjectId(@Param("tenantId") String tenantId,
                                                     @Param("objectId") String objectId)

}
