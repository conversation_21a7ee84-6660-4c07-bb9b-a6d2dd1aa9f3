package com.fxiaoke.paas.console.mapper.datarights

import com.fxiaoke.paas.console.entity.datarights.DepartmentMsg
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2019/4/1 下午2:51
 *
 */
@ContextConfiguration(locations = ["classpath:mapperContext.xml"])
@Slf4j
class DataRightsDepartmentMapperTest  extends Specification {
  @Autowired
  private DataRightsDepartmentMapper dataRightsDepartmentMapper


  def "queryDepartmentMsgByDeptId"(){
    given:
    DepartmentMsg departmentMsg=dataRightsDepartmentMapper.setTenantId("71586").queryDepartmentMsgByDeptId("71586","999999")
    println(departmentMsg.tenantId+" "+departmentMsg.name+" "+departmentMsg.deptId+" "+departmentMsg.leader+" ")
    departmentMsg.subordinateDepartment.forEach(
            {s->println(s.name+" "+s.deptId)})

    departmentMsg.users.forEach({s->println(s.name+" "+s.tenantId+" "+s.userId+" "+s.position)})
  }


}