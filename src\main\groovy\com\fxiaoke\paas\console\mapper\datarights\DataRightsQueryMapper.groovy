package com.fxiaoke.paas.console.mapper.datarights

import com.fxiaoke.paas.console.entity.datarights.DtAuth
import com.fxiaoke.paas.console.entity.datarights.DtTeam
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * @date 2019/7/8 下午5:09
 *
 */
@Mapper
interface DataRightsQueryMapper extends ITenant<DataRightsQueryMapper> {
  @Select("select data_auth_code from \${storeTableName} where tenant_id=#{tenantId} and id=#{objectId}")
  String getDataAuthCode(@Param("tenantId") String tenantId, @Param("objectId") String objectId, @Param("storeTableName") String storeTableName)

  @Select("select * from dt_auth  where tenant_id=#{tenantId} and data_auth_code=#{dataAuthCode}")
  DtAuth getDataAuthCodeByDataAuthCodeAndTenantId(@Param("dataAuthCode") String dataAuthCode, @Param("tenantId") String tenantId)

  @Select("select id,tenant_id as tenantId,object_describe_api_name as objectDescribeApiName,object_id as objectId,package as pkg,member_id as memberId,member_type as memberType,role_type as roleType,permission,is_deleted as isDeleted,object_describe_id as objectDescribeId from dt_team where tenant_id=#{tenantId} and object_id=#{objectId} and object_describe_api_name=#{apiName} and is_deleted=0")
  List<DtTeam> getDtTeamMsg(@Param("tenantId") String tenantId, @Param("objectId") String objectId, @Param("apiName") String apiName)

  @Select("select data_auth_code as dataAuthCode, owner from \${storeTableName} where tenant_id=#{tenantId} and id=#{objectId}")
  Map<String,String> getDataAuthCodeAndOwner(@Param("tenantId") String tenantId, @Param("objectId") String objectId, @Param("storeTableName") String storeTableName)
}