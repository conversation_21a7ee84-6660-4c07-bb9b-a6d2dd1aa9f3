package com.fxiaoke.paas.console.mapper.metadata

import com.facishare.paas.metadata.dao.pg.entity.metadata.Field
import com.fxiaoke.paas.console.bean.metadata.RestoreField
import com.github.mybatis.mapper.IBatchMapper
import com.github.mybatis.mapper.ICrudMapper
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

/**
 * <AUTHOR>
 * @date 2018/3/8
 */
interface FieldMapper extends ICrudMapper, IBatchMapper, ITenant<FieldMapper> {
  /**
   * 根据describeId获取field
   * @param describeId
   * @return
   */
  @Select("SELECT * FROM mt_field WHERE describe_id = #{describeId} ORDER BY field_num")
  List<Map<String, Object>> findFieldByDescribeId(@Param("describeId") String describeId)

  @Select("SELECT mf.* FROM mt_field mf join mt_describe md on mf.describe_id = md.describe_id and mf.tenant_id=md.tenant_id where md.tenant_id=#{tenantId} and md.describe_api_name=#{describeApiName} and is_current=true")
  List<Map<String, Object>> findFieldByTenantIdAndDescribe(@Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName)

  @Select("\${sql}")
  List<Map<String, Object>> findFieldByDescribeId2(@Param("sql") String sql)

  @Update("\${sql}")
  void updateBySql(@Param("sql") String sql)

  @Select("SELECT api_name,field_num,is_unique FROM mt_field WHERE tenant_id = #{tenantId} and describe_id = #{describeId}")
  List<RestoreField> findRestoreFieldByDescribeId(@Param("tenantId") String tenantId,@Param("describeId") String describeId)

  @Select("SELECT api_name FROM mt_field WHERE tenant_id = #{tenantId} and describe_api_name = #{describeApiName} and status != 'deleted'")
  List<String> findApiNameByDescribeApiName(@Param("tenantId") String tenantId,@Param("describeApiName") String describeApiName)

  @Select("SELECT * FROM mt_field WHERE tenant_id= #{tenantId} AND describe_api_name=#{describeApiName} AND api_name=#{apiName} and status!='deleted'")
  Field findFieldByApiName(@Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName, @Param("apiName") String apiName)

  @Select("SELECT * FROM mt_field WHERE tenant_id= #{tenantId} AND field_id = #{fieldId}")
  Field findFieldById(@Param("tenantId") String tenantId, @Param("fieldId") String fieldId)

  @Select("SELECT MAX(CAST(SUBSTRING(index_name, #{index}) AS INT)) from mt_field where tenant_id = #{tenantId} and index_name similar to #{indexType} and describe_api_name = #{describeApiName}")
  Integer findMaxIndexNameSuf(@Param("index") Integer index, @Param("tenantId") String tenantId, @Param("indexType") String indexType, @Param("describeApiName") String describeApiName)

  @Update("UPDATE mt_field SET index_name = #{indexName} WHERE tenant_id=#{tenantId} and field_id=#{fieldId}")
  void updateIndexNameById(@Param("indexName") indexName, @Param("tenantId") String tenantId, @Param("fieldId") String fieldId);

  @Update("UPDATE mt_field SET max_length = #{maxLength} WHERE tenant_id = #{tenantId} and field_id = #{fieldId}")
  void updateMaxLengthById(@Param("maxLength") Integer maxLength, @Param("tenantId") String tenantId, @Param("fieldId") String fieldId)

  @Update("UPDATE mt_field SET \${columnName} = \${columnName} + #{addNumber}, max_length = max_length+#{addNumber} WHERE tenant_id = #{tenantId} and field_id = #{fieldId}")
  void addNumberLengthById(@Param("columnName") String columnName,@Param("addNumber") Integer addNumber, @Param("tenantId") String tenantId, @Param("fieldId") String fieldId)

  @Update("UPDATE mt_field SET is_unique = #{isUnique} WHERE tenant_id = #{tenantId} and field_id = #{fieldId}")
  void updateUniqueById(@Param("isUnique") boolean isUnique, @Param("tenantId") String tenantId, @Param("fieldId") String fieldId)

  @Update("update mt_field set type='auto_number', start_number=1,serial_number=3 where tenant_id=#{tenantId} and field_id = #{fieldId}")
  void updateFieldType2AutoIncrement(@Param("tenantId") String tenantId, @Param("fieldId") String fieldId)

  @Select("select field_num from mt_field where tenant_id=#{tenantId} and describe_id=#{describeId} and api_name not in (\${blackFieldApiNames}) and status='deleted' and field_num is not null")
  List<String> queryDeletedFieldNum(@Param("tenantId") String tenantId, @Param("describeId") String describeId, @Param("blackFieldApiNames") String blackFieldApiNames);

  void clearFieldData(@Param("tenantId") String tenantId, @Param("columns") List<String> columns, @Param("ids") List<String> ids)

  List<String> queryDataIds(@Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName, @Param("table") String table, @Param("id") String id, @Param("limit") Integer limit);

  @Delete("delete from mt_field where tenant_id=#{tenantId} and describe_id=#{describeId} and api_name not in (\${blackFieldApiNames}) and status='deleted'")
  int recycleField(@Param("tenantId") String tenantId, @Param("describeId") String describeId, @Param("blackFieldApiNames") String blackFieldApiNames);
}
