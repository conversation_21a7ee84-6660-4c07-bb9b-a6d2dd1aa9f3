<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.paas.console.mapper.metadata.MtDataMapper">

  <select id="getDataByName" resultType="java.util.Map">
    SELECT * FROM
    ${storeTableName}
    WHERE tenant_id = #{tenantId}
    <if test="name != null">
      AND name = #{name}
    </if>
    <if test="apiName != null">
      AND object_describe_api_name = #{apiName}
    </if>
  </select>

  <select id="findRestoreIdList" resultType="java.lang.String">
    select id from mt_data where tenant_id = #{tenantId} and object_describe_api_name = #{apiName} and is_deleted = -1
    <if test="startTime != null">
      <![CDATA[ and last_modified_time > #{startTime} ]]>
    </if>
    <if test="endTime != null">
      <![CDATA[ and last_modified_time <= #{endTime} ]]>
    </if>
  </select>

  <select id="getUniqueData" resultType="java.util.Map">
    SELECT ${idName}, ${fieldNum} as value
    from ${storeTableName}
    WHERE tenant_id = #{tenantId}
      AND object_describe_api_name = #{describeApiName}
      AND ${fieldNum} IS NOT NULL
      AND is_deleted >= 0
    <if test="id!= null">
      AND ${idName} >#{id}
    </if>
    order by ${idName} asc limit 100
  </select>

</mapper>