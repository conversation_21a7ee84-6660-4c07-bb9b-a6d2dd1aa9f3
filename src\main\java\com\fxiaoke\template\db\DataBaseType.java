package com.fxiaoke.template.db;

/**
 * refer:http://blog.csdn.net/ring0hx/article/details/6152528
 * <p/>
 * <AUTHOR>
 */
public enum DataBaseType {
  MySql("mysql", "com.mysql.jdbc.Driver"),
  SQLServer("sqlserver", "com.microsoft.sqlserver.jdbc.SQLServerDriver"),
  PostgreSQL("postgresql", "org.postgresql.Driver"),
  Mongo("Mongo", "");

  private String typeName;
  private String driverClassName;

  DataBaseType(String typeName, String driverClassName) {
    this.typeName = typeName;
    this.driverClassName = driverClassName;
  }

  public String getDriverClassName() {
    return this.driverClassName;
  }

  public String getTypeName() {
    return typeName;
  }
}
