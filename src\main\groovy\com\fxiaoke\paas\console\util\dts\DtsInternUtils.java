package com.fxiaoke.paas.console.util.dts;

import com.fxiaoke.common.MapUtils;
import com.fxiaoke.helper.CollectionHelper;
import com.google.common.base.CaseFormat;
import com.google.common.base.Converter;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 内部工具类
 *
 * <AUTHOR>
 * @date 2020/11/7 16:08
 */
@UtilityClass
public class DtsInternUtils {
  private final Converter<String, String> converter = CaseFormat.LOWER_CAMEL.converterTo(CaseFormat.LOWER_UNDERSCORE);
  private final Map<String, String> tables = Maps.newConcurrentMap();
  private final Map<String, String> columns = Maps.newConcurrentMap();
  private final Pattern customApiName = Pattern.compile("^(\\w*_*)?(\\w{5,6})__c$");
  private final Pattern UPPER_WORDS = Pattern.compile("[A-Z]{3,}");

  public String lower(String s) {
    return s.toLowerCase();
  }

  public String camelConvert(String s) {
    if (StringUtils.startsWithIgnoreCase(s, "object_") || StringUtils.startsWithIgnoreCase(s, "field_")) {
      return lower(s);
    }
    return converter.convert(formatAbbrevWord(s).trim());
  }

  public String formatAbbrevWord(String s) {
    return format(UPPER_WORDS, s, k -> {
      char[] chars = k.toCharArray();
      for (int i = 1; i < chars.length - 1; i++) {
        chars[i] = (char) (chars[i] + 32);
      }
      return new String(chars);
    });
  }

  public String format(Pattern pattern, String stack, Function<String, String> replacer) {
    Matcher matcher = pattern.matcher(stack);
    if (matcher.find()) {
      StringBuilder sbd = new StringBuilder(stack.length() / 2);
      int start = 0;
      do {
        if (matcher.start() > start) {
          sbd.append(stack, start, matcher.start());
        }
        sbd.append(replacer.apply(stack.substring(matcher.start(), matcher.end())));
        start = matcher.end();
      } while (matcher.find());
      if (start < stack.length()) {
        sbd.append(stack, start, stack.length());
      }
      return sbd.toString();
    }
    return stack;
  }

  public String toTableName(String s) {
    if(StringUtils.isBlank(s)){
      return s;
    }
    return tables.computeIfAbsent(s, k -> lower(camelConvert(k)));
  }

  public String toColumnName(String s) {
    return columns.computeIfAbsent(s, k -> {
      if ("_id".equals(k)) {
        return "id";
      } else if ("ei".equals(k)) {
        return "tenant_id";
      } else {
        Matcher matcher = customApiName.matcher(k);
        if (matcher.matches()) {
          return lower(camelConvert(matcher.group(1))) + lower(matcher.group(2)) + "__c";
        }
        return lower(camelConvert(k));
      }
    });
  }

}
