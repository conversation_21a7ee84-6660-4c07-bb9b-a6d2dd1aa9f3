package com.fxiaoke.paas.console.web.metadata

import com.fxiaoke.paas.console.service.metadata.ApiNameTransferService
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody

/**
 * <AUTHOR>
 * @date 2020/5/7 11:01
 *
 */
@Controller
@Slf4j
@RequestMapping("/metadata")
class ApiNameTransferController {

  @Autowired
  private ApiNameTransferService apiNameTransferService

  @RequestMapping(path = "/apiNameTransferPage")
  def page() {
    "metadata/apiNameTransfer"
  }

  @PostMapping(path = "getApiNames", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  def getApiNames(String tenantId, String input, int type) {
    if (StringUtils.isAnyBlank(tenantId, input) || (type != 1 && type != 2)) {
      return ["code": 500, "result": "NOT FOUND"]
    } else if (type == 1) {
      String apiName = apiNameTransferService.getApiNames(tenantId, input)
      if (StringUtils.isBlank(apiName)) {
        return ["code": 500, "result": "NOT FOUND"]
      } else {
        return ["code": 200, "result": apiName]
      }
    } else if (type == 2) {
      String storeTableName = apiNameTransferService.getStoreTableName(tenantId, input)
      if (StringUtils.isBlank(storeTableName)) {
        return ["code": 500, "result": "NOT FOUND"]
      } else {
        return ["code": 200, "result": storeTableName]
      }
    } else {
      return ["code": 500, "result": "NOT FOUND"]
    }

  }

}
