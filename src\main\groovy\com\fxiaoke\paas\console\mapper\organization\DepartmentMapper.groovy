package com.fxiaoke.paas.console.mapper.organization

import com.fxiaoke.paas.console.entity.organization.DepartmentEntity
import com.github.mybatis.mapper.ICrudMapper
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Param

/**
 * Created by wangxing on 2018/03/06
 */
interface DepartmentMapper extends ITenant<DepartmentMapper>, ICrudMapper<DepartmentEntity> {

  /**
   * 通过企业ID查询所有部门ID
   * @param tenantId 企业ID
   * @return 部门ID集合
   */
  List<DepartmentEntity> getDepartmentIdByCompanyId(@Param("tenantId") String tenantId)

  /**
   * 通过企业ID和部门ID查询部门信息
   * @param tenantId 企业ID
   * @param deptIds 部门ID集合
   * @return 部门信息集合
   */
  List<DepartmentEntity> getDepartment(@Param("tenantId") String tenantId, @Param("deptIds") List<String> deptIds)

  /**
   * 通过企业ID和部门ID查询子部门信息
   * @param tenantId
   * @param parentId
   * @param page
   * @return
   */
  List<DepartmentEntity> getSubordinateDepartment(@Param("tenantId") String tenantId, @Param("parentId") String parentId)

  /**
   * 通过企业ID，用户ID,部门用户关系类型查询其所在部门信息
   * @param tenantId
   * @param userId
   * @param type
   * @param page
   * @return
   */
  List<DepartmentEntity> getRelationalDepartment(@Param("tenantId") String tenantId, @Param("userId") String userId, @Param("type") Integer type)

  /**
   * 查询部门ID下是否还有子部门
   * @param tenantId
   * @param parentIds
   * @return
   */
  List<String> listParentId(@Param("tenantId") String tenantId, @Param("deptIds") List<String> deptIds)
}