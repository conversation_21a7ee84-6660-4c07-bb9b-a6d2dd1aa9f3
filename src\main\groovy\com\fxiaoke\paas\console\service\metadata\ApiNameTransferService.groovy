package com.fxiaoke.paas.console.service.metadata

import com.fxiaoke.paas.console.mapper.metadata.ApiNameTransferMapper
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/5/7 11:21
 */
@Service
class ApiNameTransferService {

  @Autowired
  private ApiNameTransferMapper apiNameTransferMapper


  String getApiNames(String tenantId, String storeTableName) {
    if (StringUtils.isNoneBlank(tenantId, storeTableName)) {
      return apiNameTransferMapper.setTenantId(tenantId).queryApiNameByTenantIdAndStoreTableName(tenantId, storeTableName)
    } else {
      return ""
    }
  }


  String getStoreTableName(String tenantId, String apiName) {
    if (StringUtils.isNoneBlank(tenantId, apiName)) {
      return apiNameTransferMapper.setTenantId(tenantId).queryStoreTableNameByTenantIdAndApiName(tenantId, apiName)
    } else {
      return ""
    }
  }
}
