<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
  <h1>Schema管理</h1>
  <ol class="breadcrumb">
    <li><a href="${ctx}"><i class="fa fa-dashboard"></i>主页</a></li>
    <li><a href=""><i class="fa fa-dashboard"></i>Schema管理</a></li>
  </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info row">
        <div class="box-header with-border">
        </div>
        <form class="form-horizontal" action="" method="post" id="infoFrom" role="form" data-toggle="validator">
          <div class="box-body col-xs-6">
            <div class="form-group">
              <label for="tenantId" class="col-sm-4 control-label">tenantId</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="tenantId" placeholder="企业ID(必填)" required>
              </div>
            </div>
            <div class="form-group">
              <label for="paasJdbcUrl" class="col-sm-4 control-label">paasJdbcUrl</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="paasJdbcUrl" placeholder="PAAS jdbcUrl(必填)"/>
              </div>
            </div>
            <label for="biJdbcUrl" class="col-sm-4 control-label">biJdbcUrl</label>
            <div class="col-sm-4">
              <input type="text" class="form-control" style="border-radius:5px;" id="biJdbcUrl" placeholder="BI jdbcUrl(必填)"/>
            </div>
            <div class="col-sm-offset-3">
              <button type="button" id="findData" class="btn btn-primary">创建</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-12">
      <div class="box box-info row">
        <div class="box-header with-border">
        </div>
        <h2>Schema迁移</h2>
        <form class="form-horizontal" action="" method="post" id="keyForm">
          <div class="box-body col-xs-6">
            <div class="form-group">
              <label for="token" class="col-sm-4 control-label">Token</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="token" placeholder="Token" required/>
              </div>
            </div>
            <div class="form-group">
              <label for="tenantIds" class="col-sm-4 control-label">TenantIds</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="tenantIds" placeholder="TenantIds(,分割)" required/>
              </div>
            </div>
            <div class="col-sm-offset-3">
              <button type="button" id="executeButton" class="btn btn-primary">执行</button>
              <button type="button" id="stopButton" class="btn btn-primary">停止</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>


  <div class="row">
    <div class="col-md-12">
      <div class="box box-info row">
        <div class="box-header with-border">
        </div>
        <h2>Schema增量同步(仅支持线下)</h2>
        <form class="form-horizontal" action="" method="post" id="keyForm">
          <div class="box-body col-xs-6">
            <div class="col-sm-offset-3">
              <button type="button" id="copierExecuteButton" class="btn btn-primary">开始</button>
              <button type="button" id="copierStopButton" class="btn btn-primary">停止</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<#--表单验证插件-->
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script>
  function isBlank(str) {
    return str === null || str === ""
  }

  $('#findData').on('click', function () {
    var tenantId = $('#tenantId').val();
    var paasJdbcUrl = $('#paasJdbcUrl').val();
    var biJdbcUrl = $('#biJdbcUrl').val();
    if (isBlank(tenantId)) {
      alert("TenantId is not allowed to use blank string!");
      return false;
    }
    $.getJSON("${CONTEXT_PATH}/dmlmanager/init", {
      tenantId: tenantId,
      paasJdbcUrl: paasJdbcUrl,
      biJdbcUrl: biJdbcUrl
    }, function (data) {
      alert(data.info)
    });
  });

  $('#executeButton').on('click', function () {
    var key = $('#token').val();
    var tenantIds = $('#tenantIds').val();
    if (isBlank(key) || isBlank(tenantIds)) {
      alert("Token and tenantIds are not allowed to use blank string!");
      return false;
    }
    $.getJSON("${CONTEXT_PATH}/dmlmanager/run", {
      key: key,
      tenantIds: tenantIds
    }, function (data) {
      alert(data.info)
    });
  });

  $('#stopButton').on('click', function () {
    var key = $('#token').val();
    if (isBlank(key)) {
      alert("Token is not allowed to use blank string!");
      return false;
    }
    $.getJSON("${CONTEXT_PATH}/dmlmanager/stop", {
      key: key
    }, function (data) {
      alert(data.info)
    });
  });

  $('#copierExecuteButton').on('click', function () {
    $.getJSON("${CONTEXT_PATH}/dmlmanager/copier/run", {
    }, function (data) {
      alert(data.info)
    });
  });

  $('#copierStopButton').on('click', function () {
    $.getJSON("${CONTEXT_PATH}/dmlmanager/copier/stop", {
    }, function (data) {
      alert(data.info)
    });
  });

</script>
</#assign>
<#include "../layout/layout-main.ftl" />
