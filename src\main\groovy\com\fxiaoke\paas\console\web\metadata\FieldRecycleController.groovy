package com.fxiaoke.paas.console.web.metadata

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.paas.console.annotation.SystemControllerLog;
import com.fxiaoke.paas.console.service.metadata.FieldRecycleService;
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @data 2024/2/29 11:49
 */
@Controller
@Slf4j
@RequestMapping("/metadata-operate/field-recycle")
public class FieldRecycleController {
  @Autowired
  private FieldRecycleService fieldRecycleService;

  @RequestMapping("")
  def page(){
    "/metadata/recycleField"
  }

  @RequestMapping("/recycle")
  @ResponseBody
  @SystemControllerLog(description = "企业对象回收删除字段")
  def recycle(@RequestParam String tenantId,@RequestParam String describeApiName, @RequestParam String fieldApiNames) {
    try {
      def result = fieldRecycleService.recycleField(tenantId, describeApiName, fieldApiNames);
      ["code": 200, "message": result]
    } catch (Exception e) {
      log.error("回收失败", e)
      ["code": 500, "message": e.getMessage()]
    }

  }

}
