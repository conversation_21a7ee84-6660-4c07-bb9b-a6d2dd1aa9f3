package com.fxiaoke.paas.console.util.metadata

import groovy.util.logging.Slf4j

import java.text.ParseException
import java.text.SimpleDateFormat

@Slf4j
class MetadataUtil {
  private static final String INDEX_NAME = "fs-paas-statistics-%s"
  private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd")

  static def getIndexName() {
    if (isWeekend(new Date())) {
      return String.format(INDEX_NAME, sdf.format(new Date()))
    }
    Calendar calendar = Calendar.getInstance()
    calendar.set(Calendar.DAY_OF_WEEK, 1)
    return String.format(INDEX_NAME, sdf.format(calendar.getTimeInMillis()))
  }

  /**
   * 判断是否是周日
   * @param date
   * @return
   * @throws ParseException
   */
  static boolean isWeekend(Date date) throws ParseException {
    Calendar cal = Calendar.getInstance()
    cal.setTime(date)
    if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
      return true
    } else {
      return false
    }
  }

  static void main(String[] args) {
    String index = getIndexName()
    log.info(index)
  }

}
