package com.fxiaoke.paas.console.exception.workflow

/**
 * Created by yangxw on 2018/3/5.
 */
class WorkflowAdminException extends RuntimeException {

     static final long serialVersionUID = -5603472149781247376L;
     static final String ERROR_LEFT = "ERROR_LEFT code: [ ";
     static final String ERROR_RIGHT = " ]";

    /**
     * 错误码
     */
     int code = -1;

    /**
     * 错误信息
     */
     String message;

    /**
     * 抛出异常的方法的参数
     */
     transient Object[] paras;//这个不能够被序列化

    /**
     * 是否需要上报异常给监控系统
     */
     boolean needReport;

    /**
     * 保存一个异常信息
     */
     Throwable cause = this;

     WorkflowAdminException(int code) {
        this.code = code;
        this.message = new StringBuilder(ERROR_LEFT).append(code).append(ERROR_RIGHT).toString();
    }

     WorkflowAdminException(int code, String message) {
        this.code = code;
        this.message = message;
    }

     WorkflowAdminException(int code, Object... paras) {
        this.code = code;
        this.paras = paras;
        this.message = new StringBuilder(ERROR_LEFT).append(code).append(ERROR_RIGHT).toString();
    }

     WorkflowAdminException(int code, Throwable cause, Object... paras) {
        fillInStackTrace();
        this.code = code;
        this.paras = paras;
        this.message = new StringBuilder(ERROR_LEFT).append(code).append(ERROR_RIGHT).toString();
        this.cause = cause;
    }

     WorkflowAdminException(int code, String message, Throwable cause) {
        this.code = code;
        this.message = message;
        this.cause = cause;
    }

     WorkflowAdminException(String message) {
        this.message = message;
    }

     WorkflowAdminException(Throwable cause) {
        fillInStackTrace();
        this.message = cause == null ? null : cause.getMessage();
        this.cause = cause;
    }

     WorkflowAdminException(String message, Throwable cause) {
        fillInStackTrace();
        this.message = message;
        this.cause = cause;
    }

     int getCode() {
        return this.code;
    }

    @Override
     String getMessage() {
        return this.message;
    }

     Object[] getParas() {
        return this.paras;
    }

    @Override
     Throwable getCause() {
        return cause == this ? null : cause;
    }

     WorkflowAdminException needReport() {
        this.needReport = true;
        return this;
    }

     boolean isNeedReport() {
        return this.needReport;
    }

     void setNeedReport(boolean needReport) {
        this.needReport = needReport;
    }
}
