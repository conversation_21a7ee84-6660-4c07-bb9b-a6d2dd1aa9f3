package com.fxiaoke.paas.console.web.metadata

import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.service.metadata.SqlQueryService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

/**
 * <AUTHOR>
 * Created on 2018/5/14.
 */
@Controller
@Slf4j
@RequestMapping("/metadata/pg")
class PgController {

  @Autowired
  SqlQueryService sqlQueryService

  /**
   * 跳转PG信息查询
   * @return
   */
  @RequestMapping(value = "/pg-info")
  def pgInfo() {
    "metadata/pg-info"
  }

  /**
   * 获取Pg信息（锁信息）
   */
  @RequestMapping(value = "/get-sql")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 查询PG正在执行的Sql")
  def getSql(@RequestParam String id) {
    List<Map<String, Object>> pgSqlList = sqlQueryService.getSql(id.trim())
    def json = pgSqlList.collect { it ->
      [it.get("procpid"), it.get("start").toString(), it.get("lap").toString(), it.get("current_query")]
    }

    ["data": json]
  }

  /**
   * 查看持有锁和等待锁
   * @param id
   * @return
   */
  @RequestMapping(value = "/get-lock")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 查看持有锁和等待锁")
  def getLock(@RequestParam String id) {
    List<Map<String, Object>> pgLocklist = sqlQueryService.getLock(id.trim())
    def json = pgLocklist.collect { it ->
      [it.get("pid"), it.get("relname"), it.get("application_name"), it.get("start_time").toString(),
       it.get("state"), it.get("lock_status"), it.get("runtime").toString(), it.get("query")]
    }

    ["data": json]
  }

  /**
   * 查询连接信息
   * @param id
   * @return
   */
  @RequestMapping(value = "/get-link")
  @ResponseBody
  def getLink(@RequestParam String id) {
    List<Map<String, Object>> pgLinkList = sqlQueryService.getLink(id.trim())
    def json = pgLinkList.collect { it ->
      [it.get("datid"), it.get("datname"), it.get("pid"), it.get("usesysid"), it.get("usename"), it.get("application_name"),
       it.get("client_addr").toString(), it.get("client_port"), it.get("backend_start").toString(), it.get("query")]
    }
    ["data": json]
  }

}
