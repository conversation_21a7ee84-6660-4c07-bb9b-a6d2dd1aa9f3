package com.fxiaoke.paas.console.web.metadata

import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.github.autoconf.ConfigFactory
import groovy.util.logging.Slf4j
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.RequestMapping

import javax.annotation.PostConstruct

/**
 * 搜索日志
 * <AUTHOR>
 * Created on 2018/3/20.
 */
@Controller
@Slf4j
@RequestMapping("/metadata/flog")
class FlogController {

  private String flogUrl


  @PostConstruct
  void init() {
    ConfigFactory.getConfig("paas-console-base", { config ->
      flogUrl = config.get("flog")
    })
  }

  /**
   * 日志搜索
   * @param model
   * @return
   */
  @RequestMapping("/")
  def flog(ModelMap model) {
    model.put("flogUrl", flogUrl)
    "redirect:" + flogUrl
//    "metadata/flog"
  }

}
