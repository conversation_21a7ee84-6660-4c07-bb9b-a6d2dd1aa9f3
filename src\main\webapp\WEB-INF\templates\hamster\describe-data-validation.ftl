<#assign headContent>
    <link href="//static.foneshare.cn/oss/AdminLTE/plugins/datatables/dataTables.bootstrap.css" rel="stylesheet"
          type="text/css"/>
    <link type="text/css" href="//static.foneshare.cn/oss/datatables-checkboxes/dataTables.checkboxes.css"
          rel="stylesheet"/>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
    <style>
        #datatable td:nth-child(6) {
            text-align: right;
        }

        #datatable td:nth-child(7) {
            text-align: right;
        }
    </style>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>新版数据验证</h1>
        <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i>主页</a></li>
        </ol>
    </section>
</#assign>

<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div id="warningInfo" class="alert alert-warning hide">
                    <span id="closeInfo" href="#" class="close">&times;</span>
                    <strong id="hitInfo"></strong>
                </div>
                <div class="box box-info" style="margin-bottom: 1px;">
                    <div class="box-body" style="padding: 20px;">
                        <form class="form-inline">
                            <div class="form-group">
                                <label for="tenantId" class="control-label">企业ID</label>
                                <input type="text" style="border-radius:5px;" class="form-control" id="tenantId" name="tenantId" placeholder="企业ID">
                            </div>
                            <div class="form-group">
                                <label for="tenantId" class="control-label">是否统计opLog</label>
                                <input type="text" style="border-radius:5px;" class="form-control" id="isQueryOpLog" name="isQueryOpLog" placeholder="true|false">
                            </div>
                            <button type="button" id="findSub" class="btn btn-primary">compare</button>
                        </form>
                    </div>
                    <div class="box-footer clearfix" style="padding: 2px;">
                        <div class="clearfix"></div>
                    </div>
                </div>
                <div class="box box-info">
                    <div class="box-body">
                        <table id="datatable" class="table table-hover table-bordered" cellspacing="0" width="100%">
                            <thead>
                            <tr>
                                <th>biz</th>
                                <th>table name</th>
                                <th>record count(old)</th>
                                <th>record count(new)</th>
                                <th>is support op log</th>
                                <th>is migrate</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="box-footer clearfix">
                    </div>
                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script type="application/javascript">

        $(document).ready(function () {
            var myTable = $("#datatable").DataTable({
                "processing": true,
                "searching": true,
                "ajax": "",
                "columnDefs": [
                    {"width": "20%", "targets": 0},
                    {"width": "30%", "targets": 1},
                    {"width": "15%", "targets": 2},
                    {"width": "15%", "targets": 3},
                    {"width": "10%", "targets": 4},
                    {"width": "10%", "targets": 5}
                ],
                columns: [
                    {data: "biz"},
                    {data: "tableName"},
                    {data: "oldCount"},
                    {data: "newCount"},
                    {data: "isSupportOpLog"},
                    {data: "isMigrate"}
                ],
                "mark": true,
                "iDisplayLength": 10,
                "sPaginationType": "full_numbers",
                "language": {
                    "url": "${ctx}/static/js/datatables-zh_CN.json"
                },
            });

            $("#findSub").click(function () {
                var tenantId = $("#tenantId").val();
                var queryOpLog = $("#isQueryOpLog").val();
                if (queryOpLog == null || "true" !== queryOpLog) {
                    queryOpLog = "false";
                }
                if (tenantId.length !== 0) {
                    myTable.ajax.url("${CONTEXT_PATH}/data-validation/describe_data_count?tenantId=" + tenantId + "&isQueryOpLog=" + queryOpLog).load();
                } else {
                    $('#hintModal').modal("show");
                }
            });
        });
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
