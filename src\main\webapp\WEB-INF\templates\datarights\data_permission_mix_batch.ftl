<#assign title="数据详情查询">
<#assign active_nav="data_permission_basic">
<#assign headContent>
        <style>
          .doSearch {
            width: 8%;
            margin-left: 20px
          }

          #dataPermission_table {
            margin-top: 10px;
            width: 97%;
            margin-left: 1.5%;
          }

          input {
            margin-left: 10px;
          }

          #dataTable2 th {
            vertical-align: middle;
            align-items: center;
          }

          #dataTable2 td {
            vertical-align: middle;
            align-items: center
          }

          .table > thead:first-child > tr:first-child > th {
            text-align: center;
            vertical-align: middle;
          }

          .table > tbody > tr > td {
            text-align: center;
          }

          input {
            width: 10%;
            height: 34px;
            line-height: 34px;
            box-sizing: border-box;
            border-radius: 4px;
            border: 1px solid #c8cccf;
            color: #6a6f77;
            -web-kit-appearance: none;
            -moz-appearance: none;
            outline: 0;
            text-decoration: none;
          }

        </style>
</#assign>
<#assign bodyContent>
  <#assign breadcrumbContent>
    <section class="content-header">
      <h1>数据详情查询</h1>
      <ol class="breadcrumb">
        <li><a href="#"><i class="fa fa-dashboard"></i>查询</a></li>
      </ol>
    </section>
  </#assign>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <div class="box-header with-border">
          <h3 class="box-title"></h3>
        </div>
        <form class="form-horizontal" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="permission_basic">
              <input placeholder="企业_ID"/>
              <input placeholder="数据_ID"/>
              <input placeholder="ApiName"/>
              <button type="button" class="doSearch btn btn-primary">Send</button>
            </div>
          </div>
        </form>
        <button id="collapse-btn">折叠</button>
        <button id="expand-btn">展开</button>
        <div id="json"></div>
      </div>

    </div>
  </div>

</section>
</#assign>
<#assign scriptContent>

<script>
  function getTree() {
    var tenantId = $("#permission_basic input").eq(0).val();
    var objectId = $("#permission_basic input").eq(1).val();
    var apiName = $("#permission_basic input").eq(2).val();
    if (tenantId == "") {
      alert("企业ID不可为空!");
      return false;
    }
    if (objectId == "") {
      alert("数据ID不可为空");
      return false;
    }
    if (apiName == "") {
      alert("apiName不可为空");
      return false;
    }
    $.ajax({
      type: 'Get',
      url: '${CONTEXT_PATH}/datarights/mixBatch',
      data: {
        tenantId: tenantId,
        id: objectId,
        apiName: apiName
      },
      contentType: "application/json", //必须有
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        $("#json").JSONView(result);
        $("#json-collapsed").JSONView(result, {collapsed: true, nl2br: true});

        $('#collapse-btn').on('click', function () {
          $('#json').JSONView('collapse');
        });
        $('#expand-btn').on('click', function () {
          $('#json').JSONView('expand');
        });

      },
      error: function (error) {
        alert('网络异常');
      }

    })


  };


  $(".doSearch").on("click", function () {
    getTree();
  });
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
