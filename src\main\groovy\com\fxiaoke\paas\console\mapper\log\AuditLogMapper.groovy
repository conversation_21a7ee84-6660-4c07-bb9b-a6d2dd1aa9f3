package com.fxiaoke.paas.console.mapper.log

import com.fxiaoke.paas.console.entity.log.AuditLog
import com.github.mybatis.mapper.ICrudMapper
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * @date 2018/4/12
 */
interface AuditLogMapper extends ICrudMapper<AuditLog> {

  @Insert("INSERT INTO console_log(executor, description, argument, execute_time) values(#{auditLog.executor}, #{auditLog.description}, #{auditLog.argument,typeHandler=com.github.mybatis.handler2.JSONBStringTypeHandler}, #{auditLog.executeTime})")
  Integer addAuditLog(@Param("auditLog") AuditLog auditLog)

  @Select("SELECT * FROM console_log ORDER BY execute_time DESC LIMIT #{num}")
  List<AuditLog> findAuditLogByNum(@Param("num") int num)

  @Delete("DELETE FROM console_log WHERE execute_time < #{time}")
  Integer deleteAuditLogByTime(@Param("time") Date time)

  /**
   * 查询当前最大时间
   * @return
   */
  @Select("SELECT MAX(execute_time) FROM console_log")
  Date findMaxExecuteTime()

  /**
   *
   * @param time
   * @return
   */
  @Select("SELECT count(*) FROM console_log WHERE execute_time > #{time}")
  Integer findNewLogNum(@Param("time") Date time)
}
