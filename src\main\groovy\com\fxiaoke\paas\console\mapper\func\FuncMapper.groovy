package com.fxiaoke.paas.console.mapper.func

import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * @date 2020/5/8 16:58
 */
interface FuncMapper extends ITenant<FuncMapper> {

  @Select("SELECT app_id  appid,role_code rolecode,role_name rolename,role_type roletype from \${schemaName}.fc_role where tenant_id=#{tenantId}")
  List<Map<String, String>> queryRoleCodesByTenantId(@Param("schemaName") String schemaName, @Param("tenantId") String tenantId)


  @Select("SELECT distinct user_id from \${schemaName}.fc_user_role where tenant_id=#{tenantId} and role_codes@>'\${roleCode}'")
  List<String> queryUsersByTenantIdAndRoleCode(@Param("schemaName") String schemaName, @Param("tenantId") String tenantId, @Param("roleCode") String roleCode)

}
