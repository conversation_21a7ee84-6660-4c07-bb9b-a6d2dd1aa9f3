package com.fxiaoke.paas.console.service.datarights

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONException
import com.alibaba.fastjson.JSONObject
import com.fxiaoke.common.http.spring.HttpSupportFactoryBean
import com.fxiaoke.common.http.spring.OkHttpSupport
import com.github.autoconf.ConfigFactory
import com.google.common.base.Splitter
import groovy.util.logging.Slf4j
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.apache.commons.lang3.StringUtils
import org.apache.http.HttpStatus
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import javax.annotation.PostConstruct
import javax.annotation.Resource
import java.util.stream.Collectors
import java.util.stream.StreamSupport

/**
 * <AUTHOR>
 * @date 2019/4/17 下午3:10
 *
 */
@Slf4j
@Component
class DataRightsStoreTableNameService {
  @Resource(name = "httpSupport")
  private OkHttpSupport client
  private String describeStaticUrl
  private Set<String> ignoredApiName

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-data-auth-base", { config ->
      ignoredApiName = StreamSupport.stream(Splitter.on(",").trimResults().split(config.get("ignoredApiName")).spliterator(), false)
              .collect(Collectors.toSet())
    })
    // describe
    ConfigFactory.getConfig("describe-static-config", { config -> describeStaticUrl = config.get("describe.static.url") })
  }


  String byDescribeApiName(String tenantId, String describeApiName) {
    if (ignoredApiName.contains(describeApiName)) {
      return null
    }
    String table = null
    String url = describeStaticUrl + "?tenantId=" + tenantId + "&describeApiName=" + describeApiName
    Request request = new Request.Builder().url(url).get().build()
    try {
      client.syncExecute(request, { response ->
        if (response.code() == HttpStatus.SC_NOT_FOUND) {
          return null
        }
        String json = response.body().string()
        JSONObject object = JSON.parseObject(json)
        table = object.getString("storeTableName")
        if (table == null || "null".equals(table)) {
          table = ""
        }
        if (!StringUtils.isBlank(table)) {
          log.debug("StoreTableName of master describe, storeTableName:{},tenantId:{}, describeApiName:{}", table, tenantId, describeApiName)
        }

      })
    } catch (IOException | JSONException ex) {
      log.error("execute error: " + url)
      table = ""
    }
    if (table.isEmpty()) {
      table = "mt_data"
    }
    return table
  }
}
