package com.fxiaoke.paas.console.web.organization

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.facishare.paas.pod.client.DbRouterClient
import com.facishare.paas.pod.dto.RouterInfo
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData
import com.facishare.uc.api.service.EnterpriseEditionService
import com.fxiaoke.common.http.spring.OkHttpSupport
import com.fxiaoke.helper.StringHelper
import com.fxiaoke.paas.console.bean.organization.object.EnterpriseObject
import com.fxiaoke.paas.console.bean.pod.PodRouterConfig
import com.fxiaoke.paas.console.service.metadata.SqlServerRouteService

import com.fxiaoke.paas.console.util.organization.typeconvert.EnterpriseConvert
import com.github.autoconf.ConfigFactory
import com.github.autoconf.api.IConfig
import com.google.common.base.CharMatcher
import com.google.common.base.Splitter
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import okhttp3.MediaType
import okhttp3.Request
import okhttp3.RequestBody
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.collections.MapUtils
import org.apache.commons.lang3.BooleanUtils
import org.apache.commons.lang3.StringUtils
import org.elasticsearch.common.Strings
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.util.ObjectUtils
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

import javax.annotation.PostConstruct
import javax.annotation.Resource

/**
 * <AUTHOR> @date 2019/7/5 上午10:19
 */
@RestController
@Slf4j
@RequestMapping(path = "/organization")
class OrganizationMsgController {
  @Autowired
  private EnterpriseEditionService enterpriseEditionService
  @Autowired
  private DbRouterClient dbRouterClient
  @Autowired
  private SqlServerRouteService serverRouteService

  private String LICENSE_VERSION_URL

  private String LICENSE_MODULE_URL

  @Resource(name = "httpSupport")
  private OkHttpSupport client

  private Map<String, Map<String, String>> schemaRouterMap
  String tenantDbAddress

  private List<PodRouterConfig> podRouterConfigs

  private Map<String, String> routeBizConfig
  private Map<String, List<String>> INDEPENDENT_DB_DESCRIBE = Maps.newHashMap();

  @Autowired
  private
  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-console-organization", { iconfig ->
      this.LICENSE_VERSION_URL = iconfig.get("LICENSE-BASE-URL") + "/paas/license/product/version"
      this.LICENSE_MODULE_URL = iconfig.get("LICENSE-BASE-URL") + "/paas/license/module"
      tenantDbAddress = iconfig.get("tenantDbAddress")
    })
    ConfigFactory.getConfig("paas-console-base", { iconfig ->
      String[] podRouterConfigList = iconfig.get("podRouterConfig").split(",")

      List<PodRouterConfig> list = new ArrayList<>()
      for (String podRouter : podRouterConfigList) {
        String[] router = podRouter.split(":")
        if (router.length != 3) {
          continue;
        }
        PodRouterConfig config = PodRouterConfig.builder().biz(router[0] as String).dialect(router[1] as String).application(router[2] as String).build()
        list.add(config)
      }
      podRouterConfigs = list
      Splitter.MapSplitter splitter = Splitter.on(CharMatcher.anyOf(",")).withKeyValueSeparator(":");
      routeBizConfig = splitter.split(iconfig.get("routeBizConfig", "clickhouse:CH,elasticsearch:ES,postgresql:PG,mongodb:MONGO"));
    })
    ConfigFactory.getConfig("fs-paas-copier-url", { iConfig ->
      JSONArray jsonArray = JSONArray.parseArray(iConfig.getString())
      Map<String, Map<String, String>> schemaTemp = Maps.newHashMap()
      for (int i = 0; i < jsonArray.size(); i++) {
        JSONObject tenantIdRouter = jsonArray.getJSONObject(i)
        List<String> tenantIds = tenantIdRouter.getObject("tenantIds", List.class)
        String old = tenantIdRouter.getString("old")
        String New = tenantIdRouter.getString("new")
        String biOld = tenantIdRouter.getString("bi-old")
        String biNew = tenantIdRouter.get("bi-new")
        for (String tenantId : tenantIds) {
          Map<String, String> tenantIdMap = Maps.newHashMap()
          tenantIdMap.put("old", old)
          tenantIdMap.put("new", New)
          tenantIdMap.put("bi-old", biOld)
          tenantIdMap.put("bi-new", biNew)
          schemaTemp.put(tenantId, tenantIdMap)
        }
        this.schemaRouterMap = schemaTemp
      }
    })
    ConfigFactory.getConfig("fs-metadata", {config ->
      INDEPENDENT_DB_DESCRIBE = parseMapFromConfig(config, "big-object-use-new-db")
    })


  }

  @GetMapping(path = "tenantDb", produces = "application/json;charset=utf-8")
  String tenantDb(@RequestParam String tenantId) {
    StringBuilder url = new StringBuilder("https://grafana." + tenantDbAddress + ".cn/d/pg-global/postgresql-global?orgId=1")
    String crmPgRouterInfoUrl = StringUtils.substringBetween(dbRouterClient.queryRouterInfo(tenantId, "CRM", "application", "postgresql").getJdbcUrl(), "//", "/")
    String biPgRouterInfoUrl = StringUtils.substringBetween(dbRouterClient.queryRouterInfo(tenantId, "BI", "application", "postgresql").getJdbcUrl(), "//", "/")
    String crmPgIp = crmPgRouterInfoUrl.substring(0, crmPgRouterInfoUrl.indexOf(":"))
    String biPgIp = biPgRouterInfoUrl.substring(0, biPgRouterInfoUrl.indexOf(":"))
    Boolean crmChange = true
    Boolean biChange = true
    if ("firstshare".equals(tenantDbAddress)) {
      url.append("&var-host=" + crmPgIp)
      url.append("&var-host=" + biPgIp)
    } else if ("foneshare".equals(tenantDbAddress)) {
      if (crmPgIp.contains("156") || crmPgIp.contains("157")) {
        crmPgIp = crmPgIp.replaceFirst("156", "12")
        crmPgIp = crmPgIp.replaceFirst("157", "13")
        crmChange = false
      }
      if (biPgIp.contains("156") || crmPgIp.contains("157")) {
        biPgIp = biPgIp.replaceFirst("156", "14")
        biPgIp = biPgIp.replaceFirst("157", "15")
        biChange = false
      }
      if (crmChange) {
        crmPgIp = crmPgIp.replaceFirst("56", "52")
        crmPgIp = crmPgIp.replaceFirst("57", "53")
      }
      if (biChange) {
        biPgIp = biPgIp.replaceFirst("56", "54")
        biPgIp = biPgIp.replaceFirst("57", "55")
      }
      url.append("&var-host=" + crmPgIp)
      url.append("&var-host=" + biPgIp)
    }
    url.append("&var-interval=\$__auto_interval_interval")
    return JSONObject.toJSONString(url)
  }


  @GetMapping(path = "tenantMsg", produces = "application/json;charset=utf-8")
  String tenantMsg(@RequestParam String tenantId) {
    Map<String, Object> map = Maps.newLinkedHashMap()
    try {
      if (StringHelper.isNullOrBlank(tenantId)) {
        return JSONObject.toJSONString(map)
      }
      tenantId = tenantId.trim()
      BatchGetSimpleEnterpriseDataArg batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(
              enterpriseIds: null,
              enterpriseAccounts: Arrays.asList(tenantId)
      )
      BatchGetSimpleEnterpriseDataResult batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
      List<SimpleEnterpriseData> enterpriseList = batchGetSimpleEnterpriseDataResult.simpleEnterpriseList
      List<EnterpriseObject> enterpriseObjectList = enterpriseList?.collect { EnterpriseConvert.enterpriseDataConvertEnterpriseObject(it) }
      if (CollectionUtils.isNotEmpty(enterpriseList)) {
        SimpleEnterpriseData simpleEnterpriseData = enterpriseList.get(0)
        if (simpleEnterpriseData != null && simpleEnterpriseData.enterpriseId != 0 && simpleEnterpriseData.enterpriseAccount.equals(tenantId)) {
          tenantId = simpleEnterpriseData.enterpriseId
        }
      } else {
        batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(
                enterpriseIds: Arrays.asList(Integer.parseInt(tenantId)),
                enterpriseAccounts: null
        )
        batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
        enterpriseList = batchGetSimpleEnterpriseDataResult.simpleEnterpriseList
        enterpriseObjectList = enterpriseList?.collect { EnterpriseConvert.enterpriseDataConvertEnterpriseObject(it) }
      }


      map.put("企业信息", convertEnterpriseObject(enterpriseObjectList.get(0)))
      for (config in podRouterConfigs) {
        String dialect = routeBizConfig.get(config.getDialect())
        String biz = config.getBiz()
        try {
          RouterInfo router = dbRouterClient.queryRouterInfo(tenantId, config.getBiz(), config.getApplication(), config.getDialect())
          String jdbcUrl = router.getJdbcUrl()
          if ("elasticsearch" == config.getDialect()) {
            map.put("当前" + dialect + " " + biz + "路由", jdbcUrl + "/" + router.getDbName())
          } else if ("mongodb" == config.getDialect()) {
            map.put("当前" + dialect + " " + biz + "路由", jdbcUrl.substring(jdbcUrl.lastIndexOf("@") + 1))
          } else {
            map.put("当前" + dialect + " " + biz + "路由", jdbcUrl)
          }
        } catch (Exception ignored) {
          map.put("当前" + dialect + " " + biz + "路由", "未找到路由")
          continue
        }
      }

      if (!ObjectUtils.isEmpty(INDEPENDENT_DB_DESCRIBE) && INDEPENDENT_DB_DESCRIBE.containsKey(tenantId)) {
        Map<String, String> bigObjectMap = Maps.newHashMap();
        for (String describeApiName : INDEPENDENT_DB_DESCRIBE.get(tenantId)) {
          RouterInfo originRouter = dbRouterClient.queryRouterInfo(tenantId, "CRM", "application", "postgresql")
          try {
            RouterInfo router = dbRouterClient.queryRouterInfo(tenantId, "CRM", "fs-metadata-service", "postgresql", describeApiName)
            if (!originRouter.getJdbcUrl().equals(router.getJdbcUrl())) {
              bigObjectMap.put(describeApiName + " 对象路由", router.getJdbcUrl())
            }
          } catch (Exception ignored) {
            log.warn("unassigned big object routing")
          }
        }
        if (MapUtils.isNotEmpty(bigObjectMap)) {
          map.put("独立大对象路由", bigObjectMap)
        }
      }


      //String sqlServerRoutUrl = serverRouteService.getUrl(Integer.valueOf(tenantId))
      RouterInfo crmPgRouterInfo = dbRouterClient.queryRouterInfo(tenantId, "CRM", "application", "postgresql")
      RouterInfo biPgRouterInfo = dbRouterClient.queryRouterInfo(tenantId, "BI", "application", "postgresql")
      //map.put("当前SQLServer路由",sqlServerRoutUrl)
      //map.put("当前Pg PASS 路由", crmPgRouterInfo.getJdbcUrl())
      //map.put("当前Pg BI 路由", biPgRouterInfo.getJdbcUrl())

      Map<String, String> schemaMessageMap = Maps.newLinkedHashMap()
      Map<String, String> schemaRouter = schemaRouterMap.get(tenantId)
      if (MapUtils.isNotEmpty(schemaRouter)) {
        schemaMessageMap.put("PAAS 原路由", schemaRouter.get("old"))
        schemaMessageMap.put("PAAS 新路由", schemaRouter.get("new"))
        schemaMessageMap.put("BI 原路由", schemaRouter.get("bi-old"))
        schemaMessageMap.put("BI 新路由", schemaRouter.get("bi-new"))
        map.put("Schema隔离信息", schemaMessageMap)
      }
      Map<String, String> schemaInfo = Maps.newHashMap()

      if (BooleanUtils.isTrue(crmPgRouterInfo.getStandalone())) {
        schemaInfo.put("PAAS路由状态", "路由至新Schema(schema企业)")
      } else {
        schemaInfo.put("PAAS路由状态", "路由至原Schema(pubic企业)")
      }
      if (BooleanUtils.isTrue(biPgRouterInfo.getStandalone())) {
        schemaInfo.put("BI路由状态", "路由至新Schema(schema企业)")
      } else {
        schemaInfo.put("BI路由状态", "路由至原Schema(pubic企业)")
      }
      try {
        RouterInfo biCHPgRouterInfo = dbRouterClient.queryRouterInfo(tenantId, "BI", "fs-bi-stat", "clickhouse")
        if (BooleanUtils.isTrue(biCHPgRouterInfo.getStandalone())) {
          schemaInfo.put("CH路由状态", "路由至新Schema(schema企业)")
        } else {
          schemaInfo.put("CH路由状态", "路由至原Schema(pubic企业)")
        }
      } catch (Exception e) {
        log.error("" + e)
      }
      map.put("schema路由信息", schemaInfo)
      //分版
      //分版基本信息
      JSONObject queryParam = new JSONObject()
      queryParam.put("licenseContext", new HashMap() {
        {
          put("tenantId", tenantId)
          put("appId", "CRM")
          put("userId", "1000")

        }
      })
      String result = null
      Request request = new Request.Builder()
              .post(RequestBody.create(MediaType.parse("application/json;charset=utf-8"), queryParam.toJSONString())).url(LICENSE_VERSION_URL).build()
      client.syncExecute(request, {
        response ->
          result = response.body().string()
      })
      JSONObject licenseResultJSONObject = JSONObject.parseObject(result)
      String retureMsg = licenseResultJSONObject.get("errMessage")
      if (retureMsg.equals("success")) {
        JSONArray resultArray = licenseResultJSONObject.getJSONArray("result")
        List<Map<String, Object>> licenseResult = resultArray?.collect { convertLicenseObject(it) }

        map.put("分版基本信息", licenseResult)

      } else {
        log.warn("query license error! tenantId:{}", tenantId)
      }
      //model信息
      request = new Request.Builder()
              .post(RequestBody.create(MediaType.parse("application/json;charset=utf-8"), queryParam.toJSONString())).url(LICENSE_MODULE_URL).build()

      client.syncExecute(request, {
        response ->
          result = response.body().string()
      })
      JSONObject moduleResultJSONObject = JSONObject.parseObject(result)
      retureMsg = moduleResultJSONObject.get("errMessage")
      if (retureMsg.equals("success")) {
        JSONArray resultArray = moduleResultJSONObject.getJSONArray("result")
        List<Map<String, Object>> moduleResult = resultArray?.collect { convertModuleObject(it) }

        map.put("Module基本信息", moduleResult)

      } else {
        log.warn("query module error! tenantId:{}", tenantId)
      }
      return JSONObject.toJSONString(map)
    } catch (Exception e) {
      log.error("simpleEnterpriseData failed", e)
      map.put("message", e.message)
      return JSONObject.toJSONString(map)
    }
  }

  private Map<String, String> convertEnterpriseObject(EnterpriseObject enterpriseObject) {
    Map<String, String> maps = Maps.newHashMap()
    maps.put("EI", String.valueOf(enterpriseObject.enterpriseId))
    maps.put("EA", enterpriseObject.enterpriseAccount)
    maps.put("企业名称", enterpriseObject.enterpriseName)
    maps.put("创建时间", enterpriseObject.createTime)
    maps.put("状态", enterpriseObject.runStatus)
    return maps
  }

  private Map<String, Object> convertLicenseObject(JSONObject jsonObject) {
    String productType = null
    String productTypeFlag = jsonObject.get("productType")
    switch (productTypeFlag) {
      case "0":
        productType = "主版本"
        break
      case "1":
        productType = "资源包"
        break
      case "2":
        productType = "行业套件"
        break
      case "3":
        productType = "应用"
        break
    }
    Map<String, Object> maps = Maps.newHashMap()
    maps.put("EI", jsonObject.get("tenantId"))
    maps.put("产品类型", productType)
    maps.put("产品名称", jsonObject.get("productName"))
    maps.put("当前版本", jsonObject.get("currentVersion"))
    maps.put("版本名称", jsonObject.get("versionName"))
    return maps
  }

  private Map<String, Object> convertModuleObject(JSONObject jsonObject) {
    Map<String, Object> maps = Maps.newHashMap()
    maps.put("ID", jsonObject.get("id"))
    maps.put("EI", jsonObject.get("tenantId"))
    maps.put("产品Id", jsonObject.get("productId"))
    maps.put("module名称", jsonObject.get("moduleName"))
    maps.put("moduleCode", jsonObject.get("moduleCode"))
    maps.put("moduleParaPojos", jsonObject.get("moduleParaPojos"))
    return maps
  }
  private static <T> Map<String, T> parseMapFromConfig(IConfig config, String key) {
    String data = config.get(key);
    if (Strings.isNullOrEmpty(data)) {
      return Maps.newHashMap();
    }
    try {
      return JSON.parseObject(data, Map.class);
    } catch (Exception e) {
      log.error("config name {} key {} parseMapError", config.getName(), key, e);
    }

    return new HashMap<>();
  }
}
