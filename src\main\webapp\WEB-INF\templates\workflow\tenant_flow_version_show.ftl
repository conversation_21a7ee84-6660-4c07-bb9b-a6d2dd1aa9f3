<#assign title="流程版本展示">
<#assign active_nav="workflow">
<#assign headContent>
<style>

    #tc {
        height: 100px;
        width: 100%;
    }

    #workflow {
        width: 50%;
        float: left;
    }

    #rules {
        width: 50%;
        float: left;
    }

    pre {
        outline: 1px solid #ccc;
        padding: 5px;
        margin: 5px;
        width: 80%;
        height: 750px;
        margin-left: 10%;
    }

    .string {
        color: green;
    }

    .number {
        color: darkorange;
    }

    .boolean {
        color: blue;
    }

    .null {
        color: magenta;
    }

    .key {
        color: red;
    }
</style>
</#assign>
<#assign bodyContent>
    <#assign breadcrumbContent>
    <section class="content-header">
        <h1>流程定义</h1>
        <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i>展示</a></li>
        </ol>
    </section>
    </#assign>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title"></h3>
                </div>
                <form class="form-horizontal" action="${ctx}/biz/add" method="post" id="myForm" role="form"
                      data-toggle="validator">
                    <div class="container-fluid">
                        <div>
                            <table class="table" id="dataTable">
                                <thead>
                                <tr id="advanced">
                                    <td style="width: 20%; vertical-align: middle"><a><strong>企业ID：${tenantId}</strong></a>
                                    </td>
                                    <td style="width: 20%; vertical-align: middle">
                                        <a><strong>业务名称：${appName}</strong></a></td>
                                    <td style="width: 20%; vertical-align: middle"><a><strong>流程类型：${flowType}</strong></a>
                                    </td>
                                    <td style="width: 20%; vertical-align: middle">
                                        <a><strong>SRC_ID：${sourceId}</strong></a></td>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div id="workflow_and_rules">
                            <div id="workflow">
                                <legend>WorkFlow</legend>
         <pre id="workflow_result">
         </pre>
                            </div>
                            <div id="rules">
                                <legend>WorkFlow_Rules</legend>
         <pre id="rules_result">
         </pre>
                            </div>
                        </div>
                        <div id="tc"></div>
                    </div>
            </div>
        </div>
        <div class="box-footer clearfix">
            <div class="form-group">
                <div class="col-sm-offset-2 col-sm-2">
                    <button type="button" class="btn btn-default" onclick="history.back()">返回</button>
                </div>
            </div>
            <div class="clearfix"></div>
        </div>
        </form>
    </div>
    </div>
    </div>
</section>
</#assign>
<#assign scriptContent>
<script>
    $(document).ready(function () {

        var rules_result = ${triggerRuleString};
        $("#rules_result").html(syntaxHighlight(rules_result));

        var workflow_result = ${workflowString};
        $("#workflow_result").html(syntaxHighlight(workflow_result));

        function syntaxHighlight(json) {
            if (typeof json != 'string') {
                json = JSON.stringify(json, undefined, 2);
            }
            json = json.replace(/&/g, '&').replace(/</g, '<').replace(/>/g, '>');
            return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
                var cls = 'number';
                if (/^"/.test(match)) {
                    if (/:$/.test(match)) {
                        cls = 'key';
                    } else {
                        cls = 'string';
                    }
                } else if (/true|false/.test(match)) {
                    cls = 'boolean';
                } else if (/null/.test(match)) {
                    cls = 'null';
                }
                return '<span class="' + cls + '">' + match + '</span>';
            });
        }


    });
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
