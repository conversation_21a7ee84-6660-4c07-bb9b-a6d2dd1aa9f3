package com.fxiaoke.paas.console.bean.organization.object

import groovy.transform.ToString
import lombok.Getter
import lombok.Setter

/**
 * Created by wangxing on 2018/03/06
 */
@Getter
@Setter
@ToString
class DepartmentObject implements Serializable {

  String id
  String deptId
  String name
  String managerId
  String status
  String createBy
  String createTime
  String tenantId
  String parentId
  String lastModifiedBy
  String lastModifiedTime
  Integer isDeleted
  String description
  String text
  List<DepartmentObject> nodes
}
