<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/jquery-ui.min.css" rel="stylesheet" type="text/css"/>
    <link href="//static.foneshare.cn/oss/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="//static.foneshare.cn/oss/bootstrap-tokenfield/0.12.0/css/tokenfield-typeahead.min.css" rel="stylesheet">
    <link href="//static.foneshare.cn/oss/bootstrap-tokenfield/0.12.0/css/bootstrap-tokenfield.min.css" rel="stylesheet">
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>

<#assign breadcrumbContent>
    <section class="content-header">
      <h1>查路由</h1>
      <ol class="breadcrumb">
        <li><a href="${CONTEXT_PATH}/trm"><i class="fa fa-dashboard"></i>主页</a></li>
        <li class="active">查路由</li>
      </ol>
    </section>
</#assign>

<#assign bodyContent>
    <section class="content">
      <div class="row">
        <div class="col-md-12">
          <div class="box box-info">
            <div class="box-header with-border">
              <h3 class="box-title"></h3>
            </div>
            <form class="form-horizontal" action="${CONTEXT_PATH}/findRout" method="post">
              <div class="box-body">

                <div class="form-group">
                  <label for="moudel" class="col-sm-2 control-label">业务</label>
                  <div class="col-sm-4">
                    <select id="module" name="module" class="selectpicker show-tick form-control" title="Nothing selected" data-live-search="false" required>
                                        <#list moduleList! as module>
                                          <option value="${module}" <#if ((module) == "CRM")> selected="selected"</#if>>${module}</option>
                                        </#list>
                    </select>
                    <div class="help-block with-errors"></div>
                  </div>
                </div>

              <#--                        <div class="form-group">-->
              <#--                            <label for="pkg" class="col-sm-2 control-label">package</label>-->
              <#--                            <div class="col-sm-4">-->
              <#--                                <select id="pkg" name="pkg" class="selectpicker show-tick form-control" title="Nothing selected" data-live-search="false" required>-->
              <#--                                    <#list moduleList! as module>-->
              <#--                                        <option value="${module}" <#if ((module) == "CRM")> selected="selected"</#if>>${module}</option>-->
              <#--                                    </#list>-->
              <#--                                </select>-->
              <#--                                <div class="help-block with-errors"></div>-->
              <#--                            </div>-->
              <#--                        </div>-->

                <div class="form-group">
                  <label for="resourceType" class="col-sm-2 control-label">资源类型</label>
                  <div class="col-sm-4">
                    <select id="resourceType" name="resourceType" class="selectpicker show-tick form-control" title="Nothing selected" data-live-search="false" required>
                                        <#list resourceTypeList! as resourceType>
                                          <option value="${resourceType}" <#if ((resourceType) == "postgresql")>selected="selected"</#if>>${resourceType}</option>
                                        </#list>
                    </select>
                    <div class="help-block with-errors"></div>
                  </div>
                </div>

                <div class="form-group">
                  <label for="tenantId" class="col-sm-2 control-label">EI</label>
                  <div class="col-sm-4">
                    <input type="text" class="form-control" style="border-radius:5px;" id="ei" name="ei" value="" placeholder="EI">
                  </div>
                </div>
                <div class="form-group">
                  <label for="tenantId" class="col-sm-2 control-label">EA</label>
                  <div class="col-sm-4">
                    <input type="text" class="form-control" style="border-radius:5px;" id="ea" name="ea" value="" placeholder="EA">
                  </div>
                </div>
                <div class="form-group">
                  <label for="apiName" class="col-sm-2 control-label">对象ApiName</label>
                  <div class="col-sm-4">
                    <input type="text" class="form-control" style="border-radius:5px;" id="apiName" name="apiName" value="" placeholder="describeApiName">
                  </div>
                </div>
                <br><br>
                <div class="form-group">
                  <label for="result" class="col-sm-2 control-label">结果</label>
                  <div class="col-sm-4">
                    <input type="text" class="form-control" style="border-radius:5px;" id="result" name="result" value="" placeholder="结果">
                  </div>
                </div>
                <div class="form-group">
                  <div class="col-sm-4">
                  <p><a style="color: #ff2d21">此模块将废弃，请使用新地址查询路由：</a><a href="${ctx}/org/tenantMsg">企业详细信息查询</a></p>
                  </div>
                </div>

              </div>
              <div class="box-footer clearfix">
                <div class="col-sm-offset-3">
                  <a id="confirm" class="btn btn-info">查找</a>
                  <input id="cancel_btn" class="btn" type="button" value="返回" onclick="history.back()"/>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>


    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script>
      $(document).ready(function () {
//        查询
        $('#confirm').on('click', function () {
          $('#result').val('');

          var module = $('#module').val();
          // var pkg = $('#pkg').val();
          var resourceType = $('#resourceType').val();
          var ea = $('#ea').val();
          var ei = $('#ei').val();
          if (ea == '' && ei == '') {
            alert("EA EI 二者必须填写一个！")
            return false;
          }
          var dataObject = {
            module: module,
            // pkg: pkg,
            resourceType: resourceType,
            ea: ea,
            ei: ei
          };
          $.ajax({
            url: "${CONTEXT_PATH}/metadata/rout/find-rout",
            contentType: "application/json",
            data: JSON.stringify(dataObject),
            dataType: "json",
            type: "POST",
            traditional: true,
            success: function (data) {
              if (data.code === 200) {
                $('#result').val(data.result)
              } else {
                $('#result').val(data.error)
              }
            }
          });
        });

      });

    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
