package com.fxiaoke.paas.console.mapper.metadata

import com.fxiaoke.paas.console.entity.metadata.MtDescribe
import com.github.mybatis.mapper.IBatchMapper
import com.github.mybatis.mapper.ICrudMapper
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * Created on 2018/3/5.
 */
interface DescribeMapper extends ICrudMapper, IBatchMapper, ITenant<DescribeMapper> {

  /**
   * 测试切换数据源
   * @return
   */
  @Select("SELECT display_name FROM mt_describe WHERE tenant_id='2' AND describe_api_name='object_cmvNB__c' AND is_deleted=true")
  String dbTest()

  /**
   * 根据tenantId获取describeList
   * @param sql
   * @return
   */
  @Select("SELECT describe_id,tenant_id,describe_api_name,display_name,last_modified_time FROM mt_describe WHERE is_current=TRUE AND tenant_id = #{tenantId}")
  List<Map<String, String>> describeListByTenantId(@Param("tenantId") String tenantId)

  /**
   * 根据tenantId和apiName获取describe
   * @param sql
   * @return
   */
  @Select("SELECT describe_id,tenant_id,describe_api_name,display_name,last_modified_time FROM mt_describe WHERE is_current=TRUE AND tenant_id = #{tenantId} AND describe_api_name = #{apiName}")
  List<Map<String, String>> describeByTenantIdAndApiName(@Param("tenantId") String tenantId, @Param("apiName") String apiName)

  /**
   * 根据tenantId查询企业的所有描述
   * @param tenantId
   * @return
   */
  @Select("SELECT describe_api_name from mt_describe where tenant_id =#{tenantId} and is_current = true and is_deleted = false ")
  List<String> getDescribeApiNamesByTenantId(@Param("tenantId") String tenantId)

  /**
   * 根据describeId获取describe
   * @param describeId
   * @return
   */
  @Select("SELECT * FROM mt_describe WHERE describe_id=#{describeId}")
  Map<String, String> findDescribeById(@Param("tenantId") String tenantId, @Param("describeId") String describeId)

  /**
   * 根据describeId获取apiName和displayName
   * @param describeId
   * @return
   */
  @Select("SELECT tenant_id,describe_id,describe_api_name,display_name FROM mt_describe WHERE tenant_id=#{tenantId} AND describe_id=#{describeId}")
  Map<String, String> findByDescribeId(@Param("tenantId") String tenantId, @Param("describeId") String describeId)

  @Select("\${sql}")
  Map<String, String> findByDescribeId2(@Param("sql") String sql)

  /**
   * 根据describeId获取api_name和store_table_name
   * @param describeId
   * @return
   */
  @Select("SELECT describe_api_name,store_table_name FROM mt_describe WHERE describe_id=#{describeId} AND tenant_id =#{tenantId} AND is_current = true AND is_deleted = false")
  Map<String, String> findStoreTableNameAndApiName(@Param("tenantId") String tenantId, @Param("describeId") String describeId)

  @Select("\${sql}")
  Map<String, String> findStoreTableNameAndApiName2(@Param("sql") String sql)

  /**
   * 根据tenant_d和describe_api_name获取store_table_name
   * @param tenantId
   * @param apiName
   * @return
   */
  @Select("SELECT store_table_name FROM mt_describe WHERE is_current=TRUE AND tenant_id = #{tenantId} AND describe_api_name = #{apiName}")
  String findTableNameByTenantIdAndApiName(@Param("tenantId") String tenantId, @Param("apiName") String apiName)

  @Select("SELECT store_table_name tablename FROM mt_describe WHERE is_current=TRUE AND is_deleted = false and tenant_id = #{tenantId} AND describe_api_name = #{apiName}")
  Map<String, Object> queryTableNameAndIsBigObject(@Param("tenantId") String tenantId, @Param("apiName") String apiName)

  @Select("SELECT * FROM mt_describe WHERE is_current=TRUE AND tenant_id = #{tenantId} AND describe_api_name = #{apiName}")
  MtDescribe findDescribeByTenantIdAndApiName(@Param("tenantId") String tenantId, @Param("apiName") String apiName)
}