<#assign title="部门负责人查询">
<#assign active_nav="data_permission_dept">
<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
<style>
    .doSearch {
        width: 8%;
        margin-left: 20px
    }

    #dataPermission_table {
        margin-top: 10px;
        width: 98%;
        margin-left: 1%;
    }

    input {
        margin-left: 10px;
    }

    #dataTable2 th {
        vertical-align: middle;
        align-items: center;
    }

    #dataTable2 td {
        vertical-align: middle;
        align-items: center
    }

    .table > thead:first-child > tr:first-child > th {
        text-align: center;
        vertical-align: middle;
    }

    .table > tbody > tr > td {
        text-align: center;
    }

    .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
        width: 10%;
    }

    input {
        width: 10%;
        height: 34px;
        line-height: 34px;
        box-sizing: border-box;
        border-radius: 4px;
        border: 1px solid #c8cccf;
        color: #6a6f77;
        -web-kit-appearance: none;
        -moz-appearance: none;
        outline: 0;
        text-decoration: none;
    }
</style>
</#assign>
<#assign bodyContent>
    <#assign breadcrumbContent>
    <section class="content-header">
        <h1>部门负责人查询</h1>
        <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i>查询</a></li>
        </ol>
    </section>
    </#assign>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title"></h3>
                </div>
                <form class="form-horizontal" action="${ctx}/biz/add" method="post" id="myForm" role="form"
                      data-toggle="validator">
                    <div class="container-fluid">
                        <div id="permission_leader">
                            <div class="container-fluid">
                                <span style="margin-left:10px">关系标志</span><select class="selectpicker">
                                <option value="0">直属部门</option>
                                <option value="1">部门负责人</option>
                                <option value="2">部门直接负责人</option>
                                <option value="" selected>Nothing selected</option>
                            </select>
                                <input placeholder="企业_ID"/>
                                <input placeholder="App_ID"/>
                                <input placeholder="用户_ID"/>
                                <input placeholder="Dept_ID"/>
                                <button type="button" class="doSearch btn btn-primary">Send</button>
                            </div>
                            <div id="dataPermission_table">
                                <table class="table table-striped table-bordered table-condensed dataTable no-footer"
                                       id="dataTable2">
                                    <thead>
                                    <tr>
                                        <th>企业ID</th>
                                        <th>用户_ID</th>
                                        <th>用户姓名</th>
                                        <th>部门ID</th>
                                        <th>部门名称</th>
                                        <th>关系标识</th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
</#assign>
<#assign scriptContent>
<script>
    $(document).ready(function () {
        var bootstrapDom = "<'row'<'col-sm-6'l>>" +
                "<'row'<'col-sm-12'tr>>" +
                "<'row'<'col-sm-5'i><'col-sm-7'p>>";
        var table = $("#dataTable2").DataTable({
            "dom": bootstrapDom,
            "processing": false,
            "serverSide": true,
            "search": {
                "regex": true
            },
            "order": [[2, 'desc']],
            "ajax": "${CONTEXT_PATH}/user/dept/cache/query?dataObject=",
            "columnDefs": [
                {"width": "16%", "targets": 0},
                {"width": "16%", "targets": 1},
                {"width": "16%", "targets": 2},
                {"width": "16%", "targets": 3},
                {"width": "16%", "targets": 4},
                {"width": "16%", "targets": 5},
            ],
            columns: [
                {data: "tenantId"},
                {data: "userId"},
                {data: "userName"},
                {data: "deptId"},
                {data: "deptName"},
                {data: "relationType"},
            ],
            "iDisplayLength": 25,
            "sPaginationType": "full_numbers",
            "language": {
                "processing": "加载中...",
                "lengthMenu": "显示 _MENU_ 项结果",
                "InfoEmpty": "显示第 0 至 0 项结果，共 0 项",
                "zeroRecords": "没有匹配结果",
                "emptyTable": "没有数据",
                "info": "",
                "infoEmpty": "",
                "infoFiltered": "",
                "infoPostFix": "",
                "search": "搜索:",
                "url": "",
                "paginate": {
                    "first": "首页",
                    "previous": "上页",
                    "next": "下页",
                    "last": "末页"
                }
            }
        });

    <#--#if (param.search)-->
    <#--table.search("${param.search.escapeJavaScript()}").draw();-->
    <#--#end-->


        $(".doSearch").on("click", function () {
            var tenantId = $("#permission_leader input").eq(0).val();
            var AppId = $("#permission_leader input").eq(1).val();
            var users = $("#permission_leader input").eq(2).val();
            var depts = $("#permission_leader input").eq(3).val();
            var relationType = $("#permission_leader select").val();
            if (tenantId == "") {
                alert("企业ID不可为空!");
                return false;
            }
            if (AppId == "") {
                alert("AppId不可为空");
                return false;
            }
            var dataObject = {
                "relationType":relationType,
                "tenantId":tenantId,
                "AppId":AppId,
                "users":users,
                "depts":depts
            }
            table.ajax.url("${CONTEXT_PATH}/user/dept/cache/query?dataObject=" + encodeURIComponent(JSON.stringify(dataObject))).load();
        });
    });
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
