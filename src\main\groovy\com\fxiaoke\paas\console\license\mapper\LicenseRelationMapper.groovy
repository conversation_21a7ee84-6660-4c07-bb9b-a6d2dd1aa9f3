package com.fxiaoke.paas.console.license.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 */
interface LicenseRelationMapper {
  @Select("SELECT parent_product_id FROM license_relation WHERE tenant_id = #{tenantId} AND product_id = #{productId} AND product_version = #{productVersion} AND product_name = #{productName}")
  String findParentLicenses(@Param("tenantId") String tenantId, @Param("productId") String productId, @Param("productVersion") String productVersion,
                            @Param("productName") String productName);
}
