<#assign headContent>
<link href="//static.foneshare.cn/oss/AdminLTE/plugins/datatables/dataTables.bootstrap.css" rel="stylesheet"
      type="text/css"/>
<link type="text/css" href="//static.foneshare.cn/oss/datatables-checkboxes/dataTables.checkboxes.css"
      rel="stylesheet"/>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
<style>
    #datatable td:nth-child(6) {
        text-align: right;
    }

    #datatable td:nth-child(7) {
        text-align: right;
    }
</style>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
    <h1>角色功能权限查询</h1>
    <ol class="breadcrumb">
        <li><a href="#"><i class="fa fa-dashboard"></i>主页</a></li>
    </ol>
</section>
</#assign>

<#assign bodyContent>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div id="warningInfo" class="alert alert-warning hide">
                <span id="closeInfo" href="#" class="close">&times;</span>
                <strong id="hitInfo"></strong>
            </div>
            <div class="box box-info" style="margin-bottom: 1px;">
                <div class="box-body" style="padding: 20px;">
                    <form class="form-inline">
                        <div class="form-group">
                            <label for="tenantId" class="control-label">企业ID</label>
                            <input type="text" style="border-radius:5px;" class="form-control" id="tenantId" name="tenantId" placeholder="企业ID">
                        </div>
                        <div class="form-group">
                            <label for="roleCode" class="control-label">角色Code</label>
                            <input type="text" style="border-radius:5px;" class="form-control" id="roleCode" name="roleCode"
                                   placeholder="RoleCode">
                        </div>
                        <div class="form-group">
                            <label for="roleCode" class="control-label">用户ID</label>
                            <input type="text" style="border-radius:5px;" class="form-control" id="userId" name="userId"
                                   placeholder="UserId">
                        </div>
                        <div class="form-group">
                            <label for="appId" class="col-sm-1 control-label" style="margin-top: 5px;margin-right: 5%">AppId</label>
                            <div class="col-sm-8">
                                <select id="appId" name="appId" class="selectpicker show-tick" title="Nothing selected"
                                        data-live-search="false">
                                    <#list appIdList! as appId>
                                        <option value="${appId}"
                                                <#if ((appId) == "CRM")>selected="selected"</#if>>${appId}</option>
                                    </#list>
                                </select>
                            </div>
                        </div>
                        <button type="button" id="findSub" class="btn btn-primary">查询</button>
                    </form>
                </div>
                <div class="box-footer clearfix" style="padding: 2px;">
                    <div class="clearfix"></div>
                </div>
            </div>
            <div class="box box-info">
                <div class="box-body">
                    <table id="datatable" class="table table-hover table-bordered" cellspacing="0" width="100%">
                        <thead>
                        <tr>
                            <th>权限</th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="box-footer clearfix">
                </div>
            </div>
        </div>
    </div>
</section>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script type="application/javascript">
    $(document).ready(function () {
        var table = $("#datatable").DataTable({
//            "deferRender": true,
            "processing": true,
            "ajax": "",
            "columnDefs": [],
            "language": {
                "url": "${ctx}/static/js/datatables-zh_CN.json"
            },
            "mark": true,
            "paging": false
        });

//        提交
        $('#findSub').on('click', function () {
            $('#warningInfo').addClass('hide');
            var tenantId = $('#tenantId').val();
            var roleCode = $('#roleCode').val();
            var userId = $('#userId').val();
            if (roleCode === "" || tenantId === "" || userId === "") {
                $('#warningInfo').removeClass('hide');
                $('#hitInfo').html('关键字段不能为空！');
                return;
            }
            var appId = $('#appId').val();
            var dataObject = {
                tenantId: tenantId,
                roleCode: roleCode,
                userId: userId,
                appId: appId
            };
//            将对象转化为json字符串放在url中传递到后端
            table.ajax.url("${ctx}/func/role-limit?dataObject=" + encodeURIComponent(JSON.stringify(dataObject))).load();
        });


    });
    /**
     * 信息提示栏关闭
     */
    $('#closeInfo').on('click', function () {
        $('#warningInfo').addClass('hide');
    });

</script>
</#assign>
<#include "../layout/layout-main.ftl" />
