<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.paas.console.mapper.metadata.StatMapper">
    <select id="batchFindDesNum" resultType="java.util.Map">
        SELECT object_describe_api_name,count(*) FROM mt_data WHERE tenant_id = #{tenantId}
        AND is_deleted = 0
        AND object_describe_api_name IN
        <foreach collection="describeApiNames" item="name" index="index" open="(" separator="," close=")">
            #{name}
        </foreach>
        GROUP BY object_describe_api_name
    </select>

    <select id="findDescribeIds" resultType="java.lang.String">
        SELECT describe_id FROM mt_describe WHERE tenant_id = #{tenantId}
        AND is_deleted IS NOT TRUE
        AND describe_api_name IN
        <foreach collection="describeApiNames" item="name" index="index" open="(" separator="," close=")">
            #{name}
        </foreach>
    </select>

    <select id="findFieldSum" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM mt_field WHERE describe_id IN
        <foreach collection="describeIds" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findField" resultType="java.util.Map">
        SELECT field_id,tenant_id,describe_id,api_name,type,field_label,create_time FROM mt_field WHERE describe_id IN
        <foreach collection="describeIds" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findFieldNumByDescribeIds" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM mt_field WHERE describe_id IN
        <foreach collection="describeIds" item="describeId" index="index" open="(" separator="," close=")">
            #{describeId}
        </foreach>
        GROUP BY describe_id
    </select>

</mapper>