package com.fxiaoke.paas.console.service.basicPlatform


import com.fxiaoke.paas.console.service.OKHttpService
import com.fxiaoke.paas.console.service.metadata.DescribeService
import com.fxiaoke.paas.console.service.metadata.FieldService
import com.fxiaoke.paas.console.service.metadata.SqlQueryService
import com.github.autoconf.ConfigFactory
import com.google.common.collect.Maps
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import javax.annotation.Resource
import javax.ws.rs.core.MediaType
import java.time.LocalDateTime
import java.time.ZoneOffset

@Service
class CalculateService {

    @Autowired
    private OKHttpService okHttpService

    @Autowired
    SqlQueryService sqlQueryService

    @Autowired
    DescribeService describeService

    @Resource
    private FieldService fieldService

    private String clickhouseUrl;

    private String searchQueryUrl;

    private String dateType;

    private Long dateCount;

    private final static String DAY = "DAY";
    private final static String WEEK = "WEEK";
    private final static String MONTH = "MONTH";

    @PostConstruct
    void init() {
        ConfigFactory.getConfig("paas-console-base", { config ->
            searchQueryUrl = config.get("fs-udobj-app-web-url")
            clickhouseUrl = config.get("clickhouse-url")
        })

        ConfigFactory.getConfig("fs-paas-appframework-config", { config ->
            String clickHouseQueryTime = config.get("clickHouseQueryTime")
            if (StringUtils.isNotEmpty(clickHouseQueryTime)) {
                def splitArr = clickHouseQueryTime.split("\\|")
                dateType = StringUtils.isEmpty(splitArr[0]) ? DAY : splitArr[0]
                dateCount = StringUtils.isEmpty(splitArr[1]) ? 7 : Long.valueOf(splitArr[1])
            }
        })
    }

    /**
     * 查询clickHousehttps://log.firstshare.cn/api/v1/instances/1/complete?
     * @param id
     * @param tenantId
     * @param db
     * @return
     */
    def selectByClickHouse(String id, String tenantId, String db, String key) {
        if (!tenantId) {
            throw new RuntimeException("请输入企业id")
        }
        Map<String, Object> headers = [:]
        headers.put("x-fs-ei", tenantId)
        headers.put("x-fs-userInfo", "-10000")
        LocalDateTime now = LocalDateTime.now()
        long startTimeStamp = Long.valueOf(now.toEpochSecond(ZoneOffset.of("+08:00")))
        Long finalTimeStamp = getTimeScope(now)
        String query = "select * from " + db + " where _time_second_ >= toDateTime(" + String.valueOf(finalTimeStamp) + ") and _time_second_ < toDateTime(" + String.valueOf(startTimeStamp) + ") and (`" + key + "` = '" + id + "') order by _time_second_ desc limit 1000 OFFSET 0"
        Map<String, Object> result = okHttpService.post(clickhouseUrl, headers, ["query": query], MediaType.APPLICATION_JSON)
        if (!result || result.get("code") != 0 || Objects.isNull(result.get("data"))) {
            throw new RuntimeException("查询clickHouse异常")
        }
        Map<String, Object> data = result.get("data")
        return data.get("logs")
    }

    def getTimeScope(LocalDateTime now) {
        switch (dateType) {
            case DAY:
                return now.minusDays(dateCount).toEpochSecond(ZoneOffset.of("+08:00"))
            case WEEK:
                return now.minusWeeks(dateCount).toEpochSecond(ZoneOffset.of("+08:00"))
            case MONTH:
                return now.minusMonths(dateCount).toEpochSecond(ZoneOffset.of("+08:00"))
            default:
                return now.minusDays(7).toEpochSecond(ZoneOffset.of("+08:00"))
        }
    }


    /**
     * 获取字段的计算路径
     * @param tenantId
     * @param objectApiName
     * @param fieldApiName
     * @return
     */
    def getCalculateGraph(String tenantId, String objectApiName, String fieldApiName) {
        String url = searchQueryUrl + "calculate/service/calculateGraph"
        def headers = [:]
        headers.put("x-fs-ei", tenantId)
        headers.put("x-fs-userInfo", "-10000")
        def params = [:]
        params.put("objectApiName", objectApiName)
        params.put("fieldApiName", fieldApiName)
        okHttpService.post(url, headers as Map<String, Object>, params, MediaType.APPLICATION_JSON)
    }

    /**
     * 获取字段id
     * @param tenantId
     * @param objectApiName
     * @param fieldApiName
     * @return
     */
    private def getFieldId(String tenantId, String objectApiName, String fieldApiName) {
        List<Map<String, String>> describeList = describeService.loadDescribeFromPg(tenantId.trim(), objectApiName, "")
        if (!describeList) {
            throw new RuntimeException("对象不存在")
        }
        Map describe = describeList.get(0)
        if (!describe) {
            throw new RuntimeException("对象不存在")
        }
        String describeId = describe.get("describe_id")

        List<Map<String, Object>> fieldList = fieldService.fieldListByDescribeId(tenantId, describeId)

        Map<String, Map<String, Object>> fieldMap = [:]
        for (final def fieldDescribe in fieldList) {
            String api_name = fieldDescribe.get("api_name")
            fieldMap.put(api_name, fieldDescribe)
        }
        Map<String, Object> fieldDescribe = fieldMap.get(fieldApiName)
        if (!fieldDescribe) {
            throw new RuntimeException("字段不存在")
        }
        return fieldDescribe.get("field_id")
    }

    /**
     * 获取字段的oplog日志
     * @param tenantId
     * @param objectApiName
     * @param fieldApiName
     * @return
     */
    def getFieldChangeLog(String tenantId, String objectApiName, String fieldApiName) {
        String fieldId = getFieldId(tenantId, objectApiName, fieldApiName)
        if (StringUtils.isEmpty(fieldId)) {
            throw new RuntimeException("字段id不存在")
        }
        return selectByClickHouse(fieldId, tenantId, "`logger`.`paas_oplog`", "id")
    }

    /**
     * 根据指定字段过滤日志
     * @param fieldApiName
     * @param logs
     */
    def filterLogsByField(String fieldApiName, List<Map<String, Object>> logs) {
        if (!fieldApiName) {
            return logs
        }
        return logs.findAll { x -> x.get("extra") && x.get("extra").toString().contains(fieldApiName) }
    }

    /**
     * 计算且更新
     * @param tenantId
     * @param dataIds
     * @param fieldApiNames
     * @param objectApiName
     * @return
     */
    def triggerCalculateAndUpdate(String tenantId, List<String> dataIds, List<String> fieldApiNames, String objectApiName) {
        Map<String, Object> params = ["objectApiName": objectApiName, "fieldApiNames": fieldApiNames, "dataIds": dataIds]
        String url = searchQueryUrl + "calculate/service/calculateAndUpdateFormulaFields";

        Map<String, String> headers = new HashMap<>();
        headers.put("x-fs-ei", tenantId)
        headers.put("x-fs-userInfo", "-10000")
        return okHttpService.post(url, headers, params, MediaType.APPLICATION_JSON)
    }

    def triggerCalculate(String tenantId, String objectApiName, String fieldApiName, String dataId) {
        Map<String, Object> params = Maps.newHashMap()
        String url = searchQueryUrl + "calculate/service/calculateCountOrFormulaField";
        params = ["objectApiName": objectApiName, "fieldApiName": fieldApiName, "dataId": dataId]
        Map<String, String> headers = new HashMap<>();
        headers.put("x-fs-ei", tenantId)
        headers.put("x-fs-userInfo", "-10000")
        return okHttpService.post(url, headers, params, MediaType.APPLICATION_JSON)
    }

    def submitCalculate(String tenantId, List objectApiNames, List fieldApiNames) {
        String url = searchQueryUrl + "calculate/service/submit_calculate_job"
        List calculateJobList = []
        if (fieldApiNames != null && fieldApiNames.size() > 0) {
            calculateJobList.add(["objectApiName": objectApiNames.get(0), "fieldApiNameList": fieldApiNames])
        } else {
            for (String objectApiName : objectApiNames) {
                String sql = "select api_name from mt_field where tenant_id='" + tenantId + "' and is_active=true and is_index=true and type in('formula','quote','count') and describe_api_name='" + objectApiName + "'"
                List<Map<String, Object>> sqlResult = sqlQueryService.sqlQuery(sql.trim(), tenantId.trim(), true, true)
                List calculateFieldList = []
                for (final def result in sqlResult) {
                    calculateFieldList.add(result.get("api_name"))
                }
                calculateJobList.add(["objectApiName": objectApiName, "fieldApiNameList": calculateFieldList])
            }
        }
        Map<String, String> headers = new HashMap<>();
        headers.put("x-fs-ei", tenantId)
        headers.put("x-fs-userInfo", "-10000")
        return okHttpService.post(url, headers, ["calculateJobList": calculateJobList, "manual": false], MediaType.APPLICATION_JSON)
    }
}
