package com.fxiaoke.paas.console.web.metadata

import com.alibaba.fastjson.JSONObject
import com.facishare.paas.metadata.dao.pg.entity.metadata.Field
import com.fxiaoke.common.SqlEscaper
import com.fxiaoke.paas.common.service.SchemaHelper
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.entity.metadata.MtDescribe
import com.fxiaoke.paas.console.mapper.metadata.DescribeMapper
import com.fxiaoke.paas.console.mapper.metadata.FieldMapper
import com.google.common.collect.Maps;
import groovy.util.logging.Slf4j
import io.protostuff.Response
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody

/**
 * <AUTHOR>
 * @date 2022/8/23 16:11
 */
@Controller
@Slf4j
@RequestMapping("/metadata-operate/add-capacity")
class AddFieldCapacityController {

  String findDataColumnSql="select ordinal_position column_id,COLUMN_NAME,DATA_TYPE,CHARACTER_MAXIMUM_LENGTH data_length,\n" +
          "  IS_NULLABLE nullable,null pk_column\n" +
          "  from information_schema.columns where table_name=''"

  @Autowired
  FieldMapper fieldMapper

  @Autowired
  DescribeMapper describeMapper

  @Autowired
  SchemaHelper schemaHelper

  private static String UPDATE_SQL = "update mt_field set max_length = 2000, type = 'long_text' where tenant_id='%s' and field_id='%s'"

  @RequestMapping("")
  def page(){
    "/metadata/addCapacity"
  }

  @RequestMapping("/query")
  @ResponseBody
  def query(String tenantId, String describeApiName, String fieldApiName){

    if(StringUtils.isAnyBlank(tenantId, describeApiName, fieldApiName)){
      return ["code": "400", "message": "企业ID和描述字段ID不可为空"]
    }
    try {
      Field field = fieldMapper.setTenantId(tenantId).findFieldByApiName(tenantId, describeApiName, fieldApiName)
      if(field == null){
        return ["code": "500", "message": "不存在对应ID描述字段"]
      }
      String tableName = describeMapper.setTenantId(tenantId).findTableNameByTenantIdAndApiName(tenantId, describeApiName)
      String schema = schemaHelper.isSpecialSchema(tenantId) ? "sch_" + tenantId : "public"
      if (StringUtils.isBlank(tableName) || (field.getFieldNum() != null && !schemaHelper.isSpecialSchema(tenantId))) {
        tableName = "mt_data"
      }
      String columnName = field.getFieldNum() == null ? fieldApiName : "value" + field.getFieldNum()
      Map<String, Object> map = Maps.newLinkedHashMap()
      map.put("fieldSize", field.getMaxLength())
      map.put("length", field.getLength())
      map.put("decimal_places", field.getDecimalPlaces())
      map.put("fieldType", field.getType())
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("SELECT ordinal_position column_id,COLUMN_NAME,DATA_TYPE,CHARACTER_MAXIMUM_LENGTH data_length," +
              " IS_NULLABLE nullable,null pk_column FROM information_schema.columns WHERE ")
      stringBuilder.append(SqlEscaper.pg_op_eq("table_name", tableName)).append(" AND ")
      stringBuilder.append(SqlEscaper.pg_op_eq("column_name", columnName)).append(" AND ")
      stringBuilder.append(SqlEscaper.pg_op_eq("table_schema", schema))
      Map<String, Object> columns = fieldMapper.setTenantId(tenantId).findFieldByDescribeId2(stringBuilder.toString()).get(0)
      map.put("column", columns)
      return ["code": 200, "info": JSONObject.toJSONString(map)]
    } catch (Exception ignored){
      return ["code": 500, "message": "失败"]
    }
  }

  @RequestMapping("/add")
  @ResponseBody
  @SystemControllerLog(description = "企业自定义对象描述字段扩容")
  def addCapacity(String fieldType, String tenantId, String describeApiName, String fieldApiName, String attributeType, Integer newCapacity){
    if(StringUtils.isAnyBlank(tenantId, describeApiName, fieldApiName)){
      ["code": "400", message: "企业ID和描述字段ID不可为空"]
    }
    try {
      Field field = fieldMapper.setTenantId(tenantId).findFieldByApiName(tenantId, describeApiName, fieldApiName)
      if(field == null) {
        return ["code": 500, "message": "不存在对应ID描述字段"]
      }
      MtDescribe describe = describeMapper.setTenantId(tenantId).findDescribeByTenantIdAndApiName(tenantId, describeApiName)
      if(describe == null || (describe.getStoreTableName()!=null && field.getFieldNum() == null)) {
        return ["code": 200, "info": ["message":"预设对象预设字段不支持更改"]]
      }
      String tableName = describe.getStoreTableName()
      String columnName = field.getFieldNum() == null ? fieldApiName : "value" + field.getFieldNum()
      if(StringUtils.isEmpty(tableName) || (field.getFieldNum() != null && !schemaHelper.isSpecialSchema(tenantId))){
        tableName = "mt_data"
      }
      String schema = "public"
      if(schemaHelper.isSpecialSchema(tenantId)) {
        schema = "sch_" + tenantId
      }
      if (["number", "currency","percentile"].contains(field.getType())) {
        if (!fieldType.equals("number") || StringUtils.isEmpty(attributeType) || !["length","decimal_places"].contains(attributeType)) {
          return ["code": 400, "message": "选择类型不对，或者属性类型不对"]
        }
        Integer currentLength = attributeType.equals("length")? field.getLength() : field.getDecimalPlaces();
        if (newCapacity <= currentLength || newCapacity <= 0) {
          return ["code": 200, "info": ["message":"目标属性长度小于当前长度，无需更新"]]
        }

        fieldMapper.setTenantId(tenantId).addNumberLengthById(attributeType, newCapacity - currentLength, tenantId, field.getFieldId())
        return ["code": 200, "info": ["message":"更新成功"]]

      } else {
        Map<String, Object> map = Maps.newHashMap()
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("SELECT ordinal_position column_id,COLUMN_NAME,DATA_TYPE,CHARACTER_MAXIMUM_LENGTH data_length," +
                " character_octet_length data_actual_length FROM information_schema.columns WHERE ")
        stringBuilder.append(SqlEscaper.pg_op_eq("table_name", tableName)).append(" AND ")
        stringBuilder.append(SqlEscaper.pg_op_eq("table_schema", schema)).append(" AND ")
        stringBuilder.append(SqlEscaper.pg_op_eq("column_name", columnName));
        Map<String, Object> columns = fieldMapper.setTenantId(tenantId).findFieldByDescribeId2(stringBuilder.toString()).get(0)
        int dataLength = columns.get("data_length") != null ? columns.get("data_length") : columns.get("data_actual_length") / 4
        if (dataLength != null && newCapacity <= dataLength) {
          if (field.getMaxLength() < newCapacity) {
            fieldMapper.setTenantId(tenantId).updateMaxLengthById(newCapacity, tenantId, field.getFieldId())
            map.put("message", "update field max_size success")
          } else {
            map.put("warn", "目标容量小于等于原容量，不进行操作")
          }
        } else {
          map.put("warn", "因为数据对应列的长度不满足扩容需求，建议新建自定义字段再扩容")
        }
        return ["code": 200, "info": JSONObject.toJSONString(map)]
      }
    } catch (Exception ignored){
      return ["code": 500, "message": "失败"]
    }
  }

  @RequestMapping("/switch/long/text")
  @ResponseBody
  @SystemControllerLog(description = "企业自定义对象描述字段扩容")
  def switchLongText(String fieldType, String tenantId, String describeApiName, String fieldApiName) {
    Field field = fieldMapper.setTenantId(tenantId).findFieldByApiName(tenantId, describeApiName, fieldApiName)
    if(field == null) {
      return ["code": 500, "message": "不存在对应ID描述字段"]
    }
    if (!fieldType.equals("text") || !"text".equals(field.getType())) {
      return ["code": 500, "message": "选中类型不对或字段类型不是单行文本"]
    }
    MtDescribe describe = describeMapper.setTenantId(tenantId).findDescribeByTenantIdAndApiName(tenantId, describeApiName)
    if(describe == null || (describe.getStoreTableName()!=null && field.getFieldNum() == null)) {
      return ["code": 200, "info": ["message":"预设对象预设字段不支持更改"]]
    }
    def result =query(tenantId, describeApiName, fieldApiName);
    if (result.get("code")!= 200) {
      return result;
    } else {
      Map map = JSONObject.parseObject(result.get("info") as String, Map.class);
      if(map.get("data_length") != null && map.get("data_length") < 2000) {
        return["code": 500, "message":"数据库列长度不支持转多行文本，提审批扩容列长后再进行操作"]
      }
      fieldMapper.setTenantId(tenantId).updateBySql(String.format(UPDATE_SQL, tenantId, field.getFieldId()));
      return ["code":200, "info": ["message":"已处理完毕"]]
    }
  }

}
