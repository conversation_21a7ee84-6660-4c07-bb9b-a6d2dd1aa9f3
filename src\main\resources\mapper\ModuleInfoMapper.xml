<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fxiaoke.paas.console.license.mapper.ModuleInfoMapper">

  <resultMap id="masterLicense" type="com.facishare.paas.license.pojo.MasterLicensePojo">
    <result property="moduleName" column="module_name"/>
    <result property="moduleCode" column="module_code"/>
  </resultMap>

  <resultMap id="moduleInfoEntity" type="com.fxiaoke.paas.console.entity.license.ModuleInfoEntity">
    <result property="soldOut" column="sold_out"/>
    <result property="moduleCode" column="module_code"/>
  </resultMap>

  <select id="queryModuleCodeByModuleCode" resultType="java.lang.String">
    SELECT DISTINCT module_code FROM module_info WHERE tenant_id = #{tenantId} AND del_flag is false AND module_code IN
    <foreach collection="moduleCodes" item="moduleCode" open="(" separator="," close=")">
      #{moduleCode}
    </foreach>
  </select>
  <select id="queryModuleInfoByProId2" resultMap="masterLicense">
    select mi.module_name,mi.module_code from product_info AS pi2 inner join module_info AS mi on pi2.id = mi.product_id
    where pi2.tenant_id = #{tenantId} AND mi.tenant_id = #{tenantId} AND mi.del_flag = FALSE AND pi2.del_flag = FALSE
    <if test="ids != null">
      AND pi2.id IN
      <foreach collection="ids" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
  </select>

  <select id="queryModuleByProductId" resultMap="moduleInfoEntity">
    select distinct module_code,sold_out  from module_info mi where tenant_id = 'default' and product_id = #{productId}
  </select>
</mapper>
