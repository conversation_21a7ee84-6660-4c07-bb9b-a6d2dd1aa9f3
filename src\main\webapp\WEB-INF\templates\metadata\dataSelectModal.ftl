<!-- 模态框（Modal） -->
<div class="modal fade" id="dataSelectModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                        class="sr-only">Close</span></button>
                <h4 class="modal-title" id="myModalLabel">选择时间</h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="host" class="col-sm-1 control-label">From</label>
                    <div class="col-sm-4">
                        <input type="text" class="form-control" id="startTime" value=""/>
                    </div>
                    <span class="glyphicon glyphicon-calendar"
                          onclick="WdatePicker({skin:'whyGreen',errDealMode:-1, dateFmt:'yyyy-MM-dd HH:mm:ss',vel:'00:00:00',el:'startTime', maxDate:'now', onpicked:function(dp){$('#startTime').change()}})"></span>
                </div>
                <br><br>
                <div class="form-group">
                    <label for="host" class="col-sm-1 control-label">To</label>
                    <div class="col-sm-4">
                        <input type="text" class="form-control" id="endTime" value="now()"/>
                    </div>
                    <span class="glyphicon glyphicon-calendar"
                          onclick="WdatePicker({skin:'whyGreen',errDealMode:-1, dateFmt:'yyyy-MM-dd HH:mm:ss',el:'endTime', maxDate:'now', onpicked:function(dp){$('#endTime').change()}})"></span>
                </div>
                <br>
                <button id="dateShowApply" style="margin-left: 30%" class="btn btn-info" data-dismiss="modal">Apply</button>
            </div>
            <div class="modal-header">
                <h4>快速选择</h4>
            </div>
            <div class="modal-body">
                <button id="dateShow24" class="btn btn-info btn-xs" value="24h" data-dismiss="modal">最近一天</button>
                <button id="dateShow72" class="btn btn-info btn-xs" value="72h" data-dismiss="modal">最近三天</button>
                <button id="dateShow5Day" class="btn btn-info btn-xs" value="5d" data-dismiss="modal">最近五天</button>
                <button id="dateShowOneMonth" class="btn btn-info btn-xs" value="1m" data-dismiss="modal">最近一个月</button>
                <button id="dateShowThreeMonth" class="btn btn-info btn-xs" value="3m" data-dismiss="modal">最近三个月</button>
                <button id="dateShowOneYear" class="btn btn-info btn-xs" value="1y" data-dismiss="modal">最近一年</button>
            </div>
            <div class="modal-footer">
                <a type="button" class="btn btn-default" data-dismiss="modal">返回</a>
            </div>
        </div>
    </div>
</div>



