package com.fxiaoke.paas.console.mapper.metadata

import lombok.extern.slf4j.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2018/3/6
 */
@ContextConfiguration(locations = ["classpath:mapperContext.xml"])
@Slf4j
class DescribeMapperTest extends Specification {

  @Autowired
  private DescribeMapper describeMapper

  def "多数据源测试"() {
    given:
    String displayName = describeMapper.setTenantId("2").dbTest()

    expect:
    displayName
  }

  def "describe列表测试"() {
    given:
    def describeList = describeMapper.setTenantId("2").describeListByTenantId("tenant_id='2'")

    expect:
    describeList
  }

  def "获取api_name和store_table_name测试"() {
    given:
    Map<String, String> map = describeMapper.setTenantId("2").findStoreTableNameAndApiName("74255","597fd7d4bab09ca40c20263d")

    expect:
    map.get("store_table_name")
  }
}
