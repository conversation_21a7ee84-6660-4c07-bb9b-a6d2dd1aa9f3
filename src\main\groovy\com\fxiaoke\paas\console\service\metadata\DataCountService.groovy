package com.fxiaoke.paas.console.service.metadata

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.fxiaoke.paas.console.bean.metadata.SchemaDbResource
import com.github.autoconf.ConfigFactory
import com.google.common.base.Splitter
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import groovy.util.logging.Slf4j
import lombok.Cleanup
import org.apache.commons.collections.CollectionUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import java.sql.Connection
import java.sql.DatabaseMetaData
import java.sql.ResultSet
import java.util.concurrent.atomic.AtomicInteger
import java.util.stream.Collectors

/**
 * <AUTHOR>
 * @date 2019/10/18 10:41 AM
 *
 */
@Service
@Slf4j
class DataCountService {

    private Map<String, SchemaDbResource> dbResourceMap = Maps.newHashMap()
    @Autowired
    private JdbcService jdbcService
    private Set<String> eiTableNames
    private Set<String> ignoreTables

    @PostConstruct
    void init() {
        ConfigFactory.getConfig("fs-paas-copier-url", { iConfig ->
            JSONArray jsonArray = JSONArray.parseArray(iConfig.getString())
            int size = jsonArray.size()
            for (int i = 0; i < size; i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i)
                List<String> tenantIds = JSONObject.parseObject(jsonObject.getString("tenantIds"), List.class)
                String oldUrl = jsonObject.getString("old")
                String newUrl = jsonObject.getString("new")
                if (CollectionUtils.isNotEmpty(tenantIds)) {
                    tenantIds.forEach({ tenantId -> dbResourceMap.put(tenantId, SchemaDbResource.builder().tenantId(tenantId).newUrl(newUrl).oldUrl(oldUrl).build()) })
                }
            }
        })
        ConfigFactory.getConfig("fs-paas-copier", { iConfig ->
            eiTableNames = Sets.newHashSet(Splitter.on(",").trimResults().omitEmptyStrings().splitToList(iConfig.get("use-ei-table")))
        })
        ConfigFactory.getConfig("fs-paas-service-describe", { iConfig ->
            ignoreTables = Sets.newHashSet(Splitter.on("|").omitEmptyStrings().trimResults().splitToList(iConfig.get("describe.api.oldTable")))
        })

    }


    List<List<String>> count(String tenantId) {
        SchemaDbResource schemaDbResource = dbResourceMap.get(tenantId)
        List<String> newTables = Lists.newArrayList()
        jdbcService.connection(schemaDbResource.newUrl).query("select tablename from pg_tables where schemaname=\'sch_" + tenantId + "\';", { rs ->
            while (rs.next()) {
                String tableName = rs.getString("tablename")
                if (ignoreTables.contains(tableName)) {
                    continue
                }
                newTables.add(tableName)
            }
        })
        List<String> old = Lists.newArrayList()
        jdbcService.connection(schemaDbResource.oldUrl).query("select tablename from pg_tables where schemaname=\'public\';", { rs ->
            while (rs.next()) {
                String tableName = rs.getString("tablename")
                if (ignoreTables.contains(tableName)) {
                    continue
                }
                old.add(tableName)
            }
        })
        List<String> oldTables = Lists.newArrayList()
        if (CollectionUtils.isEmpty(newTables)) {
            return Lists.newArrayList()
        }
        newTables.forEach({ table ->
            if (table.endsWith("__c") && !oldTables.contains("mt_data")) {
                oldTables.add("mt_data")
            } else if (!table.endsWith("__c")) {
                oldTables.add(table)
            }
        })
        List<List<String>> list = Lists.newArrayList()
        Map<String, List<String>> resultListMaps = Maps.newHashMap()
        oldTables.forEach({ table ->
            Set<String> columns = Sets.newHashSet()
            @Cleanup Connection connection = jdbcService.connection(schemaDbResource.oldUrl).connection()
            DatabaseMetaData databaseMetaData = connection.getMetaData()
            @Cleanup ResultSet resultSet1 = databaseMetaData.getColumns(null, "public", table, "%");
            while (resultSet1.next()) {
                columns.add(resultSet1.getString("COLUMN_NAME"))
            }
            if (!columns.contains("tenant_id")) {
                return
            }
            if (!old.contains(table)) {
                List<String> res = resultListMaps.computeIfAbsent(table, { k -> Lists.newArrayList() })
                res.add(table)
                res.add("0")
                return
            }
            if (table.equalsIgnoreCase("mt_data")) {
                try {
                    jdbcService.connection(schemaDbResource.oldUrl).query("select count(1) from public.mt_data where tenant_id = '" + tenantId + "' and object_describe_api_name like '%__c'", { rs ->
                        if (rs.next()) {
                            List<String> res = resultListMaps.computeIfAbsent("mt_data", { k -> Lists.newArrayList() })
                            res.add("mt_data")
                            res.add(String.valueOf(rs.getInt(1)))
                        } else {
                            List<String> res = resultListMaps.computeIfAbsent("mt_data", { k -> Lists.newArrayList() })
                            res.add("mt_data")
                            res.add("0")
                        }
                    })
                } catch (Exception e) {
                    log.error(e);
                }
            } else {
                try {
                    jdbcService.connection(schemaDbResource.oldUrl).query("select count(1) from public." + table + " where " + (eiTableNames.contains(table) ? "ei" : "tenant_id") + " = '" + tenantId + "';", { rs ->
                        if (rs.next()) {
                            List<String> res = resultListMaps.computeIfAbsent(table, { k -> Lists.newArrayList() })
                            res.add(table)
                            res.add(String.valueOf(rs.getInt(1)))
                        } else {
                            List<String> res = resultListMaps.computeIfAbsent(table, { k -> Lists.newArrayList() })
                            res.add(table)
                            res.add("0")
                        }
                    })

                } catch (Exception e) {
                    log.error(e);
                }
            }
        })
        int mtDataCount = 0
        newTables.forEach({
            table ->

                Set<String> columns = Sets.newHashSet()
                @Cleanup Connection connection = jdbcService.connection(schemaDbResource.newUrl).connection()
                DatabaseMetaData databaseMetaData = connection.getMetaData()
                @Cleanup ResultSet resultSet1 = databaseMetaData.getColumns(null, "sch_" + tenantId, table, "%")
                while (resultSet1.next()) {
                    columns.add(resultSet1.getString("COLUMN_NAME"))
                }
                if (!columns.contains("tenant_id")) {
                    return
                }

                if (table.endsWith("__c")) {
                    try {
                        jdbcService.connection(schemaDbResource.newUrl).query("select count(1) from sch_" + tenantId + "." + table + " where tenant_id = '" + tenantId + "';", { rs ->
                            if (rs.next()) {
                                mtDataCount += rs.getInt(1)
                                List<String> list1 = resultListMaps.computeIfAbsent(table, { k -> Lists.newArrayList() })
                                if (list1.size() == 0) {
                                    AtomicInteger atomicInteger = new AtomicInteger(0)
                                    jdbcService.connection(schemaDbResource.oldUrl).query("select count(1) from public.mt_data where tenant_id = '" + tenantId + "' and object_describe_api_name ilike '" + table + "';", { resultSet ->
                                        if (resultSet.next()) {
                                            atomicInteger.set(resultSet.getInt(1))
                                        }
                                    })
                                    list1.add(table)
                                    list1.add(atomicInteger.get())
                                    list1.add(String.valueOf(rs.getInt(1)))

                                } else if (list1.size() == 1) {
                                    list1.add(String.valueOf(rs.getInt(1)))
                                }
                            }
                        })
                    } catch (Exception e) {
                        log.error(e);
                    }
                } else {
                    try {
                        jdbcService.connection(schemaDbResource.newUrl).query("select count(1) from sch_" + tenantId + "." + table + " where " + (eiTableNames.contains(table) ? "ei" : "tenant_id") + " = '" + tenantId + "';", { rs ->
                            if (rs.next()) {
                                resultListMaps.computeIfAbsent(table, { k -> Lists.newArrayList() }).add(String.valueOf(rs.getInt(1)))
                            } else {
                                resultListMaps.computeIfAbsent(table, { k -> Lists.newArrayList() }).add("0")
                            }

                        })
                    } catch (Exception e) {
                        log.error(e);
                    }
                }

        })

        List<String> mtDataList = resultListMaps.computeIfAbsent("mt_data", { k -> Lists.newArrayList() })
        if (mtDataList.size() == 0) {
            mtDataList.add("mt_data")
            mtDataList.add("0")
            mtDataList.add(String.valueOf(mtDataCount))
        } else if (mtDataList.size() == 2) {
            mtDataList.add(String.valueOf(mtDataCount))
        }

        resultListMaps.forEach({ k, v ->
            list.add(v)
        })

        return list.stream().filter({ o -> !String.valueOf(o.get(1)).equals(String.valueOf(o.get(2))) }).sorted({ o1, o2 -> o1.get(0).compareTo(o2.get(0)) }).collect(Collectors.toList())
    }


}
