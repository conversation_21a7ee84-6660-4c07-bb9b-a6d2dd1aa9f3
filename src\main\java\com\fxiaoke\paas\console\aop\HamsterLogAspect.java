package com.fxiaoke.paas.console.aop;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.license.util.IdUtil;
import com.fxiaoke.paas.console.annotation.HamsterControllerLog;
import com.fxiaoke.paas.console.log.HamsterConsoleAuditLog;
import com.fxiaoke.paas.console.service.hamster.HamsterConsoleLogService;
import com.github.shiro.support.ShiroCasRealm;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Aspect
@Component
public class HamsterLogAspect {
  @Autowired
  HamsterConsoleLogService service;

  @Resource(name = "casRealm")
  private ShiroCasRealm cas;

  // Controller层切点 （注解路径）
  @Pointcut(value = "@annotation(com.fxiaoke.paas.console.annotation.HamsterControllerLog)")
  void controllerAspect() {
  }

  @Around(value = "controllerAspect()")
  Object getLogAfter(ProceedingJoinPoint joinPoint) throws Throwable {
    String description = this.getControllerMethodDescription(joinPoint);
    HamsterConsoleAuditLog auditLog = new HamsterConsoleAuditLog();
    auditLog.setId(IdUtil.generateId());
    auditLog.setDescription(description);
    auditLog.setExecutor(cas.getCurrentUser().getDisplayName());
    auditLog.setArgument(JSON.toJSONString(joinPoint.getArgs()));
    auditLog.setExecuteTime(System.currentTimeMillis());
    Object proceed = null;
    try {
      proceed = joinPoint.proceed();
    } catch (Exception e) {
      log.error("getLogAfter is fail :{},", description, e);
    }
    service.insertHamsterConsoleLog(auditLog);
    return proceed;
  }

  private String getControllerMethodDescription(JoinPoint joinPoint) {
    return ((MethodSignature) joinPoint.getSignature()).getMethod().getAnnotation(HamsterControllerLog.class).description();
  }

}
