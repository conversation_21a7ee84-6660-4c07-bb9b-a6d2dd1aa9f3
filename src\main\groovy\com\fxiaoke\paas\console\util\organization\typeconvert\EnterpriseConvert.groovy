package com.fxiaoke.paas.console.util.organization.typeconvert

import com.alibaba.fastjson.JSONObject
import com.facishare.uc.api.model.fscore.RunStatus
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData
import com.fxiaoke.paas.console.bean.organization.object.EnterpriseObject

import java.text.SimpleDateFormat

/**
 * Created by wangxing on 2018/03/13
 */
class EnterpriseConvert {

  static EnterpriseObject enterpriseDataConvertEnterpriseObject(SimpleEnterpriseData simpleEnterpriseData) {
    EnterpriseObject enterpriseObject = new EnterpriseObject()
    enterpriseObject.setEnterpriseId(simpleEnterpriseData.getEnterpriseId())
    enterpriseObject.setEnterpriseAccount(simpleEnterpriseData.getEnterpriseAccount())
    enterpriseObject.setEnterpriseName(simpleEnterpriseData.getEnterpriseName())
    if (simpleEnterpriseData.getCreateTime() != null) {
      SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日 HH:mm:SS")
      String creaetTime = simpleDateFormat.format(simpleEnterpriseData.getCreateTime())
      enterpriseObject.setCreateTime(creaetTime)
    }
    if (simpleEnterpriseData.getRunStatus() == RunStatus.RUN_STATUS_READY) {
      enterpriseObject.setRunStatus("准备中")
    } else if (simpleEnterpriseData.getRunStatus() == RunStatus.RUN_STATUS_WAIT) {
      enterpriseObject.setRunStatus("待开通")
    } else if (simpleEnterpriseData.getRunStatus() == RunStatus.RUN_STATUS_NORMAL) {
      enterpriseObject.setRunStatus("已开通")
    } else if (simpleEnterpriseData.getRunStatus() == RunStatus.RUN_STATUS_INVALIDATE) {
      enterpriseObject.setRunStatus("已作废")
    } else if (simpleEnterpriseData.getRunStatus() == RunStatus.RUN_STATUS_STOP) {
      enterpriseObject.setRunStatus("已停用")
    } else if (simpleEnterpriseData.getRunStatus() == RunStatus.RUN_STATUS_DELETE) {
      enterpriseObject.setRunStatus("已删除")
    }
    return enterpriseObject
  }

}
