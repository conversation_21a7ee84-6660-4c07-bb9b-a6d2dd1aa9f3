package com.fxiaoke.paas.console.license.mapper;

import com.facishare.paas.license.pojo.MasterLicensePojo
import com.facishare.paas.license.pojo.ModuleParaPojo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
interface ModuleParaMapper {

  List<MasterLicensePojo> selectAllPara(@Param("tenantId")String tenantId, @Param("productIds")List<String> productIds);

  List<MasterLicensePojo> selectAllParaByModuleId(@Param("tenantId")String tenantId, @Param("moduleId")String moduleId);

  ModuleParaPojo selectPara(@Param("tenantId")String tenantId, @Param("productId")String productId);
}
