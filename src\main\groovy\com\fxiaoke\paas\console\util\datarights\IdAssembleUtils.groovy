package com.fxiaoke.paas.console.util.datarights

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils

/**
 * Created by yangxw on 2018/3/16.
 */
class IdAssembleUtils {

    def static getShareCache(JSONArray idList, String type) {
        List<String> shareCacheList = new ArrayList<>();
        if (CollectionUtils.isEmpty(idList)) {
            return shareCacheList;
        }
        Iterator it = idList.iterator()
        JSONObject jsonObject;
        String id;
        while (it.hasNext()) {
            jsonObject = it.next()
            id = jsonObject.getString("receiveUser")
            if (StringUtils.isNotBlank(jsonObject.getString("receiveType")) && StringUtils.isNotBlank(id) && !shareCacheList.contains(id) && jsonObject.getString("receiveType").equals(type)) {
                shareCacheList.add(id);
            }
        }
        if (type.equals("用户")) {
            Iterator it2 = idList.iterator()
            while (it2.hasNext()) {
                jsonObject = it2.next()
                id = jsonObject.getString("shareUser")
                if (StringUtils.isNotBlank(id) && !shareCacheList.contains(id)) {
                    shareCacheList.add(id)
                }
            }
        }
        return shareCacheList;
    }

    def static getShare(JSONArray idList, String type) {
        List<String> shareCacheList = new ArrayList<>();
        if (CollectionUtils.isEmpty(idList)) {
            return shareCacheList;
        }
        Iterator it = idList.iterator()
        JSONObject jsonObject;
        String id;
        while (it.hasNext()) {
            jsonObject = it.next()
            id = jsonObject.getString("receiveId")
            if (StringUtils.isNotBlank(jsonObject.getString("receiveId")) && StringUtils.isNotBlank(id) && !shareCacheList.contains(id) && jsonObject.getString("receiveType").equals(type)) {
                shareCacheList.add(id);
            }
            id = jsonObject.getString("shareId")
            if (StringUtils.isNotBlank(jsonObject.getString("shareId")) && StringUtils.isNotBlank(id) && !shareCacheList.contains(id) && jsonObject.getString("shareType").equals(type)) {
                shareCacheList.add(id);
            }
        }
        if (type.equals("用户")) {
            Iterator it2 = idList.iterator()
            while (it2.hasNext()) {
                jsonObject = it2.next()
                id = jsonObject.getString("creator")
                if (StringUtils.isNotBlank(id) && !shareCacheList.contains(id)) {
                    shareCacheList.add(id)
                }
                id = jsonObject.getString("modifier")
                if (StringUtils.isNotBlank(id) && !shareCacheList.contains(id)) {
                    shareCacheList.add(id)
                }
            }
        }
        return shareCacheList;
    }


}
