package com.fxiaoke.paas.console.web.organization

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.facishare.paas.pod.client.DbRouterClient
import com.facishare.paas.pod.dto.RouterInfo
import com.facishare.paas.pod.util.DialectUtil
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData
import com.facishare.uc.api.service.EnterpriseEditionService
import com.fxiaoke.helper.StringHelper
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.bean.organization.arg.DepartmentArg
import com.fxiaoke.paas.console.bean.organization.arg.DeptUserArg
import com.fxiaoke.paas.console.bean.organization.arg.EmployeeArg
import com.fxiaoke.paas.console.bean.organization.object.DataResponse
import com.fxiaoke.paas.console.bean.organization.object.DepartmentObject
import com.fxiaoke.paas.console.bean.organization.object.EmployeeObject
import com.fxiaoke.paas.console.bean.organization.object.EnterpriseObject
import com.fxiaoke.paas.console.service.metadata.SqlServerRouteService
import com.fxiaoke.paas.console.service.organization.OrganizationService
import com.fxiaoke.paas.console.util.organization.typeconvert.EnterpriseConvert
import com.google.common.base.CharMatcher
import com.google.common.base.Splitter
import com.google.common.base.Strings
import com.google.common.collect.Lists
import groovy.util.logging.Slf4j
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.*

/**
 * Created by wangxing on 2018/03/06
 */
@Controller
@Slf4j
@RequestMapping("/organization")
class OrganizationAdminController {

  @Autowired
  OrganizationService organizationService
  @Autowired
  EnterpriseEditionService enterpriseEditionService
  @Autowired
  private EnterpriseEditionService enterpriseEditionService

  @Autowired
  private DbRouterClient dbRouterClient
  @Autowired
  SqlServerRouteService serverRouteService
  /**
   * 通过企业ID查询所有部门ID
   * @param tenantId 企业ID
   * @return 部门ID集合
   */
  @RequestMapping("/departmentId")
  @ResponseBody
  @SystemControllerLog(description = "组织架构 -- 通过企业ID查询所有部门ID")
  def getDepartmentIdByCompanyId(String tenantId) {
    List<DepartmentObject> departmentIdList = new ArrayList<>()
    if (tenantId != null && tenantId.length() != 0) {
      departmentIdList = organizationService.getDepartmentIdByCompanyId(tenantId.trim())
    }
    departmentIdList
  }

  /**
   * 通过企业ID和部门ID查询部门信息
   * @param request
   * @param param
   * @return
   */
  @RequestMapping(value = "/department")
  @ResponseBody
  @SystemControllerLog(description = "组织架构 -- 通过企业ID和部门ID查询部门信息")
  def getDepartmentInfo(@RequestParam String tenantId, @RequestParam String deptIds) {
    List<DepartmentObject> departmentList = new ArrayList<>()
    if (!Strings.isNullOrEmpty(tenantId)) {
      DepartmentArg departmentArg = new DepartmentArg()
      departmentArg.setTenantId(tenantId.trim())
      List<String> deptIdList = Lists.newArrayList()
      deptIdList = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(deptIds.trim())
      if (deptIdList.size() > 0) {
        departmentArg.deptIds = deptIdList
        departmentList = organizationService.getDepartment(departmentArg)
      }
    }
    ["data": departmentList]
  }

  /**
   * 通过企业ID和部门ID查询子部门信息
   * @param request
   * @return
   */
  @RequestMapping("/subordinateDepartment")
  @ResponseBody
  @SystemControllerLog(description = "组织架构 -- 通过企业ID和部门ID查询子部门信息")
  def getSubordinateDepartmentInfo(@RequestParam String tenantId, @RequestParam String parentId) {
    DataResponse<DepartmentObject> dataResponse = new DataResponse<>()
    List<DepartmentObject> subordinateDepartments = new ArrayList<>()
    if (!Strings.isNullOrEmpty(tenantId)) {
      DepartmentArg departmentArg = new DepartmentArg()
      departmentArg.setTenantId(tenantId.trim())
      departmentArg.setParentId(parentId.trim())
      subordinateDepartments = organizationService.getSubordinateDepartment(departmentArg)
    }
    ["data": subordinateDepartments]
  }

  /**
   * 通过企业ID，用户ID,部门用户关系类型查询其所在部门信息
   * @return request
   */
  @RequestMapping("/relationalDepartment")
  @ResponseBody
  @SystemControllerLog(description = "组织架构 -- 通过企业ID，用户ID,部门用户关系类型查询其所在部门信息")
  def getRelationalDeparment(@RequestParam String tenantId, @RequestParam String userId, @RequestParam String type) {
    List<DepartmentObject> departmentObjects = new ArrayList<>()
    if (!Strings.isNullOrEmpty(tenantId)) {
      DeptUserArg deptUserArg = new DeptUserArg()
      deptUserArg.setTenantId(tenantId.trim())
      deptUserArg.setUserId(userId.trim())
      if ("" == type) {
        deptUserArg.setType(null)
      } else {
        deptUserArg.setType(Integer.valueOf(type.trim()))
      }
      departmentObjects = organizationService.getRelationalDepartment(deptUserArg)
    }
    ["data": departmentObjects]
  }

  /**
   * 通过企业ID查询所有员工ID
   * @param tenantId 企业ID
   * @return
   */
  @RequestMapping("/employeeId")
  @ResponseBody
  @SystemControllerLog(description = "组织架构 -- 通过企业ID查询所有员工ID")
  def getEmployeeIdByCompanyId(String tenantId) {
    List<EmployeeObject> employeeIdList = new ArrayList<>()
    if (tenantId != null && tenantId.length() != 0) {
      employeeIdList = organizationService.getEmployeeIdByCompanyId(tenantId.trim())
    }
    employeeIdList
  }

  /**
   * 通过企业ID，员工ID查询员工信息
   * @return
   */
  @RequestMapping(value = "/employee", method = RequestMethod.POST)
  @ResponseBody
  @SystemControllerLog(description = "组织架构 -- 通过企业ID，员工ID查询员工信息")
  def getEmployeeInfo(@RequestBody String data) {
    String tenantId = JSON.parseObject(data).get("tenantId")
    String userIds = JSON.parseObject(data).get("userIds")

    List<EmployeeObject> employeeObjects = new ArrayList<>()
    if (Strings.isNullOrEmpty(tenantId)) {
      return ["data": new EmployeeObject()]
    }
    EmployeeArg employeeArg = new EmployeeArg()
    employeeArg.setTenantId(tenantId.trim())
    List<String> userIdList = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(userIds.trim())
    if (userIdList.size() > 0) {
      employeeArg.userIds = userIdList
      employeeObjects = organizationService.getEmployee(employeeArg)
    }
    //获取用户的所属部门
    List<Map<String, String>> deptNameList = organizationService.getDeptByTenantIdAndUserId(tenantId, userIdList)
    if (!deptNameList.isEmpty()) {
      employeeObjects.each { employee ->
        List<String> departmentName = Lists.newArrayList()
        deptNameList.each { deptMap ->
          if (employee.userId == deptMap.get("user_id")) {
            departmentName.add(deptMap.get("department_name"))
          }
        }
        departmentName = new ArrayList(new HashSet(departmentName))
        employee.setDepartment(departmentName.join(",").replace(",", ",<br>"))
      }
    }

    ["data": employeeObjects]
  }

  /**
   * 通过企业ID,部门ID,部门员工关系类型查询其所有的员工信息
   * @param request
   * @return
   */
  @RequestMapping("/relationalEmployee")
  @ResponseBody
  @SystemControllerLog(description = "组织架构 -- 通过企业ID,部门ID,部门员工关系类型查询其所有的员工信息")
  def getRelationalEmployee(@RequestParam String tenantId, @RequestParam String departmentId, @RequestParam String type) {
    List<EmployeeObject> employeeObjects = new ArrayList<>()
    if (!Strings.isNullOrEmpty(tenantId)) {
      DeptUserArg deptUserArg = new DeptUserArg()
      deptUserArg.setTenantId(tenantId.trim())
      deptUserArg.setDepartmentId(departmentId.trim())
      if ("" == type) {
        deptUserArg.setType(null)
      } else {
        deptUserArg.setType(Integer.valueOf(type.trim()))
      }
      employeeObjects = organizationService.getRelationalEmployee(deptUserArg)
    }
    ["data": employeeObjects]
  }

  /**
   * 通过手机号查询员工信息
   * @param request
   * @return
   */
  @RequestMapping(value = "/employeeByTel")
  @ResponseBody
  @SystemControllerLog(description = "组织架构 -- 通过手机号查询员工信息")
  def getEmployeeByTel(@RequestParam String tel) {
    List<EmployeeObject> employeeObjects = new ArrayList<>()
    if (!Strings.isNullOrEmpty(tel)) {
      employeeObjects = organizationService.getEmployeeByTel(tel.trim())
    }
    if (employeeObjects.isEmpty()) {
      employeeObjects.add(EmployeeObject.builder().userId("不存在").build())
    }
    ["data": employeeObjects]
  }

  /**
   * 查询企业信息
   * @param request
   * @return
   */
  @RequestMapping(value = "/enterpriseInfo")
  @ResponseBody
//  @SystemControllerLog(description = "组织架构 -- 查询企业信息")
  def simpleEnterpriseData(@RequestParam String tenantIds, @RequestParam String accounts) {
    try {
      if (StringHelper.isNullOrBlank(tenantIds) && StringHelper.isNullOrBlank(accounts)) {
        return ["code": 200, "data": null]
      }
      BatchGetSimpleEnterpriseDataArg batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(
              enterpriseIds: tenantIds?.tokenize(",")*.trim().findAll { it -> it =~ /^\d+$/ }.collect { it as Integer },
              enterpriseAccounts: accounts?.tokenize(",")*.trim().findAll { it } as List<String>
      )
      def batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
      return ["code": "200",
              "data": batchGetSimpleEnterpriseDataResult.getSimpleEnterpriseList()?.collect {
                EnterpriseConvert.enterpriseDataConvertEnterpriseObject(it)
              }
      ]
    } catch (Exception e) {
      log.error("simpleEnterpriseData failed", e)
      return ["code": "500", "data": null, "message": e.message]
    }
  }

  @RequestMapping("/query-rout")
  @ResponseBody
  def findRout(@RequestParam String param) {
    String tenantId = param

    BatchGetSimpleEnterpriseDataArg batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(
            enterpriseIds: null,
            enterpriseAccounts: Arrays.asList(tenantId))

    BatchGetSimpleEnterpriseDataResult batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
    List<SimpleEnterpriseData> enterpriseList = batchGetSimpleEnterpriseDataResult.simpleEnterpriseList
    if (CollectionUtils.isNotEmpty(enterpriseList)) {
      SimpleEnterpriseData simpleEnterpriseData = enterpriseList.get(0)
      if (simpleEnterpriseData != null && simpleEnterpriseData.enterpriseId != 0 && simpleEnterpriseData.enterpriseAccount.equals(tenantId)) {
        tenantId = simpleEnterpriseData.enterpriseId
      }
    }
    try {
      RouterInfo result = dbRouterClient.queryRouterInfo(tenantId, "CRM", "application", DialectUtil.POSTGRESQL)
      RouterInfo resultBi = dbRouterClient.queryRouterInfo(tenantId, "BI", "application", DialectUtil.POSTGRESQL)
      ["code": 200, "jdbcUrl": result.jdbcUrl, "standalone": result.standalone, "biJdbcUrl": resultBi.jdbcUrl, "biStandalone": resultBi.standalone]
    } catch (Exception e) {
      log.error("查询路由异常，error:", e)
      ["code": 500, "error": "查询失败,请检查企业ID是否正确"]
    }
  }

/**
 * 部门树状图
 * @param request
 * @return
 */
  @RequestMapping(value = "/departmentTree", method = RequestMethod.POST)
  @ResponseBody
  @SystemControllerLog(description = "组织架构 -- 部门树状图")
  def testTree(@RequestParam String tenantId, @RequestParam String parentId) {
    List<DepartmentObject> departmentObjectList = Lists.newArrayList()
    if (!Strings.isNullOrEmpty(tenantId)) {
      departmentObjectList = organizationService.departmentTree(tenantId.trim(), parentId.trim())
    }
    return JSONObject.toJSON(departmentObjectList)
  }

}
