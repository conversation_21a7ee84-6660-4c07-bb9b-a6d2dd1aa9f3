package com.fxiaoke.paas.console.mapper.metadata

import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * <AUTHOR>
 * Created on 2018/3/19.
 */
@ContextConfiguration(value = ["classpath:mapperContext.xml"])
@Slf4j
class RuleMapperTest extends Specification {

  @Autowired
  RuleMapper ruleMapper

  def "findRuleByTenantId"() {
    given:
    def tenantId = "2"
    expect:
    ruleMapper.setTenantId(tenantId).findRuleByTenantId(tenantId)
  }

  def "findRuleByRuleId"() {
    given:
    def tenantId = "2"
    def ruleId = "5a65600a830bdb880b76eb12"
    expect:
    ruleMapper.setTenantId(tenantId).findRuleByRuleId(ruleId)
  }

}
