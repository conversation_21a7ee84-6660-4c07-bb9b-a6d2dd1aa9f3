<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">


  <bean class="com.fxiaoke.paas.console.service.metadata.SpecialTableService">
    <property name="createSpecialTableSql">
      <value>
        CREATE TABLE IF NOT EXISTS public.dt_SPECIAL_TABLE_NAME_auth
        (
        id character varying(64) COLLATE pg_catalog."default" NOT NULL,
        tenant_id character varying(64) COLLATE pg_catalog."default" NOT NULL,
        pkg character varying(64) COLLATE pg_catalog."default" NOT NULL,
        object_describe_api_name character varying(64) COLLATE pg_catalog."default" NOT NULL,
        object_id character varying(64) COLLATE pg_catalog."default" NOT NULL,
        owner character varying(64) COLLATE pg_catalog."default",
        last_modified_time bigint,
        tenant_id_object_id character varying(128) COLLATE pg_catalog."default",
        read_permission_users character varying[] COLLATE pg_catalog."default",
        write_permission_users character varying[] COLLATE pg_catalog."default",
        participants character varying[] COLLATE pg_catalog."default",
        shared_users_depts_roles character varying[] COLLATE pg_catalog."default",
        relate_rules character varying[] COLLATE pg_catalog."default",
        CONSTRAINT dt_SPECIAL_TABLE_NAME_auth_pkey PRIMARY KEY (id, tenant_id)
        )
        WITH (
        OIDS = FALSE
        )
        TABLESPACE pg_default;

        CREATE INDEX IF NOT EXISTS i_SPECIAL_TABLE_NAME_auth_tid_apin_lmt
        ON public.dt_SPECIAL_TABLE_NAME_auth USING btree
        (tenant_id COLLATE pg_catalog."default", object_describe_api_name COLLATE pg_catalog."default", last_modified_time DESC)
        TABLESPACE pg_default;

        CREATE INDEX IF NOT EXISTS i_dt_SPECIAL_TABLE_NAME_auth_participants
        ON public.dt_SPECIAL_TABLE_NAME_auth USING gin
        (participants COLLATE pg_catalog."default")
        TABLESPACE pg_default;

        CREATE INDEX IF NOT EXISTS i_dt_SPECIAL_TABLE_NAME_auth_read_permission_users
        ON public.dt_SPECIAL_TABLE_NAME_auth USING gin
        (read_permission_users COLLATE pg_catalog."default")
        TABLESPACE pg_default;

        CREATE INDEX IF NOT EXISTS i_dt_SPECIAL_TABLE_NAME_auth_relate_rules
        ON public.dt_SPECIAL_TABLE_NAME_auth USING gin
        (relate_rules COLLATE pg_catalog."default")
        TABLESPACE pg_default;

        CREATE INDEX IF NOT EXISTS i_dt_SPECIAL_TABLE_NAME_auth_shared_users
        ON public.dt_SPECIAL_TABLE_NAME_auth USING gin
        (shared_users_depts_roles COLLATE pg_catalog."default")
        TABLESPACE pg_default;

        DROP INDEX IF EXISTS i_dt_SPECIAL_TABLE_NAME_auth_tenant_id_data_id;
        CREATE INDEX IF NOT EXISTS i_dt_SPECIAL_TABLE_NAME_auth_tid_did
        ON public.dt_SPECIAL_TABLE_NAME_auth USING btree
        (tenant_id_object_id COLLATE pg_catalog."default")
        TABLESPACE pg_default;

        DROP INDEX IF EXISTS i_dt_SPECIAL_TABLE_NAME_auth_tenant_id_object_id;
        CREATE INDEX IF NOT EXISTS i_dt_SPECIAL_TABLE_NAME_auth_tid_oid
        ON public.dt_SPECIAL_TABLE_NAME_auth USING btree
        (tenant_id COLLATE pg_catalog."default", object_id COLLATE pg_catalog."default")
        TABLESPACE pg_default;

        CREATE INDEX IF NOT EXISTS i_dt_SPECIAL_TABLE_NAME_auth_tid_oid_owner_odan
        ON public.dt_SPECIAL_TABLE_NAME_auth USING btree
        (tenant_id COLLATE pg_catalog."default", owner COLLATE pg_catalog."default", object_describe_api_name COLLATE pg_catalog."default")
        TABLESPACE pg_default;

        CREATE INDEX IF NOT EXISTS i_dt_SPECIAL_TABLE_NAME_auth_write_permission_users
        ON public.dt_SPECIAL_TABLE_NAME_auth USING gin
        (write_permission_users COLLATE pg_catalog."default")
        TABLESPACE pg_default;

        CREATE UNIQUE INDEX IF NOT EXISTS i_u_dt_SPECIAL_TABLE_NAME_auth_tid_oid_odan
        ON public.dt_SPECIAL_TABLE_NAME_auth USING btree
        (tenant_id COLLATE pg_catalog."default", object_id COLLATE pg_catalog."default", object_describe_api_name COLLATE pg_catalog."default")
        TABLESPACE pg_default;

        ALTER TABLE dt_SPECIAL_TABLE_NAME_auth ADD COLUMN IF NOT EXISTS sales_waiters CHARACTER VARYING [];
        ALTER TABLE dt_SPECIAL_TABLE_NAME_auth ADD COLUMN IF NOT EXISTS followers CHARACTER VARYING [];
        CREATE INDEX IF NOT EXISTS i_dt_SPECIAL_TABLE_NAME_auth_saleswaiters ON public.dt_SPECIAL_TABLE_NAME_auth USING gin (sales_waiters COLLATE pg_catalog."default") TABLESPACE pg_default;
        CREATE INDEX IF NOT EXISTS i_dt_SPECIAL_TABLE_NAME_auth_followers ON public.dt_SPECIAL_TABLE_NAME_auth USING gin (followers COLLATE pg_catalog."default") TABLESPACE pg_default;
      </value>
    </property>
  </bean>

</beans>