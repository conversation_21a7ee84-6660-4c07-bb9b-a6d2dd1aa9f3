//package com.fxiaoke.paas.console.util.workflow
//
//import com.facishare.paas.workflow.kernel.entity.TaskEntity
//import com.facishare.paas.workflow.kernel.entity.WorkflowEntity
//import com.facishare.paas.workflow.kernel.entity.WorkflowInstanceEntity
//import com.fxiaoke.paas.console.bean.workflow.TaskPojo
//import com.fxiaoke.paas.console.bean.workflow.WorkflowInstancePojo
//import com.fxiaoke.paas.console.bean.workflow.WorkflowPojo
//import com.fxiaoke.paas.console.bean.workflow.WorkflowRule
//import com.fxiaoke.paas.console.bean.workflow.WorkflowRulePojo
//import groovy.util.logging.Slf4j
//import org.apache.commons.collections.CollectionUtils
//import org.apache.commons.lang3.StringUtils
//
///**
// * Created by yangxw on 2018/3/5.
// */
//@Slf4j
//class EntityToPojoUtil {
//
//  def static entityToworkflowPojo(WorkflowEntity workflowEntity) {
//
//    if (null == workflowEntity) {
//      return null
//    }
//    if (null == workflowEntity.getId()) {
//      log.info("WorkflowEntity = {} has no id", workflowEntity)
//      return null
//    }
//    WorkflowPojo workflowPojo = new WorkflowPojo()
//    workflowPojo.setId(workflowEntity.getId().toString())
//    workflowPojo.setTenantId(workflowEntity.getTenantId())
//    workflowPojo.setAppName(AppEntityUtil.appIdToAppName(workflowEntity.getAppId()))
//    workflowPojo.setEntityName(workflowEntity.getEntityId())
//    workflowPojo.setCreateTime(ToShowStringUtil.getTimeString(workflowEntity.getCreateTime()))
//    workflowPojo.setCreator(ToShowStringUtil.getShowObject(workflowEntity.getCreator()))
//    workflowPojo.setFlowType(ToShowStringUtil.getFlowType(workflowEntity.getType()))
//    workflowPojo.setSourceWorkflowId(workflowEntity.getSourceWorkflowId())
//    workflowPojo.setModifier(ToShowStringUtil.getShowObject(workflowEntity.getModifier()))
//    workflowPojo.setModify_time(ToShowStringUtil.getTimeString(workflowEntity.getModifyTime()))
//    //workflowPojo.setCreatorAccount(this.getShowObject(creatorAccount))
//    workflowPojo.setName(ToShowStringUtil.getShowObject(workflowEntity.getName()))
//    workflowPojo.setDescription(ToShowStringUtil.getShowObject(workflowEntity.getDescription()))
//    workflowPojo.setEnable(ToShowStringUtil.convertEnableByBoolean(workflowEntity.getEnable()))
//    return workflowPojo
//
//  }
//
//
//  def static processWorkflowPojoList(List<WorkflowPojo> WorkflowPojoList) {
//    if (CollectionUtils.isNotEmpty(WorkflowPojoList)) {
//      for (WorkflowPojo workflowPojo : WorkflowPojoList) {
//        processWorkflowPojo(workflowPojo)
//      }
//    }
//  }
//
//  def static processWorkflowPojo(WorkflowPojo workflowPojo) {
//
//    if (null != WorkflowPojo) {
//      String createTime = workflowPojo.getCreateTime()
//      String appName = workflowPojo.getAppName()
//      String creator = workflowPojo.getCreator()
//      String flowType = workflowPojo.getFlowType()
//      String name = workflowPojo.getName()
//      String description = workflowPojo.getDescription()
//      String enable = workflowPojo.getEnable()
//      workflowPojo.setCreateTime(ToShowStringUtil.getTimeString(Long.parseLong(createTime)))
//      workflowPojo.setAppName(AppEntityUtil.appIdToAppName(appName))
//      workflowPojo.setCreator(ToShowStringUtil.getShowObject(creator))
//      workflowPojo.setFlowType(ToShowStringUtil.getFlowType(flowType))
//      workflowPojo.setName(ToShowStringUtil.getShowObject(name))
//      workflowPojo.setDescription(ToShowStringUtil.getShowObject(description))
//      if (StringUtils.isNotBlank(enable)) {
//        workflowPojo.setEnable(ToShowStringUtil.convertEnable(enable))
//      }
//    }
//
//  }
//
//  def static entityToWorkflowRulePojo(WorkflowRule workflowRule, String creatorAccount, String modifierAccount) {
//
//    if (null == workflowRule) {
//      return null;
//    }
//    WorkflowRulePojo workflowRulePojo = new WorkflowRulePojo()
//    workflowRulePojo.setId(workflowRule.get_id().toString())
//    workflowRulePojo.setTenantId(workflowRule.getTenantId())
//    workflowRulePojo.setAppName(AppEntityUtil.appIdToAppName(workflowRule.getAppId()))
//    workflowRulePojo.setEntityName(AppEntityUtil.entityIdToName(workflowRule.getAppId(), workflowRule.getEntityId()))
//    workflowRulePojo.setRuleType(ToShowStringUtil.getFlowType(workflowRule.getRuleType()))
//    workflowRulePojo.setActionTypes(ToShowStringUtil.getTriggerActionTypes(workflowRule.getActionTypes()))
//    workflowRulePojo.setDescription(workflowRule.getDescription())
//    workflowRulePojo.setExecutionType(ToShowStringUtil.getExecutionType(workflowRule.getExecutionType()))
//    workflowRulePojo.setConditionPattern(workflowRule.getConditionPattern())
//    workflowRulePojo.setConditions(FieldEntityToFieldObject.entityListToExpressionObjectList(workflowRule.getConditions()))
//    workflowRulePojo.setWorkflowSrcId(workflowRule.getWorkflowSrcId())
//    workflowRulePojo.setCreator(workflowRule.getCreator())
//    workflowRulePojo.setCreatorAccount(creatorAccount)
//    workflowRulePojo.setModifyTime(ToShowStringUtil.getTimeString(null == workflowRule.getModifyTime() ? workflowRule.getCreateTime() : workflowRule.getModifyTime()))
//    workflowRulePojo.setModifier(workflowRule.getModifier())
//    workflowRulePojo.setModifierAccount(modifierAccount)
//    workflowRulePojo.setTriggerConditions(ToShowStringUtil.getTriggerConditionShow(workflowRule.getConditionPattern(), workflowRule.getConditions()))
////        workflowRulePojo.setDeleted(workflowRule.isDeleted() ? ENABLED : NOT_ENABLED)
//    if (null == workflowRule.get_id()) {
//      log.info("WorkflowRule = {} has no id", workflowRule)
//      workflowRulePojo.setId("null")
//    }
//
//    return workflowRulePojo
//  }
//
//  def static entityToworkflowInstancePojo(WorkflowInstanceEntity workflowInstanceEntity, String applicantInfo) {
//
//    if (null == workflowInstanceEntity) {
//      return null
//    }
//    if (null == workflowInstanceEntity.getId()) {
//      log.info("WorkflowInstanceEntity = {} has no id", workflowInstanceEntity)
//      return null
//    }
//    WorkflowInstancePojo workflowInstancePojo = new WorkflowInstancePojo()
//    workflowInstancePojo.setId(workflowInstanceEntity.getId().toString())
//    workflowInstancePojo.setTenantId(workflowInstanceEntity.getTenantId())
//    workflowInstancePojo.setAppName(AppEntityUtil.appIdToAppName(workflowInstanceEntity.getAppId()))
//    workflowInstancePojo.setEntityName(AppEntityUtil.entityIdToName(workflowInstanceEntity.getAppId(), workflowInstanceEntity.getEntityId()))
//    workflowInstancePojo.setWorkflowId(workflowInstanceEntity.getWorkflowId())
//    workflowInstancePojo.setObjectId(workflowInstanceEntity.getObjectId())
//    workflowInstancePojo.setApplicantId(workflowInstanceEntity.getApplicantId())
//    workflowInstancePojo.setApplicantAccountAndDept(ToShowStringUtil.getShowObject(applicantInfo))
//    workflowInstancePojo.setState(ToShowStringUtil.getInstanceState(workflowInstanceEntity.getState()))
//    workflowInstancePojo.setStart(ToShowStringUtil.getTimeString(workflowInstanceEntity.getStart()))
//    workflowInstancePojo.setEnd(ToShowStringUtil.getTimeString(workflowInstanceEntity.getEnd()))
//    workflowInstancePojo.setDuration(ToShowStringUtil.getInstanceDurationTimeString(workflowInstanceEntity.getDuration()))
//    return workflowInstancePojo
//  }
//
//  def static entityTotaskPojo(TaskEntity taskEntity, String applicantInfo, String assignees, String assigneeAndOpinion) {
//
//    if (null == taskEntity) {
//      return null
//    }
//    if (null == taskEntity.getId()) {
//      log.info("TaskEntity = {} has no id", taskEntity)
//      return null
//    }
//    TaskPojo taskPojo = new TaskPojo()
//    taskPojo.setId(taskEntity.getId().toString())
//    taskPojo.setTenantId(taskEntity.getTenantId())
//    taskPojo.setAppName(AppEntityUtil.appIdToAppName(taskEntity.getAppId()))
//    taskPojo.setEntityName(AppEntityUtil.entityIdToName(taskEntity.getAppId(), taskEntity.getEntityId()))
//    taskPojo.setSourceWorkflowId(taskEntity.getSourceWorkflowId())
//    taskPojo.setWorkflowId(taskEntity.getWorkflowId())
//    taskPojo.setWorkflowInstanceId(taskEntity.getWorkflowInstanceId())
//    taskPojo.setObjectId(taskEntity.getObjectId())
//    taskPojo.setTaskType(ToShowStringUtil.getTaskType(taskEntity.getTaskType()))
//    taskPojo.setActionType(ToShowStringUtil.getActionType(taskEntity.getActionType()))
//    taskPojo.setApplicantId(taskEntity.getApplicantId())
//    taskPojo.setApplicantAccountAndDept(ToShowStringUtil.getShowObject(applicantInfo))
//    taskPojo.setState(ToShowStringUtil.getTaskState(taskEntity.getState()))
//    taskPojo.setErrMsg(taskEntity.getErrMsg())
//    taskPojo.setRemind(taskEntity.getRemind())
//    taskPojo.setCreateTime(ToShowStringUtil.getTimeString(taskEntity.getCreateTime()))
//    taskPojo.setModifyTime(ToShowStringUtil.getTimeString(taskEntity.getModifyTime()))
//    taskPojo.setAssignee(ToShowStringUtil.getShowObject(assignees))
//    taskPojo.setAssigneeAndOpinion(ToShowStringUtil.getShowObject(assigneeAndOpinion))
//    taskPojo.setDuration(ToShowStringUtil.getTaskDurationTimeString(taskEntity.getCreateTime(), taskEntity.getModifyTime()))
//    return taskPojo
//
//  }
//
//}
