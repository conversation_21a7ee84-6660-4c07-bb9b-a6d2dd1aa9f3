<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:c="http://www.springframework.org/schema/c"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	   		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	   		http://www.springframework.org/schema/task
			http://www.springframework.org/schema/task/spring-task-3.0.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

  <!--定时任务-->
  <context:component-scan base-package="com.fxiaoke.paas.console.task"/>

  <!-- 定时执行及线程池配置 -->
  <bean id="monitorAsyncTaskExecutor" class="com.github.trace.executor.MonitorAsyncTaskExecutor" c:executor-ref="myExecutor"/>
  <task:annotation-driven executor="monitorAsyncTaskExecutor" scheduler="myScheduler"/>
  <task:executor id="myExecutor" pool-size="5-50" queue-capacity="10" rejection-policy="CALLER_RUNS"/>
  <task:scheduler id="myScheduler" pool-size="5"/>
</beans>
