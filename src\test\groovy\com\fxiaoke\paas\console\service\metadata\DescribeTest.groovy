package com.fxiaoke.paas.console.service.metadata

import lombok.extern.slf4j.Slf4j
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

/**
 * <AUTHOR>
 * @date 2018/3/2
 */
@ContextConfiguration(locations = ["classpath:mapperContext.xml"])
@Slf4j
class DescribeTest extends Specification {
  @Resource
  private DescribeService describeService

  def "describeList"() {
    given:
    def result = describeService.describeList("2", null)

    expect:
    result.size()
  }

}
