<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	   http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <!-- 注册中心 -->
    <dubbo:application name="paas-console"/>
    <dubbo:registry address="${zookeeper.address}" check="false"/>

    <dubbo:registry address="${dubbo.registry.address}" check="false"/>
    <!--蜂眼 -->
    <dubbo:consumer filter="tracerpc"/>

    <!--组织架构start-->
    <!--<dubbo:reference id="enterpriseEditionService" interface="com.facishare.uc.api.service.EnterpriseEditionService" protocol="dubbo" timeout="5000" retries="0" check="false"/>-->
    <!--组织架构end-->

    <!-- url="dubbo://************:28001"   112,113使用，线上不用 -->
    <!--url="dubbo://************:28001"   线上，注册中心没有某些方法时使用-->
    <!--<dubbo:reference id="approvalFlowKernelService" interface="com.facishare.paas.workflow.kernel.service.ApprovalFlowKernelService" version="1.0.0" check="false"/>-->
    <!-- url="dubbo://************:28001"   112,113使用，线上不用 -->
    <!--url="dubbo://************:28001"   线上，注册中心没有某些方法时使用-->
    <!--<dubbo:reference id="workflowKernelService" interface="com.facishare.paas.workflow.kernel.service.WorkflowKernelService" version="1.0.0" check="false"/>-->

    <!--url="dubbo://172.31.160.9:28003"-->
    <!--<dubbo:reference id="orgOrganizationService" interface="com.facishare.paas.org.service.OrganizationService" version="1.0.0" check="false"/>-->
    <!--<dubbo:reference id="groupMemService" interface="com.facishare.paas.org.service.GroupMemService" version="1.0.0" check="false"/>-->
    <!--<dubbo:reference id="groupService" interface="com.facishare.paas.org.service.GroupService" version="1.0.0" check="false"/>-->
    <!--<dubbo:reference id="deptUserService" interface="com.facishare.paas.org.service.DeptUserService" version="1.0.0" check="false"/>-->
    <!--<dubbo:reference id="userService" interface="com.facishare.paas.org.service.UserService" version="1.0.0" check="false"/>-->


    <!--<dubbo:registry id="publicEnterpriseRegistry" address="zookeeper://***********:2181?backup=***********:2181,***********:2181" check="false"/>-->
    <!--<dubbo:reference id="IObjectDataService" interface="com.facishare.paas.metadata.api.service.IObjectDataService" check="false" timeout="60000" registry="metadataRegistry"/>-->
    <!--<dubbo:reference id="enterpriseAccountService" interface="com.facishare.ibss.em.service.api.EnterpriseAccountService" check="false"/>-->

</beans>
