package com.fxiaoke.paas.console.web.metadata

import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.service.metadata.RuleService
import com.fxiaoke.paas.console.util.DateFormatUtil
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

/**
 *  映射规则
 * <AUTHOR>
 * Created on 2018/3/19.
 */
@Controller
@Slf4j
@RequestMapping("/metadata/rule")
class RuleController {

  @Autowired
  RuleService ruleService

  /**
   * 跳转查询映射规则
   */
  @RequestMapping(value = "/find")
  def find() {
    "metadata/rule"
  }

  /**
   * 查询映射规则
   * @param id 企业ID，tenantId
   */
  @RequestMapping(value = "/find-rule")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 查询映射规则")
  def findRule(@RequestParam String id) {
    try {
      List<Map<String, Object>> ruleList = ruleService.findRuleByTenantId(id.trim())
      def json = ruleList.collect { rule ->
        [rule.get("rule_name"), rule.get("source_object_describe_api_name"),
         rule.get("target_object_describe_api_name"), rule.get("action"), rule.get("is_deleted"),
         rule.get("pkg"), rule.get("rule_api_name"), DateFormatUtil.formatLong(rule.get("last_modified_time") as Long), rule.get("rule_id")]
      }
      log.info("==查询映射规则成功==")
      ["data": json]
    } catch (Exception e) {
      log.error("查询映射规则异常，error:", e)
      ["data": []]
    }
  }

  /**
   * 获取指定规则详情
   * @param ruleId
   */
  @RequestMapping(value = "/find-info")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 查询指定规则详情")
  def findRuleByRuleId(@RequestParam String ruleId, @RequestParam String tenantId) {
    Map<String, Object> ruleMap = Maps.newHashMap()
    try {
      ruleMap = ruleService.findRuleByRuleId(tenantId.trim(), ruleId.trim())
      return ["ruleMap": ruleMap]
    } catch (Exception e) {
      log.error("查询规则详情异常，ruleId={},error:", ruleId, e)
      return ["ruleMap": ruleMap]
    }
  }


}
