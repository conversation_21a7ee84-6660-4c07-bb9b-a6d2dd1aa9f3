<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.paas.console.mapper.metadata.SqlQueryMapper">
  <select id="getLock" resultType="java.util.Map">
    SELECT
    locker.pid,
    pc.relname,
    locker. MODE,
    locker_act.application_name,
    LEAST (query_start, xact_start) start_time,
    locker_act. STATE,
    CASE
    WHEN GRANTED = 'f' THEN
    'wait_lock'
    WHEN GRANTED = 't' THEN
    'get_lock'
    END lock_status,
    CURRENT_TIMESTAMP - LEAST (query_start, xact_start) AS runtime,
    locker_act.query
    FROM
    pg_locks locker,
    pg_stat_activity locker_act,
    pg_class pc
    WHERE
    locker.pid = locker_act.pid
    AND NOT locker.pid = pg_backend_pid ()
    AND application_name != 'pg_statsinfod'
    AND locker.relation = pc.oid
    AND pc.reltype != 0 --and pc.relname='t'
    ORDER BY
    runtime DESC;
  </select>


  <delete id="deleteMtIndex">
    DELETE FROM mt_index WHERE tenant_id = #{tenantId} AND describe_api_name = #{describeApiName} AND field_num = #{fieldNum} AND data_id IN
    <foreach collection="dataIds" item="dataId" index="index" open="(" separator="," close=")">
      #{dataId}
    </foreach>
  </delete>

</mapper>