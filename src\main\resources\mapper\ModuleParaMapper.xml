<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fxiaoke.paas.console.license.mapper.ProductGrayMapper">
  <select id="queryProductBought" resultType="java.lang.String">
    select distinct tenant_id from product_gray where product_version = #{productVersion} and del_flag  = false
    <if test="expired != null">
      and expired_time > #{expired}
    </if>
  </select>

  <select id="queryProductBoughtV2" resultType="com.fxiaoke.paas.console.entity.license.ProductLicenseEntity">
    select tenant_id,start_time,expired_time from product_gray where product_version = #{productVersion} and del_flag = false
    <if test="expired != null">
      and expired_time > #{expired}
    </if>
  </select>
</mapper>
