package com.fxiaoke.paas.console.service.functional

import com.alibaba.fastjson.JSONObject
import com.google.common.base.Strings
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * Created on 2018/3/30.
 */
@Service
@Slf4j
class FunctionalService {

  /**
   * 过滤body内容
   * @param dataObject
   */
  def checkBody(String dataObject) {
    JSONObject jsonObject = JSONObject.parseObject(dataObject)
    if (Strings.isNullOrEmpty(jsonObject.getString("tenantId"))
            || Strings.isNullOrEmpty(jsonObject.getString("appId"))
            || Strings.isNullOrEmpty(jsonObject.getString("userId"))) {
      return null
    }
    def authContext = Maps.newHashMap()
    authContext["tenantId"] = jsonObject.getString("tenantId")
    authContext["appId"] = jsonObject.getString("appId")
    authContext["userId"] = jsonObject.getString("userId")

    def body = Maps.newHashMap()
    body["authContext"] = authContext

    if (checkInteger(jsonObject.getInteger("roleType"))) {
      body["roleType"] = jsonObject.getInteger("roleType")
    }
    if (!Strings.isNullOrEmpty(jsonObject.getString("key"))) {
      body["key"] = jsonObject.getString("key")
    }

    def pageInfo = Maps.newHashMap()
    def pageInfoMap = JSONObject.parseObject(jsonObject.get("pageInfo").toString(), Map.class)
    pageInfoMap.forEach({ key, value ->
      if (!Strings.isNullOrEmpty(value as String)) {
        pageInfo[key] = value as Integer
      }
    })
    if (!pageInfo.isEmpty()) {
      body["pageInfo"] = pageInfo
    }

    return body
  }

  /**
   * 检测整型是否为null
   * @param integer
   */
  static def checkInteger(Integer integer) {
    if (integer == null) {
      return false
    }
    return true
  }

}
