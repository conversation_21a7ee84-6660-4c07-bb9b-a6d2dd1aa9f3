package com.fxiaoke.paas.console.web.metadata

import com.alibaba.fastjson.JSONObject
import com.facishare.paas.pod.client.DbRouterClient
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult
import com.facishare.uc.api.service.EnterpriseEditionService
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.service.metadata.SqlServerRouteService
import com.github.autoconf.ConfigFactory
import com.google.common.base.CharMatcher
import com.google.common.base.Splitter
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.PostConstruct

/**
 * 路由查询
 * <AUTHOR> Created on 2018/3/29.
 */
@Controller
@Slf4j
@RequestMapping("/metadata/rout")
class RoutController {

  private List<String> moduleList
  private List<String> resourceTypeList
  @Autowired
  private EnterpriseEditionService enterpriseEditionService

  @Autowired
  private DbRouterClient dbRouterClient
  @Autowired
  SqlServerRouteService serverRouteService

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("paas-console-base", { config ->
      moduleList = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(config.get("module"))
      resourceTypeList = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(config.get("resourceType"))
    })
  }

  /**
   * 跳转
   * @param model
   * @return
   */
  @RequestMapping("/find")
  def find(ModelMap model) {
    model["moduleList"] = moduleList
    model["resourceTypeList"] = resourceTypeList
    "metadata/find-rout"
  }

  /**
   * 查路由
   * @param dataObject
   * @return
   */
  @RequestMapping("/find-rout")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 查询路由")
  def findRout(@RequestBody String dataObject) {
    JSONObject jsonObject = JSONObject.parseObject(dataObject)
    String ei = jsonObject.getString("ei").trim()
    String ea = jsonObject.getString("ea").trim()
    String tenantId=ei
    if (StringUtils.isBlank(tenantId)) {
      BatchGetSimpleEnterpriseDataArg batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(
              enterpriseIds: null,
              enterpriseAccounts: Arrays.asList(ea)
      )
      BatchGetSimpleEnterpriseDataResult batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
      tenantId = batchGetSimpleEnterpriseDataResult.simpleEnterpriseList.get(0).getEnterpriseId()
    }
    String result
    try {
      if (jsonObject.getString("resourceType") == "sqlServer") {
        result = serverRouteService.getUrl(Integer.valueOf(jsonObject.getString("tenantId")))
      } else {
        result = dbRouterClient.queryJdbcUrl(tenantId, jsonObject.getString("module").trim(), jsonObject.getString("resourceType").trim())
      }
      ["code": 200, "result": result]
    } catch (Exception e) {
      log.error("查询路由异常，error:", e)
      ["code": 500, "error": "查询失败,请检查企业ID是否正确"]
    }
  }

}
