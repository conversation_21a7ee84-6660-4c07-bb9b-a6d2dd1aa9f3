package com.fxiaoke.template;

import freemarker.template.TemplateMethodModelEx;
import freemarker.template.TemplateModelException;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;

import java.util.List;

public class ShiroAdminSupport implements TemplateMethodModelEx {
  @Override
  public Object exec(List list) throws TemplateModelException {
    Subject subject = SecurityUtils.getSubject();
    if (subject != null) {
      return subject.hasRole(list.get(0).toString());
    }
    return null;
  }

}
