package com.fxiaoke.paas.console.bean.workflow

import com.facishare.paas.workflow.kernel.entity.ExpressionEntity
import org.bson.types.ObjectId
import org.mongodb.morphia.annotations.Entity
import org.mongodb.morphia.annotations.Id

/**
 * Created by yangxw on 2018/3/8.
 */
@Entity(value = "workflow_rules", noClassnameStored = true)
class WorkflowRule {

         static final String WORKFLOW_SOURCE_ID = "workflowSrcId";
         static final String WORKFLOW_CREATE_TIME = "createTime";

         ObjectId _id;

         boolean deleted;

         String tenantId;

         String appId;

         String entityId;

         String ruleType;

         List<Integer> actionTypes;

         String description;

         int executionType;

         String conditionPattern;

         List<ExpressionEntity> conditions;

         String workflowSrcId;

         Long createTime;

         String creator;

         Long modifyTime;

         String modifier;

    }

