<#assign title="企业信息">
<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
<style>
  #dataTable2 th {
    vertical-align: middle;
    align-items: center;
  }

  #dataTable2 td {
    vertical-align: middle;
    align-items: center
  }

  .idObject {
    padding-left: 10px;
    width: 285px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #ccc
  }
</style>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
  <h1>企业信息查询</h1>
  <ol class="breadcrumb">
    <li><a href="../../"><i class="fa fa-dashboard"></i>任务主页</a></li>
    <li><a href=""><i class="fa fa-dashboard"></i>企业信息查询</a></li>
  </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <div class="box-header with-border">
          <form class="form-inline" action="${ctx}/describe/list" method="post" id="findForm" role="form" data-toggle="validator">
            <div class="form-group">
              <label for="tenantId">企业ID</label>
              <input type="text" class="idObject" id="companyId" placeholder="请输入企业ID，多个企业ID以逗号隔开" required/>
            </div>
            <div class="form-group">
              <label for="apiName">企业账号</label>
              <input type="text" class="idObject" id="account" placeholder="请输入企业账号，多个企业账号以逗号隔开" required/>
            </div>
            <button type="button" class="btn btn-primary" id="doSearch">查询</button>
          </form>
        </div>

        <div class="box-body">
          <table class="table table-hover table-bordered" id="dataTable2" cellspacing="0" width="100%">
            <thead>
            <tr>
              <th>企业ID</th>
              <th>企业账号</th>
              <th>企业名称</th>
              <th>创建时间</th>
              <th>企业状态</th>
            </tr>
            </thead>
          </table>
        </div>
        <div class="box-footer clearfix">
        </div>
      </div>
    </div>
  </div>
</section>
<#--提示信息模态框-->
<div class="modal fade" id="hintModal" tabindex="-1" role="dialog" aria-labelledby="sqlModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                class="sr-only">Close</span></button>
        <h4 class="modal-title" id="stopModalLabel">提示</h4>
      </div>
      <div class="modal-body">
        <h4>企业ID和企业账号不能同时为空！</h4>
      </div>
      <div class="modal-footer">
        <a type="button" class="btn btn-default" data-dismiss="modal">返回</a>
      </div>
    </div>
  </div>
</div>
</#assign>
<#assign scriptContent>
<!--搜索高亮显示-->
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script>
  $(document).ready(function () {
    var table = $("#dataTable2").DataTable({
      "searching": true,
      "ajax": "",
      "columnDefs": [
        {"width": "15%", "targets": 0},
        {"width": "15%", "targets": 1},
        {"width": "30%", "targets": 2},
        {"width": "30%", "targets": 3},
        {"width": "10%", "targets": 4}
      ],
      columns: [
        {data: "enterpriseId"},
        {data: "enterpriseAccount"},
        {data: "enterpriseName"},
        {data: "createTime"},
        {data: "runStatus"}
      ],
      "mark": true,
      "iDisplayLength": 10,
      "sPaginationType": "full_numbers",
      "language": {
        "url": "${ctx}/static/js/datatables-zh_CN.json"
      },
    });
    $("#doSearch").click(function () {
      var tenantIds = $("#companyId").val();
      var accounts = $("#account").val();
      if (tenantIds.length !== 0 || accounts.length !== 0) {
        table.ajax.url("${CONTEXT_PATH}/organization/enterpriseInfo?tenantIds=" + tenantIds + "&accounts=" + accounts).load();
      } else {
        $('#hintModal').modal("show");
      }
    });
  });
</script>
</#assign>
<#include "../layout/layout-main.ftl"/>

