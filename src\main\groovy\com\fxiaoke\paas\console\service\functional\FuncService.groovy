package com.fxiaoke.paas.console.service.functional

import com.facishare.paas.pod.client.DbRouterClient
import com.facishare.paas.pod.dto.RouterInfo
import com.facishare.paas.pod.util.DialectUtil
import com.fxiaoke.paas.console.mapper.func.FuncMapper
import com.google.common.collect.Lists
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import java.sql.Connection
import java.sql.DriverManager

/**
 * <AUTHOR>
 * @date 2020/5/8 16:58
 *
 */
@Service
class FuncService {
  @Autowired
  private FuncMapper funcMapper

  @Autowired
  private DbRouterClient dbRouterClient


  List<Map<String, String>> queryRoleCodesByTenantId(String tenantId) {
    if (StringUtils.isBlank(tenantId)) {
      return Lists.newArrayList()
    } else {
      RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, "CRM", "application", DialectUtil.POSTGRESQL)
      return funcMapper.setTenantId(tenantId).queryRoleCodesByTenantId(routerInfo.standalone ? "sch_" + tenantId : "public", tenantId)
    }
  }


  List<String> queryUsersByTenantIdAndRoleCode(String tenantId, String roleCode) {
    if (StringUtils.isAnyBlank(tenantId, roleCode)) {
      return Lists.newArrayList()
    } else {
      RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, "CRM", "application", DialectUtil.POSTGRESQL)
      return funcMapper.setTenantId(tenantId).queryUsersByTenantIdAndRoleCode(routerInfo.standalone ? "sch_" + tenantId : "public", tenantId, "{\"" + roleCode + "\"}")
    }
  }

}
