<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fxiaoke.paas.console.mapper.organization.PaasConsoleEmployeeMapper">

  <resultMap id="employeeEntity" type="com.fxiaoke.paas.console.entity.organization.EmployeeEntity">
    <id property="id" column="id"></id>
    <result property="userId" column="user_id"></result>
    <result property="nickname" column="nickname"></result>
    <result property="name" column="name"></result>
    <result property="phone" column="phone"></result>
    <result property="email" column="email"></result>
    <result property="position" column="position"></result>
    <result property="tenantId" column="tenant_id"></result>
    <result property="supervisorId" column="supervisor_id"></result>
    <result property="title" column="title"></result>
    <result property="picAddr" column="pic_addr"></result>
    <result property="description" column="description"></result>
    <result property="status" column="status"></result>
    <result property="createTime" column="create_time"></result>
    <result property="lastModifiedBy" column="last_modified_by"></result>
    <result property="lastModifiedTime" column="last_modified_by"></result>
    <result property="isDeleted" column="is_deleted"></result>
    <result property="department" column="department"></result>
    <result property="version" column="version"></result>
    <result property="createBy" column="create_by"></result>
    <result property="package" column="package"></result>
    <result property="objectDescribeId" column="object_describe_id"></result>
    <result property="objectDescribeApiName" column="object_describe_api_name"></result>
    <result property="recordType" column="record_type"></result>
    <result property="extendObjDataId" column="extend_obj_data_id"></result>
  </resultMap>

  <select id="getEmployeeIdByCompanyId" resultType="employeeEntity">
    SELECT user_id,name FROM org_employee_user WHERE tenant_id = #{tenantId}
  </select>

  <select id="getEmployee" resultType="employeeEntity">
    SELECT * FROM org_employee_user WHERE tenant_id = #{tenantId}
    <if test="userIds.size() > 0">
      AND user_id IN
      <foreach collection="userIds" item="userId" open="(" separator="," close=")">
        #{userId}
      </foreach>
    </if>
  </select>

  <select id="getRelationalEmployee" resultType="employeeEntity">
    SELECT org_employee_user.* FROM org_employee_user,org_dept_user WHERE org_dept_user.tenant_id = #{tenantId}
    AND org_dept_user.dept_id = #{deptId}
    <if test="type != null">
      AND org_dept_user.type = #{type}
    </if>
    AND org_dept_user.user_id = org_employee_user.user_id AND org_employee_user.tenant_id = #{tenantId}
  </select>

  <select id="getDeptByTenantIdAndUserId" parameterType="java.util.List" resultType="java.util.Map">
    SELECT odu.user_id,od.name as department_name FROM org_dept AS od
    INNER JOIN(
    SELECT dept_id AS d_id,user_id FROM org_dept_user
    WHERE tenant_id = #{tenantId} AND user_id IN
    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
      #{userId}
    </foreach>
    ) AS odu
    ON od.tenant_id = odu.tenant_id and dept_id = odu.d_id
  </select>

</mapper>