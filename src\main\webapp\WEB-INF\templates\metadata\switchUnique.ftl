<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>变字段(可/不可)重复</h1>
        <ol class="breadcrumb">
            <li><a href="../"><i class="fa fa-dashboard"></i>主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>变字段(可/不可)重复</a></li>
        </ol>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info row">
                    <div class="box-header with-border" style="padding: 0px">
                    </div>
                    <form class="form-horizontal" action="" onsubmit="return false;" method="post" id="myForm" role="form" data-toggle="validator">
                        <div class="box-body col-xs-6" style="margin-right: -127px">

                            <div class="form-group">
                                <label for="tenantId" class="col-sm-2 control-label">企业ID</label>
                                <div class="col-sm-6">
                                    <input type="text" style="border-radius:5px;" class="form-control" id="tenantId" name="tenantId" value="" placeholder="必填" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="describeApiName" class="col-sm-2 control-label">对象apiName</label>
                                <div class="col-sm-6">
                                    <input type="text" style="border-radius:5px;" class="form-control" id="describeApiName" name="tenantId" value="" placeholder="必填" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="fieldApiName" class="col-sm-2 control-label">字段apiName</label>
                                <div class="col-sm-6">
                                    <input type="text" style="border-radius:5px;" class="form-control" id="fieldApiName" name="tenantId" value="" placeholder="必填" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="apiName" class="col-sm-2 control-label">字段Unique</label>
                                <div class="col-sm-6">
                                    <input type="text" style="border-radius:5px;" class="form-control" id="unique" name="unique" value="" placeholder="非必填,格式为 false 或 true">
                                </div>
                            </div>
                            <div class="col-sm-offset-3" style="float: left">
                                <button type="button" id="queryButReadOnly" class="btn btn-info">查询</button>
                                <button type="button" id="switchUnique" class="btn btn-primary" disabled>unique切换</button>
                                <button type="button" id="switchFieldType" class="btn btn-primary" disabled>字段类型切换为自增编号</button>
                            </div>
                        </div>

                </div>
                <#--result展示-->
                <div class="box-body col-xs-6">
                    <h4>Result <i class="fa fa-hand-o-down"></i></h4>
                    <pre id="queryResult">
            </pre>
                    <#--loading。。。-->
                    <div class="spinner hide" id="loading">
                        <div class="spinner-container container1">
                            <div class="circle1"></div>
                            <div class="circle2"></div>
                            <div class="circle3"></div>
                            <div class="circle4"></div>
                        </div>
                        <div class="spinner-container container2">
                            <div class="circle1"></div>
                            <div class="circle2"></div>
                            <div class="circle3"></div>
                            <div class="circle4"></div>
                        </div>
                        <div class="spinner-container container3">
                            <div class="circle1"></div>
                            <div class="circle2"></div>
                            <div class="circle3"></div>
                            <div class="circle4"></div>
                        </div>
                    </div>
                </div>
                </form>
            </div>
        </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#queryButReadOnly').on('click', function () {
                $('#queryResult').html('');
                $('#loading').removeClass('hide');
                $('#queryButReadOnly').attr("disabled", "disabled");

                var tenantId = $('#tenantId').val();
                var describeApiName = $('#describeApiName').val();
                var fieldApiName = $('#fieldApiName').val();
                //TODO: 指向操作接口
                $.post("${CONTEXT_PATH}/metadata-operate/switch-unique/query", {
                    describeApiName: describeApiName,
                    fieldApiName: fieldApiName,
                    tenantId: tenantId
                }, function (result) {
                    $('#queryButReadOnly').removeAttr("disabled");
                    $('#loading').addClass('hide');

                    if (result.code === 200) {
                        info = result.info
                    } else {
                        info = result
                    }
                    $('#queryResult').JSONView(info, {
                        collapsed: false,
                        nl2br: true,
                        recursive_collapser: true
                    });
                });
            });
            $('#switchUnique').on('click', function () {
                $('#queryResult').html('');
                $('#loading').removeClass('hide');
                $('#switchUnique').attr("disabled", "disabled");

                var tenantId = $('#tenantId').val();
                var describeApiName = $('#describeApiName').val();
                var fieldApiName = $('#fieldApiName').val();
                var unique = $('#unique').val();
                //TODO: 指向操作接口
                $.post("${CONTEXT_PATH}/metadata-operate/switch-unique/switch", {
                    describeApiName: describeApiName,
                    fieldApiName: fieldApiName,
                    tenantId: tenantId,
                    unique: unique
                }, function (result) {
                    $('#switchUnique').removeAttr("disabled");
                    $('#loading').addClass('hide');

                    if (result.code === 200) {
                        info = result.info
                    } else {
                        info = result
                    }
                    $('#queryResult').JSONView(info, {
                        collapsed: false,
                        nl2br: true,
                        recursive_collapser: true
                    });
                });
            });
            $('#switchFieldType').on('click', function () {
                $('#queryResult').html('');
                $('#loading').removeClass('hide');
                $('#switchFieldType').attr("disabled", "disabled");

                var tenantId = $('#tenantId').val();
                var describeApiName = $('#describeApiName').val();
                var fieldApiName = $('#fieldApiName').val();
                //TODO: 指向操作接口
                $.post("${CONTEXT_PATH}/metadata-operate/switch-unique/switch/fieldType", {
                    describeApiName: describeApiName,
                    fieldApiName: fieldApiName,
                    tenantId: tenantId,
                }, function (result) {
                    $('#switchFieldType').removeAttr("disabled");
                    $('#loading').addClass('hide');

                    if (result.code === 200) {
                        info = result.info
                    } else {
                        info = result
                    }
                    $('#queryResult').JSONView(info, {
                        collapsed: false,
                        nl2br: true,
                        recursive_collapser: true
                    });
                });
            });
        })
        /**
         * 表单验证
         */
        var required1 = false, required2 = false, required3 = false, required4 = false;

        $('#tenantId').bind("input propertychange", function () {
            var tenantIdValue = $('#tenantId').val();
            if (tenantIdValue === null || tenantIdValue === "") {
                required1 = false;
            } else {
                required1 = true;
            }
        });

        $('#describeApiName').bind("input propertychange", function () {
            var apiNameValue = $('#describeApiName').val();
            if (apiNameValue === null || apiNameValue === "") {
                required2 = false;
            } else {
                required2 = true;
            }
        });

        $('#fieldApiName').bind("input propertychange", function () {
            var dataIdValue = $('#fieldApiName').val();
            if (dataIdValue === null || dataIdValue === "") {
                required3 = false;
            } else {
                required3 = true;
            }
        });

        $('#unique').bind("input propertychange", function () {
            var dataIdValue = $('#unique').val();
            if (dataIdValue === null || dataIdValue === "") {
                required4 = false;
            } else {
                required4 = true;
            }
        });

        $('#tenantId,#describeApiName,#fieldApiName').bind("input propertychange", function () {
            if (required1 && required2 && required3) {
                $('#switchFieldType').removeAttr("disabled");
            } else {
                $('#switchFieldType').attr("disabled", "disabled");
            }
        });

        $('#tenantId,#describeApiName,#fieldApiName,#unique').bind("input propertychange", function () {
            if (required1 && required2 && required3 && required4) {
                $('#switchUnique').removeAttr("disabled");
            } else {
                $('#switchUnique').attr("disabled", "disabled");
            }
        });
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />