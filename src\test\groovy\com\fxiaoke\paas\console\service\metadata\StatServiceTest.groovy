package com.fxiaoke.paas.console.service.metadata

import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult
import com.facishare.uc.api.service.EnterpriseEditionService
import com.fxiaoke.api.IdGenerator
import com.google.common.base.CharMatcher
import com.google.common.base.Splitter
import com.google.common.collect.Maps
import groovy.sql.Sql
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import java.util.concurrent.atomic.AtomicInteger
import java.util.function.Function
import java.util.stream.Collectors

/**
 * <AUTHOR>
 * Created on 2018/3/6.
 */
@ContextConfiguration(locations = ["classpath:mapperContext.xml"])
@Slf4j
class StatServiceTest extends Specification {
  @Autowired
  StatService statisticsService
  @Autowired
  SqlQueryService sqlQueryService
  @Autowired
  private EnterpriseEditionService enterpriseEditionService
  def "checkTableName"() {
    given:
    def str = "prepay_detail,checkins_data,customer_account,menu,price_book_product,quote,product,payment_order,payment_customer,payment_plan,quote_lines,menu_workbench,price_book"
    def list = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(str)
    def name = "aaa"
    expect:
    statisticsService.checkTableName(list, name)
  }

  def "findDescribeByTenantId"() {
    given:
    def tenantId = "2"
    def str = "prepay_detail,checkins_data,customer_account,menu,price_book_product,quote,product,payment_order,payment_customer,payment_plan,quote_lines,menu_workbench,price_book"
    def list = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(str)
    def startTime = ""
    def endTime = "now()"
    expect:
    statisticsService.findDescribeByTenantId(tenantId, list, startTime, endTime)
  }

  def "findDescribeDataLine"() {
    given:
    def tenantId = "2"
    def describeApiName = "object_n7SaH__c"
    expect:
    statisticsService.findDescribeDataLine(tenantId, describeApiName)
  }

  def "findSpecialTableNum"() {
    expect:
    statisticsService.findSpecialTableNum("74255", "sales_clue_pool", "")
  }


  def "insert"() {
    given:
    def tmap = Maps.newHashMap()
    def count = new AtomicInteger()
    count.addAndGet(22222)
    tmap.putIfAbsent('123123', count)
    long time = System.currentTimeMillis()
    expect:
    def sqlInstance = Sql.newInstance("************************************************", "metadata", "metadata@FS", "org.postgresql.Driver")
    sqlInstance.withBatch(100, """insert into tenant_data_count(id, tenant_id, server_ip, data_count,create_time,last_modified_time) values(?,?,?,?,?,?) ON CONFLICT(tenant_id) DO UPDATE SET data_count = ?, last_modified_time = ?""") {
      ps ->
        tmap.each {
          String k, AtomicInteger v ->
            ps.addBatch(IdGenerator.get(), k, "**************", v.get(), time, time, 789, time)
        }
    }

  }

  def "AtomicInteger"() {
    given:
    def count = new AtomicInteger()
    expect:
    count
  }

  def "列表分布"() {
    given:
    List<Object> list = ['2018-02-01', '2018-02-01', '2018-03-01', '2018-02-15', '2018-02-22', '2018-02-15']
    expect:
    list.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
  }

  def "demo"() {
    given:
    def d = null
    expect:
    d as String
  }

  def "map"() {
    given:
    def map = Maps.newHashMap()
    map['a'] = "123"
    expect:
    map
  }

  def "date"() {
    given:
    def endTime = System.currentTimeMillis() + 8L * 60L * 60L * 1000L
    expect:
    endTime
  }

  def "8l"() {
    given:
    def l = "8"
    def time = System.currentTimeMillis() + (l as long) * 60L * 60L * 1000L
    expect:
    time
  }


  def "batchGetSimpleEnterpriseData"(){
    given:
    BatchGetSimpleEnterpriseDataArg batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(
            enterpriseIds: Arrays.asList(579556),
            enterpriseAccounts: null
    )
    BatchGetSimpleEnterpriseDataResult batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
    expect:
    System.out.println(batchGetSimpleEnterpriseDataResult)
  }

}
