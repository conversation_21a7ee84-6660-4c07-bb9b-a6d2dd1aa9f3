package com.fxiaoke.paas.console.service.metadata

import com.fxiaoke.paas.console.entity.log.AuditLog
import com.fxiaoke.paas.console.mapper.log.AuditLogMapper
import com.fxiaoke.paas.console.util.DateFormatUtil
import groovy.util.logging.Slf4j
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service

import javax.annotation.Resource
import java.sql.SQLException

/**
 *  审计日志
 * <AUTHOR>
 * Created on 2018/4/2.
 */
@Service
@Slf4j
class AuditLogService {

  @Resource
  AuditLogMapper auditLogMapper

//  @Resource(name = "redisCache")
//  private JedisCmd jedis

  /**
   * 插入审计日志
   * @param auditLog
   */
  @Async
  def insertAuditLog(AuditLog auditLog) throws SQLException {
    def insert = auditLogMapper.addAuditLog(auditLog)
    if (insert > 0) {
      log.info("插入审计日志成功")
    } else {
      log.error("插入审计日志失败，auditLog：{}", auditLog)
    }

  }

  /**
   * 查询审计日志
   * @param num 条数
   */
  def findLog(Integer num, String userName) throws SQLException {
    List<AuditLog> auditLogList = auditLogMapper.findAuditLogByNum(num)
    // 更新auditTime
//    if (jedis.get(userName) != null) {
//      jedis.set(userName, DateFormatUtil.formatLong(findMaxExecuteTime()))
//    }
    return auditLogList
  }

  /**
   * 查询当前最大执行时间
   */
  def findMaxExecuteTime() {
    auditLogMapper.findMaxExecuteTime().getTime()
  }

  /**
   * 查询新log数
   */
  def findNewAuditLog(String userName) {
    def auditExecutorTime = jedis.get(userName)
    if (auditExecutorTime == null) {
      jedis.set(userName, DateFormatUtil.formatLong(findMaxExecuteTime()))
      return 0
    }
    return auditLogMapper.findNewLogNum(DateFormatUtil.formatString(auditExecutorTime))
  }

  /**
   * 删除30天前的审计日志
   */
  def deleteAuditLog() {
    Calendar calendar = Calendar.getInstance()
    calendar.add(Calendar.DATE, -Integer.valueOf(30))
    Long deleteTime = calendar.getTimeInMillis()

    def result = auditLogMapper.deleteAuditLogByTime(DateFormatUtil.formatString(DateFormatUtil.formatLong(deleteTime)))
    if (result > 0 && result != null) {
      log.info("删除一个月前审计日志成功")
    } else {
      log.error("删除一个月前审计日志失败")
    }
  }


}
