<#assign headContent>
<style>
  html, body {
    height: 100%;
    width: 100%;
    margin: 0;
  }

  .spinner {
    margin: 100px auto;
    width: 40px;
    height: 40px;
    position: relative;
  }

  .container1 > div, .container2 > div, .container3 > div {
    width: 9px;
    height: 9px;
    background-color: #67CF22;

    border-radius: 100%;
    position: absolute;
    -webkit-animation: bouncedelay 1.2s infinite ease-in-out;
    animation: bouncedelay 1.2s infinite ease-in-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
  }

  .spinner .spinner-container {
    position: absolute;
    width: 100%;
    height: 100%;
  }

  .container2 {
    -webkit-transform: rotateZ(45deg);
    transform: rotateZ(45deg);
  }

  .container3 {
    -webkit-transform: rotateZ(90deg);
    transform: rotateZ(90deg);
  }

  .circle1 {
    top: 0;
    left: 0;
  }

  .circle2 {
    top: 0;
    right: 0;
  }

  .circle3 {
    right: 0;
    bottom: 0;
  }

  .circle4 {
    left: 0;
    bottom: 0;
  }

  .container2 .circle1 {
    -webkit-animation-delay: -1.1s;
    animation-delay: -1.1s;
  }

  .container3 .circle1 {
    -webkit-animation-delay: -1.0s;
    animation-delay: -1.0s;
  }

  .container1 .circle2 {
    -webkit-animation-delay: -0.9s;
    animation-delay: -0.9s;
  }

  .container2 .circle2 {
    -webkit-animation-delay: -0.8s;
    animation-delay: -0.8s;
  }

  .container3 .circle2 {
    -webkit-animation-delay: -0.7s;
    animation-delay: -0.7s;
  }

  .container1 .circle3 {
    -webkit-animation-delay: -0.6s;
    animation-delay: -0.6s;
  }

  .container2 .circle3 {
    -webkit-animation-delay: -0.5s;
    animation-delay: -0.5s;
  }

  .container3 .circle3 {
    -webkit-animation-delay: -0.4s;
    animation-delay: -0.4s;
  }

  .container1 .circle4 {
    -webkit-animation-delay: -0.3s;
    animation-delay: -0.3s;
  }

  .container2 .circle4 {
    -webkit-animation-delay: -0.2s;
    animation-delay: -0.2s;
  }

  .container3 .circle4 {
    -webkit-animation-delay: -0.1s;
    animation-delay: -0.1s;
  }

  @-webkit-keyframes bouncedelay {
    0%, 80%, 100% {
      -webkit-transform: scale(0.0)
    }
    40% {
      -webkit-transform: scale(1.0)
    }
  }

  @keyframes bouncedelay {
    0%, 80%, 100% {
      transform: scale(0.0);
      -webkit-transform: scale(0.0);
    }
    40% {
      transform: scale(1.0);
      -webkit-transform: scale(1.0);
    }
  }
</style>
</#assign>
<#assign breadcrumbContent>
</#assign>
<#assign bodyContent>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <div class="box-header with-border">
          <h3 class="box-title">欢迎</h3>
        </div>
        <div class="box-body hide" id="container2" style="height: 500px;padding: 0px">
        <#--展示图-->
        </div>
        <div class="box box-info"></div>
        <div class="box-body hide" id="container" style="height: 500px;padding: 0px">
        <#--展示图-->
        </div>
      </div>
    </div>
  </div>
<#--加载动画-->
  <div class="spinner" id="loading">
    <div class="spinner-container container1">
      <div class="circle1"></div>
      <div class="circle2"></div>
      <div class="circle3"></div>
      <div class="circle4"></div>
    </div>
    <div class="spinner-container container2">
      <div class="circle1"></div>
      <div class="circle2"></div>
      <div class="circle3"></div>
      <div class="circle4"></div>
    </div>
    <div class="spinner-container container3">
      <div class="circle1"></div>
      <div class="circle2"></div>
      <div class="circle3"></div>
      <div class="circle4"></div>
    </div>
  </div>
</section>
</#assign>

<#assign scriptContent>
<script src="//static.foneshare.cn/oss/js/highcharts.js"></script>
<script src="https://img.hcharts.cn/highcharts/modules/data.js"></script>
<script>
  // console.log(window.innerHeight);

  //describe统计
  $.getJSON('${ctx}/metadata/rpc/month-describe', function (data) {
    $('#loading').addClass('hide');
    $('#container2').removeClass('hide');

    Highcharts.chart('container2', {
      chart: {
        zoomType: 'x'
      },
      title: {
        text: 'DescribeApiName变化统计图'
      },
      subtitle: {
        text: document.ontouchstart === undefined ? '' : ''
//                                '鼠标拖动可以进行缩放' : '手势操作进行缩放'
      },
      xAxis: {
        type: 'datetime',
        dateTimeLabelFormats: {
          millisecond: '%H:%M:%S.%L',
          second: '%H:%M:%S',
          minute: '%H:%M',
          hour: '%H:%M',
          day: '%m-%d',
          week: '%m-%d',
          month: '%Y-%m',
          year: '%Y'
        }
      },
      credits: {
        enabled: false
      },
      tooltip: {
        dateTimeLabelFormats: {
          millisecond: '%H:%M:%S.%L',
          second: '%H:%M:%S',
          minute: '%H:%M',
          hour: '%H:%M',
          day: '%Y-%m-%d',
          week: '%m-%d',
          month: '%Y-%m',
          year: '%Y'
        }
      },
      yAxis: {
        title: {
          text: ''
        }
      },
      legend: {
        enabled: false
      },
      plotOptions: {
        area: {
          fillColor: {
            linearGradient: {
              x1: 0,
              y1: 0,
              x2: 0,
              y2: 1
            },
            stops: [
//                                    趋势模块阴影
//                                    [0, Highcharts.getOptions().colors[0]],
//                                    [1, Highcharts.Color(Highcharts.getOptions().colors[0]).setOpacity(0).get('rgba')]
            ]
          },
          marker: {
            radius: 2
          },
          lineWidth: 1,
          states: {
            hover: {
              lineWidth: 1
            }
          },
          threshold: null
        }
      },
      series: [{
        type: 'spline',
        name: '数据量',
        data: data.dataList
      }]
    });

  });

  //数据统计
  $.getJSON('${ctx}/metadata/rpc/month-data', function (data) {
    if (data.dataCountList === null) {
      return;
    }
    $('#loading').addClass('hide');
    $('#container').removeClass('hide');

    Highcharts.chart('container', {
      chart: {
        zoomType: 'x'
      },
      title: {
        text: '企业活跃图'
      },
      subtitle: {
        text: document.ontouchstart === undefined ? '' : ''
//                                '鼠标拖动可以进行缩放' : '手势操作进行缩放'
      },
      xAxis: {
        type: 'datetime',
        dateTimeLabelFormats: {
          millisecond: '%H:%M:%S.%L',
          second: '%H:%M:%S',
          minute: '%H:%M',
          hour: '%H:%M',
          day: '%m-%d',
          week: '%m-%d',
          month: '%Y-%m',
          year: '%Y'
        }
      },
      credits: {
        enabled: false
      },
      tooltip: {
        dateTimeLabelFormats: {
          millisecond: '%H:%M:%S.%L',
          second: '%H:%M:%S',
          minute: '%H:%M',
          hour: '%H:%M',
          day: '%Y-%m-%d',
          week: '%m-%d',
          month: '%Y-%m',
          year: '%Y'
        }
      },
      yAxis: {
        title: {
          text: ''
        }
      },
      legend: {
        enabled: false
      },
      plotOptions: {
        area: {
          fillColor: {
            linearGradient: {
              x1: 0,
              y1: 0,
              x2: 0,
              y2: 1
            },
            stops: [
//                                    趋势模块阴影
//                                    [0, Highcharts.getOptions().colors[0]],
//                                    [1, Highcharts.Color(Highcharts.getOptions().colors[0]).setOpacity(0).get('rgba')]
            ]
          },
          marker: {
            radius: 2
          },
          lineWidth: 1,
          states: {
            hover: {
              lineWidth: 1
            }
          },
          threshold: null
        }
      },
      series: [{
        type: 'spline',
        name: '数据量',
        // data: data.figureList
        data: data.dataCountList
      }]
    });
  });
</script>
</#assign>

<#include "../layout/layout-main.ftl" />
