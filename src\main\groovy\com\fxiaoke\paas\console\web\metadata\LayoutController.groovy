package com.fxiaoke.paas.console.web.metadata

import groovy.util.logging.Slf4j
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam

/**
 * <AUTHOR>
 * Created on 2018/3/5.
 */
@Controller
@Slf4j
@RequestMapping("/metadata/layout")
class LayoutController {

  /**
   * 查询布局
   * @param layout
   * @return
   */
  @RequestMapping(value = "/list")
  def layout(@RequestParam(required = false) String layout) {
    return "metadata/layout"
  }


}
