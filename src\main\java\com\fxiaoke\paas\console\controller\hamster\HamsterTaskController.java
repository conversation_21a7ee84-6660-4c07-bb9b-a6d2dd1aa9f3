package com.fxiaoke.paas.console.controller.hamster;

import com.alibaba.fastjson.JSONObject;
import com.facishare.hamster.common.Result;
import com.facishare.hamster.constant.HamsterConstant;
import com.facishare.hamster.exception.HamsterException;
import com.facishare.hamster.exception.HamsterMessage;
import com.facishare.hamster.pojo.HamsterTaskItemPojo;
import com.facishare.hamster.pojo.HamsterTaskPojo;
import com.facishare.hamster.result.HamsterTaskItemResult;
import com.facishare.hamster.result.HamsterTaskResult;
import com.fxiaoke.paas.console.annotation.HamsterControllerLog;
import com.fxiaoke.paas.console.constant.ConstantString;
import com.fxiaoke.paas.console.log.HamsterConsoleAuditLog;
import com.fxiaoke.paas.console.service.hamster.CloudService;
import com.fxiaoke.paas.console.service.hamster.HamsterConsoleLogService;
import com.fxiaoke.paas.console.service.hamster.HamsterTaskService;
import com.fxiaoke.paas.console.service.hamster.HamsterTemplateService;
import com.github.autoconf.ConfigFactory;
import com.github.shiro.support.ShiroCasRealm;
import com.github.shiro.support.ShiroUser;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/hamster")
@Slf4j
public class HamsterTaskController {
  @Autowired
  HamsterTaskService hamsterTaskService;

  @Autowired
  HamsterTemplateService hamsterTemplateService;
  @Autowired
  HamsterConsoleLogService hamsterConsoleLogService;
  @Autowired
  CloudService cloudService;
  @Resource(name = "casRealm")
  private ShiroCasRealm cas;

  private Integer num;

  private static final String HAMSTER_ADMIN = "hamster-admin";
  private static final String HAMSTER_EDIT = "hamster-edit";
  private static final String HAMSTER_DELETE = "hamster-delete";
  private static final String HAMSTER_READ = "hamster-read";

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("paas-hamster", iConfig -> {
      num = iConfig.getInt("logNum");
    });
  }


  @RequestMapping(value = "/task-list")
  public ModelAndView taskList() {
    ModelAndView modelAndView = new ModelAndView("hamster/task-list");
    return modelAndView;
  }

  @HamsterControllerLog(description = "hamster-编辑任务")
  @RequestMapping("/edit_task")
  @ResponseBody
  public Map editTask(@RequestBody HamsterTaskPojo pojo) {
    try {
      ShiroUser user = cas.getCurrentUser();
      if(!(hasPermission(ConstantString.HamsterPermission.HAMSTER_ADMIN.getCode()) || hasPermission(ConstantString.HamsterPermission.HAMSTER_EDIT.getCode()))){
        return ImmutableMap.of("code", 403, "info", "权限不足");
      }
      pojo.setUpdateBy(user.getDisplayName());
      pojo.setUpdateTime(System.currentTimeMillis());
      Result result = hamsterTaskService.updateHamsterTask(pojo);
      if ("success".equals(result.getResult())) {
        return ImmutableMap.of("code", 200, "info", "编辑成功");
      } else {
        return ImmutableMap.of("code", 400, "info", "编辑失败");
      }

    } catch (Exception e) {
      log.error("edit_task is error taskId={}", pojo.getId(), e);
      return ImmutableMap.of("code", 400, "info", "失败");
    }

  }

  @HamsterControllerLog(description = "hamster-批量迁移")
  @RequestMapping("/start-migration-by-task")
  public Map startMigrationByTask(@RequestParam String taskIds) {
    try {
      Set<String> taskIdList = Sets.newHashSet(Arrays.asList(taskIds.split(",")));
      List<String> results = Lists.newArrayList();
      for (String taskId : taskIdList) {
        HamsterTaskPojo task = hamsterTaskService.queryHamsterTask(taskId).getResult().get(0);
        if ("public".equals(task.getFromSchema())) {
          HamsterTaskItemPojo item = hamsterTaskService
            .queryHamsterTaskItem(taskId)
            .getResult()
            .stream()
            .sorted(Comparator.comparing(HamsterTaskItemPojo::getOrder))
            .collect(Collectors.toList())
            .get(0);
          Result<String> result = hamsterTaskService.startMigration(taskId, item.getAction(), HamsterConstant.OperationType.EXECUTE, cas
            .getCurrentUser()
            .getDisplayName(), item.getId());
          results.add(result.getResult());
        }
      }
      if (taskIdList.size() == results.size()) {
        return ImmutableMap.of("code", 200, "info", "成功");
      } else {
        return ImmutableMap.of("code", 400, "info", "失败");
      }
    } catch (Exception e) {
      log.error("startMigrationByTask is error taskIds={}", taskIds, e);
      return ImmutableMap.of("code", 400, "info", "失败");
    }
  }

  @HamsterControllerLog(description = "hamster-开始迁移(单任务)")
  @RequestMapping("/start-migration")
  public Map startMigration(@RequestParam String taskId, @RequestParam String action, @RequestParam String operation, @RequestParam String itemId) {
    try {
      HamsterTaskPojo task = hamsterTaskService.queryHamsterTask(taskId).getResult().get(0);
      if (!hamsterTemplateService.defaultSchemaToSchemaTemplateId.equals(task.getTemplateId()) &&
        !hamsterTemplateService.defaultCloudSchemaToSchemaTemplateId.equals(task.getTemplateId())) {
        if (task.getFromSchema().contains("sch")) {
          return ImmutableMap.of("code", 400, "info", "失败，原企业为Schema");
        }
      }
      Result result = hamsterTaskService.startMigration(taskId, action, operation, cas.getCurrentUser().getDisplayName(), itemId);
      if ("success".equals(result.getResult())) {
        return ImmutableMap.of("code", 200, "info", "成功");
      } else {
        return ImmutableMap.of("code", 400, "info", "失败");
      }
    } catch (Exception e) {
      log.error("start-migration is error taskId={},action={},operation={}", taskId, action, operation, e);
      return ImmutableMap.of("code", 400, "info", "失败");
    }
  }

  @HamsterControllerLog(description = "hamster-跳过步骤")
  @RequestMapping("/skip-task-item")
  public Map skipTaskItem(@RequestParam String taskId, @RequestParam String action, @RequestParam Integer operation, @RequestParam String itemId) {
    try {
      Result result = hamsterTaskService.skipHamsterTaskItem(taskId, action, operation, itemId);
      if ("success".equals(result.getResult())) {
        return ImmutableMap.of("code", 200, "info", "成功");
      } else {
        return ImmutableMap.of("code", 400, "info", "失败");
      }
    } catch (Exception e) {
      log.error("start-migration is error taskId={},action={},operation={}", taskId, action, operation, e);
      return ImmutableMap.of("code", 400, "info", "失败");
    }
  }


  @RequestMapping("/task-item-list")
  public ModelAndView taskItemList(@RequestParam String taskId) {
    ModelAndView modelAndView = new ModelAndView("hamster/task-item-list");
    HamsterTaskPojo task = hamsterTaskService.queryHamsterTask(taskId).getResult().get(0);
    modelAndView.addObject("taskId", taskId);
    modelAndView.addObject("status", task.getStatus());
    boolean containsSch = task.getFromSchema().contains("sch");
    modelAndView.addObject("buttonDisable", containsSch && ((!hamsterTemplateService.defaultSchemaToSchemaTemplateId.equals(task.getTemplateId()) &&
      !hamsterTemplateService.defaultCloudSchemaToSchemaTemplateId.equals(task.getTemplateId()))));
    modelAndView.addObject("currentTenantId", task.getTenantId());
    modelAndView.addObject("currentTenantIdEa", hamsterTaskService.getEnterpriseEA(task.getTenantId()));
    HamsterTaskItemPojo item = hamsterTaskService
      .queryHamsterTaskItem(taskId)
      .getResult()
      .stream()
      .sorted(Comparator.comparing(HamsterTaskItemPojo::getOrder))
      .collect(Collectors.toList())
      .get(0);
    modelAndView.addObject("firstAction", item.getAction());
    modelAndView.addObject("currentItemId", item.getId());
    return modelAndView;
  }

  @RequestMapping("/query-hamster-log")
  @ResponseBody
  public JSONObject queryHamsterLog(@RequestParam String taskId, @RequestParam String action, @RequestParam String type) {
    try {
      JSONObject json = new JSONObject();
      List<Map> result = Lists.newArrayList();
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      hamsterTaskService.queryHamsterLog(taskId, action, type).getResult().stream().forEach(errorLog -> {
        Map map = Maps.newHashMap();
        map.put("企业Id", errorLog.getTenantId());
        map.put("操作人", errorLog.getUser());
        map.put("行为", errorLog.getAction());
        map.put("信息", errorLog.getMessage());
        map.put("创建时间", sdf.format(new Date(Long.parseLong(String.valueOf(errorLog.getCreateTime())))));
        result.add(map);
      });

      json.put("data", result);
      return json;
    } catch (Exception e) {
      log.error("hamsterTask is error");
      throw new HamsterException(HamsterMessage.SYSTEM_ERROR.getCode(), HamsterMessage.SYSTEM_ERROR.getMessage());
    }
  }

  @HamsterControllerLog(description = "hamster-查看步骤具体信息")
  @RequestMapping("/query-task-item")
  @ResponseBody
  public JSONObject queryTaskItem(@RequestParam String taskId, @RequestParam String action) {
    try {
      JSONObject json = new JSONObject();
      json.put("data", hamsterTaskService.queryHamsterTaskItem(taskId, action).getResult().get(0));
      return json;
    } catch (Exception e) {
      log.error("hamsterTask is error");
      throw new HamsterException(HamsterMessage.SYSTEM_ERROR.getCode(), HamsterMessage.SYSTEM_ERROR.getMessage());
    }
  }

  @RequestMapping("/query-task-item-list")
  public JSONObject queryTaskItemList(@RequestParam String taskId) {
    try {
      List<List<Object>> result = Lists.newArrayList();
      HamsterTaskItemResult hamsterTaskItemResult = hamsterTaskService.queryHamsterTaskItem(taskId);
      List<HamsterTaskItemPojo> hamsterTaskItemList = hamsterTaskItemResult
        .getResult()
        .stream()
        .sorted(Comparator.comparing(HamsterTaskItemPojo::getOrder))
        .collect(Collectors.toList());
      JSONObject json = new JSONObject();
      hamsterTaskItemList.forEach(item -> {
        List<Object> list = Lists.newArrayList();
        list.add(item.getTaskId());
        list.add(item.getName());
        list.add(item.getAction());
        list.add(item.getActionType());
        list.add(item.getStatus());
        list.add(item.getOrder());
        list.add(item.getOperation());
        list.add(item.getId());
        result.add(list);
      });
      json.put("data", result);
      return json;
    } catch (Exception e) {
      log.error("hamsterTask is error");
      throw new HamsterException(HamsterMessage.SYSTEM_ERROR.getCode(), HamsterMessage.SYSTEM_ERROR.getMessage());
    }
  }

  @HamsterControllerLog(description = "hamster-查看任务具体信息")
  @RequestMapping("/hamster-task")
  @ResponseBody
  public JSONObject hamsterTask(@RequestParam String taskId) {
    try {
      JSONObject json = new JSONObject();
      json.put("data", hamsterTaskService.queryHamsterTask(taskId).getResult().get(0));
      return json;
    } catch (Exception e) {
      log.error("hamsterTask is error");
      throw new HamsterException(HamsterMessage.SYSTEM_ERROR.getCode(), HamsterMessage.SYSTEM_ERROR.getMessage());
    }
  }

  @RequestMapping("/hamster-task-list")
  @ResponseBody
  public JSONObject hamsterTaskList(@RequestParam String taskId) {
    try {
      List<List<Object>> result = Lists.newArrayList();
      HamsterTaskResult hamsterTaskResult = hamsterTaskService.queryHamsterTask(taskId);
      List<HamsterTaskPojo> pojoList = hamsterTaskResult
        .getResult()
        .stream()
        .sorted(Comparator.comparing(HamsterTaskPojo::getCreateTime).reversed())
        .collect(Collectors.toList());
      JSONObject json = new JSONObject();
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      for (HamsterTaskPojo task : pojoList) {
        List<Object> list = Lists.newArrayList();
        list.add(false);
        list.add(task.getId());
        list.add(task.getPlanId());
        list.add(task.getTemplateId());
        list.add(task.getName());
        list.add(task.getTenantId());
        list.add(task.getFromSchema());
        list.add(task.getToSchema());
        list.add(task.getCurrentCloud());
        list.add(task.getTargetCloud());
        list.add(task.getServiceCloud());
        list.add(task.getTargetServiceCloud());
        list.add(task.getStatus().toString());
        HamsterTaskItemResult hamsterTaskItemResult = hamsterTaskService.queryHamsterTaskItem(task.getId());
        Optional<HamsterTaskItemPojo> action = hamsterTaskItemResult
          .getResult()
          .stream()
          .sorted(Comparator.comparing(HamsterTaskItemPojo::getOrder))
          .filter(item -> item.getStatus().equals(HamsterConstant.TaskItemStatus.RUNNING))
          .findFirst();
        if (action.isPresent()) {
          list.add(action.get().getName());
        } else {
          list.add(null);
        }
        list.add(sdf.format(new Date(task.getCreateTime())));
        //deleteType，0展示逻辑删除，1展示物理删除，2不展示
        String deleteType = Integer.valueOf(0).equals(task.getStatus()) ? task.getIsDeleted().toString() : "2";
        list.add(deleteType);
        result.add(list);
      }
      json.put("data", result);
      return json;
    } catch (Exception e) {
      log.error("hamsterTaskList is error");
      throw new HamsterException(HamsterMessage.SYSTEM_ERROR.getCode(), HamsterMessage.SYSTEM_ERROR.getMessage());
    }
  }


  /**
   * redirect to create task page
   *
   * @return
   */
  @RequestMapping(value = "/create-task")
  public ModelAndView createTask() {
    ModelAndView modelAndView = new ModelAndView("hamster/create-task");
    modelAndView.addObject("hamsterTemplates", hamsterTemplateService.getTemplates());
    modelAndView.addObject("clouds", cloudService.getAllClouds());
    return modelAndView;
  }

  @HamsterControllerLog(description = "hamster-保存任务")
  @RequestMapping(value = "/save-task", method = RequestMethod.POST)
  public ModelAndView saveTask(HamsterTaskPojo pojo) {
    ModelAndView modelAndView = new ModelAndView("redirect:/hamster/task-list");
    pojo.setCreateBy(cas.getCurrentUser().getDisplayName());
    pojo.setUpdateBy(cas.getCurrentUser().getDisplayName());
    hamsterTaskService.saveTask(pojo);
    return modelAndView;
  }

  @RequestMapping(value = "/has-save-role", method = RequestMethod.GET)
  @ResponseBody
  public String hasSaveRole() {
    if(!(hasPermission(ConstantString.HamsterPermission.HAMSTER_ADMIN.getCode()) || hasPermission(ConstantString.HamsterPermission.HAMSTER_EDIT.getCode()))){
      return "no";
    }
    return "yes";
  }

  @HamsterControllerLog(description = "hamster-删除任务")
  @RequestMapping(value = "/delete-task", method = RequestMethod.POST)
  @ResponseBody
  public Map deleteTask(@RequestBody Map<String, String> requestParam) {
    HamsterTaskPojo pojo = new HamsterTaskPojo();
    String taskId = requestParam.get("taskId");
    pojo.setId(taskId);
    ShiroUser user = cas.getCurrentUser();
    if(!(hasPermission(ConstantString.HamsterPermission.HAMSTER_ADMIN.getCode()) || hasPermission(ConstantString.HamsterPermission.HAMSTER_DELETE.getCode()))){
      return ImmutableMap.of("code", 403, "info", "权限不足");
    }
    pojo.setUpdateBy(user.getDisplayName());
    try {
      Result<String> result;
      if("1".equals(requestParam.get("soft"))) {
        // 逻辑删除
        result = hamsterTaskService.softDeleteTask(pojo);
      }else{
        // 物理删除
        result = hamsterTaskService.deleteTask(pojo);
      }
      if ("success".equals(result.getResult())) {
        return ImmutableMap.of("code", 200, "info", "删除成功");
      } else {
        return ImmutableMap.of("code", 400, "info", "删除失败");
      }

    } catch (Exception e) {
      log.error("delete_task is error taskId={}", pojo.getId(), e);
      return ImmutableMap.of("code", 400, "info", "删除失败");
    }
  }

  @RequestMapping(value = "/supplement-migrate-task")
  public ModelAndView supplementMigrationTask() {
    ModelAndView modelAndView = new ModelAndView("hamster/supplement-migrate-task");
    return modelAndView;
  }

  @HamsterControllerLog(description = "hamster-执行补迁数据")
  @RequestMapping(value = "/supplement-migration-data")
  public JSONObject supplementMigrationData(HamsterTaskPojo pojo, String tables, String biz, Boolean isCompareData) {
    JSONObject jsonObject = new JSONObject();
    JSONObject jsonParam = new JSONObject();
    jsonParam.put("tenantId", pojo.getTenantId());
    jsonParam.put("tables", tables);
    jsonParam.put("biz", biz);
    try {
      jsonObject.put("param", jsonParam);
      jsonObject.put("result", hamsterTaskService.supplementMigrateData(pojo.getTenantId(), tables, biz, BooleanUtils.isTrue(isCompareData)));
      log.info("param:tenantId:{},tables:{},biz:{}", pojo.getTenantId(), tables, biz);
    } catch (Exception e) {
      log.error("hamster supplement migration data error");
      throw new HamsterException(HamsterMessage.SYSTEM_ERROR.getCode(), HamsterMessage.SYSTEM_ERROR.getMessage());
    }
    return jsonObject;
  }

  @RequestMapping(value = "/console-log")
  public ModelAndView consoleLog() {
    return new ModelAndView("hamster/audit-log");
  }

  @RequestMapping(value = "/query-console-log")
  public JSONObject queryConsoleLog() {
    JSONObject json = new JSONObject();
    List<List<String>> result = Lists.newArrayList();
    List<HamsterConsoleAuditLog> auditLogs = hamsterConsoleLogService.queryHamsterConsoleLog(num);
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    for (HamsterConsoleAuditLog auditLog : auditLogs) {
      List<String> list = Lists.newArrayList();
      list.add(auditLog.getId());
      list.add(sdf.format(new Date(Long.parseLong(String.valueOf(auditLog.getExecuteTime())))));
      list.add(auditLog.getExecutor());
      list.add(auditLog.getDescription());
      list.add(auditLog.getArgument());
      result.add(list);
    }
    json.put("data", result);
    return json;
  }

  private boolean hasPermission(String permission) {
    List<String> permissionList = cas.getCurrentUser().getPermissions();
    if (CollectionUtils.isEmpty(permissionList)) {
      return false;
    }
    return permissionList.contains(permission);
  }

}
