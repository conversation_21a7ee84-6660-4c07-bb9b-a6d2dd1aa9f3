<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>操作记录</h1>
        <ol class="breadcrumb">
            <li><a href="../"><i class="fa fa-dashboard"></i>任务主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>Hamster审计日志</a></li>
        </ol>
    </section>
</#assign>
<#assign bodyContent>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title"></h3>
                </div>
                <div class="box-body">
                    <table id="datatable" class="table table-hover table-bordered" cellpadding="0" width="100%">
                        <thead>
                        <tr>
                            <th>ID</th>
                            <th>操作时间</th>
                            <th>操作人</th>
                            <th>操作描述</th>
                            <th>参数</th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</section>

</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script>
        $(document).ready(function () {
            var table = $("#datatable").DataTable({
                "processing": true,
                "displayLength": 25,
                "ajax": "${ctx}/hamster/query-console-log",
                "language": {
                    "url": "${ctx}/static/js/datatables-zh_CN.json"
                },
                "autoWidth": false,
                "stateSave": true,
                "mark": true,
                // "scrollX": true,
                "order": [[1, 'desc']]
            });
        });
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />