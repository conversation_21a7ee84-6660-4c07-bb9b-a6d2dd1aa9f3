package com.fxiaoke.paas.console.service.metadata

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONException
import com.alibaba.fastjson.JSONObject
import com.facishare.paas.pod.client.DbRouterClient
import com.facishare.paas.pod.dto.RouterInfo
import com.facishare.paas.pod.util.DialectUtil
import com.fxiaoke.common.Pair
import com.fxiaoke.common.http.HttpClient
import com.fxiaoke.common.http.spring.HttpSupportFactoryBean
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.paas.console.bean.metadata.DbResource
import com.fxiaoke.paas.console.mapper.metadata.DescribeMapper
import com.github.autoconf.ConfigFactory
import com.google.common.collect.ImmutableMap
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import lombok.NonNull
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import javax.annotation.Resource
import java.sql.SQLException

/**
 * <AUTHOR> Created on 2018/3/5.
 */
@Service
@Slf4j
class DescribeService {
  @Resource
  private DescribeMapper describeMapper
  @Autowired
  private JdbcService jdbcService
  @Autowired
  private DbRouterClient dbRouterClient
  private Map<String, String> dataAuthTableMaps = Maps.newConcurrentMap()
  private HttpSupportFactoryBean podOkHttpSupport = new HttpSupportFactoryBean()
  private String baseDescribeStaticUrl

  @PostConstruct
  void init() {
    try {
      dataAuthTableMaps = getAllStoreTableNames()
    } catch (Exception e) {
      log.error("cannot getAllStoreTableNames, ", e)
    }
    ConfigFactory.getConfig("fs-paas-data-auth-base", { config ->
      baseDescribeStaticUrl = config.get("describe.static.url")
    })
    podOkHttpSupport.init()
  }
  /**
   * 查询describe列表，可只根据tenantId查询，也可根据tenantId和apiName查询
   * @param tenantId
   * @param apiName
   * @return
   */
  List<Map<String, String>> describeList(@NonNull String tenantId, String apiName) {
    if (StringUtils.isBlank(apiName)) {
      return describeMapper.setTenantId(tenantId).describeListByTenantId(tenantId)
    }
    return describeMapper.setTenantId(tenantId).describeByTenantIdAndApiName(tenantId, apiName)
  }

  List<Map<String, String>> loadDescribeFromPg(String tenantId, String objectApiName,String objectDisplayName) {
    String sql = "SELECT describe_id,display_name,describe_api_name,last_modified_time FROM mt_describe WHERE is_current=TRUE AND tenant_id = ?"
    if(StringUtils.isNotBlank(objectApiName)){
      sql = sql +"  AND describe_api_name ilike '%"+StringUtils.trim(objectApiName)+"%' "
    }
    if(StringUtils.isNotBlank(objectDisplayName)){
      sql = sql +"  AND display_name like '%"+StringUtils.trim(objectDisplayName)+"%' "
    }

    List<Map<String, String>> describeList = Lists.newArrayList()
    try {
      Pair<JdbcConnection, String> conn = jdbcService.conn(tenantId, sql)
      conn.first.prepareQuery(conn.second, { st ->
        st.setString(1, tenantId)
      }, {
        rs ->
          while (rs.next()) {
            describeList.add(ofMap(rs.getString("describe_id"), tenantId, rs.getString("describe_api_name"), rs.getString("display_name"), Objects.toString(rs.getLong("last_modified_time"), "0")))
          }
      })
    } catch (Exception e) {
      log.error("get describe fail, tenantId {},objectApiName {}, e ", tenantId, objectApiName, e)
    }
    return describeList
  }

  /**
   * 查询describe详情
   * @param tenantId
   * @param describeId
   * @return
   */
  Map<String, String> describeInfo(@NonNull String tenantId, @NonNull String describeId) {
    return describeMapper.setTenantId(tenantId).findDescribeById(tenantId, describeId)
  }

  /**
   * 获取apiName和displayName用来前端显示用
   * @param tenantId
   * @param describeId
   * @return
   */
  Map<String, String> apiNameAndDisplayName(@NonNull String tenantId, @NonNull String describeId) {
    Pair<JdbcConnection, String> pair = jdbcService.conn(tenantId, "SELECT tenant_id,describe_id,describe_api_name,display_name FROM mt_describe WHERE tenant_id='" + tenantId + "' AND describe_id='" + describeId + "'")
    return describeMapper.setTenantId(tenantId).findByDescribeId2(pair.second)
  }

  Map<String, String> getAllStoreTableNames() throws SQLException {
    String jdbcUrl = dbRouterClient.queryJdbcUrl("-100", "CRM", DialectUtil.POSTGRESQL)
    String dbName = extractDbName(jdbcUrl)
    DbResource db = DbResource.builder().name(dbName).masterUrl(jdbcUrl).slaveUrl(jdbcUrl).build()
    Map<String, String> result = Maps.newHashMap()
    String sql = String.format("select describe_api_name,store_table_name from mt_describe where tenant_id='-100'")
    jdbcService.connection(db.masterUrl).query(sql, { rs ->
      while (rs.next()) {
        result.put(rs.getString("describe_api_name"), rs.getString("store_table_name"))
      }
    })
    return result
  }

  private String extractDbName(String jdbcUrl) {
    return jdbcUrl.substring(jdbcUrl.lastIndexOf('/') + 1)
  }

  String getStoreTableName(String tenantId, String describeApiName) {
    if (describeApiName.endsWith("__c")) {
      //是否schema隔离企业
      return isStandalone(tenantId) ? StringUtils.lowerCase(describeApiName) : "mt_data"
    }
    String storeTableNme = dataAuthTableMaps.get(describeApiName)
    if (StringUtils.isBlank(storeTableNme)) {
      Map<String, String> storeTableNames = getStoreTableNames(tenantId)
      storeTableNames.forEach({ k, v ->
        if (!k.endsWith("__c")) {
          dataAuthTableMaps.put(k, v)
        }
      })
      return storeTableNames.get(describeApiName)
    }
    return storeTableNme
  }

  boolean isStandalone(String tenantId) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, "CRM", "fs-metadata-service", "postgresql")
    if (routerInfo == null) {
      return false
    }
    return routerInfo.getStandalone()
  }

  Map<String, String> getStoreTableNames(String tenantId) {
    Map<String, String> result = Maps.newHashMap()
    String url = baseDescribeStaticUrl + "/paas/metadata/describe/getStoreTableName?tenantId=" + tenantId
    try {
      String json = HttpClient.defaultClient().get(url)
      JSONObject object = JSON.parseObject(json)
      JSONArray tableNames = object.getJSONArray("result")
      if (Objects.isNull(tableNames)) {
        return result
      }
      for (int i = 0; i < tableNames.size(); i++) {
        JSONObject t = tableNames.getJSONObject(i)
        if (Objects.isNull(t)) {
          continue
        }
        String describeApiName = t.getString("describe_api_name")
        String storeTableName = t.getString("store_table_name")
        if (StringUtils.isBlank(storeTableName))
          storeTableName = "mt_data"
        else if ("customer".equals(storeTableName)) {
          storeTableName = "biz_account"
        } else if ("opportunity".equals(storeTableName)) {
          storeTableName = "biz_opportunity"
        } else if ("contact".equals(storeTableName)) {
          storeTableName = "biz_contact"
        } else if ("contract".equals(storeTableName)) {
          storeTableName = "biz_contract"
        } else if ("trade_bill".equals(storeTableName)) {
          storeTableName = "biz_invoice_application"
        } else if ("sales_clue".equals(storeTableName)) {
          storeTableName = "biz_leads"
        } else if ("sales_clue_pool".equals(storeTableName)) {
          storeTableName = "biz_leads_pool"
        } else if ("marketing_event".equals(storeTableName)) {
          storeTableName = "biz_marketing_event"
        } else if ("trade_refund".equals(storeTableName)) {
          storeTableName = "biz_refund"
        } else if ("return_order_product".equals(storeTableName)) {
          storeTableName = "biz_returned_goods_invoice_product"
        } else if ("customer_trade".equals(storeTableName)) {
          storeTableName = "biz_sales_order"
        } else if ("trade_product".equals(storeTableName)) {
          storeTableName = "biz_sales_order_product"
        }
        String finalStoreTableName = storeTableName
        result.computeIfAbsent(describeApiName, { v -> finalStoreTableName })
      }
    } catch (IOException | JSONException ex) {
      log.error("execute error: " + url)
    }
    return result
  }

  private Map ofMap(String describeId, String tenantId, String describeApiName, String displayName, String lastModifiedTime) {
    Map map = Maps.newHashMap()
    map.put("describe_id", describeId)
    map.put("tenant_id", tenantId)
    map.put("describe_api_name", describeApiName)
    map.put("display_name", displayName)
    map.put("last_modified_time", lastModifiedTime)
    return map
  }

}
