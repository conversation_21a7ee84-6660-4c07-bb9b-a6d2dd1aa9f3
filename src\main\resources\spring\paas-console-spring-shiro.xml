<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="
         http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
         http://www.springframework.org/schema/util
         http://www.springframework.org/schema/util/spring-util-3.0.xsd">

  <description><PERSON><PERSON>安全配置</description>

  <!-- Shiro Filter -->
  <bean id="shiroFilter" class="com.github.shiro.support.MyShiroFilterFactoryBean"
        p:successUrl="/"
        p:unauthorizedUrl="/unauthorized"
        p:securityManager-ref="securityManager">
    <property name="filters">
      <util:map>
        <entry key="casFilter" value-ref="casFilter"/>
        <entry key="myUserFilter" value-ref="myUserFilter"/>
        <entry key="customPerms" value-ref="customPermissionsFilter"/>
      </util:map>
    </property>
    <property name="filterChainDefinitions">
      <value>
        /shiro-cas = casFilter
        /admin/** = perms[upm]
        /hamster/** = customPerms[hamster-admin, hamster-read]
        /basicPlatform/** = perms[base-platform]
        /metadata/** = perms[paas-metadata]
        /dts/** = perms[dts-admin]
        /metadata-restore/** = perms[paas-metadata-manipulate-data]
        /metadata-repair/** = perms[paas-metadata-manipulate-data]
        /metadata-operate/** = perms[paas-metadata-manipulate-data]
        /workflow/** = perms[paas-workflow]
        /datarights/** = perms[paas-datarights]
        /map/** = perms[paas-datarights]
        /license/** = perms[paas-license-auth]
        /license-write/** = perms[paas-license-auth-write]
        /license-admin/** = perms[paas-license-auth-admin]
        /func/** = perms[paas-license-auth]
        /sandBox/** = perms[paas-sandBox]
        /org/** = perms[paas-organization]
        /datarights_refresh/**=perms[paas-dataauth-write]
        /i18n/**=perms[paas-dataauth-write]
        /hubble/**=perms[paas-hubble]
        /static/** = anon
        /rest/** = anon
        /ping = anon
        /k8s-ping = anon
        /bizconf/query = anon
        /interface/** = anon
        /organization/enterpriseInfo = anon
        /organization/query-rout = anon
        /anonymous/** = anon
        /** = myUserFilter
      </value>
    </property>
  </bean>

  <bean id="customPermissionsFilter" class="com.fxiaoke.paas.console.filter.CustomPermissionsAuthorizationFilter"/>

  <bean id="myUserFilter" class="com.github.shiro.support.MyUserFilter"/>
  <bean id="casFilter" class="com.github.shiro.support.MyCasFilter" p:failureUrl="/unauthorized"/>

  <bean id="casRealm" class="com.github.shiro.support.ShiroCasRealm"
        p:defaultRoles="user"
        p:defaultPermissions=""
        p:roleAttributeNames="roles"
        p:permissionAttributeNames="permissions"/>

  <bean id="securityManager" class="org.apache.shiro.web.mgt.DefaultWebSecurityManager"
        p:realm-ref="casRealm" p:subjectFactory-ref="casSubjectFactory" p:cacheManager-ref="shiroCache"/>

  <!-- 如果要实现cas的remember me的功能，需要用到下面这个bean，并设置到securityManager的subjectFactory中 -->
  <bean id="casSubjectFactory" class="org.apache.shiro.cas.CasSubjectFactory"/>
  <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean"
        p:staticMethod="org.apache.shiro.SecurityUtils.setSecurityManager"
        p:arguments-ref="securityManager"/>
  <bean id="shiroCache" class="com.github.shiro.support.SpringCacheManagerWrapper" p:cacheManager-ref="cacheManager"/>
  <!-- 保证实现了Shiro内部lifecycle函数的bean执行 -->
  <bean id="lifecycleBeanPostProcessor" class="org.apache.shiro.spring.LifecycleBeanPostProcessor"/>
  <bean id="shiroRedis" class="com.github.jedis.support.JedisFactoryBean" p:configName="shiro-redis"/>
  <!-- declare Redis Cache Manager -->
  <bean id="redisCacheManager" class="com.github.spring.RedisCacheManager" c:redis-ref="shiroRedis" c:expiration="86400"/>
</beans>
