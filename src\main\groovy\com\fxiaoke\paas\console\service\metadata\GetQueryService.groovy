package com.fxiaoke.paas.console.service.metadata

import groovy.util.logging.Slf4j
import org.elasticsearch.index.query.BoolQueryBuilder
import org.elasticsearch.index.query.QueryBuilder
import org.elasticsearch.index.query.QueryBuilders
import org.elasticsearch.index.query.TermQueryBuilder
import org.springframework.stereotype.Service

/**
 * 构建describe query
 * <AUTHOR>
 * Created on 2018/8/13.
 */
@Service
@Slf4j
class GetQueryService {

  /**
   * 构建describe变化query
   * @return
   */
  static QueryBuilder getMonthQuery() {
    BoolQueryBuilder booleanQueryBuilder = QueryBuilders.boolQuery()
//            .must(QueryBuilders.rangeQuery("stamp").gte("now-3d").lt("now"))
            .must(QueryBuilders.termQuery("is_active", true))
    return booleanQueryBuilder
  }

  /**
   * 构造根据tenantId和ip获取全网记录总数query
   */
  static QueryBuilder createAllRecordNumRpcQuery(String tenantId, String ip) {
    def queryBuilder = QueryBuilders.boolQuery().must(QueryBuilders.matchAllQuery())
            .must(QueryBuilders.matchPhraseQuery("is_special", "false"))
            .mustNot(QueryBuilders.matchPhraseQuery("tenant_id", tenantId))
            .mustNot(QueryBuilders.matchPhraseQuery("ip", ip))

    return queryBuilder
  }

  /**
   * 构造全网统计query
   * @return
   */
  static QueryBuilder createRpcQuery() {
    TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery("is_active", true)
    return termQueryBuilder
  }

  /**
   * 构造获取排除ip，id的全部记录数的query
   * @param tenantId
   * @param ip
   * @return
   */
  static QueryBuilder createAllNumRpcQuery(String tenantId, String ip) {
    def queryBuilder = QueryBuilders.boolQuery().must(QueryBuilders.matchAllQuery())
            .mustNot(QueryBuilders.matchPhraseQuery("tenant_id", tenantId))
            .mustNot(QueryBuilders.matchPhraseQuery("ip", ip))

    return queryBuilder
  }

  /**
   * 创建企业ID下apiName统计query
   * @param tenantId
   * @return
   */
  QueryBuilder createTenantCountQuery(String tenantId) {
    return QueryBuilders.boolQuery()
            .must(QueryBuilders.termQuery("tenant_id", tenantId))
            .must(QueryBuilders.termQuery("is_active", true))
  }

  /**
   * 构建企业下有数据apiName的查询query
   * @param tenantId
   * @return
   */
  def createHaveDataApiNameQuery(String tenantId) {
    return QueryBuilders.boolQuery()
            .mustNot(QueryBuilders.termQuery("count", 0))
            .must(QueryBuilders.termQuery("tenant_id", tenantId))
            .must(QueryBuilders.termQuery("is_active", true))
  }

}
