package com.fxiaoke.paas.console.service

import com.fxiaoke.common.PasswordUtil
import com.fxiaoke.paas.console.bean.license.PvInfo
import com.fxiaoke.paas.console.service.license.LicenseSyncJobService
import com.google.common.collect.Maps
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = ["classpath:mapperContext.xml"])
class LicenseSyncJobServiceTest extends Specification {

  @Resource
  LicenseSyncJobService licenseSyncJobService

  def "sync"() {
    expect:
    licenseSyncJobService.sync()
  }

  def "insertTenantLicense"() {
    given:
    PvInfo pvInfo = PvInfo.builder().tenantId("ceshi").productVersion("ceshi222").startTime(1565193600000).expiredTime(1565280000000).build()
    expect:
    licenseSyncJobService.insertTenantLicense(pvInfo)
  }

  def "decode"() {
    expect:
    PasswordUtil.decode("E156CD3A55F2F850573A0AF8CF5FAC6FFDEDA8822A846217")
  }

  def "batchCheckTenant"() {
    given:
    Map<String, PvInfo> pvMap = Maps.newHashMap()
    pvMap.put("2", new PvInfo())
    pvMap.put("3", new PvInfo())
    pvMap.put("7", new PvInfo())
    pvMap.put("74255", new PvInfo())
    expect:
    licenseSyncJobService.batchCheckTenant(pvMap)
  }

  def "addSync"() {
    given:
    String tenantId = "74255"
    expect:
    licenseSyncJobService.addSync(tenantId)
  }
}
