package com.fxiaoke.paas.console.web.metadata

import com.alibaba.fastjson.JSON
import com.facishare.paas.foundation.persistence.PersistenceConfiguration
import com.facishare.paas.pod.exception.DbRouterException
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.service.metadata.SqlQueryService
import com.fxiaoke.paas.console.util.DateFormatUtil
import com.github.autoconf.ConfigFactory
import com.google.common.base.CharMatcher
import com.google.common.base.Splitter
import com.google.common.collect.Lists
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.jdbc.BadSqlGrammarException
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.PostConstruct

/**
 * <AUTHOR>
 * Created on 2018/3/20.
 */
@Controller
@Slf4j
@RequestMapping("/metadata/sql")
class SqlQueryController {


  private List<String> moduleList
  private List<String> resourceTypeList
  private Map<String, String> prohibitQueryTenantIdObjectMap

  @Autowired
  SqlQueryService sqlQueryService

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("paas-console-base", { config ->
      moduleList = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(config.get("module"))
      resourceTypeList = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(config.get("resourceType"))
      Splitter.MapSplitter splitter = Splitter.on(CharMatcher.anyOf(",")).withKeyValueSeparator(":");
      prohibitQueryTenantIdObjectMap = splitter.split(config.get("prohibitQueryTenantIdObjectMap", "1:object_1xkt0__c"));
    })
  }

  /**
   * 跳转查询页
   * @param model
   * @return
   */
  @RequestMapping(value = "/query")
  def query(ModelMap model) {
    model.put("moduleList", moduleList)
    model.put("resourceTypeList", resourceTypeList)
    "metadata/query"
  }

  /**
   * sql查询
   * @param sql
   */
  @RequestMapping(value = "/query-result", method = RequestMethod.POST)
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 执行自主Sql查询")
  def sqlQuery(@RequestParam String sql,
               @RequestParam String module,
               @RequestParam String resourceType,
               @RequestParam String tenantId,
               @RequestParam(required = false) String describeApiName,
               @RequestParam(defaultValue = "false") boolean enableNestloop,
               @RequestParam(required = false, defaultValue = "false") boolean readOnly) {
    if (sql.isEmpty() || tenantId.isEmpty()) {
      return ["code": 400, "error": "请输入Sql和租户ID"]
    }
    if (!DateFormatUtil.checkSql(sql)) {
      log.info("checkSql is false :{}", sql)
      return ["code": 403, "error": "Sql不符合规范，只能进行查询！"]
    }
    if (resourceType == "sqlServer") {
      return ["code": 403, "error": "暂不支持SqlServer查询"]
    }

    if (module.toLowerCase() == "crm" && resourceType == "clickhouse") {
      return ["code": 403, "error": "paas暂不支持clickhouse查询"]
    }
    for (Map.Entry<String, String> entry : prohibitQueryTenantIdObjectMap.entrySet()){
      if (tenantId.equals(entry.getKey())) {
        List prohibitApiNames = Lists.newArrayList(entry.getValue().split(";"))
        for (String prohibitApiName : prohibitApiNames)
          if (sql.toLowerCase().contains(prohibitApiName)) {
            return ["code": 403, "error": "禁止查询"]
          }
      }
    }
    try {
      List<Map<String, Object>> resultList = Lists.newArrayList()
      if(!sqlQueryService.checkUpperLimit()) {
        return ["code":400, "info": "当日查询次数已达上限(若需增加次数可提审批, 内容：申请调大paasconsole查询限制次数  审批人：->直属leader->李磊)"]
      }
      if (module != "CRM") {
        resultList = sqlQueryService.sqlQueryForOther(sql.trim(), module, resourceType, tenantId.trim(), readOnly)
        return ["code": 200, "info": JSON.toJSONString(resultList)]
      }
      PersistenceConfiguration.DescribeHolder.set(describeApiName)
      resultList = sqlQueryService.sqlQuery(sql.trim(), tenantId.trim(), readOnly, enableNestloop)
      return ["code": 200, "info": JSON.toJSONString(resultList)]
    } catch(BadSqlGrammarException e){
      log.warn("sql语法异常, sql={}, error:", sql, e)
      sqlQueryService.decrementCount()
      return ["code": 400, "error": "sql异常", "errorInfo": e.getMessage()]
    } catch(DbRouterException e){
      log.warn("企业路由异常, tenantId={}, error", tenantId, e)
      sqlQueryService.decrementCount();
      return ["code": 500, "error": "企业路由异常", "errorInfo": e.getMessage()]
    }catch (Exception e) {
      log.warn("Sql查询异常，sql={},error:", sql, e)
      if(StringUtils.isNotBlank(e.getMessage())) {
        return ["code": 500, "error": "查询异常", "errorInfo": e.getMessage()]
      } else if(StringUtils.isNotBlank(e.getCause().getMessage())) {
        return ["code": 500, "error": "查询异常", "errorInfo": e.getCause().getMessage()]
      }
      return ["code": 500, "error": "查询异常", "errorInfo": e]
    } finally {
      PersistenceConfiguration.DescribeHolder.remove()
      sqlQueryService.unlock(SqlQueryService.threadLock.get())
    }
  }

  /**
   * 分析
   * @return
   */
  @RequestMapping(value = "/explain", method = RequestMethod.POST)
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 进行Sql分析")
  def explain(@RequestParam String sql,
              @RequestParam String module,
              @RequestParam String resourceType,
              @RequestParam (required = false)String describeApiName,
              @RequestParam(defaultValue = "false") boolean enableNestloop,
              @RequestParam String tenantId) {

    if (sql.isEmpty() || tenantId.isEmpty()) {
      return ["code": 400, "error": "请输入Sql和租户ID"]
    }
    if (!DateFormatUtil.checkSql(sql)) {
      return ["code": 403, "error": "Sql不符合规范，只能分析查询语句！"]
    }
    if (resourceType == "clickhouse") {
      return  ["code": 403 , "error": "暂不支持clickhouse分析"]
    }
    try {
      Map result
      if (module.toLowerCase() != "crm") {
        result = sqlQueryService.explainOtherQuery(tenantId, module, resourceType, sql, enableNestloop)
      } else {
        PersistenceConfiguration.DescribeHolder.set(describeApiName)
        result = sqlQueryService.explainQuery(sql.trim(), tenantId.trim(), enableNestloop)
      }
      return ["code": 200, "result": result]
    } catch (Exception e) {
      log.error("分析异常,error:", e)
      return ["code": 500, "error": "分析异常", "errorInfo": e.getMessage()]
    } finally {
      PersistenceConfiguration.DescribeHolder.remove()
//      sqlQueryService.unlock(SqlQueryService.threadLock.get())
    }
  }

  @RequestMapping(value = "/explain2", method = RequestMethod.POST)
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 进行Sql分析")
  def explain2(@RequestParam String sql,
               @RequestParam String module,
               @RequestParam String resourceType,
               @RequestParam (required = false)String describeApiName,
               @RequestParam(defaultValue = "false") boolean enableNestloop,
               @RequestParam String tenantId) {

    if (sql.isEmpty() || tenantId.isEmpty()) {
      return ["code": 400, "error": "请输入Sql和租户ID"]
    }
    if (!DateFormatUtil.checkSql(sql)) {
      return ["code": 403, "error": "Sql不符合规范，只能分析查询语句！"]
    }
    if (resourceType == "clickhouse") {
      return  ["code": 403 , "error": "暂不支持clickhouse分析"]
    }
    try {
      String explainUrl
      if (module.toLowerCase() != "crm") {
        explainUrl = sqlQueryService.explainOtherQuery2(tenantId, module, resourceType, sql, enableNestloop)
      } else {
        PersistenceConfiguration.DescribeHolder.set(describeApiName)
        explainUrl = sqlQueryService.explainQuery2(sql.trim(), tenantId.trim(), enableNestloop)
      }
      return ["code": 200, "explainUrl": explainUrl]
    } catch (Exception e) {
      log.error("分析异常,error:", e)
      return ["code": 500, "error": "分析异常", "errorInfo": e.getMessage()]
    } finally {
      PersistenceConfiguration.DescribeHolder.remove()
//      sqlQueryService.unlock(SqlQueryService.threadLock.get())
    }
  }


}
