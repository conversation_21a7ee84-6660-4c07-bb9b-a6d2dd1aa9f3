package com.fxiaoke.paas.console.web.sandBox

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.paas.console.service.OKHttpService
import com.github.autoconf.ConfigFactory
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

import javax.annotation.PostConstruct

/**
 * <AUTHOR>
 * @date 2020/8/7 15:41
 */
@RestController
@RequestMapping("/sandBox")
class SandBoxController {

  private String sandBoxUrl

  @Autowired
  private OKHttpService okHttpService

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-console-url", { iConfig ->
      this.sandBoxUrl = iConfig.get("sandbox-url")
    })
  }


  @PostMapping(path = "/queryTransactionById", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  def queryByTransactionId(@RequestBody Map<String, String> params) {
    String transactionId = params.get("transactionId")
    if (StringUtils.isBlank(transactionId)) {
      return ["status": 200, "message": "transactionId is blank! "]
    } else {
      JSONObject jsonObject = new JSONObject().fluentPut("transactionId", transactionId)
      String result = okHttpService.postJSON(sandBoxUrl + "/tenant/sandbox/query/by/transaction/id", jsonObject)
      return ["status": 200, "message": result]
    }

  }
}
