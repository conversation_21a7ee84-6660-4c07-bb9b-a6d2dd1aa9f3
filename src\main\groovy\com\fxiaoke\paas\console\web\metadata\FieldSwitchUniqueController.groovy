package com.fxiaoke.paas.console.web.metadata

import com.alibaba.fastjson.JSONObject
import com.facishare.paas.metadata.dao.pg.entity.metadata.Describe
import com.facishare.paas.metadata.dao.pg.entity.metadata.Field
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.entity.metadata.MtDescribe
import com.fxiaoke.paas.console.entity.metadata.MtUnique
import com.fxiaoke.paas.console.mapper.metadata.DataMapper
import com.fxiaoke.paas.console.mapper.metadata.DescribeMapper
import com.fxiaoke.paas.console.mapper.metadata.FieldMapper
import com.fxiaoke.paas.console.mapper.metadata.MtUniqueMapper
import com.fxiaoke.paas.console.service.metadata.FieldSwitchUniqueService
import com.fxiaoke.release.FsGrayRelease
import com.fxiaoke.release.FsGrayReleaseBiz
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @date 2022/8/29 11:39
 */
@Controller
@Slf4j
@RequestMapping("/metadata-operate/switch-unique")
class FieldSwitchUniqueController {

  @Autowired
  FieldSwitchUniqueService fieldSwitchUniqueService

  @RequestMapping("")
  def page(){
    "/metadata/switchUnique"
  }

  @RequestMapping("/query")
  @ResponseBody
  def query(String tenantId, String describeApiName, String fieldApiName) {
    if (StringUtils.isAnyBlank(tenantId, describeApiName, fieldApiName)) {
      return ["code": 400, "message": "企业ID和描述字段ID不可为空"]
    }
    try {
      Field field = fieldSwitchUniqueService.findField(tenantId, describeApiName, fieldApiName)
      if (field == null) {
        return ["code": 400, "message": "不存在对应ID描述字段"]
      }
      if (!"name".equals(field.getApiName()) && !"custom".equals(field.getDefineType())) {
        return ["code": 400, "message": "此字段为预制字段，不支持更改"]
      }
      Map<String, Object> map = Maps.newHashMap();
      Integer count = fieldSwitchUniqueService.countUnique(tenantId,describeApiName, fieldApiName)
      map.put("field", field);
      map.put("count uniqueData", fieldSwitchUniqueService.countUniqueData(tenantId, describeApiName, fieldApiName, field.getFieldNum() as String))
      if(field.getIsUnique() && field.getApiName().equals("name")) {
        if(fieldSwitchUniqueService.checkUniqueIndex(tenantId, describeApiName)) {
          return ["code":400, "warn info": "此字段使用的唯一索引进行约束，不可通过接口变更，请联系DBA处理"]
        }
      }
      map.put("count mt_unique", count)
      return ["code": 200, "info": JSONObject.toJSONString(map)]
    } catch (Exception e) {
      return ["code": 500, "message": e]
    }
  }

  @RequestMapping("/switch")
  @ResponseBody
  @SystemControllerLog(description = "企业自定义对象描述字段改Unique属性")
  def switchUnique(String tenantId, String describeApiName, String fieldApiName, String unique){
    if(StringUtils.isAnyBlank(tenantId, describeApiName, fieldApiName)){
      return ["code": 400, "message": "企业ID和描述字段ID不可为空"]
    }
    if(!'true'.equals(unique) && !'false'.equals(unique)){
      return ["code": 400, "message": "unique的格式不符合规范"]
    }
    boolean isUnique = Boolean.valueOf(unique)
    try {
      Field field = fieldSwitchUniqueService.findField(tenantId, describeApiName, fieldApiName)
      if (field == null) {
        return ["code": 500, "message": "不存在对应ID描述字段"]
      }
      if (!"name".equals(field.getApiName()) && !"custom".equals(field.getDefineType())) {
        return ["code": 400, "message": "此字段为预制字段，不支持更改"]
      }
      if(field.getIsUnique() == isUnique) {
        return ["code":200, "info": ["message" : "字段is_unique为" + isUnique + ",无需修改"]]
      }
      if(field.getIsUnique() && field.getApiName().equals("name")) {
        if(fieldSwitchUniqueService.checkUniqueIndex(tenantId, describeApiName)) {
          return ["code":400, "info":["warn info","此字段使用的唯一索引进行约束，不可通过接口变更，请联系DBA处理"]]
        }
      }
        String message = fieldSwitchUniqueService.switchUnique(tenantId, field, isUnique)
      if(!StringUtils.isBlank(message)) {
        return ["code":400, "message":message]
      }
      return ["code":200, "info": ["message":  "已处理完毕"]]
    } catch (Exception e) {
      return ["code": 500, "message": e.getMessage()]
    }
  }

  @RequestMapping("/switch/fieldType")
  @ResponseBody
  @SystemControllerLog(description = "企业自定义对象描述字段切换类型为自增编号")
  def switchFieldType(String tenantId, String describeApiName, String fieldApiName) {
    if(StringUtils.isAnyBlank(tenantId, describeApiName, fieldApiName)){
      return ["code": 400, "message": "企业ID和描述字段ID不可为空"]
    }
    try {
      Field field = fieldSwitchUniqueService.findField(tenantId, describeApiName, fieldApiName)
      if (field == null) {
        return ["code": 500, "message": "不存在对应描述字段"]
      }
      if (!"name".equals(field.getApiName()) && !"custom".equals(field.getDefineType())) {
        return ["code": 400, "message": "此字段为预制字段，不支持更改"]
      }
      if(field.getType() != "text") {
        return ["code":200, "info": ["message" : "字段类型不为text类型,不支持切换为自增编号"]]
      }
      String message = fieldSwitchUniqueService.switchFieldType(tenantId, field)
      if(!StringUtils.isBlank(message)) {
        return ["code":400, "message":message]
      }
      return ["code":200, "info": ["message":  "已处理完毕"]]
    } catch (Exception e) {
      return ["code": 500, "message": e.getMessage()]
    }
  }
}
