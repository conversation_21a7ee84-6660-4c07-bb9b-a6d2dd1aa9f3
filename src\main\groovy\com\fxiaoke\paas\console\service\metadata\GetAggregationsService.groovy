package com.fxiaoke.paas.console.service.metadata

import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import org.elasticsearch.script.Script
import org.elasticsearch.script.ScriptType
import org.elasticsearch.search.aggregations.AggregationBuilders
import org.elasticsearch.search.aggregations.bucket.terms.Terms
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder
import org.springframework.stereotype.Service

/**
 * 构建describe聚合
 * <AUTHOR>
 * Created on 2018/8/13.
 */
@Service
@Slf4j
class GetAggregationsService {

  private static final int PAGE_SIZE = 100

  /**
   * 构建describe聚合
   */
  static def getAggregation() {
    def sumBuilder = AggregationBuilders.sum("data_count").field("count")
    TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms("describe_api_name")
            .field("api_name").size(3).order(Terms.Order.aggregation("data_count", false))
    termsAggregationBuilder.subAggregation(sumBuilder)
    return termsAggregationBuilder
  }

  /**
   * 构造api_name数量的builder
   * @return
   */
  static def getApiNameCountAggregation() {
//    return PipelineAggregatorBuilders.bucketScript("api_name_count", new Script(ScriptType.INLINE, "painless", "doc['tenant_id'].value + ' ' + doc['api_name'].value", Maps.newHashMap()))
    return AggregationBuilders.cardinality("api_name_count").script(new Script(ScriptType.INLINE, "painless", "doc['tenant_id'].value + ' ' + doc['api_name'].value", Maps.newHashMap()))
  }

  /**
   * 构造统计记录数的聚合函数
   * @return
   */
  static TermsAggregationBuilder createAggBuilder() {
    def countBuilder = AggregationBuilders.sum("totalCount").field("count")

    TermsAggregationBuilder apiNameBuilder
    apiNameBuilder = AggregationBuilders.terms("apiName").field("api_name")
            .size(PAGE_SIZE).order(Terms.Order.aggregation("totalCount", false))
    apiNameBuilder.subAggregation(countBuilder)

    TermsAggregationBuilder tenantIdBuilder = apiNameBuilder
    tenantIdBuilder = AggregationBuilders.terms("tenantId").field("tenant_id").size(PAGE_SIZE)
    tenantIdBuilder.subAggregation(apiNameBuilder)

    TermsAggregationBuilder resultBuilder = tenantIdBuilder
    return resultBuilder
  }

  /**
   * 构建获取全网记录统计top100
   * @return
   */
  TermsAggregationBuilder createAggBuilder2() {

    def totalCountBuilder = AggregationBuilders.sum("totalCount").field("count")

    TermsAggregationBuilder apiNameTermsAggregationBuilder
    apiNameTermsAggregationBuilder = AggregationBuilders
            .terms("apiName")
            .field("api_name")
            .order(Terms.Order.aggregation("totalCount", false))
    apiNameTermsAggregationBuilder.subAggregation(totalCountBuilder)

    TermsAggregationBuilder tenantIdTermsBuilder = AggregationBuilders
            .terms("tenantId")
            .field("tenant_id")
            .size(PAGE_SIZE)
            .order(Terms.Order.aggregation("tidCount", false))
    def tidCountBuilder = AggregationBuilders.sum("tidCount").field("count")
    tenantIdTermsBuilder.subAggregation(tidCountBuilder)
    tenantIdTermsBuilder.subAggregation(apiNameTermsAggregationBuilder)

    return tenantIdTermsBuilder
  }

  /**
   * 构造统计对象数的聚合函数
   * @return
   */
  static TermsAggregationBuilder createObjectCountAggBuilder() {
    def apiNameCountBuilder = AggregationBuilders.count("apiName").field("api_name")
    TermsAggregationBuilder tenantIdBuilder
    tenantIdBuilder = AggregationBuilders.terms("tenantId").field("tenant_id").size(PAGE_SIZE).order(Terms.Order.aggregation("apiName", false))
    tenantIdBuilder.subAggregation(apiNameCountBuilder)

    TermsAggregationBuilder resultBuilder = tenantIdBuilder
    return resultBuilder
  }

  /**
   * 构造根据tenantId和ip获取全网记录总数聚合函数
   * @return
   */
  static def createAllRecordNumAggBuilder() {
    def countBuilder = AggregationBuilders.sum("number").field("count")
    return countBuilder
  }

  /**
   * 构造排除ip，id获取top10的聚合函数
   */
  static TermsAggregationBuilder createCountTopNumAggBuilder() {
    def sumBuilder = AggregationBuilders.sum("count_num").field("count")
    TermsAggregationBuilder termsAggregationBuilder
    termsAggregationBuilder = AggregationBuilders.terms("record_count").field("tenant_id")
            .size(10).order(Terms.Order.aggregation("count_num", false))
    termsAggregationBuilder.subAggregation(sumBuilder)

    TermsAggregationBuilder resultBuilder = termsAggregationBuilder

    return resultBuilder
  }

  /**
   * 创建企业id下apiName统计Agg
   * @return
   */
  def createdTenantApiNameCountAggBuilder() {
    def sumAggregationBuilder = AggregationBuilders.sum("apiNameCount").field("count")
    TermsAggregationBuilder termsAggregationBuilder
    termsAggregationBuilder = AggregationBuilders.terms("apiName").field("api_name")
            .order(Terms.Order.aggregation("apiNameCount", false))
            .size(1000)
    termsAggregationBuilder.subAggregation(sumAggregationBuilder)

    return termsAggregationBuilder
  }

  /**
   * 构建企业下数据量agg
   * @return
   */
  def createdTenantCountAggBuilder() {
    AggregationBuilders.sum("tenantCount").field("count")
  }

  /**
   * 构建企业下有数据apiName的查询agg
   * @return
   */
  def createdHaveDataApiNameAggBuilder() {
    return AggregationBuilders.count("apiName").field("api_name")
  }

}
