package com.fxiaoke.paas.console.mapper.metadata

import com.fxiaoke.paas.console.entity.metadata.MtDescribe
import com.github.mybatis.mapper.IBatchMapper
import com.github.mybatis.mapper.ICrudMapper
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * Created on 2018/3/6.
 */
interface StatMapper extends ICrudMapper, IBatchMapper, ITenant<StatMapper> {

    /**
     * 查询企业ID下的数据量(没有涉及版本)
     * @param tenantId 企业ID
     * @return
     */
    @Select("SELECT COUNT(*) FROM mt_describe WHERE tenant_id = #{tenantId}")
    int findTenantDataNum(@Param("tenantId") String tenantId)

    /**
     * tenantId下对应的describe_api_name数（没有涉及版本）
     * @param tenantId 企业ID
     * @return
     */
    @Select("SELECT describe_api_name,count(*) FROM mt_describe WHERE tenant_id = #{tenantId} GROUP BY describe_api_name")
    List<Map> findDescribeApiName(@Param("tenantId") String tenantId)

    /**
     * 查询指定tenantId下的describe
     * @param tenantId 企业ID
     * @return
     */
    @Select("SELECT DISTINCT * FROM mt_describe WHERE tenant_id = #{tenantId} AND is_current = true AND is_deleted IS NOT TRUE")
    List<MtDescribe> findDescribeByTenantId(@Param("tenantId") String tenantId)

    /**
     * 查询tenantId下当前版本对象总数
     * @param tenantId
     * @return
     */
    @Select("SELECT COUNT(*) FROM mt_describe WHERE tenant_id = #{tenantId} AND is_current = true AND is_deleted IS NOT TRUE")
    Integer findDescribeNumByTenantId(@Param("tenantId") String tenantId)

    /**
     * 指定修改时间范围查询
     * @param tenantId 企业ID
     * @param startTime 初始时间
     * @param endTime 结束时间
     * @return
     */
    @Select("SELECT DISTINCT * FROM mt_describe WHERE tenant_id = #{tenantId} AND is_current = true  AND is_deleted IS NOT TRUE AND last_modified_time > #{startTime} AND last_modified_time < #{endTime}")
    List<MtDescribe> findDescribeByTime(@Param("tenantId") String tenantId, @Param("startTime") Long startTime, @Param("endTime") Long endTime)

    /**
     * 批量查询通表
     * @param tenantId 企业ID
     * @param describeApiNames 描述名称
     * @return
     */
    List<Map<String, Integer>> batchFindDesNum(@Param("tenantId") String tenantId, @Param("describeApiNames") List<String> describeApiNames)

    /**
     * 查询专表数据量
     * @param table 表名
     * @param tenantId 企业ID
     * @return
     */
    @Select("SELECT object_describe_api_name,COUNT(*) FROM \${table} WHERE tenant_id = #{tenantId} AND is_deleted = 0 AND object_describe_api_name = #{describeApiName}  GROUP BY object_describe_api_name")
    Map findDataNum(@Param("table") String table, @Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName)

    /**
     * 获取老对象数据量
     * @param table
     * @param tenantId
     * @return
     */
    @Select("SELECT COUNT(1) FROM \${table} WHERE ei = #{tenantId} AND is_deleted IS NOT true")
    Long findStoreTableNumByEi(@Param("table") String table, @Param("tenantId") String tenantId)

    @Select("SELECT COUNT(1) FROM \${table} WHERE ei = #{tenantId} AND is_deleted = 0")
    Long findStoreTableNumByEiAndIsDeleted(@Param("table") String table, @Param("tenantId") String tenantId)

    /**
     * 获取指定表的字段
     * @param tableName
     * @return
     */
    @Select("select column_name,data_type from information_schema.columns where table_name = #{tableName}")
    List<Map> findFieldByTableName(@Param("tableName") String tableName)

    /**
     * 查询describeIds
     * @return
     */
    List<String> findDescribeIds(@Param("tenantId") String tenantId, @Param("describeApiNames") List<String> describeApiNames)

    /**
     * 获取field数量
     * @param describeIds
     * @return
     */
    Integer findFieldSum(@Param("describeIds") List<String> describeIds)

    /**
     * 获取所有有效field
     * @param describeIds
     * @return
     */
    List<Map<String, Object>> findField(@Param("describeIds") List<String> describeIds)

    /**
     *  获取对象下的所有describeId
     * @param tenantId
     * @param describeApiName
     * @return
     */
    @Select("SELECT * FROM mt_describe WHERE tenant_id = #{tenantId} AND describe_api_name = #{describeApiName} AND is_current = true")
    List<String> findAppointDescribeIds(@Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName)

    /**
     * 获取describeId对应的字段数
     * @param describeIds
     * @return
     */
    List<Integer> findFieldNumByDescribeIds(@Param("describeIds") List<String> describeIds)

    /**
     * 获取指定对象(注意当前版本)
     * @return
     */
    @Select("SELECT * FROM mt_describe WHERE tenant_id = #{tenantId} AND describe_api_name = #{describeApiName} AND is_current = true")
    MtDescribe findMtDescribe(@Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName)

    @Select("\${sql}")
    MtDescribe findMtDescribe2(@Param("sql") String sql)

    /**
     * 根据describeId获取对象近三个月数据量(通表)
     * @param describeId
     * @param threeMonth
     * @return
     */
    @Select("select last_modified_time from mt_data where object_describe_id = #{describeId} AND last_modified_time > #{threeMonth} ORDER BY last_modified_time")
    List<Map<String, Object>> findDataNumByDescribeId(@Param("describeId") String describeId, @Param("threeMonth") Long threeMonth)

    @Select("\${sql}")
    List<Map<String, Object>> findDataNumByDescribeId2(@Param("sql") String sql)


}