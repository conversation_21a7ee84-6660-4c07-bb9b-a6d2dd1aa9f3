package com.fxiaoke.paas.console.mq.hamster

import com.facishare.hamster.pojo.HamsterTaskPojo
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.paas.console.mq.hamster.validation.TableDataCountService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(locations = ["classpath:mapperContext.xml"])
@Slf4j
class HamsterEventConsumerTest extends Specification {

  @Autowired
  TableDataCountService tableDataCountService
  def "test12"() {
    given:
    HamsterTaskPojo task = new HamsterTaskPojo();
    task.setServiceCloud("fstest")
    task.setTargetServiceCloud("fstest")
    JdbcConnection connectionNewPaas = tableDataCountService.getConnection("77996", "PAAS", true, task);
    String kkk =  String.format("grant select,insert,delete,update,trigger,references on table %s.fd_id_generator  to fs_pgdb_b_u_metadata;",
            "sch_" + 77996)
    connectionNewPaas.executeUpdate(kkk);
    println("-----------------")
  }
}
