<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
    <style>
        .spinner {
            margin: 100px auto;
            width: 40px;
            height: 40px;
            position: relative;
        }

        .container1 > div, .container2 > div, .container3 > div {
            width: 9px;
            height: 9px;
            background-color: #67CF22;

            border-radius: 100%;
            position: absolute;
            -webkit-animation: bouncedelay 1.2s infinite ease-in-out;
            animation: bouncedelay 1.2s infinite ease-in-out;
            -webkit-animation-fill-mode: both;
            animation-fill-mode: both;
        }

        .spinner .spinner-container {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .container2 {
            -webkit-transform: rotateZ(45deg);
            transform: rotateZ(45deg);
        }

        .container3 {
            -webkit-transform: rotateZ(90deg);
            transform: rotateZ(90deg);
        }

        .circle1 {
            top: 0;
            left: 0;
        }

        .circle2 {
            top: 0;
            right: 0;
        }

        .circle3 {
            right: 0;
            bottom: 0;
        }

        .circle4 {
            left: 0;
            bottom: 0;
        }

        .container2 .circle1 {
            -webkit-animation-delay: -1.1s;
            animation-delay: -1.1s;
        }

        .container3 .circle1 {
            -webkit-animation-delay: -1.0s;
            animation-delay: -1.0s;
        }

        .container1 .circle2 {
            -webkit-animation-delay: -0.9s;
            animation-delay: -0.9s;
        }

        .container2 .circle2 {
            -webkit-animation-delay: -0.8s;
            animation-delay: -0.8s;
        }

        .container3 .circle2 {
            -webkit-animation-delay: -0.7s;
            animation-delay: -0.7s;
        }

        .container1 .circle3 {
            -webkit-animation-delay: -0.6s;
            animation-delay: -0.6s;
        }

        .container2 .circle3 {
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }

        .container3 .circle3 {
            -webkit-animation-delay: -0.4s;
            animation-delay: -0.4s;
        }

        .container1 .circle4 {
            -webkit-animation-delay: -0.3s;
            animation-delay: -0.3s;
        }

        .container2 .circle4 {
            -webkit-animation-delay: -0.2s;
            animation-delay: -0.2s;
        }

        .container3 .circle4 {
            -webkit-animation-delay: -0.1s;
            animation-delay: -0.1s;
        }

        @-webkit-keyframes bouncedelay {
            0%, 80%, 100% {
                -webkit-transform: scale(0.0)
            }
            40% {
                -webkit-transform: scale(1.0)
            }
        }

        @keyframes bouncedelay {
            0%, 80%, 100% {
                transform: scale(0.0);
                -webkit-transform: scale(0.0);
            }
            40% {
                transform: scale(1.0);
                -webkit-transform: scale(1.0);
            }
        }
    </style>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>描述字段扩容</h1>
        <ol class="breadcrumb">
            <li><a href="../"><i class="fa fa-dashboard"></i>主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>描述字段扩容</a></li>
        </ol>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info row">
                    <div class="box-header with-border" style="padding: 0px">
                    </div>
                    <form class="form-horizontal" action="" onsubmit="return false;" method="post" id="myForm" role="form" data-toggle="validator">
                        <div class="box-body col-xs-6" style="margin-right: -127px">

                            <div class="form-group">
                                <label for="fieldType" class="col-sm-2 control-label">类型</label>
                                <div class="col-sm-6">
                                    <select id="fieldType" name="fieldType" class="selectpicker show-tick form-control" title="Nothing selected" data-live-search="false"">
                                        <list>
                                            <option value="text" selected="selected">文本</option>
                                            <option value="number">数字</option>
                                        </list>
                                    </select>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="tenantId" class="col-sm-2 control-label">企业ID</label>
                                <div class="col-sm-6">
                                    <input type="text" style="border-radius:5px;" class="form-control" id="tenantId" name="tenantId" value="" placeholder="必填" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="describeApiName" class="col-sm-2 control-label">对象apiName</label>
                                <div class="col-sm-6">
                                    <input type="text" style="border-radius:5px;" class="form-control" id="describeApiName" name="tenantId" value="" placeholder="必填" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="fieldApiName" class="col-sm-2 control-label">字段apiName</label>
                                <div class="col-sm-6">
                                    <input type="text" style="border-radius:5px;" class="form-control" id="fieldApiName" name="tenantId" value="" placeholder="必填" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="attributeType" class="col-sm-2 control-label">扩容属性</label>
                                <div class="col-sm-6">
                                    <input type="text" style="border-radius:5px;" class="form-control" id="attributeType" name="attributeType" value="" placeholder="数字用，length(整数位)、decimal_places(小数位)">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="apiName" class="col-sm-2 control-label">目标长度</label>
                                <div class="col-sm-6">
                                    <input type="text" style="border-radius:5px;" class="form-control" id="capacity" name="capacity" value="" placeholder="非必填">
                                </div>
                            </div>
                            <div class="col-sm-offset-3" style="float: left">
                                <button type="button" id="queryButReadOnly" class="btn btn-info">查询</button>
                                <button type="button" id="addCapacity" class="btn btn-primary" disabled>扩容</button>
                                <button type="button" id="switch2LogText" class="btn btn-primary" >转多行文本</button>
                            </div>
                        </div>

                </div>
                <#--result展示-->
                <div class="box-body col-xs-6">
                    <h4>Result <i class="fa fa-hand-o-down"></i></h4>
                    <pre id="queryResult">
            </pre>
                    <#--loading。。。-->
                    <div class="spinner hide" id="loading">
                        <div class="spinner-container container1">
                            <div class="circle1"></div>
                            <div class="circle2"></div>
                            <div class="circle3"></div>
                            <div class="circle4"></div>
                        </div>
                        <div class="spinner-container container2">
                            <div class="circle1"></div>
                            <div class="circle2"></div>
                            <div class="circle3"></div>
                            <div class="circle4"></div>
                        </div>
                        <div class="spinner-container container3">
                            <div class="circle1"></div>
                            <div class="circle2"></div>
                            <div class="circle3"></div>
                            <div class="circle4"></div>
                        </div>
                    </div>
                </div>
                </form>
            </div>
        </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#queryButReadOnly').on('click', function () {
                $('#queryResult').html('');
                $('#loading').removeClass('hide');
                $('#queryButReadOnly').attr("disabled", "disabled");

                var tenantId = $('#tenantId').val();
                var describeApiName = $('#describeApiName').val();
                var fieldApiName = $('#fieldApiName').val();
                //TODO: 指向操作接口
                $.post("${CONTEXT_PATH}/metadata-operate/add-capacity/query", {
                    describeApiName: describeApiName,
                    fieldApiName: fieldApiName,
                    tenantId: tenantId
                }, function (result) {
                    $('#queryButReadOnly').removeAttr("disabled");
                    $('#loading').addClass('hide');

                    if (result.code === 200) {
                        info = result.info
                    } else {
                        info = result
                    }
                    $('#queryResult').JSONView(info, {
                        collapsed: false,
                        nl2br: true,
                        recursive_collapser: true
                    });
                });
            });
            $('#addCapacity').on('click', function () {
                $('#queryResult').html('');
                $('#loading').removeClass('hide');
                $('#addCapacity').attr("disabled", "disabled");
                var fieldType = $('#fieldType').val();
                var tenantId = $('#tenantId').val();
                var describeApiName = $('#describeApiName').val();
                var fieldApiName = $('#fieldApiName').val();
                var attributeType = $('#attributeType').val();
                var capacity = $('#capacity').val();
                //TODO: 指向操作接口
                $.post("${CONTEXT_PATH}/metadata-operate/add-capacity/add", {
                    fieldType: fieldType,
                    describeApiName: describeApiName,
                    fieldApiName: fieldApiName,
                    tenantId: tenantId,
                    attributeType: attributeType,
                    newCapacity: capacity
                }, function (result) {
                    $('#addCapacity').removeAttr("disabled");
                    $('#loading').addClass('hide');

                    if (result.code === 200) {
                        info = result.info
                    } else {
                        info = result
                    }
                    $('#queryResult').JSONView(info, {
                        collapsed: false,
                        nl2br: true,
                        recursive_collapser: true
                    });
                });
            });
            $('#switch2LogText').on('click', function () {
                $('#queryResult').html('');
                $('#loading').removeClass('hide');
                $('#addCapacity').attr("disabled", "disabled");
                var fieldType = $('#fieldType').val();
                var tenantId = $('#tenantId').val();
                var describeApiName = $('#describeApiName').val();
                var fieldApiName = $('#fieldApiName').val();
                //TODO: 指向操作接口
                $.post("${CONTEXT_PATH}/metadata-operate/add-capacity/switch/long/text", {
                    fieldType: fieldType,
                    describeApiName: describeApiName,
                    fieldApiName: fieldApiName,
                    tenantId: tenantId
                }, function (result) {
                    $('#addCapacity').removeAttr("disabled");
                    $('#loading').addClass('hide');

                    if (result.code === 200) {
                        info = result.info
                    } else {
                        info = result
                    }
                    $('#queryResult').JSONView(info, {
                        collapsed: false,
                        nl2br: true,
                        recursive_collapser: true
                    });
                });
            });
        })

        const selectElement = document.getElementById("fieldType");

        selectElement.addEventListener("change", function(event) {
            const selectedOption = selectElement.options[selectElement.selectedIndex]
            if (selectedOption.value ==="number") {
                console.log("number is selected")
                $('#switch2LogText').addClass('hide');
            } else if (selectedOption.value ==="text") {
                console.log("text is selected")
                $('#switch2LogText').removeClass('hide');
            }
            // 在此处编写处理select切换事件的代码
        });
        /**
         * 表单验证
         */
        var required1 = false, required2 = false, required3 = false, required4 =false;

        $('#tenantId').bind("input propertychange", function () {
            var tenantIdValue = $('#tenantId').val();
            if (tenantIdValue === null || tenantIdValue === "") {
                required1 = false;
            } else {
                required1 = true;
            }
        });

        $('#describeApiName').bind("input propertychange", function () {
            var apiNameValue = $('#describeApiName').val();
            if (apiNameValue === null || apiNameValue === "") {
                required2 = false;
            } else {
                required2 = true;
            }
        });

        $('#fieldApiName').bind("input propertychange", function () {
            var dataIdValue = $('#fieldApiName').val();
            if (dataIdValue === null || dataIdValue === "") {
                required3 = false;
            } else {
                required3 = true;
            }
        });

        $('#capacity').bind("input propertychange", function () {
            var dataIdValue = $('#capacity').val();
            if (dataIdValue === null || dataIdValue === "") {
                required4 = false;
            } else {
                required4 = true;
            }
        });


        $('#tenantId,#describeApiName,#fieldApiName,#capacity').bind("input propertychange", function () {
            if (required1 && required2 && required3 && required4) {
                $('#addCapacity').removeAttr("disabled");
            } else {
                $('#addCapacity').attr("disabled", "disabled");
            }
        });
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />