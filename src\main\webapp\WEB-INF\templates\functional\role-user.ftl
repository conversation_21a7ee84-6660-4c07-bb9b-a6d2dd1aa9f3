<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
    <h1>批量查询角色下的用户</h1>
    <ol class="breadcrumb">
        <li><a href="../"><i class="fa fa-dashboard"></i>任务主页</a></li>
        <li><a href=""><i class="fa fa-dashboard"></i></a></li>
    </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info row">
                <div id="warningInfo" class="alert alert-warning hide">
                    <span id="closeInfo" href="#" class="close">&times;</span>
                    <strong id="hitInfo"></strong>
                </div>
                <div class="box-header with-border">
                    <h3 class="box-title"></h3>
                </div>
                <form class="form-horizontal" action="" method="post" id="myForm" role="form"
                      data-toggle="validator">
                    <div class="box-body col-xs-6" style="margin-right: -127px">
                        <div class="form-group">
                            <label for="tenantId" class="col-sm-2 control-label">企业ID</label>
                            <div class="col-sm-6">
                                <input type="text" style="border-radius:5px;" class="form-control" id="tenantId" name="tenantId" value="" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="appId" class="col-sm-2 control-label">AppId</label>
                            <div class="col-sm-6">
                                <select id="appId" name="appId" class="selectpicker show-tick form-control" title="Nothing selected" data-live-search="false">
                                    <#list appIdList! as appId>
                                        <option value="${appId}" <#if ((appId) == "CRM")>selected="selected"</#if>>${appId!}</option>
                                    </#list>
                                </select>
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="userId" class="col-sm-2 control-label">用户ID</label>
                            <div class="col-sm-6">
                                <input type="text" style="border-radius:5px;" class="form-control" id="userId" name="userId" value="" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="roles" class="col-sm-2 control-label">角色</label>
                            <div class="col-sm-6">
                                <textarea id="roles" name="roles" class="form-control" style="height: 250px;border-radius:5px;" required></textarea>
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="col-sm-offset-3 col-sm-2">
                            <button type="button" id="findSub" class="btn btn-primary">查询</button>
                        </div>
                    </div>
                    <div class="box-body col-xs-6">
                        <table id="datatable" class="table table-hover table-bordered" cellspacing="0" width="100%">
                            <thead>
                            <tr>
                                <th>角色</th>
                                <th>用户</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<script>
    $(document).ready(function () {
        var _table = $("#datatable").DataTable({
//            "deferRender": true,
            "processing": true,
//            "ajax": "",
            "columnDefs": [],
            "language": {
                "url": "${ctx}/static/js/datatables-zh_CN.json"
            },
            "mark": true
//            "paging":false
        });

//        查询
        $('#findSub').on('click', function () {
            $('#warningInfo').addClass('hide');
            var tenantId = $('#tenantId').val();
            var roles = $('#roles').val();
            if (tenantId === "" || roles === "") {
                $('#warningInfo').removeClass('hide');
                $('#hitInfo').html('关键字段不能为空！');
                return;
            }
            var appId = $('#appId').val();
            var userId = $('#userId').val();
            $.getJSON("${CONTEXT_PATH}/func/batch-user", {
                tenantId: tenantId,
                appId: appId,
                userId: userId,
                roles: roles
            }, function (result) {
                if (result.code === 200) {
                    $("#datatable").dataTable().fnClearTable();
                    $("#datatable").dataTable().fnAddData(result.data);
                } else {
                    $('#warningInfo').removeClass('hide');
                    $('#hitInfo').html(result.error);
                }
            });
        });

    });
    /**
     * 信息提示栏关闭
     */
    $('#closeInfo').on('click', function () {
        $('#warningInfo').addClass('hide');
    });

</script>
</#assign>
<#include "../layout/layout-main.ftl" />
