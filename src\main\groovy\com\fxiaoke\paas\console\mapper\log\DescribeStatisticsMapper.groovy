package com.fxiaoke.paas.console.mapper.log

import com.fxiaoke.paas.console.entity.log.DescribeStatistics
import com.github.mybatis.mapper.IBatchMapper
import com.github.mybatis.mapper.ICrudMapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * Created on 2018/8/29.
 */
interface DescribeStatisticsMapper extends ICrudMapper<DescribeStatistics>, IBatchMapper<DescribeStatistics> {

  /**
   * 查询近一年数据
   * @param time
   * @return
   */
  @Select("SELECT * FROM describe_statistics WHERE create_time > #{time} ORDER BY create_time DESC")
  List<DescribeStatistics> findAllByTime(@Param("time") Date time)

}