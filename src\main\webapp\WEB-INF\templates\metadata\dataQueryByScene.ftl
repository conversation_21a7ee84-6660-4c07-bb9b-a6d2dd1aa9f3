<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>模拟场景筛选</h1>
        <ol class="breadcrumb">
            <li><a href="${ctx}"><i class="fa fa-dashboard"></i>主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>模拟场景筛选</a></li>
        </ol>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info row">
                    <div class="box-header with-border">
                    </div>
                    <form class="form-horizontal" action="" method="post" id="infoFrom" role="form" data-toggle="validator">
                        <div class="box-body col-xs-6">
                            <div class="form-group">
                                <label for="tenantId" class="col-sm-4 control-label">tenantId</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" style="border-radius:5px;" id="tenantId" placeholder="企业ID(必填)" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="userId" class="col-sm-4 control-label">userId</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" style="border-radius:5px;" id="userId" placeholder="账号ID(必填)" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="describeApiName" class="col-sm-4 control-label">describeApiName</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" style="border-radius:5px;" id="describeApiName" placeholder="describeApiName(必填)" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="dataName" class="col-sm-4 control-label">dataName</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" style="border-radius:5px;" id="dataName" placeholder="dataName">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="isAuth" class="col-sm-4 control-label">is_auth</label>
                                <div class="col-sm-4">
                                    <select id="isAuth" class="form-control" style="border-radius:5px;">
                                        <option value="true">true</option>
                                        <option value="false">false</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="sceneType" class="col-sm-4 control-label">场景</label>
                                <div class="col-sm-4">
                                    <select id="sceneType" class="form-control" style="border-radius:5px;">
                                        <option value="ALL">全部</option>
                                        <option value="MY_PARTICIPANT">我参与的</option>
                                        <option value="MY_RESPONSIBLE">我负责的</option>
                                        <option value="MY_SUBORDINATE_RESPONSIBLE">我下属负责的</option>
                                        <option value="MY_RESPONSIBLE_DEPARTMENT">我负责部门的</option>
                                        <option value="MY_SHARED_DATA">共享给我的</option>
                                        <option value="MY_SUBORDINATE_PARTICIPANT">我下属参与的</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="searchSource" class="col-sm-4 control-label">数据来源</label>
                                <div class="col-sm-4">
                                    <select id="searchSource" class="form-control" style="border-radius:5px;">
                                        <option value="db">DB</option>
                                        <option value="es">ES</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="pageSize" class="col-sm-4 control-label">数量</label>
                                <div class="col-sm-4">
                                    <input type="number" class="form-control" style="border-radius:5px;" id="pageSize" placeholder="pageSize">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="detailInfo" class="col-sm-4 control-label">是否显示详细信息</label>
                                <div class="col-sm-4">
                                    <select id="detailInfo" class="form-control" style="border-radius:5px;">
                                        <option value="false">否</option>
                                        <option value="true">是</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-offset-3">
                                <button type="button" id="queryData" class="btn btn-primary">查询</button>
                                <input id="cancel_btn" class="btn" type="button" value="返回" onclick="history.back()"/>
                            </div>
                        </div>
                        <div class="box-body col-xs-6">
                            <h4>Result <i class="fa fa-hand-o-down"></i></h4>
                            <pre id="dataInfo" style="">
                        </pre>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<#--表单验证插件-->
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script>
        $('#queryData').on('click', function () {
            if ($('#tenantId').val() == "" || $('#describeApiName').val() == "" || $('#userId').val() == ""){
                return false;
            }

            $.ajax({
                type:"POST",
                url:"${CONTEXT_PATH}/metadata/dataQuery/findDataByScene",
                data: JSON.stringify({
                    tenantId: $('#tenantId').val(),
                    userId: $('#userId').val(),
                    describeApiName: $('#describeApiName').val(),
                    dataName: $('#dataName').val(),
                    isAuth: $('#isAuth').val(),
                    sceneType: $('#sceneType').val(),
                    searchSource: $('#searchSource').val(),
                    detailInfo: $('#detailInfo').val(),
                    pageSize: $('#pageSize').val()
                }),
                contentType: "application/json",
                dataType:"json",
                success:function(data){
                    if (data !== "") {
                        $('#queryData').removeAttr("disabled");
                        $('#dataInfo').JSONView(data, {
                            collapsed: false,
                            nl2br: true,
                            recursive_collapser: true
                        });
                    }
                }
            })

        });

    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
