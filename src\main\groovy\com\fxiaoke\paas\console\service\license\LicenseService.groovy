package com.fxiaoke.paas.console.service.license

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.facishare.paas.license.Result.*
import com.facishare.paas.license.arg.*
import com.facishare.paas.license.common.LicenseContext
import com.facishare.paas.license.common.LicenseObjectInfoContext
import com.facishare.paas.license.common.Result
import com.facishare.paas.license.constant.LicenseConstant
import com.facishare.paas.license.exception.LicenseException
import com.facishare.paas.license.exception.PaasMessage
import com.facishare.paas.license.http.LicenseClient
import com.facishare.paas.license.pojo.LicenseAccessResult
import com.facishare.paas.license.pojo.MasterLicensePojo
import com.facishare.paas.license.pojo.ModuleParaPojo
import com.facishare.paas.license.pojo.ProductInfoPojo
import com.facishare.paas.license.pojo.ProductLicensePojo
import com.facishare.paas.pod.client.DbRouterClient
import com.fxiaoke.common.http.handler.SyncCallback
import com.fxiaoke.common.http.spring.OkHttpSupport
import com.fxiaoke.paas.console.bean.license.OverviewDto
import com.fxiaoke.paas.console.bean.license.QuoteOverviewRequest
import com.fxiaoke.paas.console.entity.license.LicenseObjectEntity
import com.fxiaoke.paas.console.entity.license.ModuleInfoEntity
import com.fxiaoke.paas.console.entity.license.ModuleInfoIdCodeEntity
import com.fxiaoke.paas.console.entity.license.ProductLicenseEntity
import com.fxiaoke.paas.console.license.mapper.*
import com.fxiaoke.paas.console.mapper.metadata.DataMapper
import com.fxiaoke.paas.console.mapper.metadata.DescribeMapper
import com.fxiaoke.paas.console.service.metadata.JdbcService
import com.fxiaoke.template.server.LicenseServer2
import com.github.autoconf.ConfigFactory
import com.google.common.base.CharMatcher
import com.google.common.base.Splitter
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import groovy.util.logging.Slf4j
import lombok.Getter
import okhttp3.*
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import javax.annotation.Resource

@Service
@Slf4j
class LicenseService {

  private String licenceVersionUrl
  private String licenceUrl
  private String productUrl
  private String updateParaUrl
  private String queryParaUrl
  private String celarCatchUrl
  private String queryModuleUrl
  private String queryObjectUrl
  private String queryBySqlUrl
  private String sendMqUrl
  private String queryPackageUrl
  private String queryOverviewInfoUrl
  private String scanOverviewUrl
  private String licenseBaseUrl
  private Set<String> vipTenantIds = Sets.newHashSet()

  @Resource(name = "httpSupport")
  OkHttpSupport okHttpSupport
  @Resource(name = "licenseClient")
  private LicenseClient licenseClient
  @Resource
  private LicenseServer2 licenseServer2
  @Autowired
  private DataMapper dataMapper;
  @Autowired
  private ModuleInfoMapper moduleInfoMapper
  @Autowired
  private ProductGrayMapper productGrayMapper
  @Autowired
  private ProductInfoMapper productInfoMapper
  @Autowired
  private ProductLicenseMapper productLicenseMapper
  @Autowired
  private LicenseRelationMapper licenseRelationMapper
  @Autowired
  private ModuleParaMapper moduleParaMapper
  @Autowired
  private LicenseObjectInfoMapper licenseObjectInfoMapper
  @Autowired
  private DescribeMapper describeMapper
  @Autowired
  private DbRouterClient dbRouterClient
  @Autowired
  private JdbcService jdbcService

  @Getter
  private Map<String, String> LicenseChineseNameMap = Maps.newHashMap()
  @Getter
  private Map<String, String> KeyChineseNameMap = Maps.newHashMap()

  private Map<String, String> ObjectChineseNameMap = Maps.newHashMap()

  private Map<String, String> VersionChineseNameMap = Maps.newHashMap()

  private JSONObject moduleRelation

  Map<String, String> putOutModule

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("paas-console-base,fs-paas-license", { config ->
      licenceVersionUrl = config.get("licenceVersionUrl")
      licenceUrl = config.get("licenceUrl")
      productUrl = config.get("productUrl")
      updateParaUrl = config.get("updateParaUrl")
      queryParaUrl = config.get("queryParaUrl")
      celarCatchUrl = config.get("celarCatchUrl")
      queryModuleUrl = config.get("queryModuleUrl")
      queryObjectUrl = config.get("queryObjectUrl")
      queryBySqlUrl = config.get("queryBySqlUrl")
      sendMqUrl = config.get("sendMqUrl")
      queryPackageUrl = config.get("queryPackageUrl", "http://paas.nsvc.foneshare.cn/fs-paas-license/internal/license/query/package")
      queryOverviewInfoUrl = config.get("queryOverviewInfoUrl", "http://paas.nsvc.foneshare.cn/fs-paas-license/paasLicense/query/overview")
      scanOverviewUrl = config.get("scanOverviewUrl", "http://************:56188/fs-paas-license-surrogate/scan/scanTenantId")
      licenseBaseUrl = config.get("licenseBaseUrl")
      vipTenantIds = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(config.get("vipTenantIds"))
    })
    ConfigFactory.getConfig("fs-paas-license-static", { config ->
      JSONObject jsonObject = JSONObject.parseObject(config.getString())
      LicenseChineseNameMap = (Map<String, String>) jsonObject.get("LicenseChineseNameMap")
      ObjectChineseNameMap = (Map<String, String>) jsonObject.get("ObjectChineseNameMap")
      KeyChineseNameMap = (Map<String, String>) jsonObject.get("KeyChineseNameMap")
      VersionChineseNameMap = (Map<String, String>) jsonObject.get("VersionChineseNameMap")
    })
    Splitter.MapSplitter splitter = Splitter.on(CharMatcher.anyOf(",")).withKeyValueSeparator(":");
    ConfigFactory.getConfig("fs-paas-license-relation", { config -> moduleRelation = JSONObject.parseObject(config.getString()) })
    ConfigFactory.getConfig("fs-paas-license", { config -> putOutModule = splitter.split(config.get("putOutModule")); })
  }

  /**
   * 查询licnese版本
   * @param arg
   * @return
   * @throws LicenseException
   */
  LicenseVersionResult queryProductVersion(QueryProductArg arg) throws LicenseException {
//    licenseClient.updateTime(UpdateLicenseDateArg.builder().context(arg.getLicenseContext()).orderNumber("123456789").expiredTime(1650297600001).build())
//    this.createProduct(arg.getLicenseContext())
    return licenseClient.queryProductVersion(arg)
  }

  /**
   * 查询licnese版本
   * @param arg
   * @return
   * @throws LicenseException
   */
  LicenseAccessResult accessLicense(LicenseAccessArg arg) throws LicenseException {
//    licenseClient.updateTime(UpdateLicenseDateArg.builder().context(arg.getLicenseContext()).orderNumber("123456789").expiredTime(1650297600001).build())
//    this.createProduct(arg.getLicenseContext())
    return licenseClient.access(arg)
  }

  /**
   * 查询指定license
   * @param arg
   * @return
   */
  def queryLicense(QueryDefaultDataArg arg) throws LicenseException {
    return postToLicense(licenceUrl, JSONObject.toJSONString(arg), arg.getContext().getTenantId(), LicenseListResult.class)
  }

  /**
   * 查询product
   * @param arg
   * @return
   */
  def queryProduct(QueryProductArg arg) throws LicenseException {
    return postToLicense(productUrl, JSONObject.toJSONString(arg), arg.getLicenseContext().getTenantId(), ProductInfoResult.class)
  }

  /**
   * 查询企业下module
   * @param arg
   * @return
   */
  ModuleInfoResult queryModule(QueryModuleArg arg) throws LicenseException {
//        return postToLicense(queryModuleUrl, JSONObject.toJSONString(arg), arg.getLicenseContext().getTenantId(), ModuleInfoResult.class)
    return licenseClient.queryModule(arg)
  }

  /**
   * 查询配额
   * @param arg
   * @return
   */
  def queryPara(QueryModuleParaArg arg) {
    BatchQueryModuleParaArg arg1 = new BatchQueryModuleParaArg()
    arg1.setContext(arg.getContext())
    Map<String, Set<String>> map = Maps.newHashMap()
    map.put(arg.getModuleCode(), arg.getParaKeys())
    arg1.setModuleCodeParaKeyMap(map)
    return licenseClient.batchQueryModulePara(arg1)
//    return licenseClient.queryModulePara(arg)
  }

  /**
   * 更新配额
   * @param arg
   * @return
   */
  def updatePara(UpdateModuleParaArg arg) {
    return postToLicense(updateParaUrl, JSONObject.toJSONString(arg), arg.getContext().getTenantId(), Result.class)
  }

  /**
   * 清除缓存
   * @param context
   * @return
   */
  def clearCatch(LicenseContext context) {
    return postToLicense(celarCatchUrl, JSONObject.toJSONString(context), context.getTenantId(), Result.class)
  }

  /**
   * 添加灰度产品
   * @param postbody
   * @return
   */
  def postToGrayPrice(Map<String, Object> postbody) {
    return postToLicense(licenseBaseUrl + "/paas/license/gray/add", JSONObject.toJSONString(postbody))
  }

  /**
   * 添加对象
   * @param postbody
   * @return
   */
  def addLincenseObject(Map<String, Object> postbody) {
    return postToLicense(licenseBaseUrl + "/internal/license/add/license/object", JSONObject.toJSONString(postbody))
  }

  /**
   * 删除对象
   * @param postbody
   * @return
   */
  def deleteLincenseObject(Map<String, Object> postbody) {
    return postToLicense(licenseBaseUrl + "/internal/license/delete/license/object", JSONObject.toJSONString(postbody))
  }

  def queryLicenseByOrderNumber(CheckOrderNumberArg arg) {
    List<ProductLicenseEntity> licenses = productLicenseMapper.queryProductLicenseByOrderNumber(arg.getContext().getTenantId(), arg.getOrderNumbers())
    for (final def license in licenses) {
      if (license.getProductVersion().equals("extention_package")) {
        ModuleParaPojo para = moduleParaMapper.selectPara(arg.getContext().getTenantId(), license.getProductId())
        license.setModuleCode(para.getModuleCode())
        license.setParaKey(para.getParaKey())
        license.setParaValue(para.getParaValue())
      }
    }
    return licenses
  }

  def handleSpecialApproval(SpecialApprovalArg arg) {
    return postToLicense(licenseBaseUrl + "/product/launch/handleSpecialApprovals", JSONObject.toJSONString(arg))
  }

  def queryFsSalesOrderInfoByName(String tenantId, String name) {
    return dataMapper.setTenantId(tenantId).queryFsSalesOrderInfoByName(tenantId, name)
  }

  def queryFsSalesOrderProductInfoByName(String tenantId, String name) {
    return dataMapper.setTenantId(tenantId).queryFsSalesOrderProductInfoByName(tenantId, name)
  }

  def queryFsProductInfoByName(String tenantId, String id) {
    return dataMapper.setTenantId(tenantId).queryFsProductInfoByName(tenantId, id)
  }


  /**
   * 根据企业id查找主版本管理
   * @param tenantId
   * @return
   */
  ProductInfoResult queryMasterVersion(String tenantId) {
    return postToLicense(licenseBaseUrl + "/internal/license/master/query", tenantId)
  }

  List<MasterLicensePojo> allMajorLicense(String tenantId) {
    return queryAllVersion(tenantId)
  }

  List<MasterLicensePojo> queryAllVersion(String tenantId) {
    if (StringUtils.isEmpty(tenantId)) {
      throw new LicenseException(PaasMessage.SYSTEM_ERROR.getCode(), PaasMessage.SYSTEM_ERROR.getMessage() + "tenantId is empty");
    }
    List<MasterLicensePojo> result = Lists.newArrayList();
    //获取所有主版本 行业套件
    List<ProductInfoPojo> productInfoEntities = productInfoMapper.queryProductInfoByTenantId(tenantId);
    getAllLicense(tenantId, result, productInfoEntities);
    return result;
  }

  private void getAllLicense(String tenantId, List<MasterLicensePojo> result, List<ProductInfoPojo> productInfoEntities) {
    productInfoEntities.each { productInfoEntity ->
      //查询是否有父版本
      String parentProductId = licenseRelationMapper.findParentLicenses(productInfoEntity.getTenantId(), productInfoEntity.getId(), productInfoEntity
              .getProductVersion(), productInfoEntity.getProductName());
      if (StringUtils.isNotEmpty(parentProductId)) {
        ProductInfoPojo parentProductInfo = productInfoMapper.queryProInfoByProId(parentProductId);
        getModuleLicense(tenantId, result, productInfoEntity, parentProductInfo);
      } else {
        getModuleLicense(tenantId, result, productInfoEntity, null);
      }
    }
  }

  private void getModuleLicense(String tenantId,
                                List<MasterLicensePojo> result,
                                ProductInfoPojo productInfoEntity,
                                ProductInfoPojo parentProductInfoEntity) {
    List<String> productIds = Lists.newArrayList(productInfoEntity.getId());
    List<MasterLicensePojo> masterLicensePojoList = Lists.newArrayList();
    if (Objects.nonNull(parentProductInfoEntity)) {
      productIds.add(parentProductInfoEntity.getId());
      masterLicensePojoList = moduleInfoMapper.queryModuleInfoByProId2(tenantId, productIds);
    } else {
      masterLicensePojoList = moduleInfoMapper.queryModuleInfoByProId2(tenantId, productIds);
    }

    Map<String, MasterLicensePojo> moduleParaPojoHashMap = Maps.newHashMap();
    List<MasterLicensePojo> masterLicense = moduleParaMapper.selectAllPara(tenantId, productIds);

    List<String> subtractModules = getSubtractModules(productInfoEntity.getProductVersion())

    Map<String, String> paraKeyValue = getChildPara(productInfoEntity.getProductVersion())
    masterLicense.each { masterLicensePojo ->
      if (subtractModules.contains(masterLicensePojo.getModuleCode())) {
        return
      }
      String key = this.getModuleParaPojoKey(masterLicensePojo);
      if (paraKeyValue.containsKey(masterLicensePojo.getParaKey())) {
        masterLicensePojo.setParaValue(paraKeyValue.get(masterLicensePojo.getParaKey()))
      }
      if (moduleParaPojoHashMap.containsKey(key)) {
        masterLicensePojo.setParaValue(String.valueOf(
                Integer.valueOf(masterLicensePojo.getParaValue()) + Integer.valueOf(moduleParaPojoHashMap.get(key).getParaValue())));
      }
      masterLicensePojo.setProductVersion(productInfoEntity.getProductVersion());
      masterLicensePojo.setProductName(LicenseChineseNameMap.get(productInfoEntity.getProductVersion()));
      masterLicensePojo.setKeyName(KeyChineseNameMap.get(masterLicensePojo.getParaKey()));
      result.add(masterLicensePojo);
      moduleParaPojoHashMap.put(this.getModuleParaPojoKey(masterLicensePojo), masterLicensePojo);
      moduleParaPojoHashMap.put(masterLicensePojo.getModuleCode(), null);
    };


    masterLicensePojoList.each { masterLicensePojo ->
      if (moduleParaPojoHashMap.containsKey(masterLicensePojo.getModuleCode()) || subtractModules.contains(masterLicensePojo.getModuleCode())) {
        return;
      } else {
        masterLicensePojo.setProductVersion(productInfoEntity.getProductVersion());
        masterLicensePojo.setProductName(LicenseChineseNameMap.get(productInfoEntity.getProductVersion()));
        result.add(masterLicensePojo);
        moduleParaPojoHashMap.put(masterLicensePojo.getModuleCode(), masterLicensePojo);
      }
    };
  }

  String getModuleParaPojoKey(MasterLicensePojo masterLicensePojo) {
    if (StringUtils.isAnyEmpty(masterLicensePojo.getModuleCode(), masterLicensePojo.getParaKey())) {
      return null;
    }
    return String.format("%s-%s", masterLicensePojo.getModuleCode(), masterLicensePojo.getParaKey());
  }

  List<String> queryProductBought(ProductBoughtArg arg) {
    String version = arg.getProductVersion();
    String productVersion = productInfoMapper.queryProductInfoId(LicenseConstant.DEFAULT_TENANTID, version, arg.getProductName());
    List<String> appVersion = moduleInfoMapper.queryModuleCodeByModuleCode(LicenseConstant.DEFAULT_TENANTID, Lists.newArrayList(version));
    List<String> grayVersions = productGrayMapper.queryGrayVersions();
    if (StringUtils.isEmpty(productVersion) && CollectionUtils.isEmpty(appVersion) && !grayVersions.contains(version)) {
      throw new LicenseException(PaasMessage.SYSTEM_ERROR.getCode(), PaasMessage.SYSTEM_ERROR.getMessage() + "version not exist");
    }
    List<String> tenantIds = Lists.newArrayList()
    //不要过期的
    if (arg.getExpired()) {
      Long now = System.currentTimeMillis();
      tenantIds.addAll(productLicenseMapper.queryProductBought(version, now));
      tenantIds.addAll(productGrayMapper.queryProductBought(version, now));
    } else {
      tenantIds.addAll(productLicenseMapper.queryProductBought(version, null));
      tenantIds.addAll(productGrayMapper.queryProductBought(version, null));
    }
    return tenantIds;
  }

  List<ProductLicenseEntity> queryProductBoughtV2(ProductBoughtArg arg) {
    String version = arg.getProductVersion();
    String productVersion = productInfoMapper.queryProductInfoId(LicenseConstant.DEFAULT_TENANTID, version, arg.getProductName());
    List<String> appVersion = moduleInfoMapper.queryModuleCodeByModuleCode(LicenseConstant.DEFAULT_TENANTID, Lists.newArrayList(version));
    List<String> grayVersions = productGrayMapper.queryGrayVersions();
    if (StringUtils.isEmpty(productVersion) && CollectionUtils.isEmpty(appVersion) && !grayVersions.contains(version)) {
      throw new LicenseException(PaasMessage.SYSTEM_ERROR.getCode(), PaasMessage.SYSTEM_ERROR.getMessage() + "version not exist");
    }
    List<ProductLicenseEntity> result = Lists.newArrayList()
    //不要过期的
    if (arg.getExpired()) {
      Long now = System.currentTimeMillis();
      result.addAll(productLicenseMapper.queryProductBoughtV2(version, now));
      result.addAll(productGrayMapper.queryProductBoughtV2(version, now));
    } else {
      result.addAll(productLicenseMapper.queryProductBoughtV2(version, null));
      result.addAll(productGrayMapper.queryProductBoughtV2(version, null));
    }
    return result;
  }


  private List<String> getSubtractModules(String productVersion) {
    //获取父版本下的module增减情况
    Map<String, Set<String>> moduleMap = getRelationModules(productVersion);
    if (moduleMap == null) {
      log.warn("productVersion={},the relation modules is nonentity ", productVersion);
      return Lists.newArrayList();
    }
    return new ArrayList<>(moduleMap.get("subtractModules"));
  }

  private Map<String, String> getChildPara(String productVersion) {
    JSONObject relation = moduleRelation.getJSONObject(productVersion)
    if (relation == null) {
      return Maps.newHashMap()
    }
    JSONObject paraRelation = relation.getJSONObject("para")
    if (paraRelation == null) {
      return Maps.newHashMap()
    }

    Map<String, String> paraKeyValue = Maps.newHashMap()
    paraRelation.forEach({ k, v -> paraKeyValue.put(k, String.valueOf(v)) })

    return paraKeyValue
  }

  private Map<String, Set<String>> getRelationModules(String key) {
    JSONObject relation = moduleRelation.getJSONObject(key)
    if (relation == null) {
      return null
    }
    JSONObject moduleRelation = relation.getJSONObject("module")
    if (moduleRelation == null) {
      return null
    }
    Set<String> addModules = Sets.newHashSet()
    Set<String> subtractModules = Sets.newHashSet()
    Set<String> noOutModules = Sets.newHashSet()
    //moduleCode,1
    moduleRelation.forEach({ k, v ->
      if (LicenseConstant.TradeModuleType.ADD_MODULE == v) {
        addModules.add(k)
      } else if (LicenseConstant.TradeModuleType.SUBTRACT_MODULE == v) {
        subtractModules.add(k)
      } else if (LicenseConstant.TradeModuleType.NO_OUT_MODULE == v) {
        noOutModules.add(k)
      }
    });
    Map<String, Set<String>> result = Maps.newHashMap()
    result.put("addModules", addModules)
    result.put("subtractModules", subtractModules)
    result.put("noOutModules", noOutModules)
    return result;
  }

  /**
   * 根据产品id查找应用
   * @param productId
   * @return
   */
  ModuleInfoResult queryModuleInfoByProId(String productId) {
    return postToLicense(licenseBaseUrl + "/internal/query/moduleInfo/byProId", productId)
  }

  ParaInfoResult queryModuleParaByProId(String productId) {
    return postToLicense(licenseBaseUrl + "/internal/query/modulePara/byProId", productId)
  }

  LicenseObjectInfoResult queryObjectByProId(String productId) {
    return postToLicense(licenseBaseUrl + "/internal/query/object/byProId", productId)
  }

  Result queryLicenseRelation(String tenantId) {
    return postToLicense(licenseBaseUrl + "/internal/license/lice/relation", tenantId)
  }


  /**
   * 查询下发对象
   * @param context
   * @return
   */
  Result queryObject(LicenseObjectInfoContext context) {
//        return postToLicense(queryObjectUrl, JSONObject.toJSONString(context), context.getContext().getTenantId(), Result.class)
    return licenseClient.queryApiNameByLicense(context)
  }

  /**
   * sql查询license
   * @param arg
   * @return
   */
  def queryLicenseBySql(QuerySqlArg arg) {
    return postToLicense(queryBySqlUrl, JSONObject.toJSONString(arg), arg.getTenantId(), Result.class)
  }

  /**
   * 查询到期时间
   * @param arg
   * @return
   */
  def queryExpire(QueryExpireTimeArg arg) {

    licenseClient.queryLicenseExpired(arg)
  }

  /**
   * 手动发送mq
   * @param arg
   * @return
   */
  def sendMq(SendMqArg arg) {
    return postToLicense(sendMqUrl, JSONObject.toJSONString(arg), arg.getContext().getTenantId(), Result.class)
  }

  /**
   * 查询企业的员工数
   * @param context
   * @return
   */
  def queryMaxHeadCount(LicenseContext context) {
    return licenseClient.queryMaxHeadCount(context)
  }

  /**
   * 校验指定module
   * @param arg
   * @return
   */
  def judgeModule(JudgeModuleArg arg) {
    licenseServer2.judgeModule()
    return licenseClient.judgeModule(arg)
  }

  /**
   * 校验指定产品是否有效
   * @param arg
   */
  def checkAppValid(CheckValidityArg arg) {
    return licenseClient.checkValidity(arg)
  }

  def queryPackage(LicenseContext context) {
    return this.postToLicense(queryPackageUrl, JSONObject.toJSONString(context), context.getTenantId(), Result.class)
  }

  def loadVipCache() {
    vipTenantIds.forEach({ tenantId ->
      LicenseContext context = new LicenseContext()
      context.setUserId("1000")
      context.setAppId("CRM")
      context.setTenantId(tenantId)

      LicenseObjectInfoContext infoContext = new LicenseObjectInfoContext()
      infoContext.setContext(context)
      infoContext.setCrmKey("crm_manage_custom_object")
      infoContext.setModuleType("0")

      licenseClient.queryApiNameByLicense(infoContext)
    })
  }


  private void createProduct(LicenseContext context) {
    ProductLicenseArg arg = new ProductLicenseArg()
    arg.setContext(context)
    ProductLicensePojo productLicensePojo = new ProductLicensePojo()
    productLicensePojo.setTenantId("77")
    productLicensePojo.setProductVersion("standardpro_edition")
    productLicensePojo.setProductName("CRM")
    productLicensePojo.setProductType("0")
    productLicensePojo.setStartTime(1564156800000)
    productLicensePojo.setExpiredTime(1595779200000)
    productLicensePojo.setTrialFlag(Boolean.FALSE)
    productLicensePojo.setOrderNumber("q0i20i102i3")
    productLicensePojo.setMaxHeadCount(100)
    productLicensePojo.setLicenseType("1")

    arg.setProductLicensePojo(productLicensePojo)

    licenseClient.createProductLicense(arg)
  }

  /**
   * 查询概览
   * @param tenantId
   * @param totalPara 是否返回总配额
   * @return
   */
  List<OverviewDto> queryOverview(String tenantId, boolean totalPara, boolean needScan) {
//    先扫描
    if (needScan) {
      List<String> tenantIds = Lists.newArrayList(tenantId)
      this.postToLicense(scanOverviewUrl, JSON.toJSONString(tenantIds), tenantId, Object.class)
      log.info("scan {} is end", tenantId)
    }
    QuoteOverviewRequest request = new QuoteOverviewRequest()
    request.setTotalPara(totalPara)

    LicenseContext context = new LicenseContext()
    context.setTenantId(tenantId)
    context.setAppId("CRM")
    context.setUserId("-10000")
    request.setContext(context)

    List<Object> objectList = this.postToLicense(queryOverviewInfoUrl, JSON.toJSONString(request), tenantId, List.class)
    List<OverviewDto> dtoList = Lists.newArrayList()
    objectList?.forEach({ it ->
      OverviewDto dto = JSON.parseObject(it.toString(), OverviewDto.class)
      dto.setParaName(this.getParaName(dto.getParaKey()))
      dtoList.add(dto)
    })

    dtoList
  }

  /**
   * 请求license
   * @param url
   * @param body
   * @param tenantId
   * @param clazz
   * @return
   */
  private <T> T postToLicense(String url, String body, String tenantId, Class<T> clazz) {
    Headers headers = new Headers.Builder().add("tenantId", tenantId).add("X-fs-Enterprise-Id", tenantId).build()
    Request request = new Request.Builder().url(url).post(RequestBody.create(MediaType.parse("application/json; chaset=UTF-8"), body)).headers(headers).build()
    return (T) okHttpSupport.syncExecute(request, new SyncCallback() {
      @Override
      Object response(Response response) throws Exception {
        if (response.isSuccessful()) {
          if (url.endsWith("overview")) {
            return JSONObject.parseObject(response.body().string()).get("data")
          }
          return JSON.parseObject(response.body().string(), clazz)
        } else {
          log.error("call http failed. code:${}, body:{}", response.code(), response.body().string())
          throw new LicenseException(PaasMessage.SYSTEM_ERROR.getCode(), PaasMessage.SYSTEM_ERROR.getMessage() + " http failed")
        }
      }
    })
  }

  /**
   * post请求
   * @param url
   * @param body
   * @return
   */
  private <T> T postToLicense(String url, String body) {
    Request request = new Request.Builder().url(url).post(RequestBody.create(MediaType.parse("application/json; chaset=UTF-8"), body)).build()
    return (T) okHttpSupport.syncExecute(request, new SyncCallback() {
      @Override
      Object response(Response response) throws Exception {
        if (response.isSuccessful()) {
          if (url.endsWith("overview")) {
            return JSONObject.parseObject(response.body().string()).get("data")
          }
          return JSON.parseObject(response.body().string())
        } else {
          log.error("call http failed. code:${}, body:{}", response.code(), response.body().string())
          throw new LicenseException(PaasMessage.SYSTEM_ERROR.getCode(), PaasMessage.SYSTEM_ERROR.getMessage() + " http failed")
        }
      }
    })
  }
  /**
   * 不传入json对象的post请求
   * @param url
   * @param body
   * @return
   */
  private <T> T postToLicenseNoJSON(String url, String body) {
    Request request = new Request.Builder().url(url).post(RequestBody.create(MediaType.parse("chaset=UTF-8"), body)).build()
    return (T) okHttpSupport.syncExecute(request, new SyncCallback() {
      @Override
      Object response(Response response) throws Exception {
        if (response.isSuccessful()) {
          if (url.endsWith("overview")) {
            return JSONObject.parseObject(response.body().string()).get("data")
          }
          return JSON.parseObject(response.body().string())
        } else {
          log.error("call http failed. code:${}, body:{}", response.code(), response.body().string())
          throw new LicenseException(PaasMessage.SYSTEM_ERROR.getCode(), PaasMessage.SYSTEM_ERROR.getMessage() + " http failed")
        }
      }
    })
  }

  private String getParaName(String paraKey) {
    switch (paraKey) {
      case "custom_objects_limit":
        return "自定义对象"
      case "custom_function_limit":
        return "自定义函数"
      case "custom_component_limit":
        return "自定义组件"
      case "conditional_sharing_limit":
        return "基于条件的共享规则"
      case "data_share_rules_limit":
        return "基于来源的共享规则"
      case "custom_roles_limit":
        return "自定义角色"
      case "open_api_hits_limit":
        return "OPEN API"
      case "workflow_limit":
        return "工作流"
      case "bpm_process_limit":
        return "BPM"
      case "approval_flow_limit":
        return "审批流"
      case "stage_process_limit":
        return "阶段推进器"
      case "data_count_limit":
        return "数据条数"
      case "data_byte_count_limit":
        return "数据量"
      default:
        return null
    }
  }

  List<LicenseObjectEntity> queryObjectTemplate(String versionOrCode) {
    List<String> productVersions = productInfoMapper.queryProductVersionByTenantId("default")
    List<String> moduleCodes = moduleInfoMapper.queryModuleCodeByDefault()
    List<LicenseObjectEntity> licenseObjectEntities = new ArrayList<LicenseObjectEntity>()
    if ("ALL".equals(versionOrCode)) {
      licenseObjectEntities = licenseObjectInfoMapper.queryObjectInfoApiNameFromALL()
      getObjectChineseName(licenseObjectEntities, productVersions, moduleCodes)
      return licenseObjectEntities
    }

    if (productVersions.contains(versionOrCode)) {
      licenseObjectEntities = licenseObjectInfoMapper.queryObjectInfoApiName(versionOrCode, null)
      if (CollectionUtils.isEmpty(licenseObjectEntities)) {
        return
      }
      getObjectChineseName(licenseObjectEntities, productVersions, moduleCodes)
    } else if (moduleCodes.contains(versionOrCode)) {
      licenseObjectEntities = licenseObjectInfoMapper.queryObjectInfoApiName(null, versionOrCode)
      if (CollectionUtils.isEmpty(licenseObjectEntities)) {
        return
      }
      getObjectChineseName(licenseObjectEntities, productVersions, moduleCodes)
    }
    return licenseObjectEntities
  }


  List<LicenseObjectEntity> queryObjectTemplateV2(String versionOrCode) {
    List<String> productVersions = productInfoMapper.queryProductVersionByTenantId("default")
    List<String> moduleCodes = moduleInfoMapper.queryModuleCodeByDefault()
    List<LicenseObjectEntity> licenseObjectEntities = new ArrayList<LicenseObjectEntity>()

    Map<String, String> objectSoldOut = Maps.newHashMap()

    if (productVersions.contains(versionOrCode)) {
      String productId = productInfoMapper.queryProductInfoId(LicenseConstant.DEFAULT_TENANTID, versionOrCode, "CRM")
      List<ModuleInfoEntity> moduleCodeList = moduleInfoMapper.queryModuleByProductId(productId)

      licenseObjectEntities = licenseObjectInfoMapper.queryObjectInfoApiName(versionOrCode, null)
      for (final def moduleCode in moduleCodeList) {
        putOutModule.forEach({ k, v ->
          if (k == moduleCode.getModuleCode()) {
            objectSoldOut.put(v, moduleCode.getSoldOut())
          }
        })
        licenseObjectEntities.addAll(licenseObjectInfoMapper.queryObjectInfoApiName(null, moduleCode.getModuleCode()))
      }

      licenseObjectEntities = licenseObjectEntities.groupBy { it.apiName }.collect { key, value -> value[0] }

      for (final def licenseObject in licenseObjectEntities) {
        objectSoldOut.forEach({ k, v ->
          if (k == licenseObject.getApiName()) {
            if (0 == v) {
              return
            }
            Date date = new Date(Long.parseLong(v))
            licenseObject.setSoldOut(date.format("yyyy-MM-dd HH:mm:ss"))
          }
        })
      }

      getObjectChineseName(licenseObjectEntities, productVersions, moduleCodes)
    } else if (moduleCodes.contains(versionOrCode)) {
      licenseObjectEntities = licenseObjectInfoMapper.queryObjectInfoApiName(null, versionOrCode)
      if (CollectionUtils.isEmpty(licenseObjectEntities)) {
        return
      }
      getObjectChineseName(licenseObjectEntities, productVersions, moduleCodes)
    }
    return licenseObjectEntities
  }

  private List<LicenseObjectEntity> getObjectChineseName(List<LicenseObjectEntity> licenseObjectEntities, List<String> productVersions, List<String> moduleCodes) {
    Iterator<LicenseObjectEntity> iterator = licenseObjectEntities.iterator()
    while (iterator.hasNext()) {
      LicenseObjectEntity entity = iterator.next()
      String moduleCode = entity.getModuleCode()
      String productVersion = entity.getProductVersion()
      if (!"-1".equals(String.valueOf(entity.getApiName().indexOf(" ")))) {
        iterator.remove()
      } else {
        entity.setApiChineseName(ObjectChineseNameMap.get(entity.getApiName()))
        if (moduleCodes.contains(moduleCode)) {
          entity.setProductName(VersionChineseNameMap.get(moduleCode))
          continue
        }
        if (productVersions.contains(productVersion)) {
          entity.setProductName(LicenseChineseNameMap.get(productVersion))
          continue
        }
        entity.setProductName(null)
      }
    }
  }

  /**
   * 查询企业下所有应用以及配额信息
   * @param tenantId 企业ID
   * @return 应用和配额信息
   */
  Object allAppLicense(String tenantId) {
    if (StringUtils.isEmpty(tenantId)) {
      throw new LicenseException(PaasMessage.SYSTEM_ERROR.getCode(), PaasMessage.SYSTEM_ERROR.getMessage() + "tenantId is empty");
    }

    // 获取企业下所有主版本信息
    List<ModuleInfoIdCodeEntity> modules = moduleInfoMapper.queryModuleCodeAndIdByDefault()

    // 构建结果列表
    List<List<String>> result = Lists.newArrayList()

    // 遍历每个模块，查询对应的配额信息
    modules.each { module ->
      String moduleId = module.getId()
      String moduleCode = module.getModuleCode()
      String moduleName = module.getModuleName()

      // 根据moduleId查询对应的modulePara信息
      List<MasterLicensePojo> paraList = moduleParaMapper.selectAllParaByModuleId(tenantId, moduleId)

      // 如果没有配额信息，添加基本信息
      if (CollectionUtils.isEmpty(paraList)) {
        result << [moduleCode, moduleName, "", "", ""]
      } else {
        // 处理有配额信息的情况
        paraList.each { para ->
          String paraKey = para.getParaKey() ?: ""
          String keyName = para.getKeyName() ?: KeyChineseNameMap.get(paraKey) ?: ""
          String paraValue = para.getParaValue() ?: ""

          result << [moduleCode, moduleName, paraKey, keyName, paraValue]
        }
      }
    }

    return result
  }
}
