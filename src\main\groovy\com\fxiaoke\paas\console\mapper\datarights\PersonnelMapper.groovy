package com.fxiaoke.paas.console.mapper.datarights

import com.fxiaoke.paas.console.entity.datarights.Personnel
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Param

/**
 * <AUTHOR>
 * @date 2019/3/29 下午7:09
 *
 */
interface PersonnelMapper extends ITenant<PersonnelMapper> {

  /**
   * 查询人员的上级、下级、用户组、角色、所属部门
   * @param tenantId
   * @param userId
   * @param objectId
   * @return
   */
  Personnel queryPersonnelByObjectId(@Param("tenantId") String tenantId,
                                     @Param("userId") String userId,
                                     @Param("objectId") String objectId)

}