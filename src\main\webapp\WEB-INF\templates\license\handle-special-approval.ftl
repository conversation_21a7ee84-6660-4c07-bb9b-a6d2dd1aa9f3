<#assign headContent>
    <link href="//static.foneshare.cn/oss/AdminLTE/plugins/datatables/dataTables.bootstrap.css" rel="stylesheet"
          type="text/css"/>
    <link type="text/css" href="//static.foneshare.cn/oss/datatables-checkboxes/dataTables.checkboxes.css"
          rel="stylesheet"/>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
    <style>
        #datatable td:nth-child(6) {
            text-align: right;
        }

        #datatable td:nth-child(7) {
            text-align: right;
        }

        .modal-dialog {

            position: absolute;

            top: 0;

            bottom: 0;

            left: 0;

            right: 0;
        }

        .modal-content {

            /*overflow-y: scroll; */

            position: absolute;

            top: 0;

            bottom: 0;

            width: 100%;
        }

        .modal-body {

            overflow-y: scroll;

            position: absolute;

            top: 55px;

            bottom: 65px;

            width: 100%;
        }

        .modal-header .close {
            margin-right: 15px;
        }

        .modal-footer {

            position: absolute;

            width: 100%;

            bottom: 0;
        }

    </style>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>处理特殊审批</h1>
        <ol class="breadcrumb">
            <li><a href=".."><i class="fa fa-dashboard"></i>任务主页</a></li>
        </ol>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info">
                    <div class="box-header with-border">
                        <ul class="nav nav-tabs" id="myTab">
                            <li role="presentation" class="active"><a href="#createDesigned" data-toggle="tab">查询订单</a></li>
                        </ul>
                    </div>
                    <div class="box-body">
                        <div class="tab-content">
                            <div class="tab-pane fade in active" id="createDesigned" id="type">
                                <form class="form-horizontal" action="${ctx}/license-admin/handle-special-approvals" method="post" id="crmForm">
                                    <div class="box-body">
                                        <div class="form-group">
                                            <label for="tenantId" class="col-sm-1 control-label" style="width: 120px">企业id</label>
                                            <div class="col-sm-4">
                                                <input type="text" style="border-radius:5px;width:1000px" class="form-control" id="tenantId" name="tenantId" placeholder="必填" required/>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="orderName" class="col-sm-1 control-label" style="width: 120px">
                                                <select id="order">
                                                    <option>请选择</option>
                                                    <option>销售订单</option>
                                                    <option>订单产品</option>
                                                </select>
                                            </label>
                                            <div class="col-sm-4">
                                                <input type="text" style="border-radius:5px;width:1000px" class="form-control" id="orderName" name="orderName" placeholder="必填" required/>
                                            </div>
                                            <div class="col-sm-offset-9 col-sm-2">
                                                <button id="findSub" type="button" class="btn btn-primary">查询</button>
                                                <button type="button" class="btn btn-default" onclick="history.back()">返回</button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal fade" id="myModalVoid" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                                            <h4 class="modal-title" id="myModalLabel">作废订单 (备注：更改productLicense)</h4>
                                        </div>
                                        <div class="modal-body">
                                            id :
                                            <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="idV" name="idV" placeholder="必填" required/>
                                            请输入相同id,确认执行
                                            <input type="text" style="border-radius:5px;width:400px" class="form-control" id="idCV" name="idCV" placeholder="必填" required/>
                                            企业id :
                                            <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="TenantIdV" name="TenantIdV" placeholder="必填" required/>
                                            订单号 :
                                            <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="orderNumberV" name="orderNumberV" placeholder="必填" required/>
                                            产品id :
                                            <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="productIdV" name="productIdV" placeholder="必填" required/>
                                            产品 :
                                            <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="productV" name="productV" placeholder="必填" required/>
                                            过期时间 :
                                            <span class="glyphicon glyphicon-calendar"
                                                  onclick="WdatePicker({skin:'whyGreen',errDealMode:-1, dateFmt:'yyyy-MM-dd HH:mm:ss',vel:'00:00:00',el:'expiredTime', onpicked:function(dp){$('#expiredTime').change()}})">
                                               </span>
                                            <input type="text" style="width: 180px" class="form-control" id="expiredTime" name="expiredTime" value="" width="120px" onblur="checkExpiredTime()" placeholder="必填" required/>
                                            <span id="expiredTimeSpan" style="color: #ff2d21"></span>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                                            <button type="button" class="btn btn-primary" id="table_editV" onclick="expiredOrder('expired')">提交</button>
                                        </div>
                                    </div>
                                </div><!-- /.modal-content &ndash;&gt;-->
                            </div>
                            <div class="modal fade" id="myModalDel" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                                            <h4 class="modal-title" id="myModalLabel">删除订单 (备注：逻辑删除 product_license module_info module_para)</h4>
                                        </div>
                                        <div class="modal-body">
                                            id :
                                            <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="idD" name="idD" placeholder="必填" required/>
                                            请输入相同id,确认执行
                                            <input type="text" style="border-radius:5px;width:400px" class="form-control" id="idCD" name="idCD" placeholder="必填" required/>
                                            企业id :
                                            <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="TenantIdD" name="TenantIdD" placeholder="必填" required/>
                                            订单号 :
                                            <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="orderNumberD" name="orderNumberD" placeholder="必填" required/>
                                            产品id :
                                            <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="productIdD" name="productIdD" placeholder="必填" required/>
                                            产品 :
                                            <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="productD" name="productD" placeholder="必填" required/>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                                            <button type="button" class="btn btn-primary" id="table_editD" onclick="delOrder('delete')">提交</button>
                                        </div>
                                    </div><!-- /.modal-content &ndash;&gt;-->
                                </div>
                            </div>
                            <div class="modal fade" id="myModalMax" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
                                <div class="modal-dialog modal-lg" style="display: inline-block;">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                                            <h4 class="modal-title" id="myModalLabel">更改企业员工数 (备注：更改productLicense)</h4>
                                        </div>
                                        <div class="modal-body">
                                            id :
                                            <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="idM" name="idM" placeholder="必填" required/>
                                            请输入相同id,确认执行
                                            <input type="text" style="border-radius:5px;width:400px" class="form-control" id="idCM" name="idCM" placeholder="必填" required/>
                                            企业id :
                                            <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="TenantIdM" name="TenantIdM" placeholder="必填" required/>
                                            订单号 :
                                            <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="orderNumberM" name="orderNumberM" placeholder="必填" required/>
                                            产品id :
                                            <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="productIdM" name="productIdM" placeholder="必填" required/>
                                            产品 :
                                            <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="productM" name="productM" placeholder="必填" required/>
                                            产品类型 :
                                            <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="productTypeM" name="productTypeM" placeholder="必填" required/>
                                            0 : 主版本 1 : 资源包 2 : 行业套件 3 : 应用 (备注:只有主版本可以修改员工数！)
                                            员工数 :
                                            <input type="text" style="border-radius:5px;width:400px" class="form-control" id="maxCountM" name="maxCountM" placeholder="必填" required/>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                                            <button type="button" class="btn btn-primary" id="table_editM" onclick="maxCountOrder('max_count')">提交</button>
                                        </div>
                                    </div><!-- /.modal-content &ndash;&gt;-->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="box box-info">
                        <div class="box-body">
                            <table id="datatable" class="table table-hover table-bordered" cellspacing="0" width="100%">
                                <thead>
                                <tr>
                                    <th>id</th>
                                    <th>企业ID</th>
                                    <th>产品ID</th>
                                    <th>CRM</th>
                                    <th>产品名称</th>
                                    <th>产品类型</th>
                                    <th>订单号</th>
                                    <th>企业员工数</th>
                                    <th>开始时间</th>
                                    <th>到期时间</th>
                                    <th>moduleCode</th>
                                    <th>paraKey</th>
                                    <th>paraValue</th>
                                    <th>productCode</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/my97datepicker/WdatePicker.js"></script>
    <script type="js"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            var table = $("#datatable").DataTable({
//            "deferRender": true,
                "processing": true,
                "ajax": "",
                dom: 'Blfrtip',
                buttons: [
                    'copy', 'excel', 'pdf'
                ],
                "columnDefs": [{
                    // 定义操作列,######以下是重点########
                    "targets": 14,//操作按钮目标列
                    "render": function (data, type, row, meta) {

                        /*'<div class="pull-right"><a href="javascript:void(0)" onclick="edit(event,'+ data +')"><i class="fa fa-edit"></i>&nbsp;Edit</a></div>';*/

                        var id = row.toString().split(',')[0];
                        var tenantId = row.toString().split(',')[1];
                        var productId = row.toString().split(',')[2];
                        var product = row.toString().split(',')[4];
                        var productType = row.toString().split(',')[5];
                        var orderNumber = row.toString().split(',')[6];

                        var html = "<button class='btn btn-primary' onclick='editD(\"" + id + "\",\"" + tenantId + "\",\"" + product + "\",\"" + orderNumber + "\",\"" + productId + "\")'>删除订单</button>"
                        html += "<button class='btn btn-primary' onclick='editV(\"" + id + "\",\"" + tenantId + "\",\"" + product + "\",\"" + orderNumber + "\",\"" + productId + "\")'>过期订单</button>"
                        html += "<button class='btn btn-primary' onclick='editM(\"" + id + "\",\"" + tenantId + "\",\"" + product + "\",\"" + orderNumber + "\",\"" + productType + "\",\"" + productId + "\")'>更改企业员工数</button>"
                        return html;
                    }
                }],
                "language": {
                    "url": "${ctx}/static/js/datatables-zh_CN.json"
                },
                "mark": true,
                "paging": false
            });

            $('#findSub').on('click', function () {
                var myselect = document.getElementById("order");
                var index = myselect.selectedIndex;
                var order = myselect.options[index].value;
                $('#warningInfo').addClass('hide');
                $("#datatable").dataTable().fnClearTable();
                var tenantId = $('#tenantId').val();
                if (tenantId == "") {
                    $('#warningInfo').removeClass('hide');
                    $('#hitInfo').html('关键字段不能为空！');
                    return;
                }
                var orderName = $('#orderName').val();
                var dataObject = {
                    order: order,
                    tenantId: tenantId,
                    orderName: orderName
                };
//            将对象转化为json字符串放在url中传递到后端

                table.ajax.url("${ctx}/license-admin/handle-special-approvals?dataObject=" + encodeURIComponent(JSON.stringify(dataObject))).load();
            });

        })

        function editD(id, tenantId, product, orderNumber, productId) {
            $('#idD').val(id);
            $('#TenantIdD').val(tenantId);
            $('#productD').val(product);
            $('#orderNumberD').val(orderNumber);
            $('#productIdD').val(productId);
            $('#myModalDel').modal({
                //backdrop:'static'//若不加backdrop会影响模态框界面拖动
            });
            /*var table=$('#table_edit').off().on('click','tr',function(){
                console.log(123123555)
                console.log(table.row(this).data());//可将该行所有数据在控制台输出
                //table.row(this).data().列   可将该列内容输出
            });*/
        }

        function editV(id, tenantId, product, orderNumber, productId) {
            $('#idV').val(id);
            $('#TenantIdV').val(tenantId);
            $('#productV').val(product);
            $('#orderNumberV').val(orderNumber);
            $('#productIdV').val(productId);
            $('#myModalVoid').modal({});
        }

        function editM(id, tenantId, product, orderNumber, productType, productId) {
            $('#idM').val(id);
            $('#TenantIdM').val(tenantId);
            $('#productM').val(product);
            $('#orderNumberM').val(orderNumber);
            $('#productTypeM').val(productType);
            $('#productIdM').val(productId);
            $('#myModalMax').modal({});
        }

        function delOrder(type) {
            var id = $('#idD').val();
            var tenantId = $('#TenantIdD').val();
            var orderNumber = $('#orderNumberD').val();
            var productId = $('#productIdD').val();
            var idCheck = $('#idCD').val();
            var SpecialApprovalObject = {
                id: id,
                idCheck: idCheck,
                tenantId: tenantId,
                orderNumber: orderNumber,
                type: type,
                productId: productId,
                deleteFlag: true

            }

            $.ajax({
                url: '${ctx}/license-admin/handle-special-approvals-object', // 请求的URL地址
                contentType: "application/json", //必须有
                data: JSON.stringify(SpecialApprovalObject),// 这次请求要携带的数据
                dataType: 'text',
                type: 'POST', //请求的方式
                traditional: true,
                success: function (data) {//请求成功之后的回调函数
                    if (data == "success") {
                        alert("删除成功")
                        $("#findSub").click();
                        $('#myModalDel').modal('hide');
                        //window.location.reload()
                    } else {
                        alert("删除失败")
                    }
                }
            })

        }

        function expiredOrder(type) {
            var id = $('#idV').val();
            var tenantId = $('#TenantIdV').val();
            var orderNumber = $('#orderNumberV').val();
            var expiredTime = $('#expiredTime').val();
            var productId = $('#productIdV').val();
            var idCheck = $('#idCV').val();
            var SpecialApprovalObject = {
                id: id,
                tenantId: tenantId,
                orderNumber: orderNumber,
                type: type,
                productId: productId,
                idCheck: idCheck,
                expiredTime: expiredTime
            }

            $.ajax({
                url: '${ctx}/license-admin/handle-special-approvals-object', // 请求的URL地址
                contentType: "application/json", //必须有
                data: JSON.stringify(SpecialApprovalObject),// 这次请求要携带的数据
                dataType: 'text',
                type: 'POST', //请求的方式
                traditional: true,
                success: function (data) { //请求成功之后的回调函数
                    if (data == "success") {
                        alert("执行成功")
                        $("#findSub").click();
                        $('#myModalVoid').modal('hide')
                    } else {
                        alert("执行失败")
                    }
                }
            })

        }

        function maxCountOrder(type) {
            var id = $('#idM').val();
            var tenantId = $('#TenantIdM').val();
            var orderNumber = $('#orderNumberM').val();
            var maxCount = $('#maxCountM').val();
            var productType = $('#productTypeM').val();
            var productId = $('#productIdM').val();
            var idCheck = $('#idCM').val();
            var SpecialApprovalObject = {
                id: id,
                idCheck: idCheck,
                tenantId: tenantId,
                orderNumber: orderNumber,
                type: type,
                maxCount: maxCount,
                productId: productId,
                productType: productType
            }


            $.ajax({
                url: '${ctx}/license-admin/handle-special-approvals-object', // 请求的URL地址
                contentType: "application/json", //必须有
                data: JSON.stringify(SpecialApprovalObject),// 这次请求要携带的数据
                dataType: 'text',
                type: 'POST', //请求的方式
                traditional: true,
                success: function (data) { //请求成功之后的回调函数
                    if (data == "success") {
                        alert("更改成功")
                        $("#findSub").click();
                        $('#myModalMax').modal('hide')
                    } else {
                        alert("更改失败")
                    }
                }
            })

        }

        var date_Format = /^((\d{2}(([02468][048])|([13579][26]))[\-\/\s]?((((0?[13578])|(1[02]))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\-\/\s]?((0?[1-9])|([1-2][0-9])))))|(\d{2}(([02468][1235679])|([13579][01345789]))[\-\/\s]?((((0?[13578])|(1[02]))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\-\/\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\s((([0-1][0-9])|(2?[0-3]))\:([0-5]?[0-9])((\s)|(\:([0-5]?[0-9])))))?$/

        function checkExpiredTime() {
            var stTime = $('#expiredTime').val();
            if (date_Format.test(stTime)) {
                $('#expiredTimeSpan').html("正确");
            } else {
                $('#expiredTimeSpan').html("错误");
            }
        }
    </script>

</#assign>
<#include "../layout/layout-main.ftl" />
