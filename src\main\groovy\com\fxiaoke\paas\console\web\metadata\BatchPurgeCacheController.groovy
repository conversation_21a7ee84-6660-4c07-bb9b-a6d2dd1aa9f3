package com.fxiaoke.paas.console.web.metadata

import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.bean.metadata.BatchPurgeCacheArg
import com.fxiaoke.paas.console.service.metadata.BatchPurgeCacheService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

@Controller
@Slf4j
@RequestMapping("/metadata/batch/purge")
class BatchPurgeCacheController {
  @Autowired
  BatchPurgeCacheService batchPurgeCacheService

  /**
   * 跳转清缓存页面
   * @return
   */
  @RequestMapping("")
  def loadPage(){
    "metadata/batchPurgeCache"
  }

  @RequestMapping(value = "/batchPurgeCache", method = RequestMethod.POST)
  @ResponseBody
  @SystemControllerLog(description = "清理缓存")
  def purgeCache(@RequestBody List<BatchPurgeCacheArg> args) {
    // 处理数据
    if(args.isEmpty()){
      return ["result": "{\"result\":\"params must not empty\"}"]
    }
    batchPurgeCacheService.batchPurgeCache(args)
  }

}
