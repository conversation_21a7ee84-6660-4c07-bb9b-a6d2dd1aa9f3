package com.fxiaoke.paas.console.controller.hamster;

import com.fxiaoke.paas.console.annotation.HamsterControllerLog;
import com.fxiaoke.paas.console.entity.ApiResponse;
import com.fxiaoke.paas.console.mq.hamster.validation.TableDataCountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/data-validation")
@Slf4j
public class DataValidationController {

  @Autowired
  private TableDataCountService TableDataCountService;

  @RequestMapping(value = "/data-count")
  public ModelAndView taskList() {
    ModelAndView modelAndView = new ModelAndView("hamster/data-count");
    return modelAndView;
  }

  @RequestMapping(value = "/describe-data-validation")
  public ModelAndView dataCountValidation() {
    ModelAndView modelAndView = new ModelAndView("hamster/describe-data-validation");
    return modelAndView;
  }

  @RequestMapping(value = "/table-data-count")
  @HamsterControllerLog(description = "hamster-开始数据验证")
  public ApiResponse tableDataCount(@RequestParam String tenantId, @RequestParam Boolean isQueryBISlave) {
    ApiResponse response = ApiResponse.builder().code(200).message("success").build();
    try {
      response.setData(TableDataCountService.calculate(tenantId, null, BooleanUtils.isTrue(isQueryBISlave)).stream().filter(x -> !x.areEqual()).collect(Collectors.toList()));
    } catch (Exception e) {
      response.setCode(500);
      response.setMessage(e.getMessage());
    }
    return response;
  }

  @RequestMapping(value = "/describe_data_count")
  @HamsterControllerLog(description = "hamster-新版数据验证")
  public ApiResponse describeDataCount(@RequestParam String tenantId, @RequestParam Boolean isQueryOpLog) {
    ApiResponse response = ApiResponse.builder().code(200).message("success").build();
    try {
      response.setData(TableDataCountService
                         .calculateRefactor(tenantId, BooleanUtils.isTrue(isQueryOpLog), null)
                         .stream()
                         .filter(x -> !x.areEqual())
                         .collect(Collectors.toList()));
    } catch (Exception e) {
      response.setCode(500);
      response.setMessage(e.getMessage());
    }
    return response;
  }

}
