<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:p="http://www.springframework.org/schema/p" xmlns:aop="http://www.springframework.org/schema/aop"
       default-lazy-init="true" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context-3.1.xsd
       http://www.springframework.org/schema/mvc
       http://www.springframework.org/schema/mvc/spring-mvc.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

  <bean id="fastJsonConfig" class="com.alibaba.fastjson.support.config.FastJsonConfig"
        p:charset="UTF-8"
        p:dateFormat="yyyy-MM-dd HH:mm:ss"/>

  <mvc:annotation-driven>
    <mvc:message-converters>
      <bean class="org.springframework.http.converter.StringHttpMessageConverter" p:supportedMediaTypes="text/plain; charset=UTF-8"/>
    </mvc:message-converters>
  </mvc:annotation-driven>
  <context:component-scan base-package="com.fxiaoke.paas.console.web"/>
  <context:component-scan base-package="com.fxiaoke.paas.console.controller.hamster"/>
  <import resource="classpath:spring/paas-console-spring-cms.xml"/>
  <bean class="org.springframework.web.servlet.handler.BeanNameUrlHandlerMapping"/>
  <bean id="httpClient" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"></bean>
  <bean class="com.fxiaoke.paas.console.proxy.OkHttpClient" lazy-init="false">
    <property name="client" ref="httpClient"></property>
  </bean>
  <!-- freeMarker 的配置 -->
  <bean id="freeMarkerConfigurer"
        class="org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer" lazy-init="false">
    <property name="templateLoaderPath" value="/WEB-INF/templates/"/>
    <property name="defaultEncoding" value="UTF-8"/>
    <property name="freemarkerSettings">
      <props>
        <prop key="template_update_delay">10</prop>
        <prop key="locale">zh_CN</prop>
        <prop key="datetime_format">yyyy-MM-dd HH:mm:ss</prop>
        <prop key="date_format">yyyy-MM-dd</prop>
      </props>
    </property>
    <property name="freemarkerVariables">
      <map>
        <entry key="ctx" value="#{servletContext.contextPath}"/>
        <entry key="CONTEXT_PATH" value="#{servletContext.contextPath}"/>
      </map>
    </property>
  </bean>

  <mvc:resources mapping="/static/**" location="classpath:/static/"/>

  <!-- freeMarker 视图解析器 -->
  <bean class="org.springframework.web.servlet.view.freemarker.FreeMarkerViewResolver">
    <property name="order" value="1"/>
    <property name="viewClass" value="org.springframework.web.servlet.view.freemarker.FreeMarkerView"/>
    <property name="contentType" value="text/html; charset=utf-8"/>
    <property name="cache" value="false"/>
    <property name="suffix" value=".ftl"/>
    <property name="exposeRequestAttributes" value="true"/>
    <property name="exposeSessionAttributes" value="true"/>
    <property name="exposeSpringMacroHelpers" value="true"/>
    <property name="requestContextAttribute" value="rc"/>
  </bean>


  <!-- 容器默认的DefaultServletHandler处理 所有静态内容与无RequestMapping处理的URL-->
  <mvc:default-servlet-handler/>

  <aop:aspectj-autoproxy proxy-target-class="true"/>
</beans>
