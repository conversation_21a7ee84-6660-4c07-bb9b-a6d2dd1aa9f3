package com.fxiaoke.paas.console.web.metadata

import com.alibaba.excel.EasyExcel
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.facishare.paas.pod.client.DbRouterClient
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.paas.console.entity.metadata.DictExcelData
import com.fxiaoke.paas.console.entity.metadata.FileExcelData
import com.fxiaoke.paas.console.service.metadata.JdbcService
import com.fxiaoke.template.InternUtils
import com.google.common.base.CharMatcher
import com.google.common.base.Joiner
import com.google.common.base.Splitter
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import groovy.util.logging.Slf4j
import lombok.Cleanup
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

import javax.servlet.http.HttpServletResponse
import java.text.SimpleDateFormat

/**
 * 导出文件、附件信息
 *
 * @date 2021/11/1 15:59
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/rest/attachment/dict")
@Controller
class MetadataAttachmentController {
  @Autowired
  DbRouterClient dbRouterClient
  @Autowired
  JdbcService jdbcService

  @GetMapping(value = "/paas-dict", produces = "application/json; charset=UTF-8")
  @ResponseBody
  def queryByObjectApiNames(String tenantId,
                            @RequestParam(defaultValue = "*") String objectApiNames,
                            @RequestParam(defaultValue = "true") boolean checkDataExists,
                            @RequestParam(defaultValue = "false") boolean isDatax
  ) {
    return query(tenantId, objectApiNames, checkDataExists, isDatax)
  }

  @GetMapping(value = "/export-paas-dict", produces = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8')
  def exportExcel(String tenantId,
                  @RequestParam(defaultValue = "*") String objectApiNames,
                  @RequestParam(defaultValue = "true") boolean checkDataExists,
                  @RequestParam(defaultValue = "false") boolean isDatax,
                  @RequestParam(defaultValue = "1632067200000") long createTime,
                  HttpServletResponse response) {
    List<FileExcelData> files = Lists.newArrayList()
    Splitter.on(CharMatcher.anyOf(',|;')).split(tenantId).forEach { String ei ->
      // 查询文件、图片、签名等类型的字段
      List<DictExcelData> lines
      try {
        lines = query(ei, objectApiNames, checkDataExists, isDatax)
      } catch (Exception e) {
        log.error("cannot query, ei: $ei, ", e)
        return
      }
      // 逐个字段查询文件信息
      lines.forEach {
        log.info("try scan ${it}")
        try {
          exportAttachments(ei, createTime, it, files)
        } catch (Exception e) {
          log.error("cannot scan $it, ", e)
        }
      }
      log.info("tenantId: $ei, found {} items", files.size())
    }

    response.setCharacterEncoding("UTF-8")
    response.setContentType('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8')
    String fileName = CharMatcher.anyOf('+ ').replaceFrom(URLEncoder.encode("${tenantId}-附件和图片信息", 'UTF-8'), '%20')
    response.setHeader("Content-disposition", "attachment;filename=${fileName}.xlsx")
    EasyExcel.write(response.getOutputStream(), FileExcelData.class).sheet("数据字典").doWrite(files)
  }

  private void exportAttachments(String tenantId, long createTime, DictExcelData dict, List<FileExcelData> files) {
    def table = dict.storeTableName
    def slot = dict.storeSlotName == null ? dict.fieldApiName : dict.storeSlotName
    // 查询租户所在数据库（确认是否做了schema隔离）
    def pair = jdbcService.conn(tenantId)
    @Cleanup def jdbc = pair.first
    def schema = pair.second
    def sql = "SELECT $slot FROM ${schema}.\"${table}\" WHERE tenant_id='$tenantId' AND $slot IS NOT NULL AND is_deleted='0' AND create_time>${createTime}"
    SimpleDateFormat df = new SimpleDateFormat('yyyy-MM-dd HH:mm:ss')
    jdbc.query(sql) {
      while (it.next()) {
        def json = it.getString(1)
        if (json.length() > 1) {
          def items = JSON.parseArray(json)
          for (JSONObject file : (items as List<JSONObject>)) {
            def ext = file.getString('ext')
            def path = file.getString('path')
            def name = file.getString('filename')
            def size = file.getIntValue('size')
            def ctime = file.getLongValue('create_time')
            if (ctime > 0) {
              ctime = df.format(new Date(ctime))
            } else {
              ctime = ''
            }
            def f = FileExcelData.builder().tenantId(tenantId).describeApiName(dict.describeApiName).displayName(dict.displayName).fieldLabel(dict.fieldLabel).fieldApiName(dict.fieldApiName).storeSlotName(dict.storeSlotName).storeTableName(dict.storeTableName).type(dict.type).ext(ext).fileName(name).path(path).size(size).creatTime(ctime).build()
            files.add(f)
          }
        }
      }
    }
  }

  private List<DictExcelData> query(String tenantId, String objectApiNames, boolean checkDataExists, boolean isDatax) {
    // 查询租户所在数据库（确认是否做了schema隔离）
    def pair = jdbcService.conn(tenantId)
    @Cleanup def jdbc = pair.first
    def schema = pair.second

    if ('*' == objectApiNames) {
      // 查询有数据的表
      def size = checkDataExists ? 0 : -1
      def query = "SELECT relname FROM pg_stat_user_tables WHERE schemaname='$schema' AND n_live_tup>$size"
      List<String> hasDataTables = jdbc.findMany(query)
      def ids = Joiner.on(',').join(hasDataTables)
      log.info("check pg_stat_user_tables, found tables: $ids")
      // 检查含有tenant_id的表
      query = "SELECT table_name FROM information_schema.columns WHERE table_schema='$schema' AND column_name='tenant_id' AND table_name=any('{$ids}')"
      hasDataTables = jdbc.findMany(query)
      log.info("check information_schema, found tables: $hasDataTables")
      // 检查企业数据
      Set<String> rechecked = Sets.newLinkedHashSet()
      if (checkDataExists) {
        hasDataTables.each {
          def sql = "SELECT * FROM ${schema}.\"${it}\" WHERE tenant_id='${tenantId}' LIMIT 1"
          String id = jdbc.findOne(sql)
          if (id != null) {
            rechecked.add(it)
          }
        }
      } else {
        rechecked = Sets.newLinkedHashSet(hasDataTables)
      }
      log.info("check data exists, found tables: $rechecked")

      // 检查租户库对象存储表
      List<String> names = Lists.newArrayList()
      query = "SELECT describe_api_name,store_table_name FROM ${schema}.mt_describe WHERE tenant_id='$tenantId' AND is_deleted=false"
      jdbc.cursor(query) {
        while (it.next()) {
          def describe_api_name = it.getString(1)
          def store_table_name = it.getString(2)
          if (it.wasNull() && schema == 'public') {
            store_table_name = 'mt_data'
          }
          if (rechecked.contains(store_table_name)) {
            log.info("found-0 describe_api_name:${describe_api_name}, store_table_name:${store_table_name}")
            rechecked.remove(store_table_name)
            names += describe_api_name
          } else {
            log.warn("skip-0 describe_api_name:${describe_api_name}, store_table_name:${store_table_name}")
          }
        }
      }
      names += 'AccountObj'
      objectApiNames = Joiner.on(',').join(names)
      log.info("got objectApiNames from tenantDB: $objectApiNames")
      def lines = queryByObjectApiNames(schema, tenantId, objectApiNames, jdbc, isDatax)

      // 检查系统库-100租户预制信息
      if (!rechecked.isEmpty()) {
        log.info("try load from SystemDB, tables: $rechecked")
        def pair2 = jdbcService.conn('-100')
        @Cleanup def system = pair2.first
        query = "SELECT describe_api_name,store_table_name FROM public.mt_describe WHERE tenant_id='-100' AND is_deleted=false"
        names.clear()
        system.cursor(query) {
          while (it.next()) {
            def describe_api_name = it.getString(1)
            def store_table_name = it.getString(2)
            if (it.wasNull() && schema == 'public') {
              store_table_name = 'mt_data'
            }
            if (rechecked.contains(store_table_name)) {
              log.info("found-1 describe_api_name:${describe_api_name}, store_table_name:${store_table_name}")
              rechecked.remove(store_table_name)
              names += describe_api_name
            } else {
              log.warn("skip-1 describe_api_name:${describe_api_name}, store_table_name:${store_table_name}")
            }
          }
        }
        objectApiNames = Joiner.on(',').join(names)
        log.info("got objectApiNames from SystemDB: $objectApiNames")
        def fromSystem = queryByObjectApiNames('public', '-100', objectApiNames, system, isDatax)
        log.info("load {} fields from SystemDB", fromSystem.size())
        lines.addAll(fromSystem)
      }
      return lines
    } else {
      return queryByObjectApiNames(schema, tenantId, objectApiNames, jdbc, isDatax)
    }
  }

  List<DictExcelData> queryByObjectApiNames(String schema, String tenantId, String objectApiNames, JdbcConnection jdbc, boolean isDatax) {
    def clean = CharMatcher.anyOf('\'\"\\;-').removeFrom(objectApiNames)
    def query = "SELECT describe_api_name,display_name,store_table_name FROM ${schema}.mt_describe WHERE tenant_id='$tenantId' AND is_deleted=false AND describe_api_name=any('{$clean}')"
    Map<String, List<String>> rows = Maps.newHashMap()
    jdbc.query(query) {
      while (it.next()) {
        def describe_api_name = it.getString(1)
        def display_name = it.getString(2)
        def store_table_name = isDatax ? InternUtils.toTableName(describe_api_name) : it.getString(3)
        rows[describe_api_name] = Lists.newArrayList(display_name, store_table_name)
      }
    }

    // 查询field信息
    List<DictExcelData> lines = Lists.newArrayList()
    query = "SELECT describe_api_name,api_name,field_label,type,field_num FROM ${schema}.mt_field WHERE tenant_id='$tenantId' AND describe_api_name=any('{$clean}') AND is_active='t' AND type in ('file_attachment', 'image', 'signature') ORDER BY describe_api_name"
    jdbc.cursor(query) {
      while (it.next()) {
        def describe_api_name = it.getString(1)
        def api_name = it.getString(2)
        def field_label = it.getString(3)
        //datax无需输出字段类型
        def type = isDatax ? "" : it.getString(4)
        def field_num = it.getInt(5)
        if (it.wasNull()) {
          field_num = -1
        }
        def common = rows[describe_api_name]
        def display_name = ''
        def store_table_name = ''
        if (common != null) {
          display_name = common[0]
          store_table_name = common[1]
          if (store_table_name == null && 'public' == schema) {
            store_table_name = isDatax ? describe_api_name.toLowerCase() : 'mt_data'
          }
        }
        String slot = isDatax ? InternUtils.toColumnName(api_name) : (field_num != -1 ? 'value' + field_num : null)
        def line = DictExcelData.builder().describeApiName(describe_api_name).displayName(display_name).fieldLabel(field_label).fieldApiName(api_name).storeSlotName(slot).storeTableName(store_table_name).type(type).build()
        lines.add(line)
      }
    }
    return lines
  }
}