package com.fxiaoke.paas.console.bean.workflow

import io.swagger.annotations.ApiModelProperty

/**
 * Created by yangxw on 2018/3/9.
 */
class BatchQueryWorkflowInstanceArg {
    @ApiModelProperty("实例id")
     String id;

    @ApiModelProperty("租户id")
     String tenantId;

    @ApiModelProperty("应用Id")
     String appId;

    @ApiModelProperty("流程实例所属的流程id")
     String workflowId;

    @ApiModelProperty("流程实例绑定的objectId")
     String objectId;

    @ApiModelProperty("实体Id")
     String entityId;

    @ApiModelProperty("流程实例的申请人id")
     String applicantId;

    @ApiModelProperty("当查询申请人名字时，可能对应多个userId，那么只要匹配其中一个就行")
     List<String> applicantIdList;

    @ApiModelProperty("流程实例当前状态")
     String state;

    @ApiModelProperty("查询区间-起始时间")
     Long startTime;

    @ApiModelProperty("查询区间-结束时间")
     Long endTime;

    @ApiModelProperty("流程实例持续时间不低于durationMinTime")
     Long durationMinTime;

    @ApiModelProperty("流程实例持续时间不高于durationMaxTime")
     Long durationMaxTime;

    @ApiModelProperty("分页参数-起始位置")
     int start;

    @ApiModelProperty("分页参数-记录数量")
     int length;

     List<OrderArg> orderArgList;
}
