package com.fxiaoke.paas.console.service.hamster;

import com.github.autoconf.ConfigFactory;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CloudService {

  private List<String> hamsterClouds;

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("paas-console-base", iConfig -> {
      hamsterClouds = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(iConfig.get("hamsterClouds", ""));
    });
  }

  public List<String> getAllClouds() {
    if (CollectionUtils.isNotEmpty(hamsterClouds)){
      return hamsterClouds;
    }
    List<String> clouds = Lists.newArrayList();
    clouds.add("fstest");
    clouds.add("fstest_cloud");
    clouds.add("foneshare");
    clouds.add("ale-public-prod");
    clouds.add("cloudmodel-public-prod");
    clouds.add("forceecrm-public-prod");
    clouds.add("hisense-public-prod");
    clouds.add("huaweicloud-public-prod");
    clouds.add("huaweicloud-sbt-prod");
    clouds.add("hws-public-prod");
    clouds.add("ksc-ksc-prod");
    clouds.add("ucd-public-prod");
    clouds.add("ucd-public-test");
    clouds.add("xjgc-public-prod");
    clouds.add("mengniu-public-prod");
    clouds.add("hsyk-public-prod");
    return clouds;
  }
}
