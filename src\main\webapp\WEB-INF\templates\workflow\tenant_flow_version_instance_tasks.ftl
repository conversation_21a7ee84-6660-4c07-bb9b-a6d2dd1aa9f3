<#assign title="TASK查询">
<#assign active_nav="workflow_instance_task">
<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
<style>

    .doSearch {
        width: 8%;
        margin-left: 20px
    }

    .back {
        height: 32px;
        line-height: 32px;
        padding: 0 20px;
        border: 5px solid #3e84e9;
        background-color: #3e84e9;
        border-radius: 3px;
        color: #fff !important;
        text-align: center;
        text-decoration: none;
        cursor: pointer;
        font-family: inherit;
        vertical-align: middle;
        margin-left: 10px
    }

    #dataTable2 th {
        vertical-align: middle;
        align-items: center;
    }

    #dataTable2 td {
        vertical-align: middle;
    }

    .table > thead:first-child > tr:first-child > th {
        text-align: center;
        vertical-align: middle;
    }

    .table > tbody > tr > td {
        text-align: center;
    }

    input {
        width: 10%;
        height: 34px;
        line-height: 34px;
        box-sizing: border-box;
        border-radius: 4px;
        border: 1px solid #c8cccf;
        color: #6a6f77;
        -web-kit-appearance: none;
        -moz-appearance: none;
        outline: 0;
        text-decoration: none;
        margin-left: 20px;
    }

    .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
        width: 120px;
        margin-left: 20px;
    }

</style>
</#assign>
<#assign bodyContent>
    <#assign breadcrumbContent>
    <section class="content-header">
        <h1>Task查询</h1>
        <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i>查询</a></li>
        </ol>
    </section>
    </#assign>
<section class="content">
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title"></h3>
                    </div>
                    <form class="form-horizontal" action="${ctx}/biz/add" method="post" id="myForm" role="form"
                          data-toggle="validator">
                        <div class="container-fluid">
                            <div id="advanced3">
                                <input placeholder="租户_ID"/>
                                <input placeholder="Instance_ID"/>
                                <input placeholder="Task_ID"/>
                                <input placeholder="Object_ID"/>
                                <select id="appId" class="selectpicker">
                                    <option value="CRM">CRM</option>
                                    <option value="BPM">BPM</option>
                                    <option value="facishare-xt">协同</option>
                                    <option value="" selected>全部</option>
                                </select>
                                <button type="button" class="doSearch btn btn-primary">查找</button>
                            </div>
                            <div style="margin-top: 20px;">
                                <table class="table table-striped table-bordered table-condensed dataTable no-footer"
                                       id="dataTable2">
                                    <thead>
                                    <tr>
                                        <th>企业 ID</th>
                                        <th>TASK ID</th>
                                        <th>审批人/部门</th>
                                        <th>审批人/意见/时间</th>
                                        <th>所属流程ID</th>
                                        <th>所属实例ID</th>
                                        <th>状态</th>
                                        <th>类型</th>
                                        <th>创建时间</th>
                                        <th>修改时间</th>
                                        <th>持续时间</th>
                                        <th>TASK-定义</th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                </div>
                </form>
            </div>
        </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script>
        $(document).ready(function () {
            var bootstrapDom = "<'row'<'col-sm-6'l>>" +
                    "<'row'<'col-sm-12'tr>>" +
                    "<'row'<'col-sm-5'i><'col-sm-7'p>>";
            var table = $("#dataTable2").DataTable({
                "dom": bootstrapDom,
                "processing": true,
                "serverSide": true,
                "search": {
                    "regex": true
                },
//            "ordering":false,
                "order": [[8, "desc"]],
                "ajax": "${CONTEXT_PATH}/tenant/flow/version/instance/tasks?dataObject=" + encodeURIComponent(JSON.stringify(getDataObject())),
                "columnDefs": [
                    {"width": "80px", "targets": 0},
                    {"width": "200px", "targets": 1},
                    {"width": "200px", "targets": 2},
                    {"width": "200px", "targets": 3},
                    {"width": "200px", "targets": 4},
                    {"width": "200px", "targets": 5},
                    {"width": "100px", "targets": 6},
                    {"width": "100px", "targets": 7},
                    {"width": "160px", "targets": 8},
                    {"width": "160px", "targets": 9},
                    {"width": "100px", "targets": 10},
                    {"width": "100px", "targets": 11},
                ],
                columns: [
                    {
                        data: "tenantId"
                    },
                    {
                        data: "id"
                    },
                    {
                        data: "assignee"
                    },
                    {
                        data: "assigneeAndOpinion"
                    },
                    {
                        data: "workflowId", render: function (data, type, row, meta) {
                        return "<a href=\"${CONTEXT_PATH}/workflow/page/tenant/flow/versions/from/link?workflowId=" + data + "&tenantId=" + row.tenantId + "\">" + data + "</a>";
                    }
                    },
                    {
                        data: "workflowInstanceId", render: function (data, type, row, meta) {
                        return "<a href=\"${CONTEXT_PATH}/workflow/page/instances/from/link?instanceId=" + data + "&tenantId=" + row.tenantId + "\">" + data + "</a>";
                    }
                    },
                    {
                        data: 'state'
                    },
                    {
                        data: "taskType"
                    },
                    {
                        data: "createTime"
                    },
                    {
                        data: "modifyTime"
                    },
                    {
                        data: "duration"
                    },
                    {
                        data: ".....", render: function (data, type, row, meta) {
                        return "<a href=\"${CONTEXT_PATH}/workflow/page/task/show?tenantId=" + row.tenantId + "&taskId=" + row.id + "\">" + "TASK-定义" + "</a>"
                    }
                    }
                ],
                "iDisplayLength": 10,
                "sPaginationType": "full_numbers",
                "language": {
                    "processing": "加载中>>>>",
                    "lengthMenu": "显示 _MENU_ 项结果",
                    "zeroRecords": "没有匹配结果",
                    "emptyTable": "没有数据",
                    "info": "",
                    "infoEmpty": "",
                    "infoFiltered": "",
                    "infoPostFix": "",
                    "search": "搜索:",
                    "url": "",
                    "paginate": {
                        "first": "首页",
                        "previous": "上页",
                        "next": "下页",
                        "last": "末页"
                    }
                }
            });

            $("#advanced3 .doSearch").click(function () {
                var tenantId = $("#advanced3 input").eq(0).val();
                var instanceId = $("#advanced3 input").eq(1).val();
                var taskId = $("#advanced3 input").eq(2).val();
                var objectId = $("#advanced3 input").eq(3).val();
                var appId = $("#advanced3 select").val();
                var dataObject = {
                    "tenantId":tenantId,
                    "instanceId":instanceId,
                    "taskId":taskId,
                    "objectId":objectId,
                    "appId":appId
                }
                table.ajax.url('${CONTEXT_PATH}/tenant/flow/version/instance/tasks?dataObject=' + encodeURIComponent(JSON.stringify(dataObject))).load();
            });
        });

        function getDataObject() {
            var dataObject = {
                "tenantId":"${(tenantId)!""}",
                "instanceId":"${(instanceId)!""}",
                "taskId":"${(taskId)!""}",
                "objectId":"${(objectId)!""}",
                "appId":"${(appId)!""}"
            }
            return dataObject;
        }
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
