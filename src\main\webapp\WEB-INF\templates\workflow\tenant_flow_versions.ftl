<#assign title="流程版本">
<#assign active_nav="workflow">
<#assign headContent>
<link href="//static.foneshare.cn/oss/datatables.min.css" rel="stylesheet" type="text/css"/>
<style>

  #dataTable2 th {
    vertical-align: middle;
    align-items: center;
  }

  #dataTable2 td {
    vertical-align: middle;
  }

  .table > thead:first-child > tr:first-child > th {
    text-align: center;
    vertical-align: middle;
  }

  .table > tbody > tr > td {
    text-align: center;
  }

  .table > thead:first-child > tr:first-child > th {
    text-align: center;
    vertical-align: middle;
  }

  .table > tbody > tr > td {
    text-align: center;
  }

</style>
</#assign>
<#assign bodyContent>
    <#assign breadcrumbContent>
    <section class="content-header">
      <h1>流程版本</h1>
      <ol class="breadcrumb">
        <li><a href="#"><i class="fa fa-dashboard"></i>展示</a></li>
      </ol>
    </section>
    </#assign>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <div class="box-header with-border">
          <h3 class="box-title"></h3>
        </div>
        <form class="form-horizontal" action="${ctx}/biz/add" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div>
              <table class="table table-striped table-bordered table-condensed dataTable no-footer"
                     id="dataTable2">
                <thead>
                <tr>
                  <th align="center">流程版本-ID</th>
                  <th align="center">实例数量（进行中/所有）</th>
                  <th align="center">创建日期</th>
                  <th align="center">创建人ID</th>
                  <th align="center">创建人</th>
                  <th align="center">修改人ID</th>
                  <th align="center">修改人</th>
                  <th align="center">修改日期</th>
                  <th align="center">查看流程定义</th>
                </tr>
                </thead>
              </table>
            </div>
          </div>
      </div>
    </div>
    </form>
  </div>
  </div>
  </div>
</section>
</#assign>
<#assign scriptContent>
<script>
  $(document).ready(function () {
    function version(data, type, row) {
      return "<a href='${CONTEXT_PATH}/workflow/page/tenant/flow/version/instances?workflowId=" + data + "&tenantId=${(tenantId)!}&tenantName=${(tenantName)!}&flowType=${(flowType)!}&flowName=${(flowName)!}&appId=${(appId)!}&entityName=${(entityName)!}&openTime=${(openTime)!}&sourceId=${(sourceId)!}&createTime=" + row.createTime + "'>查看定义</a>"
    }

    var table = $("#dataTable2").DataTable({
      "processing": false,
//            "serverSide": true,
      "search": {
        "regex": true
      },
      "order": [[7, "desc"]],
      "ajax": "${CONTEXT_PATH}/tenant/flow/versions/?tenantId=${(tenantId)!""}&sourceId=${(sourceId)!""}&appId=${(appId)!""}&workflowId=${(workflowId)!""}",
      "columnDefs": [
        {"width": "11%", "targets": 0},
        {"width": "12%", "targets": 1},
        {"width": "11%", "targets": 2},
        {"width": "11%", "targets": 3},
        {"width": "11%", "targets": 4},
        {"width": "11%", "targets": 5},
        {"width": "11%", "targets": 6},
        {"width": "11%", "targets": 7},
        {"width": "11%", "targets": 8}
      ],
      columns: [
        {
          data: 'id', render: function (data, type, row, meta) {
            if (type == 'display') {
              return "<a href=\"${CONTEXT_PATH}/workflow/page/tenant/flow/version/instances?workflowId=" + data +
                      "&tenantId=${(tenantId)!""}\">" + data + "</a>";
            <#--return "<a href='${CONTEXT_PATH}/workflow/page/tenant/flow/version/instances?workflowId=" + data + "&tenantId=${tenantId!}'></a>"-->
            } else {
              return data;
            }
          }
        },
        {
          data: "inProgressAll"
        },
        {
          data: "createTime"
        },
        {
          data: "creator"
        },
        {
          data: "creatorAccount"
        },
        {
          data: "modifier"
        },
        {
          data: "modifyAccount"
        },
        {
          data: "modify_time"
        },
        {
          data: "id", render: function (data, type, row, meta) {
            <#--return "<a href=\"${CONTEXT_PATH}/workflow/page/tenant/flow/version/show?workflowId=" + data +-->
            <#--"&tenantId=${(tenantId)!""}&tenantName=${(tenantName)!""}&flowType=${(flowType)!""}&flowName=${(flowName)!""}"-->
            <#--+ "&appId=${(appId)!""}&entityName=${(entityName)!""}&openTime=${(openTime)!""}&sourceId=${(sourceId)!""}&createTime="-->
            <#--+ row.createTime + "\">查看定义</a>";-->
            return "<a href='${CONTEXT_PATH}/workflow/page/tenant/flow/version/show?workflowId=" + data + "&tenantId=${(tenantId)!}&tenantName=${(tenantName)!}&flowType=${(flowType)!}&flowName=${(flowName)!}&appId=${(appId)!}&entityName=${(entityName)!}&openTime=${(openTime)!}&sourceId=${(sourceId)!}&createTime=" + row.createTime + "'>查看定义</a>";
          }
        }
      ],
      "iDisplayLength": 25,
      "sPaginationType": "full_numbers",
      "language": {
        "processing": "加载中...",
        "lengthMenu": "显示 _MENU_ 项结果",
        "zeroRecords": "没有匹配结果",
        "emptyTable": "没有数据",
        "info": "",
        "infoEmpty": "",
        "infoFiltered": "",
        "infoPostFix": "",
        "search": "搜索:",
        "url": "",
        "paginate": {
          "first": "首页",
          "previous": "上页",
          "next": "下页",
          "last": "末页"
        }
      }
    });
  });
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
