<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fxiaoke.paas.console.mapper.datarights.PersonnelMapper">

  <select id="queryPersonnelByObjectId" resultMap="queryPersonnelByObjectIdResultMap">
SELECT
  a.user_id         as userId,
  b.user_id         as subordinate,
  c.user_id         as superior,
  dt_team.role_Type as role,
  org_dept.name     as department,
   org_dept.dept_id     as deptId,
  org_group.name as groupName,
  org_group.id as groupId
from org_employee_user as a
  left join org_employee_user as b on a.user_id = b.leader and a.tenant_id = b.tenant_id
  left join org_employee_user as c on a.leader = c.user_id and a.tenant_id = c.tenant_id
  left join dt_team on dt_team.member_id = a.user_id and dt_team.tenant_id = a.tenant_id
  left join org_dept_user on org_dept_user.tenant_id = a.tenant_id and org_dept_user.user_id = a.user_id
  left join org_dept on org_dept.dept_id = org_dept_user.dept_id and org_dept.tenant_id = org_dept_user.tenant_id
  left join org_group_user on org_group_user.tenant_id=a.tenant_id  and a.user_id=org_group_user.user_id
  and org_group_user.is_deleted=0
  left join org_group on org_group.id=org_group_user.group_id and org_group.tenant_id=a.tenant_id
where a.tenant_id = #{tenantId} and a.user_id = #{userId} and dt_team.object_id = #{objectId}

  </select>

  <resultMap id="queryPersonnelByObjectIdResultMap" type="com.fxiaoke.paas.console.entity.datarights.Personnel">
    <result column="userId" property="userId"></result>
    <result column="superior" property="superior"></result>
    <result column="role" property="role"></result>

    <result column="tenantId" property="tenantId"></result>
    <collection property="departments" ofType="java.util.Map">
      <result column="department" property="department"></result>
      <result column="deptId" property="deptId"></result>
    </collection>
    <collection property="subordinates"  ofType="java.util.Map">
      <result column="subordinate" property="subordinate"></result>
    </collection>
    <collection property="group" ofType="java.util.Map">
      <result column="groupName" property="groupName"></result>
      <result column="groupId" property="groupId"></result>
    </collection>
  </resultMap>

</mapper>

