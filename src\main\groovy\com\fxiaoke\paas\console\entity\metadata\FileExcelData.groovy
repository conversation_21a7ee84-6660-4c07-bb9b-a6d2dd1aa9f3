package com.fxiaoke.paas.console.entity.metadata

import com.alibaba.excel.annotation.ExcelProperty
import com.alibaba.excel.annotation.write.style.ColumnWidth
import com.alibaba.excel.annotation.write.style.ContentStyle
import com.alibaba.excel.annotation.write.style.HeadStyle
import com.alibaba.excel.enums.BooleanEnum
import groovy.transform.builder.Builder
import lombok.Data

/**
 * 导出excel
 *
 * <AUTHOR>
 * @date 2021/10/9 10:58
 */
@Data
@Builder
@ColumnWidth(32)
@HeadStyle(shrinkToFit = BooleanEnum.TRUE)
class FileExcelData {
  @ExcelProperty("租户ID")
  String tenantId
  @ExcelProperty("对象apiName")
  String describeApiName
  @ExcelProperty("对象名称")
  String displayName
  @ContentStyle(wrapped = BooleanEnum.TRUE)
  @ExcelProperty("字段含义")
  String fieldLabel
  @ExcelProperty("字段apiName")
  String fieldApiName
  @ColumnWidth(12)
  @ExcelProperty("表字段名")
  String storeSlotName
  @ExcelProperty("表名")
  String storeTableName
  @ColumnWidth(24)
  @ExcelProperty("字段类型")
  String type
  @ColumnWidth(12)
  @ExcelProperty("后缀名")
  String ext
  @ColumnWidth(48)
  @ExcelProperty("文件名")
  String fileName
  @ColumnWidth(48)
  @ExcelProperty("文件路径")
  String path
  @ColumnWidth(12)
  @ExcelProperty("文件大小")
  int size
  @ColumnWidth(24)
  @ExcelProperty("创建时间")
  String creatTime
}