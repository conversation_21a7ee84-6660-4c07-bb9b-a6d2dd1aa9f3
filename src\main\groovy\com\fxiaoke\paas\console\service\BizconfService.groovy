package com.fxiaoke.paas.console.service

import com.facishare.paas.org.service.impl.OrganizationServiceImpl
import com.fxiaoke.common.PasswordUtil
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.paas.console.service.metadata.JdbcService
import com.fxiaoke.paas.console.util.DateFormatUtil
import com.github.autoconf.ConfigFactory
import com.google.common.collect.Sets
import groovy.util.logging.Slf4j
import lombok.NonNull
import org.apache.commons.collections4.CollectionUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import java.sql.SQLException

/**
 * 提供给深研查询mt_bizconf表
 * <AUTHOR> @date 2019-04-11
 */
@Service
@Slf4j
class BizconfService {

  private String pgUsername
  private String pgPassword
  private String podJdbcUrl
  private String podUsername
  private String podPassword
  private String paasPgBouncer

  @Autowired
  OrganizationServiceImpl organizationService
  @Autowired
  JdbcService jdbcService


  @PostConstruct
  void init() {
    ConfigFactory.getConfig("db-paas-console", { config ->
      pgUsername = config.get("pg_username")
      pgPassword = config.get("pg_password")
      boolean pgEncrypt = config.getBool("pg_password.encrypt", false);
      if(pgEncrypt) {
        pgPassword = PasswordUtil.decode(pgPassword);
      }
      podJdbcUrl = config.get("podJdbcUrl")
      podUsername = config.get("podUsername")
      podPassword = config.get("podPassword")
      boolean podEncrypt = config.getBool("podPassword.encrypt", false);
      if(pgEncrypt) {
        podPassword = PasswordUtil.decode(podPassword)
      }
    })

    ConfigFactory.getConfig("fs-metadata", { iConfig ->
      this.paasPgBouncer = iConfig.get("pgbouncer-paas-master")
    })
  }

  def queryBizconf(@NonNull String whereSql) {

    if (!DateFormatUtil.checkSql(whereSql)) {
      return ["code": 403, "error": "Sql不符合规范，只能分析查询语句！"]
    }

    log.info("Entering BizconfService queryBizconf method,whereSql={}", whereSql)
    def jdbcUrlList = getJdbcUrl()
    Set<String> result = Sets.newHashSet();

    for (String jdbcUrl : jdbcUrlList) {
      try {
        JdbcConnection jdbcConnection = new JdbcConnection(jdbcUrl, pgUsername, pgPassword)
        HashSet<String> schemaNames = getSchemaNames(jdbcConnection)
        String selectSql = "SELECT tenant_id FROM %s WHERE "

        for (String schemaName : schemaNames) {
          if (!checkTableExist(schemaName, jdbcConnection, "mt_bizconf")) {
            continue;
          }

          jdbcConnection.query(String.format(selectSql, schemaName + ".mt_bizconf") + whereSql, { resultSet ->
            while (resultSet.next()) {
              String tenantId = resultSet.getObject("tenant_id").toString();
              result.add(tenantId);
            }
          })
        }
      } catch (Exception e) {
        //偶尔数据库下线，路由资源没有移除。导致连接数据库失败，针对此种情形。跳过继续处理下一个即可
        log.error("queryBizconf:{}", jdbcUrl, e);
      }
    }
    return result;
  }

  private HashSet<String> getSchemaNames(JdbcConnection jdbcConnection) {
    Set<String> schemaNames = Sets.newHashSet()
    jdbcConnection.query("SELECT nspname FROM pg_namespace WHERE  nspname like 'sch_%'", { it ->
      while (it.next()) {
        String schema = it.getString(1);
        String tenantId = getTenantIdInSch(schema);
        if (tenantId == "6666666666") {
          continue;
        }
        if (organizationService.checkTenantNormalStatus(tenantId)) {
          schemaNames.add(schema)
        }
      }
    })
    if (CollectionUtils.isEmpty(schemaNames)) {
      schemaNames.add("public");
    }
    return schemaNames;
  }

  private List<String> getJdbcUrl() {
    JdbcConnection jdbcConnection = new JdbcConnection(podJdbcUrl, podUsername, podPassword)
    def getJdbcUrlSql = "SELECT  db_name FROM pod_metadata_resource WHERE biz='CRM' AND status=0"
    List<String> jdbcUrlList = []
    jdbcConnection.query(getJdbcUrlSql, { resultSet ->
      while (resultSet.next()) {
        def dbName = resultSet.getString(1)
        //去除系统库
        if (dbName.contains("metadata_sys") || dbName.contains("fsdb012001")) {
          continue
        }
        jdbcUrlList.add("jdbc:postgresql://" + paasPgBouncer + "/" + dbName)
      }
    })
    jdbcUrlList
  }

  private String getTenantIdInSch(String sch_tenantId) {
    String tenantId = "";
    for (int i = 0; i < sch_tenantId.length(); i++) {
      if (sch_tenantId.charAt(i) >= 48 && sch_tenantId.charAt(i) <= 57)
        tenantId += sch_tenantId.charAt(i);
    }
    return tenantId;
  }

  private boolean checkTableExist(String schema, JdbcConnection connection, String table) throws SQLException {
    int count = connection.prepareCount("select count(*) from information_schema.tables where table_schema= ? and table_name= ? ", { statement ->
      statement.setString(1, schema);
      statement.setString(2, table);
    });
    return count == 1;
  }
}
