package com.fxiaoke.paas.console.util

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.paas.console.entity.datarights.DepartmentMsg
import com.fxiaoke.paas.console.entity.datarights.Personnel
import com.fxiaoke.paas.console.entity.datarights.Principal
import com.fxiaoke.paas.console.entity.datarights.RoleMember
import com.fxiaoke.paas.console.mapper.datarights.DataRightsDataMapper
import com.google.common.collect.Maps
import lombok.experimental.UtilityClass

import java.util.stream.Collectors

/**
 * 对象打包成Map的工具类
 * <AUTHOR>
 * @date 2019/4/8 下午3:16
 */
class PackUtil {

  static Map<String, Object> packPrincipal(Principal principal) {
    if (principal == null) {
      return Maps.newHashMap()
    }
    JSONObject jsonObject = new JSONObject()
    jsonObject.put("创建时间", principal.getCreateTime())
    jsonObject.put("修改时间", principal.getModifiedTime())
    jsonObject.put("用户名称", principal.getName())
    jsonObject.put("拥有人ID", principal.getOwner())
    jsonObject.put("用户ID", principal.getUserId())
    jsonObject.put("账户名", principal.getUserName())
    jsonObject.put("上级ID", principal.getLeaderID())
    jsonObject.put("类型", principal.getType())
    return jsonObject
  }

  static Map<String, Object> packPersonnel(Personnel personnel) {
    if (personnel == null) {
      return Maps.newHashMap()
    }
    JSONObject jsonObject = new JSONObject()
    jsonObject.put("用户ID", personnel.getUserId())
    jsonObject.put("部门信息", personnel.departments)
    jsonObject.put("用户组信息",personnel.group)
    jsonObject.put("角色", (Integer.parseInt(personnel.getRole())))
    jsonObject.put("下级", personnel.getSubordinates().stream().map({ k -> k.get("subordinate") }).collect(Collectors.toSet()))
    jsonObject.put("上级", personnel.getSuperior())
    return jsonObject
  }
  /**
   * @param roleMember
   * @return
   */
  static Map<String, Object> packRoleMember(RoleMember roleMember) {
    if (roleMember == null) {
      return Maps.newHashMap()
    }
    JSONObject jsonObject = new JSONObject()
    jsonObject.put("APIName", roleMember.apiName)
    jsonObject.put("用户ID", roleMember.userId)
    jsonObject.put("用户名", roleMember.userName)
    jsonObject.put("用户名称", roleMember.name)
    jsonObject.put("创建时间", roleMember.createTime)
    jsonObject.put("修改时间", roleMember.modifiedTime)
    jsonObject.put("职位", roleMember.position)
    jsonObject.put("企业ID", roleMember.tenantId)
    jsonObject.put("上级ID", roleMember.leaderID)
    jsonObject.put("数据ID", roleMember.objectId)
    jsonObject.put("角色类型", roleTypeConverter(Integer.parseInt(roleMember.type)))

    return jsonObject
  }

  static String roleTypeConverter(Integer type) {
    switch (type) {
      case DataRightsDataMapper.JOINT_FOLLOWER:
        return "联合跟进人"
      case DataRightsDataMapper.PRINCIPAL:
        return "负责人"
      case DataRightsDataMapper.ORDINARY_MEMBER:
        return "普通人员"
      case DataRightsDataMapper.SERVICE_PERSONNEL:
        return "售后人员"
    }
  }


  static String shareTypeConverter(Integer type) {
    switch (type) {
      case 1:
        return "用户"
      case 2:
        return "用户组"
      case 3:
        return "部门"
      case 4:
        return "角色"
    }

  }


  static Map<String, Object> packDepartmentMsg(DepartmentMsg departmentMsg) {
    if (departmentMsg == null) {
      return Maps.newHashMap()
    }
    JSONObject jsonObject = new JSONObject()
    jsonObject.put("部门ID", departmentMsg.getDeptId())
    jsonObject.put("部门名称", departmentMsg.getName())
    jsonObject.put("部门负责人", departmentMsg.getLeader())
    jsonObject.put("下级部门", departmentMsg.getSubordinateDepartment())
    jsonObject.put("部门成员", departmentMsg.getUsers())
    jsonObject.put("上级部门", departmentMsg.getSuperiorDepartment())
    return jsonObject
  }

}
