<#assign title="员工信息">
<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
<style>
  #dataTable2 th {
    vertical-align: middle;
    align-items: center;
  }

  #dataTable2 td {
    vertical-align: middle;
    align-items: center
  }

  .tel {
    padding-left: 10px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #ccc
  }
</style>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
  <h1>手机号查询员工信息</h1>
  <ol class="breadcrumb">
    <li><a href="../../"><i class="fa fa-dashboard"></i>任务主页</a></li>
    <li><a href=""><i class="fa fa-dashboard"></i>手机号查询员工信息</a></li>
  </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <div class="box-header with-border">
          <form class="form-inline" action="" method="post" id="findForm" role="form" onsubmit="return false;" data-toggle="validator">
            <div class="form-group">
              <label for="tenantId">手机号</label>
              <input type="text" name="companyId" class="tel" id="tel" placeholder="手机号必填" required>
            </div>
            <button type="button" class="btn btn-primary" id="doSearch">查询</button>
          </form>
        </div>

        <div class="box-body">
          <table class="table table-striped table-bordered table-condensed dataTable no-footer" id="dataTable2">
            <thead>
            <tr>
              <th>企业ID</th>
              <th>ea</th>
              <th>员工ID</th>
              <th>拼音</th>
              <th>员工姓名</th>
              <th>企业</th>
            </tr>
            </thead>
          </table>
        </div>
        <div class="box-footer clearfix">
        </div>
      </div>
    </div>
  </div>
</section>
<#--提示信息模态框-->
<div class="modal fade" id="hintModal" tabindex="-1" role="dialog" aria-labelledby="sqlModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                class="sr-only">Close</span></button>
        <h4 class="modal-title" id="stopModalLabel">提示</h4>
      </div>
      <div class="modal-body">
        <h4 id="telInfo"></h4>
      </div>
      <div class="modal-footer">
        <a type="button" class="btn btn-default" data-dismiss="modal">返回</a>
      </div>
    </div>
  </div>
</div>
</#assign>
<#assign scriptContent>
<!--搜索高亮显示-->
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script>
  $(document).ready(function () {
    var table = $("#dataTable2").DataTable({
      "searching": true,
      "ajax": "",
      columns: [
        {data: "tenantId"},
        {data: "enterpriseId"},
        {data: "userId"},
        {data: "nickname"},
        {data: "name"},
        {data: "department"}
      ],
      "iDisplayLength": 10,
      "sPaginationType": "full_numbers",
      "language": {
        "url": "${ctx}/static/js/datatables-zh_CN.json"
      }
    });

    function search(tel) {
      if (tel !== null && tel.length !== 0) {
        if ((tel.length !== 11) && (/^1(3\d|47|(5[0-3|5-9])|(7[0|7|8])|(8[0-3|5-9]))-?\d{4}-?\d{4}$/.test(tel))) {
          $('#telInfo').html("手机号不正确！");
          $('#hintModal').modal("show");
          return
        }
        table.ajax.url("${CONTEXT_PATH}/organization/employeeByTel?tel=" + tel).load();
      } else {
        $('#telInfo').html("手机号不能为空！");
        $('#hintModal').modal("show");
      }
    }

    $("#doSearch").click(function () {
      var tel = $("#tel").val().trim();
      search(tel);
    });

    // $('#tel').bind('keypress', function (event) {
    //     search($("#tel").val().trim());
    // });


  });
</script>
</#assign>
<#include "../layout/layout-main.ftl"/>
