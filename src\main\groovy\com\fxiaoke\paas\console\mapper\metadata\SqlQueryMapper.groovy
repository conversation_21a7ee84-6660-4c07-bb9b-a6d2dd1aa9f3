package com.fxiaoke.paas.console.mapper.metadata

import com.github.mybatis.mapper.IBatchMapper
import com.github.mybatis.mapper.ICrudMapper
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * Created on 2018/4/12.
 */
interface SqlQueryMapper extends ICrudMapper, IBatchMapper, ITenant<SqlQueryMapper> {

  /**
   * 执行自主sql查询
   * @param sql
   * @return
   */
  @Select("\${sql}")
  List<Map<String, Object>> sqlQuery(@Param("sql") String sql)

  @Select("\${sql}")
  void setOption(@Param("sql") String sql)

  /**
   * 分析sql
   * @param sql
   * @return
   */
  @Select("\${sql}")
  List<String> explainSql(@Param("sql") String sql)

  /**
   * 查询正在执行的sql
   * @param idle
   * @return
   */
  @Select("SELECT procpid,START,now() - START AS lap,current_query FROM(SELECT backendid,pg_stat_get_backend_pid (S.backendid) AS procpid,pg_stat_get_backend_activity_start (S.backendid) AS START,pg_stat_get_backend_activity (S.backendid) AS current_query FROM(SELECT pg_stat_get_backend_idset () AS backendid) AS S) AS S,pg_stat_activity pa WHERE current_query <> '<IDLE>' AND procpid <> pg_backend_pid () AND pa.pid = s.procpid AND pa. STATE <> 'idle' ORDER BY lap DESC;")
  List<Map<String,Object>> getSql()

  /**
   * 查看持有锁和等待锁
   * @return
   */
  List<Map<String,Object>> getLock()

  /**
   * 获取连接信息
   * @return
   */
  @Select("SELECT * FROM pg_stat_activity WHERE NOT pid = pg_backend_pid ()")
  List<Map<String,Object>> getLink()

}
