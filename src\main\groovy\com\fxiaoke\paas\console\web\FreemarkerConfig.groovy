package com.fxiaoke.paas.console.web

import com.fxiaoke.paas.console.service.metadata.AuditLogService
import com.fxiaoke.template.ShiroAdminSupport
import com.fxiaoke.template.ShiroFreeSupport
import com.github.autoconf.ConfigFactory
import com.google.common.base.Splitter
import com.google.common.collect.Lists
import freemarker.template.TemplateModelException
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer

import javax.annotation.PostConstruct

@Component
@Lazy(false)
@Slf4j
class FreemarkerConfig {
  @Autowired
  private FreeMarkerConfigurer configurer
  @Autowired
  private AuditLogService auditLogService

  @PostConstruct
  def init() {
    ConfigFactory.getConfig("navigation-bar", { config ->
      String conf = config.get("navigation", "/:网址导航,/publish/:发布系统,/cms/:配置中心,/eye/:蜂眼监控")
      List<String> list = Splitter.on(',').trimResults().omitEmptyStrings().splitToList(conf)
      List<String[]> navigation = Lists.newArrayList()
      for (String s : list) {
        navigation.add(Splitter.on(':').limit(2).trimResults().splitToList(s).toArray(new String[2]))
      }
      try {
        configurer.getConfiguration().setSharedVariable("navigation", navigation)
        configurer.getConfiguration().setSharedVariable("principal", new ShiroFreeSupport())
        configurer.getConfiguration().setSharedVariable("hasRole", new ShiroAdminSupport())

      } catch (TemplateModelException e) {
        log.error("set shared variable", e)
      }
    })
  }
}
