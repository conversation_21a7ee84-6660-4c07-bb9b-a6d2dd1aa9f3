package com.fxiaoke.paas.console.mapper.metadata

import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * <AUTHOR>
 * Created on 2018/3/19.
 */
@ContextConfiguration(value = ["classpath:mapperContext.xml"])
@Slf4j
class DataMapperTest extends Specification {

  @Autowired
  DataMapper dataMapper

  def "findDataList测试"() {
    given:
    List<Map<String, Object>> list = dataMapper.setTenantId("55910").findDataList("payment_plan", "55910", "PaymentPlanObj")

    expect:
    list
  }

  def "通表详情测试"() {
    given:
    def dataInfoList = dataMapper.setTenantId("55910").dataInfo("payment_plan", "5a783ea9830bdb38f67c4a90", "55910")

    expect:
    dataInfoList
  }

}
