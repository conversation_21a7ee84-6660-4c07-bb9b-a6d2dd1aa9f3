package com.fxiaoke.paas.console.service.metadata

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.clickhouse.client.internal.google.common.util.concurrent.RateLimiter
import com.fxiaoke.paas.console.bean.metadata.BatchPurgeCacheArg
import com.fxiaoke.paas.console.service.OKHttpService
import com.github.autoconf.ConfigFactory
import groovy.util.logging.Slf4j
import okhttp3.Headers
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import java.lang.reflect.UndeclaredThrowableException

@Service
@Slf4j
class BatchPurgeCacheService {
  private int limit = 10
  private double tps = 0.1
  private RateLimiter rateLimiter
  @Autowired
  private SqlQueryService sqlQueryService;

  @Autowired
  OKHttpService okHttpService

  private String batchPurgeCacheUrl


  @PostConstruct
  void init() {
    ConfigFactory.getConfig("paas-console-base", { config ->
      batchPurgeCacheUrl = config.get("fs-metadata-purgeCache") + "/apiName/syn"
      limit = config.getInt("limit", 10)
      tps = config.getDouble("tps", 0.1)
      rateLimiter = RateLimiter.create(tps)
    })
  }

  def batchPurgeCache(List<BatchPurgeCacheArg> args) {
    JSONArray results = new JSONArray()
    for (BatchPurgeCacheArg arg : args) {
      String tenantId = arg.getTenantId()
      List<String> apiNames = arg.getDescribeApiNames()
      for (int i = 0; i < apiNames.size(); i += limit) {
        List<String> temp = apiNames.subList(i, Math.min(i + limit, apiNames.size()))
        JSONObject params = new JSONObject()
        params.put("tenantId", tenantId)
        params.put("describeApiNames", temp)

        Headers headers = new Headers()
        headers.newBuilder().add("Content-Type", "application/json").add("cache-control", "no-cache").build()
        JSONObject resObject = new JSONObject()
        try {
          String res = okHttpService.postJSON2(batchPurgeCacheUrl, params, headers)
          resObject = JSONObject.parseObject(res)
          resObject.put("tenantId", tenantId)
          results.add(resObject)
        } catch (UndeclaredThrowableException e) {
          resObject.put("tenantId", tenantId)
          if (e.getUndeclaredThrowable() instanceof SocketTimeoutException) {
            log.warn("请求超时")
            resObject.put("message", "请求超时")
          } else {
            log.warn("清理缓存异常", e.getUndeclaredThrowable())
            resObject.put("errorMessage", e.getUndeclaredThrowable().getMessage())
          }
          results.add(resObject)
          continue
        }
        rateLimiter.acquire(1)
      }
    }
    return results.toJSONString()
  }
}