<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css" xmlns="http://www.w3.org/1999/html" xmlns="http://www.w3.org/1999/html"/>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>创建任务</h1>
        <ol class="breadcrumb">
            <li><a href="../"><i class="fa fa-dashboard"></i>任务主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>创建Schema任务</a></li>
        </ol>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info">
                    <div class="box-body">
                        <div class="tab-content">
                            <div class="tab-pane fade in active" id="createDesigned">
                                <form class="form-horizontal" action="${ctx}/hamster/save-task" method="post" id="myForm" role="form" data-toggle="validator">
                                    <div class="form-group">
                                        <div class="col">
                                            <label for="name" class="col-sm-2 control-label">任务名称</label>
                                            <div class="col-sm-3">
                                                <input type="text" class="form-control" style="border-radius:5px;" id="name" name="name" value="" placeholder="任务名称" required>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <label for="templateId" class="col-sm-2 control-label">任务模板</label>
                                            <div class="col-sm-3">
                                                <select id="templateId" name="templateId" class="selectpicker show-tick form-control" data-live-search="false">
                                                    <#list hamsterTemplates! as hamsterTemplate>
                                                        <option value="${hamsterTemplate.id}">${hamsterTemplate.name}</option>
                                                    </#list>
                                                </select>
                                                <div class="help-block with-errors"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col">
                                            <label for="tenantId" class="col-sm-2 control-label">企业id</label>
                                            <div class="col-sm-3">
                                                <input type="text" class="form-control" style="border-radius:5px;" id="tenantId" name="tenantId" value="" placeholder="必填" onkeyup="this.value=this.value.replace(/\s*/g,'')" required>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <label for="currentCloud" class="col-sm-2 control-label">DB当前云</label>
                                            <div class="col-sm-3">
                                                <select id="currentCloud" name="currentCloud" class="selectpicker show-tick form-control" data-live-search="false">
                                                    <#list clouds! as cloud>
                                                        <option value="${cloud}">${cloud}</option>
                                                    </#list>
                                                </select>
                                                <div class="help-block with-errors"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col">
                                            <label for="targetCloud" class="col-sm-2 control-label">DB目标云</label>
                                            <div class="col-sm-3">
                                                <select id="targetCloud" name="targetCloud" class="selectpicker show-tick form-control" data-live-search="false">
                                                    <#list clouds! as cloud>
                                                        <option value="${cloud}">${cloud}</option>
                                                    </#list>
                                                </select>
                                                <div class="help-block with-errors"></div>
                                            </div>
                                        </div>

                                        <div class="col">
                                            <label for="serviceCloud" class="col-sm-2 control-label">服务所在云</label>
                                            <div class="col-sm-3">
                                                <select id="serviceCloud" name="serviceCloud" class="selectpicker show-tick form-control" data-live-search="false">
                                                    <#list clouds! as cloud>
                                                        <option value="${cloud}">${cloud}</option>
                                                    </#list>
                                                </select>
                                                <div class="help-block with-errors"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col">
                                            <label for="targetServiceCloud" class="col-sm-2 control-label">服务目标云</label>
                                            <div class="col-sm-3">
                                                <select id="targetServiceCloud" name="targetServiceCloud" class="selectpicker show-tick form-control" data-live-search="false">
                                                    <#list clouds! as cloud>
                                                        <option value="${cloud}">${cloud}</option>
                                                    </#list>
                                                </select>
                                                <div class="help-block with-errors"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col">
                                            <label for="jdbcNewPaasMaster" class="col-sm-2 control-label">PaaS新地址(主库)</label>
                                            <div class="col-sm-3">
                                                <input type="text" class="form-control" style="border-radius:5px;" id="jdbcNewPaasMaster" name="jdbcNewPaasMaster" value="" placeholder="必填" onkeyup="this.value=this.value.replace(/\s*/g,'')" required>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <label for="jdbcNewPaasSlave" class="col-sm-2 control-label">PaaS新地址(从库)</label>
                                            <div class="col-sm-3">
                                                <input type="text" class="form-control" style="border-radius:5px;" id="jdbcNewPaasSlave" name="jdbcNewPaasSlave" value="" placeholder="必填" onkeyup="this.value=this.value.replace(/\s*/g,'')" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col">
                                            <label for="paasPgbouncerNewMaster" class="col-sm-2 control-label">Paas新PGBOUNCER（主）</label>
                                            <div class="col-sm-3">
                                                <input type="text" class="form-control" style="border-radius:5px;" id="paasPgbouncerNewMaster" name="paasPgbouncerNewMaster" value="" placeholder="必填" onkeyup="this.value=this.value.replace(/\s*/g,'')" required>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <label for="paasPgbouncerNewSlave" class="col-sm-2 control-label">Paas新PGBOUNCER（从）</label>
                                            <div class="col-sm-3">
                                                <input type="text" class="form-control" style="border-radius:5px;" id="paasPgbouncerNewSlave" name="paasPgbouncerNewSlave" value="" placeholder="必填" onkeyup="this.value=this.value.replace(/\s*/g,'')" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col">
                                            <label for="jdbcNewBiMaster" class="col-sm-2 control-label">BI新地址(主库)</label>
                                            <div class="col-sm-3">
                                                <input type="text" class="form-control" style="border-radius:5px;" id="jdbcNewBiMaster" name="jdbcNewBiMaster" value="" placeholder="必填" onkeyup="this.value=this.value.replace(/\s*/g,'')" required>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <label for="jdbcNewBiSlave" class="col-sm-2 control-label">BI新地址(从库)</label>
                                            <div class="col-sm-3">
                                                <input type="text" class="form-control" style="border-radius:5px;" id="jdbcNewBiSlave" name="jdbcNewBiSlave" value="" placeholder="必填" onkeyup="this.value=this.value.replace(/\s*/g,'')" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col">
                                            <label for="biPgbouncerNewMaster" class="col-sm-2 control-label">BI新PGBOUNCER（主）</label>
                                            <div class="col-sm-3">
                                                <input type="text" class="form-control" style="border-radius:5px;" id="biPgbouncerNewMaster" name="biPgbouncerNewMaster" value="" placeholder="必填" onkeyup="this.value=this.value.replace(/\s*/g,'')" required>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <label for="biPgbouncerNewSlave" class="col-sm-2 control-label">BI新PGBOUNCER（从）</label>
                                            <div class="col-sm-3">
                                                <input type="text" class="form-control" style="border-radius:5px;" id="biPgbouncerNewSlave" name="biPgbouncerNewSlave" value="" placeholder="必填" onkeyup="this.value=this.value.replace(/\s*/g,'')" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-2"></div>
                                        <div class="col-sm-3">
                                            <button id="creatTaskBtn" type="submit" class="btn btn-primary">创建</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="box-footer clearfix">
                        <div class="clearfix"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script type="application/javascript">
        $('#creatTaskBtn').on('click', function (event) {
            event.preventDefault();
            $.ajax({
                url: '${ctx}/hamster/has-save-role',
                type: 'GET',
                async: false,
                success: function (response) {
                    if (response != 'yes') {
                        alert("权限不足")
                        return;
                    }
                    let finalDecision = confirm("你确定提交吗?");
                    if (!finalDecision) {
                        return;
                    }
                    $("form").submit();
                }
            });
        })
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
