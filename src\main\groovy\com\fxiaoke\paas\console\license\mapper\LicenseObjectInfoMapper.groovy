package com.fxiaoke.paas.console.license.mapper;

import com.fxiaoke.paas.console.entity.license.LicenseObjectEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
interface LicenseObjectInfoMapper {

  List<LicenseObjectEntity> queryObjectInfoApiName(@Param("productVersion") String version, @Param("moduleCode") String moduleCode);

  @Select("select * from license_object_info where api_name is not null and crm_key = 'crm_manage_custom_object' and module_type = '0'")
  List<LicenseObjectEntity> queryObjectInfoApiNameFromALL();

}
