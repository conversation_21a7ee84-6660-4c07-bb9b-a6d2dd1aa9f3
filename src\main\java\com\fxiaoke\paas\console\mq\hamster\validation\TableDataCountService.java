package com.fxiaoke.paas.console.mq.hamster.validation;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.hamster.pojo.HamsterTaskPojo;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.common.PasswordUtil;
import com.fxiaoke.common.SqlEscaper;
import com.fxiaoke.jdbc.JdbcConnection;
import com.fxiaoke.paas.console.service.hamster.HamsterTaskService;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.sql.SQLException;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TableDataCountService {
  private Map<String, SchemaDbResource> dbResourceMap = Maps.newHashMap();
  @Autowired
  private SchemaJdbcService schemaJdbcService;

  @Autowired
  HamsterTaskService hamsterTaskService;

  @Autowired
  private DbRouterClient dbRouterClient;

  /***
   * cloud, username, password(encoded)
   */
  private Map<String, DbAccount> cloudSchemaUserPass;
  private Map<String, DbAccount> cloudSchemaUserBi;
  private Map<String, DbAccount> cloudDbConsolePass;

  ThreadPoolExecutor executor = new ThreadPoolExecutor(30, 30, 100, TimeUnit.MILLISECONDS, new LinkedBlockingDeque<>(3000));

  private String podUserName;
  private String podPassword;

  private String opLogMasterUrl;
  private String opLogUserName;
  private String opLogPassword;

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-paas-copier-url", iConfig -> {
      JSONArray jsonArray = JSONArray.parseArray(iConfig.getString());
      int size = jsonArray.size();
      for (int i = 0; i < size; i++) {
        JSONObject jsonObject = jsonArray.getJSONObject(i);
        List<String> tenantIds = JSONObject.parseObject(jsonObject.getString("tenantIds"), List.class);
        String oldUrl = jsonObject.getString("old");
        String newUrl = jsonObject.getString("new");
        String oldCloud = jsonObject.getString("sourceCloud");
        String newCloud = jsonObject.getString("targetCloud");
        String biOldUrl = jsonObject.getString("bi-old");
        String biNewUrl = jsonObject.getString("bi-new");

        if (CollectionUtils.isNotEmpty(tenantIds)) {


          tenantIds.forEach(tenantId -> {
            SchemaDbResource resource = new SchemaDbResource();
            resource.setOldCould(oldCloud);
            resource.setNewCloud(newCloud);
            resource.setNewUrl(newUrl);
            resource.setOldUrl(oldUrl);
            resource.setTenantId(tenantId);
            resource.setBiOldUrl(biOldUrl);
            resource.setBiNewUrl(biNewUrl);
            dbResourceMap.put(tenantId, resource);
          });

        }
      }
    });


    ConfigFactory.getConfig("paas-hamster", iConfig -> {
      cloudSchemaUserPass = parseDbAccountInfos(iConfig.get("cloud_schema_user_pass"));
      cloudSchemaUserBi = parseDbAccountInfos(iConfig.get("cloud_schema_user_bi"));
      cloudDbConsolePass = parseDbAccountInfos(iConfig.get("cloud_dbconsole_pass"));
    });

    ConfigFactory.getConfig("dml-manager", config -> {
      podUserName = config.get("pod_user_name", "fs_pgdb_b_u_dbconsole");
      podPassword = PasswordUtil.decode(config.get("pod_paasword", "B8A69D656A66AC412924EFB140B1C82AB7875A46A75FB135E7BD61EF64F9DCFC"));
    });

    ConfigFactory.getConfig("fs-paas-copier-consumer-db", config -> {
      opLogMasterUrl = config.get("masterUrl");
      opLogUserName = config.get("username");
      opLogPassword = PasswordUtil.decode(config.get("password"));
    });
  }


  private Map<String, DbAccount> parseDbAccountInfos(String dbAccountInfoString) {
    Map<String, DbAccount> temp = Maps.newHashMap();
    if (StringUtils.isNotEmpty(dbAccountInfoString)) {
      String[] split = dbAccountInfoString.split(";");
      for (int i = 0; i < split.length; i++) {
        String item = split[i];
        if (StringUtils.isEmpty(item)) {
          continue;
        }
        String[] cloudUserPaasItem = item.split(":");
        if (cloudUserPaasItem.length != 3) {
          continue;
        }
        if (StringUtils.isEmpty(cloudUserPaasItem[0])) {
          continue;
        }

        if (!temp.containsKey(cloudUserPaasItem[0])) {
          DbAccount dbAccount = new DbAccount();
          dbAccount.setUsername(cloudUserPaasItem[1]);
          dbAccount.setPassword(PasswordUtil.decode(cloudUserPaasItem[2]));
          temp.put(cloudUserPaasItem[0], dbAccount);
        }
      }
    }
    return temp;
  }


  public Collection<TableDataReport> calculate(String tenantId, HamsterTaskPojo task, boolean isQueryBISlave) throws Exception {
    SchemaDbResource schemaDbResource = dbResourceMap.get(tenantId);
    if (schemaDbResource == null) {
      throw new Exception("cannot find tenant in fs-paas-copier-url cms");
    }
    if (task != null) {
      schemaDbResource.setOldCould(task.getServiceCloud());
      schemaDbResource.setNewCloud(task.getTargetServiceCloud());
    }
    JdbcConnection connectionNewPaas = getConnection(schemaDbResource, true, "PAAS", true);
    JdbcConnection connectionOldPaas = getConnection(schemaDbResource, false, "PAAS", true);
    Collection<TableDataReport> tableDataReportsPaas = getTableDataReports(schemaDbResource, connectionNewPaas, connectionOldPaas, connectionOldPaas, "PAAS");
    JdbcConnection connectionNewBI = getConnection(schemaDbResource, true, "BI", true);
    JdbcConnection connectionOldBI = getConnection(schemaDbResource, false, "BI", true);
    if (isQueryBISlave) {
      SchemaDbResource biSlaveSchemaDbResource = queryBISlaveSchemaDbResource(schemaDbResource);
      if (biSlaveSchemaDbResource != null) {
        connectionOldBI = getConnection(biSlaveSchemaDbResource, false, "BI", true);
      }
    }
    Collection<TableDataReport> tableDataReportsBI = getTableDataReports(schemaDbResource, connectionNewBI, connectionOldBI, connectionOldPaas, "BI");
    tableDataReportsBI.addAll(tableDataReportsPaas);
    return tableDataReportsBI;
  }

  private SchemaDbResource queryBISlaveSchemaDbResource(SchemaDbResource masterSchemaDbResource) {
    SchemaDbResource slaveSchemaDbResource = new SchemaDbResource();
    slaveSchemaDbResource.setTenantId(masterSchemaDbResource.getTenantId());
    slaveSchemaDbResource.setOldUrl(masterSchemaDbResource.getOldUrl());
    slaveSchemaDbResource.setNewUrl(masterSchemaDbResource.getNewUrl());
    slaveSchemaDbResource.setOldCould(masterSchemaDbResource.getOldCould());
    slaveSchemaDbResource.setNewCloud(masterSchemaDbResource.getNewCloud());
    slaveSchemaDbResource.setBiNewUrl(masterSchemaDbResource.getBiNewUrl());

    RouterInfo biOldRouter = dbRouterClient.queryRouterInfo(masterSchemaDbResource.getTenantId(), "BI", "application", "postgresql", true);

    if (biOldRouter == null || StringUtils.isBlank(biOldRouter.getSlaveUrl())) {
      log.info("queryBISlaveSchemaDbResource empty, tenantId:{}", masterSchemaDbResource.getTenantId());
      return null;
    }

    slaveSchemaDbResource.setBiOldUrl(biOldRouter.getSlaveUrl());

    return slaveSchemaDbResource;
  }

  private Collection<TableDataReport> getTableDataReports(SchemaDbResource schemaDbResource,
                                                          JdbcConnection connectionNew,
                                                          JdbcConnection connectionOld,
                                                          JdbcConnection paasOldConnection,
                                                          String biz) {
    TableDataCounter oldTableDataCounter = new TableDataCounter(schemaDbResource, connectionOld, paasOldConnection, false, biz);
    TableDataCounter newTableDataCounter = new TableDataCounter(schemaDbResource, connectionNew, paasOldConnection, true, biz);
    Future<List<TableDataReport>> submitOld = executor.submit(oldTableDataCounter);
    Future<List<TableDataReport>> submitNew = executor.submit(newTableDataCounter);
    Map<String, TableDataReport> aggregation = Maps.newHashMap();

    try {
      List<TableDataReport> tableDataReportsOld = submitOld.get();
      List<TableDataReport> tableDataReportsNew = submitNew.get();

      for (TableDataReport r : tableDataReportsOld) {
        aggregation.put(r.getTableName(), r);
      }
      for (TableDataReport r : tableDataReportsNew) {
        if (aggregation.containsKey(r.getTableName())) {
          aggregation.get(r.getTableName()).setNewCount(r.getNewCount());
        } else {
          if (r.getNewCount() != 0) {
            aggregation.put(r.getTableName(), TableDataReport.builder().tableName(r.getTableName()).newCount(r.getNewCount()).build());
          }
        }
      }
    } catch (InterruptedException e) {
      log.error("getTableDataReports, tenantId:{}", schemaDbResource.getTenantId(), e);
    } catch (ExecutionException e) {
      log.error("getTableDataReports, tenantId:{}", schemaDbResource.getTenantId(), e);
    } catch (Exception ex) {
      log.error("getTableDataReports, tenantId:{}", schemaDbResource.getTenantId(), ex);
    }
    return aggregation.values().stream().collect(Collectors.toList());
  }


  public Collection<TableDataReport> calculateRefactor(String tenantId, boolean isQueryOpLog, HamsterTaskPojo task) throws Exception {
    if (StringUtils.isBlank(tenantId)) {
      return Lists.newArrayList();
    }

    SchemaDbResource schemaDbResource = dbResourceMap.get(tenantId);
    if (schemaDbResource == null) {
      throw new Exception("cannot find tenant in fs-paas-copier-url cms");
    }

    if (task != null) {
      schemaDbResource.setOldCould(task.getServiceCloud());
      schemaDbResource.setNewCloud(task.getTargetServiceCloud());
    }

    JdbcConnection connectionNewPaas = getConnection(schemaDbResource, true, "PAAS", true);
    JdbcConnection connectionOldPaas = getConnection(schemaDbResource, false, "PAAS", true);
    JdbcConnection connectionNewBI = getConnection(schemaDbResource, true, "BI", true);
    JdbcConnection connectionOldBI = getConnection(schemaDbResource, false, "BI", true);

    return batchQueryTableDataReport(schemaDbResource, tenantId, connectionNewPaas, connectionOldPaas, connectionNewBI, connectionOldBI, isQueryOpLog);
  }

  private List<TableDataReport> batchQueryTableDataReport(SchemaDbResource schemaDbResource,
                                                          String tenantId,
                                                          JdbcConnection connectionNewPaas,
                                                          JdbcConnection connectionOldPaas,
                                                          JdbcConnection connectionNewBI,
                                                          JdbcConnection connectionOldBI,
                                                          boolean isQueryOpLog) {

    Set<String> allBIMigrateTable = hamsterTaskService.queryAllMigrateTable(tenantId, "BI");
    Set<String> allPAASMigrateTable = hamsterTaskService.queryAllMigrateTable(tenantId, "PAAS");

    TableDataCounter oldPaasCounter = new TableDataCounter(schemaDbResource, connectionOldPaas, connectionOldPaas, false, "PAAS");
    TableDataCounter newPaasCounter = new TableDataCounter(schemaDbResource, connectionNewPaas, connectionOldPaas, true, "PAAS");
    TableDataCounter oldBICounter = new TableDataCounter(schemaDbResource, connectionOldBI, connectionOldPaas, false, "BI");
    TableDataCounter newBICounter = new TableDataCounter(schemaDbResource, connectionNewBI, connectionOldPaas, true, "BI");

    Future<List<TableDataReport>> submitOldPaas = executor.submit(oldPaasCounter);
    Future<List<TableDataReport>> submitNewPaas = executor.submit(newPaasCounter);

    Future<List<TableDataReport>> submitOldBI = executor.submit(oldBICounter);
    Future<List<TableDataReport>> submitNewBI = executor.submit(newBICounter);

    List<TableDataReport> result = Lists.newArrayList();

    try {
      //PAAS数据验证
      Map<String, TableDataReport> aggregationPaas = Maps.newHashMap();

      List<TableDataReport> tableDataReportsOldPaas = submitOldPaas.get();
      List<TableDataReport> tableDataReportsNewPaas = submitNewPaas.get();

      for (TableDataReport v : tableDataReportsOldPaas) {
        if (allPAASMigrateTable.contains("all") || allPAASMigrateTable.contains(v.getTableName())) {
          v.setIsMigrate(true);
        } else {
          v.setIsMigrate(false);
        }
      }

      //查询PAAS原库的数据
      for (TableDataReport r : tableDataReportsOldPaas) {
        aggregationPaas.put(r.getTableName(), r);
      }

      Map<String, Map<String, Integer>> opLogCountMap = Maps.newHashMap();

      //统计oplog数据
      if (isQueryOpLog) {
        opLogCountMap = queryOpLogCount(schemaDbResource.getTenantId(), oldPaasCounter.getCountTimeMap(), connectionOldPaas);
      }

      //查询PAAS目标库的数据更新对应表的newCount
      for (TableDataReport r : tableDataReportsNewPaas) {
        int newPaasCount = r.getNewCount();

        //如果统计的opLog数据包含当前表，需要修改目标库的newCount
        if (opLogCountMap.containsKey(r.getTableName())) {
          newPaasCount = opLogCountMap.get(r.getTableName()).getOrDefault("I", 0) + newPaasCount - opLogCountMap.get(r.getTableName()).getOrDefault("D", 0);
        }

        if (aggregationPaas.containsKey(r.getTableName())) {
          aggregationPaas.get(r.getTableName()).setNewCount(newPaasCount);
        } else {
          if (newPaasCount != 0) {
            aggregationPaas.put(r.getTableName(), TableDataReport.builder().tableName(r.getTableName()).newCount(newPaasCount).build());
          }
        }
      }

      Map<String, TableDataReport> aggregationBI = Maps.newHashMap();

      List<TableDataReport> tableDataReportsOldBI = submitOldBI.get();
      List<TableDataReport> tableDataReportsNewBI = submitNewBI.get();

      for (TableDataReport v : tableDataReportsOldBI) {
        if (allBIMigrateTable.contains("all") || allBIMigrateTable.contains(v.getTableName())) {
          v.setIsMigrate(true);
        } else {
          v.setIsMigrate(false);
        }
      }

      //查询BI原库的数据
      for (TableDataReport r : tableDataReportsOldBI) {
        aggregationBI.put(r.getTableName(), r);
      }

      for (TableDataReport r : tableDataReportsNewBI) {
        if (aggregationBI.containsKey(r.getTableName())) {
          aggregationBI.get(r.getTableName()).setNewCount(r.getNewCount());
        } else {
          if (r.getNewCount() != 0) {
            aggregationBI.put(r.getTableName(), TableDataReport.builder().tableName(r.getTableName()).newCount(r.getNewCount()).build());
          }
        }
      }

      //当BI原库和目标库表数据量不一样。如果当前表在PAAS存在，标识BI表的数据是从PAAS数据同步过去的，需要将对应的oldCount更新为PAAS原库数据量
      for (Map.Entry<String, TableDataReport> entry : aggregationBI.entrySet()) {
        String currentTableName = entry.getKey();
        TableDataReport currentData = entry.getValue();
        if (!currentData.areEqual() && aggregationPaas.containsKey(currentTableName)) {
          currentData.setOldCount(aggregationPaas.get(currentTableName).getOldCount());
        }
      }

      result.addAll(aggregationPaas.values());
      result.addAll(aggregationBI.values());
    } catch (Exception ex) {
      log.error("batchQueryTableDataReport error", ex);
    }

    return result;
  }


  /**
   * 查询opLog数据
   * 返回值：key：表名 value：操作类型：数量
   * @param tenantId
   * @param countTimeMap
   * @param connectionOldPaas
   * @return
   */
  private Map<String, Map<String, Integer>> queryOpLogCount(String tenantId, Map<String, Map<String, Long>> countTimeMap, JdbcConnection connectionOldPaas) {
    Map<String, Map<String, Integer>> result = Maps.newHashMap();

    if (StringUtils.isBlank(opLogMasterUrl) || StringUtils.isBlank(opLogUserName) || StringUtils.isBlank(opLogPassword)) {
      return result;
    }

    Map<String, Long> customCountTimeMap = Maps.newHashMap();
    Map<String, Long> presetCountTimeMap = Maps.newHashMap();

    Set<String> customTableNames = Sets.newHashSet("mt_data", "mt_data_lang", "mt_data_change");

    for (String v : countTimeMap.keySet()) {
      if (customTableNames.contains(v)) {
        customCountTimeMap.putAll(countTimeMap.get(v));
      } else {
        presetCountTimeMap.putAll(countTimeMap.get(v));
      }
    }

    //查询opLog数据（不包含mt_data、mt_data_lang、mt_data_change，因为这几个表需要根据原库查询表名）
    for (Map.Entry<String, Long> entry : presetCountTimeMap.entrySet()) {
      String queryTableName = entry.getKey();
      Long countTime = entry.getValue();
      if (StringUtils.isBlank(queryTableName)) {
        continue;
      }
      if (countTime == null) {
        countTime = System.currentTimeMillis();
      }
      result.computeIfAbsent(queryTableName, x -> Maps.newHashMap()).putAll(queryOpLogCountByOpType(tenantId, queryTableName, countTime));
    }

    if (MapUtils.isNotEmpty(customCountTimeMap)) {
      long maxQueryTime = Collections.max(customCountTimeMap.values());

      //查询mt_data、mt_data_lang、mt_data_change表的opLog数据
      queryCustomOpLogCount(tenantId, "mt_data", customCountTimeMap, maxQueryTime, connectionOldPaas).forEach((x, y) -> {
        result.computeIfAbsent(x, z -> Maps.newHashMap()).put("I", y);
      });
      queryCustomOpLogCount(tenantId, "mt_data_change", customCountTimeMap, maxQueryTime, connectionOldPaas).forEach((x, y) -> {
        result.computeIfAbsent(x, z -> Maps.newHashMap()).put("I", y);
      });
      queryCustomOpLogCount(tenantId, "mt_data_lang", customCountTimeMap, maxQueryTime, connectionOldPaas).forEach((x, y) -> {
        result.computeIfAbsent(x, z -> Maps.newHashMap()).put("I", y);
      });
    }

    return result;
  }

  private Map<String, Integer> queryOpLogCountByOpType(String tenantId, String queryTableName, long countTime) {
    Map<String, Integer> result = Maps.newHashMap();

    if (StringUtils.isBlank(opLogMasterUrl) || StringUtils.isBlank(opLogUserName) || StringUtils.isBlank(opLogPassword)) {
      return result;
    }

    //查询opLog数据（不包含mt_data、mt_data_lang、mt_data_change，因为这几个表需要根据原库查询表名）
    StringBuilder builder = new StringBuilder();
    builder
      .append("select op,count(1) as data_count from (select tenant_id, cast(keys as jsonb) ->> 'op' as op from oplog_consumer_log where ")
      .append(SqlEscaper.pg_op_eq("tenant_id", tenantId))
      .append(" and  ")
      .append(SqlEscaper.pg_op_eq("table_name", queryTableName))
      .append(" and time < ")
      .append(countTime)
      .append(" ) as a where op in ('I','D') group by op");

    try {
      JdbcConnection jdbcConnection = new JdbcConnection(opLogMasterUrl, opLogUserName, opLogPassword);

      jdbcConnection.query(builder.toString(), rs -> {
        while (rs.next()) {
          String currentOpType = rs.getString("op");
          int dataCount = rs.getInt("data_count");
          result.put(currentOpType, dataCount);
        }
      });
    } catch (SQLException e) {
      log.warn("queryOpLogCount error", e);
    }

    return result;
  }

  /**
   * 统计自定义表的opLog数量（暂时只统计插入数据的数量）
   * 返回值：key：表名 value：数量
   *
   * @param tenantId
   * @param queryTableName
   * @param countTime
   * @param connectionOldPaas
   * @return
   */
  private Map<String, Integer> queryCustomOpLogCount(String tenantId,
                                                     String queryTableName,
                                                     Map<String, Long> customCountTimeMap,
                                                     long countTime,
                                                     JdbcConnection connectionOldPaas) {
    Map<String, Integer> result = Maps.newHashMap();

    AtomicLong lastQueryTime = new AtomicLong(0);
    Map<String,Long> dataIdTimeMap = Maps.newHashMap();
    do {
      dataIdTimeMap.clear();
      dataIdTimeMap = queryCustomOpLogIds(tenantId, queryTableName, lastQueryTime, countTime);
      Map<String, Integer> customDataCount = queryCustomDataCount(tenantId, queryTableName, customCountTimeMap, dataIdTimeMap, connectionOldPaas);
      for (Map.Entry<String, Integer> entry : customDataCount.entrySet()) {
        String currentTableName = entry.getKey();
        if (!customCountTimeMap.containsKey(currentTableName)) {
          continue;
        }
        Integer totalCount = result.getOrDefault(currentTableName, 0) + entry.getValue();
        result.put(currentTableName, totalCount);
      }
      customDataCount.clear();
    } while (MapUtils.isNotEmpty(dataIdTimeMap));

    return result;
  }

  /**
   * 根据数据ID集合，统计表名和数量
   * 返回值：key：表名 value：数量
   *
   * @param tenantId
   * @param queryTableName
   * @param customCountTimeMap
   * @param dataIdTimeMap
   * @param connectionOldPaas
   * @return
   */
  private Map<String, Integer> queryCustomDataCount(String tenantId, String queryTableName, Map<String, Long> customCountTimeMap, Map<String,Long> dataIdTimeMap, JdbcConnection connectionOldPaas) {
    Map<String, Integer> result = Maps.newHashMap();
    if (StringUtils.isBlank(tenantId) || MapUtils.isEmpty(customCountTimeMap) || MapUtils.isEmpty(dataIdTimeMap)) {
      return result;
    }

    if (!Sets.newHashSet("mt_data", "mt_data_lang", "mt_data_change").contains(queryTableName)) {
      return result;
    }

    String columnName = " id, object_describe_api_name as object_describe_api_name ";
    if ("mt_data_lang".equals(queryTableName)) {
      columnName = " id, describe_api_name as object_describe_api_name ";
    }

    StringBuilder builder = new StringBuilder();

    builder
      .append("select ")
      .append(columnName)
      .append(" from ")
      .append(queryTableName)
      .append(" where ")
      .append(SqlEscaper.pg_op_eq("tenant_id", tenantId))
      .append(" and ")
      .append(SqlEscaper.pg_op_in("id", dataIdTimeMap.keySet()));
    try {
      connectionOldPaas.query(builder.toString(), rs -> {
        while (rs.next()) {
          String id = rs.getString("id");
          String describeApiName = rs.getString("object_describe_api_name");
          if (StringUtils.isAnyBlank(id,describeApiName)) {
            continue;
          }

          String describeTableName = ("mt_data_lang".equals(queryTableName) ? describeApiName.toLowerCase() + "_lang" : describeApiName.toLowerCase());

          Long maxCountTime = customCountTimeMap.get(describeTableName);
          Long currentOpTime = dataIdTimeMap.get(id);
          if (maxCountTime == null || currentOpTime == null || currentOpTime >= maxCountTime){
            continue;
          }

          result.put(describeTableName, result.getOrDefault(describeTableName, 0) + 1);
        }
      });
    } catch (SQLException e) {
      log.warn("queryCustomDataCount error", e);
    }

    return result;
  }

  /**
   * 根据表名、操作类型、时间查询opLog的数据ID集合
   * @param tenantId
   * @param currentTableName
   * @param lastQueryTime
   * @param countTime
   * @return
   */
  private Map<String, Long> queryCustomOpLogIds(String tenantId, String currentTableName, AtomicLong lastQueryTime, long countTime) {
    Map<String, Long> result = Maps.newHashMap();

    if (StringUtils.isBlank(opLogMasterUrl) || StringUtils.isBlank(opLogUserName) || StringUtils.isBlank(opLogPassword)) {
      return result;
    }

    String queryCustomTableSql = "select cast(keys as jsonb) ->> 'id' as id,time from oplog_consumer_log where tenant_id=? and  table_name = ? and cast(keys as jsonb) ->> 'op' = 'I' and time > ? and time < ? order by time asc limit 1000";

    try {
      JdbcConnection jdbcConnection = new JdbcConnection(opLogMasterUrl, opLogUserName, opLogPassword);

      jdbcConnection.prepareQuery(queryCustomTableSql, p -> {
        p.setString(1, tenantId);
        p.setString(2, currentTableName);
        p.setLong(3, lastQueryTime.get());
        p.setLong(4, countTime);
      }, rs -> {
        while (rs.next()) {
          String currentId = rs.getString("id");
          long modifyTime = rs.getLong("time");
          if (StringUtils.isBlank(currentId)) {
            continue;
          }
          result.put(currentId, modifyTime);
          lastQueryTime.set(modifyTime);
        }
      });
    } catch (SQLException e) {
      log.warn("queryCustomOpLogIds error", e);
    }
    return result;
  }

  public JdbcConnection getConnection(String tenantId, String biz, boolean isNew, HamsterTaskPojo task, boolean isSchema) {
    SchemaDbResource schemaDbResource = dbResourceMap.get(tenantId);
    if (schemaDbResource == null) {
      return null;
    }
    if (task != null) {
      schemaDbResource.setOldCould(task.getServiceCloud());
      schemaDbResource.setNewCloud(task.getTargetServiceCloud());
    }
    return getConnection(schemaDbResource, isNew, biz, isSchema);
  }

  private JdbcConnection getConnection(SchemaDbResource schemaDbResource, boolean isNew, String biz, boolean isSchema) {
    JdbcConnection connection = null;
    String cloud = isNew ? schemaDbResource.getNewCloud() : schemaDbResource.getOldCould();
    String jdbcUrl = null;
    if ("PAAS".equals(biz)) {
      jdbcUrl = isNew ? schemaDbResource.getNewUrl() : schemaDbResource.getOldUrl();
    } else {
      jdbcUrl = isNew ? schemaDbResource.getBiNewUrl() : schemaDbResource.getBiOldUrl();
    }

    if (!isSchema) {
      return schemaJdbcService.connection(jdbcUrl, podUserName, podPassword);
    }


    if (StringUtils.isNotEmpty(cloud)) {
      DbAccount newSchemaUserAccount;
      if (biz.equals("PAAS")) {
        newSchemaUserAccount = cloudSchemaUserPass.get(cloud);
      } else {
        newSchemaUserAccount = cloudSchemaUserBi.get(cloud);
      }
      if (newSchemaUserAccount != null) {
        connection = schemaJdbcService.connection(jdbcUrl, newSchemaUserAccount.getUsername(), newSchemaUserAccount.getPassword());
      }
    }

    if (connection == null) {
      connection = schemaJdbcService.connection(jdbcUrl);
    }
    return connection;
  }
}
