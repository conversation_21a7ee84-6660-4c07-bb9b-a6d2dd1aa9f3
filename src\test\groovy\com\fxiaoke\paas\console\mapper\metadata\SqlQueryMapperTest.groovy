package com.fxiaoke.paas.console.mapper.metadata

import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * <AUTHOR>
 * Created on 2018/4/12.
 */
@ContextConfiguration(value = ["classpath:mapperContext.xml"])
@Slf4j
class SqlQueryMapperTest extends Specification {

  @Autowired
  SqlQueryMapper sqlQueryMapper

  def "sqlQuery"() {
    given:
    def sql = "select * from mt_data where tenant_id = '2' limit 10"
    expect:
    sqlQueryMapper.setTenantId("2").sqlQuery(sql)
  }

  def "explainSql"() {
    given:
    def sql = "select * from mt_data where tenant_id = '2' limit 10"
    expect:
    sqlQueryMapper.setTenantId("2").explainSql(sql)
  }

  def "getSql"() {
    expect:
    sqlQueryMapper.setTenantId("2").getSql()
  }

  def "getLock"() {
    expect:
    sqlQueryMapper.setTenantId("2").getLock()
  }

}
