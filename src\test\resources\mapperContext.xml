<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/aop
        http://www.springframework.org/schema/aop/spring-aop.xsd
        ">

    <context:component-scan base-package="com.fxiaoke.paas.console.service"/>
    <context:component-scan base-package="com.fxiaoke.paas.console.dao"/>
    <bean id="httpSupport" name="httpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean" p:configName="paas-console-remotecall"/>
    <import resource="classpath*:/META-INF/spring/pod-api-client.xml"/>
    <!--option服务-->
    <import resource="classpath*:/META-INF/spring/option-api-client.xml"/>
    <bean class="com.fxiaoke.paas.console.util.HttpClientUtil" lazy-init="false" p:client-ref="httpSupport"/>
    <!--mongo-->
    <bean id="dataStore" class="com.github.mongo.support.MongoDataStoreFactoryBean" p:configName="mongo-support-workflow"/>
    <bean id="dbRouterClient" class="com.facishare.paas.pod.client.DbRouterClient"/>
    <!--jvm监控-->
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>
    <aop:aspectj-autoproxy/>

    <import resource="classpath:spring/paas-console-spring-cms.xml"/>
    <import resource="classpath:spring/paas-console-spring-guava.xml"/>
    <!--<import resource="classpath:spring/paas-console-spring-shiro.xml"/>-->
    <import resource="classpath:spring/spring-db.xml"/>
    <import resource="classpath:spring/dubbo-consumer.xml"/>
    <import resource="classpath*:/META-INF/spring/pod-api-client.xml"/>


    <!--license-->
    <import resource="classpath:spring/license-client.xml"/>
    <bean id="licenseClient" class="com.facishare.paas.license.factory.LicenseFactoryBean"/>
    <bean id="licenseFactoryService" class="com.facishare.paas.license.factory.LicenseFactoryService"/>

    <!-- 启用@AspectJ注解 -->
    <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"/>
    <aop:config proxy-target-class="true">
        <aop:aspect ref="serviceProfiler">
            <aop:around method="profile" pointcut="execution(* com.fxiaoke.paas.console.service.*.*(..))"/>
        </aop:aspect>
    </aop:config>

</beans>
