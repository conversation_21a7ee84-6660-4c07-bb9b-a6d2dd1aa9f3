package com.fxiaoke.paas.console.web

import com.alibaba.fastjson.JSON
import com.fxiaoke.paas.console.service.BizconfService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 提供给深研查询mt_bizconf表的接口
 * <AUTHOR> @date 2019-04-11
 */
@RestController
@RequestMapping(value = "/bizconf/query")
@Slf4j
class BizconfController {

  @Autowired
  private BizconfService bizconfService

  @PostMapping(value = "")
  def queryBizconf(@RequestBody String whereSql) {
    try {
      def jsonObject = JSON.parseObject(whereSql)
      bizconfService.queryBizconf(jsonObject.get("whereSql") as String)
    } catch (Exception e) {
      log.error("queryBizconf error,sql={} ", whereSql, e)
      return e.getMessage()
    }
  }
}
