<#assign headContent>
    <link href="//static.foneshare.cn/oss/AdminLTE/plugins/datatables/dataTables.bootstrap.css" rel="stylesheet"
          type="text/css"/>
    <link type="text/css" href="//static.foneshare.cn/oss/datatables-checkboxes/dataTables.checkboxes.css"
          rel="stylesheet"/>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
    <style>
        #datatable td:nth-child(6) {
            text-align: right;
        }

        #datatable td:nth-child(7) {
            text-align: right;
        }
    </style>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>查看版本对象</h1>
        <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i>主页</a></li>
        </ol>
    </section>
</#assign>

<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div id="warningInfo" class="alert alert-warning hide">
                    <span id="closeInfo" href="#" class="close">&times;</span>
                    <strong id="hitInfo"></strong>
                </div>
                <div class="box box-info" style="margin-bottom: 1px;">
                    <div class="box-body" style="padding: 20px;">
                        <form class="form-inline">
                            <div class="form-group">
                                <label for="tenantId" class="control-label">版本id</label>
                                <input type="text" style="border-radius:10px;" class="form-control" id="productId" name="productId" value="${productId}" placeholder="版本">
                            </div>
                            <div class="form-group">
                                <label for="appId" class="col-sm-1 control-label" style="margin-top: 10px;margin-right: 10%">crmKey</label>
                                <div class="col-sm-8">
                                    <select id="crmKey" name="crmKey" class="selectpicker show-tick" title="Nothing selected"
                                            data-live-search="false">
                                        <#list crmKeys! as crmKey>
                                            <option value="${crmKey}"
                                                    <#if ((crmKey) == "crm_manage_custom_object")>selected="selected"</#if>>${crmKey}</option>
                                        </#list>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="appId" class="col-sm-1 control-label" style="margin-top: 10px;margin-right: 20%">moduleType</label>
                                <div class="col-sm-8">
                                    <select id="moduleType" name="moduleType" class="selectpicker show-tick" title="Nothing selected"
                                            data-live-search="false">
                                        <#list moduleTypes! as moduleType>
                                            <option value="${moduleType}"
                                                    <#if ((moduleType) == "0")>selected="selected"</#if>>${moduleType}</option>
                                        </#list>
                                    </select>
                                </div>
                            </div>
                            <button type="button" id="findSub" class="btn btn-primary">查询</button>
                            <button type="button" id="addObject" onclick="addObj()" class="btn btn-primary">添加对象</button>
                            <input id="cancel_btn" class="btn" type="button" style="float: right" value="返回" onclick="history.back()"/>
                        </form>
                    </div>
                    <div class="box-footer clearfix" style="padding: 2px;">
                        <div class="clearfix"></div>
                    </div>
                </div>
                <div class="modal fade" id="myModalAdd" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                                <h4 class="modal-title" id="myModalLabel">添加对象</h4>
                            </div>
                            <div class="modal-body">
                                版本 :
                                <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="versionAdd" name="versionAdd" value="${productVersion}" placeholder="必填" required/>
                            </div>
                            <div class="modal-body">
                                对象名 :
                                <input type="text" style="border-radius:5px;width:400px" class="form-control" id="apiNameAdd" name="apiNameAdd" placeholder="必填" required/>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                                <button type="button" class="btn btn-primary" id="addObject" onclick="addObject('0')">提交</button>
                            </div>
                        </div><!-- /.modal-content &ndash;&gt;-->
                    </div>
                </div>
                <div class="modal fade" id="myModalDel" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                                <h4 class="modal-title" id="myModalLabel">删除对象</h4>
                            </div>
                            <div class="modal-body">
                                版本 :
                                <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="versionDel" name="versionDel" placeholder="必填" required/>
                                请输入相同版本,确认执行
                                <input type="text" style="border-radius:5px;width:400px" class="form-control" id="versionC" name="versionC" placeholder="必填" required/>
                            </div>
                            <div class="modal-body">
                                对象名 :
                                <input type="text" style="border-radius:5px;width:400px" readonly class="form-control" id="apiNameDel" name="apiNameDel" placeholder="必填" required/>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                                <button type="button" class="btn btn-primary" id="deleteObj" onclick="deleteObj('0')">提交</button>
                            </div>
                        </div><!-- /.modal-content &ndash;&gt;-->
                    </div>
                </div>
                <div class="box box-info">
                    <div class="box-body">
                        <table id="datatable" class="table table-hover table-bordered" cellspacing="0" width="100%">
                            <thead>
                            <tr>
                                <th>productVersion</th>
                                <th>describeApiName</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="box-footer clearfix">
                    </div>
                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script type="application/javascript">
        function addObj() {
            $('#myModalAdd').modal()
        }

        function addObject(type) {
            var version = $('#versionAdd').val()
            var versionC = $('#versionC').val()
            var apiName = $('#apiNameAdd').val()
            var dataObject = {
                productVersions: version,
                versionC: versionC,
                apiNames: apiName,
                check: "no",
                type: type
            }
            $.ajax({
                url: '${ctx}/license-admin/add-CRM-object', // 请求的URL地址
                contentType: "application/json", //必须有
                data: JSON.stringify(dataObject),// 这次请求要携带的数据
                dataType: 'text',
                type: 'POST', //请求的方式
                traditional: true,
                success: function (data) {//请求成功之后的回调函数
                    if (data == "success") {
                        alert("添加成功")
                        $('#findSub').click();
                        $('#myModalAdd').modal('hide');
                        //window.location.reload()
                    } else {
                        alert("添加失败")
                    }
                }
            })
        }

        function deleteObj(type) {
            var version = $('#versionDel').val()
            var versionC = $('#versionC').val()
            var apiName = $('#apiNameDel').val()
            var dataObject = {
                type: type,
                productVersions: version,
                versionC: versionC,
                apiNames: apiName,
                check: "yes"
            }

            $.ajax({
                url: '${ctx}/license-admin/delete-CRM-object', // 请求的URL地址
                contentType: "application/json", //必须有
                data: JSON.stringify(dataObject),// 这次请求要携带的数据
                dataType: 'text',
                type: 'POST', //请求的方式
                traditional: true,
                success: function (data) {//请求成功之后的回调函数
                    if (data == "success") {
                        alert("删除成功")
                        $("#findSub").click();
                        $('#myModalDel').modal('hide');
                        //window.location.reload()
                    } else {
                        alert("删除失败")
                    }
                }
            })
        }

        $(document).ready(function () {
            var productId = $('#productId').val();
            var crmKey = $('#crmKey').val();
            var moduleType = $('#moduleType').val();
            var dataObject = {
                productId: productId,
                crmKey: crmKey,
                moduleType: moduleType
            };
            var table = $("#datatable").DataTable({
//            "deferRender": true,
                "processing": true,
                "ajax": {
                    url: "${ctx}/license/view-objectDetails?dataObject=" + encodeURIComponent(JSON.stringify(dataObject)),
                    type: 'GET'
                },
                dom: 'Bfrtip',
                buttons: [
                    'copy',
                    'excel'
                ],
                "columnDefs": [{
                    // 定义操作列,######以下是重点########
                    "targets": 2,//操作按钮目标列
                    "render": function (data, type, row, meta) {
                        var productVersion = row.toString().split(',')[0];
                        var apiName = row.toString().split(',')[1];
                        var html = "<button onclick='deleteObject(\"" + productVersion + "\",\"" + apiName + "\")' class='btn btn-primary' > 删除对象</button>"
                        return html;
                    }
                }],
                "language": {
                    "url": "${ctx}/static/js/datatables-zh_CN.json"
                },
                "mark": true,
                "paging": false
            });

//        提交
            $('#findSub').on('click', function () {
                $('#warningInfo').addClass('hide');
                $("#datatable").dataTable().fnClearTable();
                var productId = $('#productId').val();
                var crmKey = $('#crmKey').val();
                var moduleType = $('#moduleType').val();
                var dataObject = {
                    productId: productId,
                    crmKey: crmKey,
                    moduleType: moduleType
                };
//            将对象转化为json字符串放在url中传递到后端
                table.ajax.url("${ctx}/license/view-objectDetails?dataObject=" + encodeURIComponent(JSON.stringify(dataObject))).load();
            });


        });
        /**
         * 信息提示栏关闭
         */
        $('#closeInfo').on('click', function () {
            $('#warningInfo').addClass('hide');
        });

        function deleteObject(productVersion, apiName) {
            $('#versionDel').val(productVersion)
            $('#apiNameDel').val(apiName);
            $('#myModalDel').modal({})
        }
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
