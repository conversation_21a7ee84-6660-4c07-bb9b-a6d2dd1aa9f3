<#assign headContent>
    <link href="//static.foneshare.cn/oss/AdminLTE/plugins/datatables/dataTables.bootstrap.css" rel="stylesheet"
          type="text/css"/>
    <link type="text/css" href="//static.foneshare.cn/oss/datatables-checkboxes/dataTables.checkboxes.css"
          rel="stylesheet"/>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
    <style>
        #datatable td:nth-child(6) {
            text-align: right;
        }

        #datatable td:nth-child(7) {
            text-align: right;
        }
    </style>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>应用详细数据</h1>
        <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i>主页</a></li>
        </ol>
    </section>
</#assign>

<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div id="warningInfo" class="alert alert-warning hide">
                    <span id="closeInfo" href="#" class="close">&times;</span>
                    <strong id="hitInfo"></strong>
                </div>
                <div class="box box-info">
                    <div class="box-body">
                        <table id="datatable" class="table table-hover table-bordered" cellspacing="0" width="100%">
                            <thead>
                            <tr>
                                <th>模块编码</th>
                                <th>模块名称</th>
                                <th>配额编码</th>
                                <th>配额名称</th>
                                <th>配额值</th>
                                <#--<th><button id="viewDetails" type="button" class="btn btn-primary">查看详情</button></th>-->
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="box-footer clearfix">
                    </div>
                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script type="application/javascript">
        $(document).ready(function () {
            var table = $("#datatable").DataTable({
//            "deferRender": true,
                "processing": true,
                "ajax": {
                    url: "${ctx}/license/all-app-license?tenantId=default",
                    type: 'GET'
                },
                dom: 'Bfrtip',
                buttons: [
                    'copy', 'excel'
                ],
                /*"pageSize: 10,                       //每页的记录行数（*）
                aLengthMenu" [5, 10, 25, 50, 100],*/
                //"aLengthMenu": [[5, 10, 15, 25, 50, 100 , -1], [5, 10, 15, 25, 50, 100, "All"]],//可供选择的每页的行数（*）
                "iDisplayLength": 10,
                /*"columnDefs": [{
                    // 定义操作列,######以下是重点########

                }],*/
                "language": {
                    "url": "${ctx}/static/js/datatables-zh_CN.json"
                },
                "mark": true,
                "paging": true
            });

//        提交
            $('#findSub').on('click', function () {
                $('#warningInfo').addClass('hide');
                $("#datatable").dataTable().fnClearTable();
                var tenantId = $('#tenantId').val();
                if (tenantId === "") {
                    $('#warningInfo').removeClass('hide');
                    $('#hitInfo').html('关键字段不能为空！');
                    return;
                }
//            将对象转化为json字符串放在url中传递到后端
                table.ajax.url("${ctx}/license/master-version-management?tenantId=" + tenantId).load();
            });


        });
        /**
         * 信息提示栏关闭
         */
        $('#closeInfo').on('click', function () {
            $('#warningInfo').addClass('hide');
        });
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
