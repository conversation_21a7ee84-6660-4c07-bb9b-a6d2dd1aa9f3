package com.fxiaoke.paas.console.mapper.metadata

import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * @date 2020/5/7 11:23
 *
 */
interface ApiNameTransferMapper extends ITenant<ApiNameTransferMapper> {

  @Select("SELECT store_table_name from mt_describe where tenant_id=#{tenantId} and describe_api_name=#{apiName} limit 1 ")
  String queryStoreTableNameByTenantIdAndApiName(@Param("tenantId") String tenantId, @Param("apiName") String apiName)


  @Select("SELECT describe_api_name from mt_describe where tenant_id=#{tenantId} and store_table_name=#{storeTableName} limit 1 ")
  String queryApiNameByTenantIdAndStoreTableName(@Param("tenantId") String tenantId, @Param("storeTableName") String storeTableName)

}
