package com.fxiaoke.template;

import com.google.common.base.CaseFormat;
import com.google.common.base.Converter;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.util.Iterator;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@UtilityClass
public class InternUtils {
  private final Converter<String, String> converter = CaseFormat.LOWER_CAMEL.converterTo(CaseFormat.LOWER_UNDERSCORE);
  private final Pattern UPPER_WORDS = Pattern.compile("[A-Z]{3,}");
  private final Pattern customApiName = Pattern.compile("^(\\w*_*)?(\\w{5,6})__c$");
  private final Map<String,String> columnTypeMap = initColumMap();

  public String lower(String s) {
    return s.toLowerCase();
  }

  public String getColumnType(String fieldType){
    return columnTypeMap.getOrDefault(fieldType,"string");
  }

  private Map<String,String> initColumMap(){
    Map<String,String> columTypeMap = Maps.newHashMap();
    columTypeMap.put("string", "string");
    columTypeMap.put("province", "string");
    columTypeMap.put("district", "string");
    columTypeMap.put("country", "string");
    columTypeMap.put("location", "string");
    columTypeMap.put("date_time", "bigint");
    columTypeMap.put("date", "bigint");
    columTypeMap.put("time", "bigint");
    columTypeMap.put("true_or_false", "string");
    columTypeMap.put("dimension", "string");
    columTypeMap.put("url", "string");
    columTypeMap.put("email", "string");
    columTypeMap.put("city", "string");
    columTypeMap.put("phone_number", "string");
    columTypeMap.put("percentile", "float");
    columTypeMap.put("object_reference", "string");
    columTypeMap.put("count", "numeric");
    columTypeMap.put("number", "numeric");
    columTypeMap.put("currency", "numeric");
    columTypeMap.put("multi_level_select_one", "string");
    columTypeMap.put("record_type", "string");
    columTypeMap.put("quote", "string");
    columTypeMap.put("group", "string");
    columTypeMap.put("signature", "string");
    columTypeMap.put("select_many", "string");
    columTypeMap.put("image", "text");
    columTypeMap.put("array", "text");
    columTypeMap.put("tag", "text");
    columTypeMap.put("rich_text", "text");
    columTypeMap.put("long_text", "text");
    columTypeMap.put("tree_path", "text");
    columTypeMap.put("use_range", "text");
    columTypeMap.put("use_scope", "text");
    columTypeMap.put("text", "text");
    columTypeMap.put("what_list_data", "text");
    columTypeMap.put("embedded_object", "text");
    columTypeMap.put("file_attachment", "text");
    columTypeMap.put("embedded_object_list", "text");
    columTypeMap.put("master_detail", "string");
    columTypeMap.put("auto_number", "string");
    columTypeMap.put("select_one", "string");
    columTypeMap.put("geo_point", "string");
    columTypeMap.put("lock_rule", "string");
    columTypeMap.put("employee", "string");
    columTypeMap.put("department", "string");
    columTypeMap.put("employee_list", "text");
    columTypeMap.put("department_list", "text");
    columTypeMap.put("formula", "string");
    return columTypeMap;
  }


  public String camelConvert(String s) {
    if (StringUtils.startsWithIgnoreCase(s, "object_") || StringUtils.startsWithIgnoreCase(s, "field_")) {
      return lower(s);
    }
    return converter.convert(formatAbbrevWord(s).trim());
  }

  public String formatAbbrevWord(String s) {
    return format(UPPER_WORDS, s, k -> {
      char[] chars = k.toCharArray();
      for (int i = 1; i < chars.length - 1; i++) {
        chars[i] = (char) (chars[i] + 32);
      }
      return new String(chars);
    });
  }

  public String toColumnName(String s) {
    if ("_id".equals(s)) {
      return "id";
    } else if ("ei".equals(s)) {
      return "tenant_id";
    } else {
      Matcher matcher = customApiName.matcher(s);
      if (matcher.matches()) {
        return lower(camelConvert(matcher.group(1))) + lower(matcher.group(2)) + "__c";
      }
      return lower(camelConvert(s));
    }
  }

  public String format(Pattern pattern, String stack, Function<String, String> replacer) {
    Matcher matcher = pattern.matcher(stack);
    if (matcher.find()) {
      StringBuilder sbd = new StringBuilder(stack.length() / 2);
      int start = 0;
      do {
        if (matcher.start() > start) {
          sbd.append(stack, start, matcher.start());
        }
        sbd.append(replacer.apply(stack.substring(matcher.start(), matcher.end())));
        start = matcher.end();
      } while (matcher.find());
      if (start < stack.length()) {
        sbd.append(stack, start, stack.length());
      }
      return sbd.toString();
    }
    return stack;
  }

  public String toTableName(String s) {
    return lower(camelConvert(s));
  }
}
