package com.fxiaoke.paas.console.web.metadata

import com.alibaba.fastjson.JSON
import com.facishare.paas.tranfer.client.TableService
import com.facishare.paas.tranfer.entity.TrMtRelationEntity
import com.fxiaoke.paas.console.mapper.metadata.SqlQueryMapper
import com.fxiaoke.paas.console.service.ReadThreadLocal
import com.fxiaoke.paas.console.util.DateFormatUtil
import com.google.common.collect.Lists
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody


/**
 * <AUTHOR>
 * @date 2022/9/27 19:14
 */
@Controller
@Slf4j
@RequestMapping("/metadata/relation")
class QueryRelationController {

  @Autowired
  SqlQueryMapper sqlQueryMapper

  @Autowired
  TableService tableService;

  @RequestMapping("/query")
  def page(){
    "metadata/queryRelation"
  }

  @RequestMapping("/query-result")
  @ResponseBody
  def query(String tenantId, String sql){
    if (sql.isEmpty() || tenantId.isEmpty()) {
      return ["code": 400, "error": "请输入Sql和租户ID"]
    }
    if (!DateFormatUtil.checkSql(sql)) {
      log.info("checkSql is false :{}", sql)
      return ["code": 403, "error": "Sql不符合规范，只能进行查询！"]
    }
    try {
      if (!sql.toLowerCase().contains("limit")) {
        if (sql.endsWith(";")) {
          sql = sql.substring(0, sql.length() - 1)
        }
        sql = sql.concat(" limit 100")
      }
      List<Map<String, Object>> resultList = tableService.selectBySQL(sql, tenantId)
      return ["code": 200, "info": JSON.toJSONString(resultList)]
    } catch (Exception e) {
      log.warn("Sql查询异常，sql={},error:", sql, e)
      return ["code": 500, "error": "查询异常", "errorInfo": e.getMessage()]
    }
  }
}
