package com.fxiaoke.template.db;

import com.fxiaoke.template.exception.DbxException;
import com.fxiaoke.template.schema.TableSchema;
import com.github.mybatis.local.TenantThreadLocal;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.Properties;
import java.util.Set;

/**
 *
 * <AUTHOR>
 * @date 2017/8/9
 */
@Slf4j
public class DBUtils {

    public final static int DEFAULT_FETCH_SIZE = 1000;
    public static final int SOCKET_TIMEOUT_IN_SECOND = 172800;

    public static Connection createConnect(DataBaseType dataBaseType, String jdbcUrl, String user, String password) {
        Properties prop = new Properties();
        prop.put("user", user);
        if (password != null) {
            prop.put("password", password);
        }
        try {
            Class.forName(dataBaseType.getDriverClassName());
            DriverManager.setLoginTimeout(15);
            return DriverManager.getConnection(jdbcUrl, prop);
        } catch (Exception e) {
            log.error("connect failed.", e);
            throw new DbxException("-1", "db connect failed");
        }
    }

    /**
     * 获取一个表的Schema
     *
     * @param connection
     * @param tableName
     * @return
     * @throws SQLException
     */
    public static TableSchema loadTableSchema(Connection connection, String tableName) throws SQLException {
        Set<String> primaryKeySet = getPrimaryKey(connection, tableName);
        TenantThreadLocal.set("1");
        @Cleanup Statement stmt = connection.createStatement();
        @Cleanup ResultSet rs = stmt.executeQuery(String.format("SELECT * FROM %s WHERE 1=2", tableName));
        ResultSetMetaData metaData = rs.getMetaData();

        int columnCount = metaData.getColumnCount();
        List<TableSchema.ColumnSchema> columnSchemaList = Lists.newArrayList();

        for (int i = 1; i <= columnCount; i++) {
            String columnName = metaData.getColumnName(i);
            int type = metaData.getColumnType(i);
            String typeName = metaData.getColumnTypeName(i);
            boolean primary = primaryKeySet.contains(columnName);
            columnSchemaList.add(TableSchema.ColumnSchema.builder()
                                                         .index(i)
                                                         .name(columnName)
                                                         .primary(primary)
                                                         .type(type)
                                                         .typeName(typeName)
                                                         .build());
        }
        return new TableSchema(tableName, columnSchemaList);
    }

    /**
     * 获取一个表哪些是主键
     *
     * @param connection
     * @param tableName
     * @return
     * @throws SQLException
     */
    public static Set<String> getPrimaryKey(Connection connection, String tableName) throws SQLException {
        Set<String> primaryKey = Sets.newHashSet();
        TenantThreadLocal.set("1");
        DatabaseMetaData metaData = connection.getMetaData();
        @Cleanup ResultSet resultSet = metaData.getPrimaryKeys(null, null, tableName);
        while (resultSet.next()) {
            primaryKey.add(resultSet.getString(4));
        }
        return primaryKey;
    }

    public static ResultSet query(Connection connection, String sql) throws SQLException {
        return query(connection, sql, DEFAULT_FETCH_SIZE);
    }

    public static ResultSet query(Connection connection, String sql, int fetchSize) throws SQLException {
        connection.setAutoCommit(false);
        Statement stmt = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY,
                                                    ResultSet.CONCUR_READ_ONLY);
        stmt.setFetchSize(fetchSize);
        stmt.setQueryTimeout(SOCKET_TIMEOUT_IN_SECOND);
        return query(stmt, sql);
    }

    public static ResultSet query(Statement stmt, String sql) throws SQLException {
        return stmt.executeQuery(sql);
    }
}
