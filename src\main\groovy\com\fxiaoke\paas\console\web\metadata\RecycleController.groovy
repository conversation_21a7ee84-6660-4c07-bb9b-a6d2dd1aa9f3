package com.fxiaoke.paas.console.web.metadata

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.common.PasswordUtil
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.github.autoconf.ConfigFactory
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.http.MediaType
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.PostConstruct
import java.sql.ResultSetMetaData

/**
 * <AUTHOR>
 * @date 2019/8/5 3:09 PM
 */
@Controller
@Slf4j
@RequestMapping("/metadata/recycle/sql/")
class RecycleController {


  private String masterUrl
  private String userName
  private String password

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("pg-recycle-metadata", { iconfig ->
      this.masterUrl = iconfig.get("masterUrl")
      this.userName = iconfig.get("username")
      this.password = iconfig.get("password")
      this.password = PasswordUtil.decode(password)
    })
  }

  @RequestMapping("/page")
  String index() {
    return "metadata/recycle-query"
  }

  @PostMapping(path = "/query", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  @SystemControllerLog(description = "元数据资源回收 -- 查询资源回收库")
  JSONObject query(@RequestParam String sql) {
    JSONObject result = new JSONObject()
    if (isNeedFilter(sql)) {
      result.put("code", 200)
      result.put("info", "{\"msg\":\"SQL is illegal!\"}")
      return result
    }
    if (StringUtils.isBlank(masterUrl) || StringUtils.isBlank(password) || StringUtils.isBlank(userName)) {
      result.put("code", 200)
      result.put("info", "{\"msg\":\"DataBase connection error!\"}")
      return result
    }

    if(sql.endsWith(";")){
      sql=sql.substring(0,sql.length()-1)
    }

    if(!(sql.contains("limit")||sql.contains("LIMIT"))){
      sql+=" LIMIT 1000"
    }
    JdbcConnection jdbcConnection = new JdbcConnection(masterUrl, userName, password)
    Set<Map<String, Object>> queryResult = Sets.newHashSet()
    jdbcConnection.query(sql, { rs ->
      ResultSetMetaData metaData = rs.getMetaData()
      int columnCount = metaData.columnCount
      while (rs.next()) {
        Map<String, Object> map = Maps.newHashMap()
        for (int i = 1; i <= columnCount; i++) {
          map.put(metaData.getColumnName(i), rs.getObject(i))
        }
        queryResult.add(map)
      }

    })
    result.put("code", 200)
    result.put("info", queryResult)
    return result


  }


  boolean isNeedFilter(String sql) {
    sql = sql.trim()
    String head = sql.substring(0, sql.indexOf(" ")).trim()
    return !head.equalsIgnoreCase("select") && !head.equalsIgnoreCase("explain")
  }

}
