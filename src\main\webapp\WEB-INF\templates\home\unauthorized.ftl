<#assign headContent>
<style>
 .box-header .box-title {
    display: inline-block;
    font-size: 30px;
    margin: 0;
    line-height: 1;
  }
</style>
</#assign>
<#assign breadcrumbContent>
</#assign>
<#assign bodyContent>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-header with-border">
                    <h1 class="box-title">抱歉，您没有操作权限！</h1>
                </div>
                <div style="width: 100%; margin: 20px; display: flex; justify-content: space-between;">
                    <!-- 第一个panel -->
                    <div class="panel panel-default" style="flex: 1; margin-right: 10px;">
                        <div class="panel-heading">权限字典
                            <small style="padding-left: 10px;color: #555;">(如果需要模块权限，请联系自己的leader授权)</small>
                        </div>
                        <div class="panel-body" style="padding: 0;">
                            <table class="table">
                                <tr>
                                    <th>模块名</th>
                                    <th>权限名</th>
                                </tr>
                                <tr>
                                    <td>元数据</td>
                                    <td>paas-metadata</td>
                                </tr>
                                <tr>
                                    <td><span style="padding-left: 20px;">--</span>自定义对象恢复数据</td>
                                    <td>paas-metadata-manipulate-data</td>
                                </tr>
                                <tr>
                                    <td><span style="padding-left: 20px;">--</span>修复index_name</td>
                                    <td>paas-metadata-manipulate-data</td>
                                </tr>
                                <tr>
                                    <td><span style="padding-left: 20px;">--</span>描述字段扩容</td>
                                    <td>paas-metadata-manipulate-data</td>
                                </tr>
                                <tr>
                                    <td><span style="padding-left: 20px;">--</span>变字段(可/不可)重复</td>
                                    <td>paas-metadata-manipulate-data</td>
                                </tr>
                                <tr>
                                    <td>数据传输</td>
                                    <td>dts-admin</td>
                                </tr>
                                <tr>
                                    <td>流程</td>
                                    <td>paas-workflow</td>
                                </tr>
                                <tr>
                                    <td>多语言</td>
                                    <td>paas-dataauth-write</td>
                                </tr>
                                <tr>
                                    <td>数据权限</td>
                                    <td>paas-datarights</td>
                                </tr>
                                <tr>
                                    <td><span style="padding-left: 20px;">--</span>缓存初始化</td>
                                    <td>paas-dataauth-write</td>
                                </tr>
                                <tr>
                                    <td>沙箱拷贝</td>
                                    <td>paas-sandBox</td>
                                </tr>
                                <tr>
                                    <td>功能权限</td>
                                    <td>paas-license-auth</td>
                                </tr>
                                <tr>
                                    <td><span style="padding-left: 20px;">--</span>预制系统库数据</td>
                                    <td>-</td>
                                </tr>
                                <tr>
                                    <td>全局搜索</td>
                                    <td>paas-hubble</td>
                                </tr>
                                <tr>
                                    <td>分版查询</td>
                                    <td>paas-license-auth</td>
                                </tr>
                                <tr>
                                    <td><span style="padding-left: 20px;">--</span>调整配额</td>
                                    <td>paas-license-auth-write</td>
                                </tr>
                                <tr>
                                    <td><span style="padding-left: 20px;">--</span>发送创建MQ</td>
                                    <td>paas-license-auth-write</td>
                                </tr>
                                <tr>
                                    <td><span style="padding-left: 20px;">--</span>添加灰度产品</td>
                                    <td>paas-license-auth-write</td>
                                </tr>
                                <tr>
                                    <td><span style="padding-left: 20px;">--</span>添加对象</td>
                                    <td>paas-license-auth-admin</td>
                                </tr>
                                <tr>
                                    <td><span style="padding-left: 20px;">--</span>处理特殊审批</td>
                                    <td>paas-license-auth-admin</td>
                                </tr>
                                <tr>
                                    <td>组织架构</td>
                                    <td>paas-organization</td>
                                </tr>
                                <tr>
                                    <td>审计日志</td>
                                    <td>-</td>
                                </tr>
                                <tr>
                                    <td>HAMSTER</td>
                                    <td>hamster-admin</td>
                                </tr>
                                <tr>
                                    <td>基础平台</td>
                                    <td>base-platform</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- 新增的第二个div -->
                    <div class="panel panel-default" style="flex: 1;">
                        <div class="panel-heading">权限获取</div>
                        <div class="panel-body">
                            <p>如需获取paas-console相应权限，请提协同审批</p>
                            <div class="panel-heading" style="background-color: #f5f5f5; font-weight: bold;">
                                协同审批模板：
                                <div class="panel-body" style="padding: 15px;">
                                    <p><strong>#paas-console权限申请</strong></p>
                                    <p><strong>申请人手机号：</strong></p>
                                    <p><strong>环境：</strong></p>
                                    <p><strong>&nbsp;&nbsp;&nbsp;foneshare：</strong> <a href="https://oss.foneshare.cn/paas-console/" target="_blank">https://oss.foneshare.cn/paas-console/</a></p>
                                    <p><strong>&nbsp;&nbsp;&nbsp;firstshare：</strong> <a href="https://oss.firstshare.cn/paas-console/" target="_blank">https://oss.firstshare.cn/paas-console/</a></p>
                                    <p><strong>权限名：</strong></p>
                                    <p><strong>审批人：</strong>直属leader->李磊Leo</p>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <p class="bg-warning" style="padding: 10px;font-size: 12px;">
                    注意事项：系统除了基于以上权限字典规则做鉴权外，还对部分请求还做了二次鉴权。比如以下请求必须同时拥有 paas-console-superadmin 角色才能允许访问<br/>
                    /paas-console/license-write/update-para<br/>
                    /paas-console/org/treeDepartment<br/>
                    /paas-console/audit/list<br/>
                    /paas-console/license/clear-catch<br/>
                </p>
                <pre style="border-width: 0;background-color: rgba(255,255,255,0);">
                 .--,       .--,
                ( (  \.---./  ) )
                 '.__/o   o\__.'
                    {=  ^  =}
                     >  -  <
                    /       \
                   //       \\
                  //|   .   |\\
                  "'\       /'"_.-~^`'-.
                     \  _  /--'         `
                   ___)( )(___
                  (((__) (__)))
            </pre>
            </div>
        </div>
    </div>
</section>
</#assign>

<#assign scriptContent>
</#assign>

<#include "../layout/layout-main.ftl" />
