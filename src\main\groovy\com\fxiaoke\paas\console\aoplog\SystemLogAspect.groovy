package com.fxiaoke.paas.console.aoplog

import com.alibaba.fastjson.JSON
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.entity.log.AuditLog
import com.fxiaoke.paas.console.service.metadata.AuditLogService
import com.github.shiro.support.ShiroCasRealm
import groovy.util.logging.Slf4j
import org.aspectj.lang.JoinPoint
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.annotation.Pointcut
import org.aspectj.lang.reflect.MethodSignature
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component

import javax.annotation.Resource

/**
 * <AUTHOR>
 * Created on 2018/4/2.
 */
@Slf4j
@Aspect
@Component
class SystemLogAspect {

  @Autowired
  AuditLogService auditLogService

  @Resource(name = "casRealm")
  private ShiroCasRealm cas

  // Controller层切点 （注解路径）
  @Pointcut(value = "@annotation(com.fxiaoke.paas.console.annotation.SystemControllerLog)")
  void controllerAspect() {
  }


  @Around(value = "controllerAspect()")
  Object getLogAfter(ProceedingJoinPoint joinPoint) throws Throwable {
    //注解说明
    String description = getControllerMethodDescription(joinPoint)

    AuditLog auditLog = AuditLog.builder()
            .executor(cas.getCurrentUser().getDisplayName())
            .description(description)
            .executeTime(new Date())
            .argument(JSON.toJSONString(joinPoint.args))
            .build()
    def proceed
    try {
      proceed = joinPoint.proceed()
      //todo 获取方法执行状态
    } catch (Exception e) {
      log.error("This method is fail :{}", description)
//      throw e
    }
    auditLogService.insertAuditLog(auditLog)
    return proceed
  }

  /**
   * 可以得到方法return的值
   * @param ret
   * @throws Throwable
   */
//  @AfterReturning(returning = "ret", pointcut = "@annotation(com.fxiaoke.paas.console.annotation.SystemControllerLog)")
  static void doAfterReturning(JoinPoint joinPoint, Object ret) throws Throwable {
    // 处理完请求，返回内容
    System.out.println(ret)
    Map<String, Object> map = (Map<String, Object>) ret
    map.get("total")
  }

  /**
   * 通过反射获取参入的参数
   * @param joinPoint
   * @return
   * @throws Exception
   */
  static String getControllerMethodDescription(JoinPoint joinPoint) throws Exception {
    return ((MethodSignature) joinPoint.signature).method.getAnnotation(SystemControllerLog.class).description()
  }

}
