package com.fxiaoke.paas.console.util.metadata

import com.alibaba.druid.pool.DruidDataSource
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j

import javax.sql.DataSource
import java.sql.Connection
import java.sql.SQLException

/**
 * <AUTHOR>
 * Created on 2018/5/31.
 */
@Slf4j
class PgCreateConnection {

  static final Map<String, DataSource> dataSourceMap = Maps.newConcurrentMap()

  static Connection getConnection(String jdbcUrl, String username, String password) throws SQLException {
    DataSource dataSource = dataSourceMap.computeIfAbsent(jdbcUrl, { ds ->
      createDruidPool(jdbcUrl, username, password)
    })
    return dataSource.getConnection()
  }

  static DataSource createDruidPool(String jdbcUrl, String username, String password) {
    log.info("create datasource" + jdbcUrl)

    DruidDataSource ds = new DruidDataSource()
    ds.setUrl(jdbcUrl)
    ds.setUsername(username)
    ds.setPassword(password)
    try {
      ds.setFilters("slf4j")
    } catch (Exception e) {
      log.error("cannot add slf4j filter with{}", jdbcUrl, e)
    }

    // 设置连接池的各种属性
    ds.setMaxActive(20)
    // 设置成0,不会立即连接
    ds.setInitialSize(0)
    ds.setMinIdle(0)
    ds.setMaxWait(30000)
    ds.setTimeBetweenEvictionRunsMillis(40000)
    ds.setMinEvictableIdleTimeMillis(40000)
    ds.setValidationQuery("SELECT 'x'")
    ds.setTestWhileIdle(false)
    ds.setTestOnReturn(false)
    ds.setTestOnBorrow(false)
    ds.setRemoveAbandoned(true)
    ds.setRemoveAbandonedTimeout(40)
    ds.setPoolPreparedStatements(false)
    ds.setLogAbandoned(true)
    ds.setMaxPoolPreparedStatementPerConnectionSize(10)

    try {
      ds.init()
    } catch (Exception e) {
      log.error("init datasource failed {}, ", jdbcUrl, e)
    }
    return ds
  }
}
