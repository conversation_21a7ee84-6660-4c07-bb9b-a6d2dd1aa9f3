package com.fxiaoke.paas.console.util

/**
 * <AUTHOR>
 * @date 2019/7/2 下午3:51
 */
final class ApiNameToStoreTableNameUtil {
  /**
   * 老预置对象转新预置对象
   * @param old 老预置对象表名
   * @return 新预置对象表名
   */
  static final String toNewStoreTableName(String old) {
    switch (old) {
//      case "visit"
//        return "visit"
      case "new_opportunity":
        return "new_opportunity"
      case "partner":
        return "partner"
      case "org_employee_user":
        return "org_employee_user"
      case "checkins_data":
        return "checkins_data"
      case "cases":
        return "cases"
      case "stand_prod_unit":
        return "stand_prod_unit"
      case "specification":
        return "specification"
      case "payment_plan":
        return "payment_plan"
      case "payment_customer":
        return "payment_customer"
      case "bpm_task":
        return "bpm_task"
      case "quote":
        return "quote"
      case "price_book_product":
        return "price_book_product"
      case "price_book":
        return "price_book"
      case "trade_payment":
        return "payment_customer"
      case "customer_account":
        return "customer_account"
      case "trade_product":
        return "biz_sales_order_product"
      case "customer_trade":
        return "biz_sales_order"
      case "return_order_product":
        return "biz_returned_goods_invoice_product"
      case "return_order":
        return "biz_returned_goods_invoice"
      case "trade_refund":
        return "biz_refund"
      case "product":
        return "biz_product"
      case "opportunity":
        return "biz_opportunity"
      case "marketing_event":
        return "biz_marketing_event"
      case "sales_clue_pool":
        return "biz_leads_pool"
      case "sales_clue":
        return "biz_leads"
      case "trade_bill":
        return "biz_invoice_application"
      case "high_seas":
        return "biz_highseas"
      case "contract":
        return "biz_contract"
      case "contact":
        return "biz_contact"
      case "customer":
        return "biz_account"
      default:
        return "mt_data"

    }

  }


}
