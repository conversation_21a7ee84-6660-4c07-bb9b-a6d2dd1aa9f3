package com.fxiaoke.paas.console.service.hamster;

import com.fxiaoke.paas.console.log.HamsterConsoleAuditLog;
import com.fxiaoke.paas.console.mapper.hamster.HamsterConsoleLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class HamsterConsoleLogService {

  @Resource
  private HamsterConsoleLogMapper hamsterConsoleLogMapper;

  @Async
  public void insertHamsterConsoleLog(HamsterConsoleAuditLog auditLog) {
    Integer insert = hamsterConsoleLogMapper.addAuditLog(auditLog);
    if (insert <= 0) {
      log.info("插入hamster日志失败,auditLog：{}", auditLog);
    }
  }

  public List<HamsterConsoleAuditLog> queryHamsterConsoleLog(Integer num) {
    return hamsterConsoleLogMapper.findAuditLogByNum(num);
  }
}
