package com.fxiaoke.paas.console.web.metadata

import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.service.metadata.PurgeCacheService
import com.fxiaoke.paas.console.util.metadata.PurgeCacheUtil
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

@Controller
@Slf4j
@RequestMapping("/metadata/purge")
class PurgeCacheController {
    @Autowired PurgeCacheService purgeCacheService

    /**
     * 跳转清缓存页面
     * @return
     */
    @RequestMapping("")
    def loadPage(){
        "metadata/purgeCache"
    }

    @RequestMapping(value = "/purgeCache", method = RequestMethod.POST)
    @ResponseBody
    @SystemControllerLog(description = "清理缓存")
    def purgeCache(@RequestParam String tenantIds){
        if(PurgeCacheUtil.containsBlank(tenantIds)){
            return ["result": "{\"result\":\"params must not empty\"}"]
        }
        purgeCacheService.purgeCacheByTenantIdS(tenantIds)
    }

    @RequestMapping(value = "/purgeCacheByFile", method = RequestMethod.POST)
    @ResponseBody
    @SystemControllerLog(description = "清理缓存")
    def purgeCacheByFile(@RequestParam String filePath){

        if(StringUtils.isEmpty(filePath)){
            log.info("filePath is empty");
            return;
        }
        purgeCacheService.purgeCacheByFile(filePath)
    }

}
