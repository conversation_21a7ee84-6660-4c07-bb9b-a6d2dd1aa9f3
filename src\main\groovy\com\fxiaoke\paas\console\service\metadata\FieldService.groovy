package com.fxiaoke.paas.console.service.metadata

import com.alibaba.fastjson.JSON
import com.fxiaoke.common.Pair
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.metadata.option.api.OptionApiClient
import com.fxiaoke.paas.console.mapper.metadata.FieldMapper
import com.github.autoconf.ConfigFactory
import com.google.common.base.CharMatcher
import com.google.common.base.Splitter
import com.google.common.collect.Sets
import groovy.util.logging.Slf4j
import lombok.NonNull
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import javax.annotation.Resource

/**
 * <AUTHOR>
 * @date 2018/3/8
 */
@Service
@Slf4j
class FieldService {
  @Resource
  private FieldMapper fieldMapper
  @Resource
  private OptionApiClient optionApiClient
  @Resource
  private JdbcService jdbcService

  private List<String> optionFieldType

  @PostConstruct
  void init() {
    ConfigFactory.getConfig "paas-console-base", { config ->
      optionFieldType = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(config.get("option_field_type"))
    }
  }

  /**
   * 获取field列表，其中有optionId的则一起查出option
   * 根据field的type来判断该字段是否为option字段
   * @param tenantId
   * @param describeId
   * @return
   */
  List<Map<String, Object>> fieldListByDescribeId(@NonNull String tenantId, @NonNull String describeId) {

    Pair<JdbcConnection, String> pair = jdbcService.conn(tenantId, "SELECT * FROM mt_field WHERE describe_id = '" + describeId + "' and tenant_id = '"+ tenantId +"' ORDER BY CASE WHEN status = 'deleted' THEN 1 ELSE 0 END, field_num")
    List<Map<String, Object>> fieldList = fieldMapper.setTenantId(tenantId).findFieldByDescribeId2(pair.second)
    Set<String> optionIds = Sets.newHashSet()
    fieldList.forEach { field ->
      if (optionFieldType.contains(field["type"])) {
        optionIds.add(field["option_id"] as String)
      }
    }
    Map<String, String> options = optionApiClient.findByMd5Set(optionIds)
    fieldList.forEach { field ->
      if (optionFieldType.contains(field["type"])) {
        field.put("select_option", JSON.parse(options[field["option_id"] as String]))
      }
    }
    fieldList
  }
}
