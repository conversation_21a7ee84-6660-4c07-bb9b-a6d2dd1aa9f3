<#assign title="流程定义">
<#assign active_nav="workflow">
<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
<style>
  .doSearch {
    width: 8%;
    margin-left: 20px
  }

  #dataTable2 th {
    vertical-align: middle;
    align-items: center;
  }

  #dataTable2 td {
    vertical-align: middle;
  }

  .table > thead:first-child > tr:first-child > th {
    text-align: center;
    vertical-align: middle;
  }

  .table > tbody > tr > td {
    text-align: center;
  }

  input {
    width: 10%;
    height: 34px;
    line-height: 34px;
    box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid #c8cccf;
    color: #6a6f77;
    -web-kit-appearance: none;
    -moz-appearance: none;
    outline: 0;
    text-decoration: none;
  }

  .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
    width: 10%;
  }
</style>
</#assign>
<#assign bodyContent>
    <#assign breadcrumbContent>
    <section class="content-header">
      <h1>流程查询</h1>
      <ol class="breadcrumb">
        <li><a href="#"><i class="fa fa-dashboard"></i>查询</a></li>
      </ol>
    </section>
    </#assign>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <div class="box-header with-border">
          <h3 class="box-title"></h3>
        </div>
        <form class="form-horizontal" action="${ctx}/biz/add" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="advanced">
              <span>流程类型</span>
              <select class="selectpicker">
                <option value="approvalflow">审批流</option>
                <option value="workflow">工作流</option>
                <option value="workflow_bpm">BPM</option>
                <option value="all" selected>全部</option>
              </select>
              <span style="margin-left: 20px">启用状态</span>
              <select class="selectpicker">
                <option value="started">已启用</option>
                <option value="stopped">已停用</option>
                <option value="all" selected>全部</option>
              </select>
              <span style="margin-left: 20px">业务名称</span>
              <select id="appId" class="selectpicker">
                <option value="CRM">CRM</option>
                <option value="BPM">BPM</option>
                <option value="facishare-xt">协同</option>
                <option value="all" selected>全部</option>
              </select>
              <input placeholder="租户ID" style="margin-left: 20px"/>
              <input placeholder="流程名称或SRC_ID" style="margin-left: 20px"/>
              <button type="button" class="doSearch btn btn-primary">查找</button>
            </div>
            <div style="margin-top: 20px">
              <table class="table table-striped table-bordered table-condensed dataTable no-footer"
                     id="dataTable2">
                <thead>
                <tr>
                  <th>流程SRC_ID</th>
                  <th>租户ID</th>
                  <th>流程名称</th>
                  <th>流程类型</th>
                  <th>业务名称</th>
                  <th>entityId</th>
                  <th>流程描述</th>
                  <th>启用状态</th>
                  <th>最后更新日期</th>
                </tr>
                </thead>
              </table>
            </div>
          </div>
      </div>
      </form>
    </div>
  </div>
  </div>
</section>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script>
  $(document).ready(function () {
    var bootstrapDom = "<'row'<'col-sm-6'l>>" +
            "<'row'<'col-sm-12'tr>>" +
            "<'row'<'col-sm-5'i><'col-sm-7'p>>";
    var table = $("#dataTable2").DataTable({
      "dom": bootstrapDom,
      "processing": false,
      "serverSide": true,
      "search": {
        "regex": true
      },
      "ajax": "${CONTEXT_PATH}/tenant/flows?dataObject=",
      "columnDefs": [],
      columns: [
        {
          data: "sourceWorkflowId", render: function (data, type, row, meta) {
            if (type === 'display') {
              return "<a href=\"${CONTEXT_PATH}/workflow/tenant/flow/versions?sourceId=" + data
                      + "&tenantId=" + row.tenantId + "&flowType=" + row.flowType + "&appId=" + row.appName + "\">" + data + "</a>";
            } else {
              return data;
            }
          }
        },
        {
          data: 'tenantId'
        },
        {
          data: 'name'
        },
        {
          data: "flowType"
        },
        {
          data: "appName"
        },
        {
          data: "entityName"
        },
        {
          data: "description"
        },
        {
          data: "enable"
        },
        {
          data: "createTime"
        }
      ],
      "iDisplayLength": 25,
      "sPaginationType": "full_numbers",
      "language": {
        "processing": true,
        "lengthMenu": "显示 _MENU_ 项结果",
        "zeroRecords": "没有匹配结果",
        "emptyTable": "没有数据",
        "info": "",
        "infoEmpty": "",
        "infoFiltered": "",
        "infoPostFix": "",
        "search": "搜索:",
        "url": "",
        "paginate": {
          "first": "首页",
          "previous": "上页",
          "next": "下页",
          "last": "末页"
        }
      }
    });

    var tenantId, SRC_IDOrName, flowType, enable, appId;
    $("#advanced .doSearch").on("click", function () {
      $("#advanced select").each(function (index) {
        if (0 === index) {
          flowType = this.value;
        }
        if (1 === index) {
          enable = this.value
        }
        if (2 === index) {
          appId = this.value
        }
      });
//
      $("#advanced input").each(function (index) {
        if (0 === index) {
          tenantId = this.value;
        }
        if (1 === index) {
          SRC_IDOrName = this.value;
        }
      });
      var dataObject = {
        flowType: flowType,
        enable: enable,
        appId: appId,
        tenantId: tenantId,
        SRC_IDOrName: SRC_IDOrName
      };
      table.ajax.url("${CONTEXT_PATH}/tenant/flows?dataObject=" + encodeURIComponent(JSON.stringify(dataObject))).load();
    });
  });
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
