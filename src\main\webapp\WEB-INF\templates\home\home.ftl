<#assign headContent>
<style>
  html, body {
    height: 100%;
    width: 100%;
    margin: 0;
  }

  #whale {
    height: 99.5%;
    width: 100%;
  }
</style>
</#assign>
<#assign breadcrumbContent>
</#assign>
<#assign bodyContent>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <div class="box-header with-border">
          <h3 class="box-title">欢迎</h3>
        </div>
        <div class="box-body">
          欢迎
          <#--<div class="" id="whale" style="height:600px"></div>-->
        </div>
      </div>
    </div>
  </div>
</section>
</#assign>

<#assign scriptContent>
<script>
  $(document).ready(function () {
    console.log(window.innerHeight);
    // $('#whale').attr('height', 792);

    var whale = (function () {

      var element = document.getElementById("whale")
              , width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth
              , height = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight
              , fps = 30
              , easy = 6
              , maxspeed = 150
              , delay = 15
              , mouse = {x: width / 2, y: height / 2}
              , defs
              , parts;

      defs = '<defs><linearGradient gradientTransform="matrix(0 -2038 1116.5 0 -157 2622)" gradientUnits="userSpaceOnUse" id=":s6y" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.25 -194.75 407.5 4.5 -111 4022)" gradientUnits="userSpaceOnUse" id=":s98" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -251.25 1004.5 0 -154 4314)" gradientUnits="userSpaceOnUse" id=":s99" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -252.5 1004.5 0 -154 4511)" gradientUnits="userSpaceOnUse" id=":s9a" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -448.5 290 0 -158 4984)" gradientUnits="userSpaceOnUse" id=":s9b" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -236.75 1004.5 0 -154 4737)" gradientUnits="userSpaceOnUse" id=":s9c" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -830.5 524 0 -158 4509)" gradientUnits="userSpaceOnUse" id=":s9d" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -289.5 1004.5 0 -154 4767)" gradientUnits="userSpaceOnUse" id=":s9e" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1143.5 646 0 -158 4064)" gradientUnits="userSpaceOnUse" id=":s9f" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -282.75 1004.5 0 -154 4809)" gradientUnits="userSpaceOnUse" id=":s9g" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1431.5 760.5 0 -157 3773)" gradientUnits="userSpaceOnUse" id=":s9h" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -232 1004.5 0 -154 4892)" gradientUnits="userSpaceOnUse" id=":s9i" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1669.5 893.5 0 -157 3435)" gradientUnits="userSpaceOnUse" id=":s9j" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -2038 1116.5 0 -157 2622)" gradientUnits="userSpaceOnUse" id=":s9k" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1956 1051.5 0 -157 2910)" gradientUnits="userSpaceOnUse" id=":s9l" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -2038 1116.5 0 -157 2622)" gradientUnits="userSpaceOnUse" id=":s9m" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -754.75 1256.5 0 -181 584)" gradientUnits="userSpaceOnUse" id=":s9n" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1003.25 1424.5 0 -166 468)" gradientUnits="userSpaceOnUse" id=":s9o" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1129 1476 0 -166 311)" gradientUnits="userSpaceOnUse" id=":s9p" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3748)" gradientUnits="userSpaceOnUse" id=":s9q" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1379 1698.5 0 -157 222)" gradientUnits="userSpaceOnUse" id=":s9r" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3748)" gradientUnits="userSpaceOnUse" id=":s9s" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1552 1975.5 0 -157 75)" gradientUnits="userSpaceOnUse" id=":s9t" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3748)" gradientUnits="userSpaceOnUse" id=":s9u" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1748.5 2040.5 0 -157 -70)" gradientUnits="userSpaceOnUse" id=":s9v" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3748)" gradientUnits="userSpaceOnUse" id=":s9w" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1916 2148.5 0 -157 -140)" gradientUnits="userSpaceOnUse" id=":s9x" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3748)" gradientUnits="userSpaceOnUse" id=":s9y" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -2141 2339 0 -158 -187)" gradientUnits="userSpaceOnUse" id=":s9z" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3748)" gradientUnits="userSpaceOnUse" id=":sa0" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -2311 2526.5 0 -157 -183)" gradientUnits="userSpaceOnUse" id=":sa1" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3748)" gradientUnits="userSpaceOnUse" id=":sa2" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -2616 2678 0 -158 -25)" gradientUnits="userSpaceOnUse" id=":sa3" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3748)" gradientUnits="userSpaceOnUse" id=":sa4" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -4970 383 0 -85 -3136)" gradientUnits="userSpaceOnUse" id=":sa5" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(44,46,44)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -2985 2849.5 0 -157 236)" gradientUnits="userSpaceOnUse" id=":sa6" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3748)" gradientUnits="userSpaceOnUse" id=":sa7" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -4970 383 0 -85 -3136)" gradientUnits="userSpaceOnUse" id=":sa8" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -3103 3052.5 0 -157 295)" gradientUnits="userSpaceOnUse" id=":sa9" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3748)" gradientUnits="userSpaceOnUse" id=":saa" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -4970 383 0 -85 -3136)" gradientUnits="userSpaceOnUse" id=":sab" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(41,44,43)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -3214 3160 0 -157 352)" gradientUnits="userSpaceOnUse" id=":sac" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3748)" gradientUnits="userSpaceOnUse" id=":sad" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -4970 383 0 -85 -3136)" gradientUnits="userSpaceOnUse" id=":sae" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(49,51,50)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -3221.5 3241 0 -124 364)" gradientUnits="userSpaceOnUse" id=":saf" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3748)" gradientUnits="userSpaceOnUse" id=":sag" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1134 293 0 -85 -3128)" gradientUnits="userSpaceOnUse" id=":sah" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(49,51,50)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -3281.5 3294 0 -124 393)" gradientUnits="userSpaceOnUse" id=":sai" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3748)" gradientUnits="userSpaceOnUse" id=":saj" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -665 284 0 -85 -3206)" gradientUnits="userSpaceOnUse" id=":sak" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(49,51,50)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -3302 3294 0 -124 328)" gradientUnits="userSpaceOnUse" id=":sal" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3748)" gradientUnits="userSpaceOnUse" id=":sam" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -3313 3294 0 -124 283)" gradientUnits="userSpaceOnUse" id=":san" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3645)" gradientUnits="userSpaceOnUse" id=":sao" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -3288 3205.5 0 -126 258)" gradientUnits="userSpaceOnUse" id=":sap" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3645)" gradientUnits="userSpaceOnUse" id=":saq" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1447 5372.5 0 -79 3302)" gradientUnits="userSpaceOnUse" id=":sar" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.6784313725490196" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -3248 3161 0 -124 218)" gradientUnits="userSpaceOnUse" id=":sas" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 3645)" gradientUnits="userSpaceOnUse" id=":sat" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1468 5761.5 0 -78 3785)" gradientUnits="userSpaceOnUse" id=":sau" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.6784313725490196" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -3124.25 3161 0 -124 162)" gradientUnits="userSpaceOnUse" id=":sav" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -175 407.5 3.5 -149 3522)" gradientUnits="userSpaceOnUse" id=":saw" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1635.5 5868 0 -77 3806)" gradientUnits="userSpaceOnUse" id=":sax" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.6784313725490196" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -2941.75 3127.5 0 -124 68)" gradientUnits="userSpaceOnUse" id=":say" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -179.5 407.5 3.5 -149 3515)" gradientUnits="userSpaceOnUse" id=":saz" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1814 5606.5 0 -78 3271)" gradientUnits="userSpaceOnUse" id=":sb0" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.6784313725490196" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -2709 3082 0 -124 -42)" gradientUnits="userSpaceOnUse" id=":sb1" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -177.75 407.5 3.5 -129 3202)" gradientUnits="userSpaceOnUse" id=":sb2" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1552 5121.5 0 -80 2883)" gradientUnits="userSpaceOnUse" id=":sb3" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.6784313725490196" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -2453.5 3026 0 -124 -149)" gradientUnits="userSpaceOnUse" id=":sb4" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -111 2897)" gradientUnits="userSpaceOnUse" id=":sb5" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1059 4556 0 -81 2579)" gradientUnits="userSpaceOnUse" id=":sb6" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.6784313725490196" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -2090 2901 0 -124 -420)" gradientUnits="userSpaceOnUse" id=":sb7" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(-0.25 -1600 2453.5 -0.75 -240 -1199)" gradientUnits="userSpaceOnUse" id=":sb8" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(67,102,114)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(2.25 -259.5 407.5 3.5 62 2397)" gradientUnits="userSpaceOnUse" id=":sb9" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -985.5 3905.5 0 -83 1856)" gradientUnits="userSpaceOnUse" id=":sba" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.6784313725490196" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1923.5 2729 0 -124 -489)" gradientUnits="userSpaceOnUse" id=":sbb" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -35 2129)" gradientUnits="userSpaceOnUse" id=":sbc" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1787.5 2593 0 -123 -493)" gradientUnits="userSpaceOnUse" id=":sbd" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(-0.25 -1600 2453.5 -0.75 -279 -1408)" gradientUnits="userSpaceOnUse" id=":sbe" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(67,102,114)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -92 1984)" gradientUnits="userSpaceOnUse" id=":sbf" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1609.5 2407 0 -124 -527)" gradientUnits="userSpaceOnUse" id=":sbg" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 1713)" gradientUnits="userSpaceOnUse" id=":sbh" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1450.5 2252.5 0 -126 -522)" gradientUnits="userSpaceOnUse" id=":sbi" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 1507)" gradientUnits="userSpaceOnUse" id=":sbj" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -1280.5 1994 0 -124 -593)" gradientUnits="userSpaceOnUse" id=":sbk" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -149 1258)" gradientUnits="userSpaceOnUse" id=":sbl" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><radialGradient cx="0" cy="0" gradientTransform="matrix(98.25 9.5 -16.5 171 -2094 -396)" gradientUnits="userSpaceOnUse" id=":sbm" r="1"><stop offset="0" stop-color="rgb(142,149,145)" stop-opacity="1.000"/><stop offset="0.4588235294117647" stop-color="rgb(56,59,57)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(0,0,0)" stop-opacity="1.000"/></radialGradient><radialGradient cx="0" cy="0" gradientTransform="matrix(-98.25 9.5 16.5 171 1845 -421)" gradientUnits="userSpaceOnUse" id=":sbn" r="1"><stop offset="0" stop-color="rgb(142,149,145)" stop-opacity="1.000"/><stop offset="0.4588235294117647" stop-color="rgb(56,59,57)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(0,0,0)" stop-opacity="1.000"/></radialGradient><linearGradient gradientTransform="matrix(0 -1038.5 1669 0 -124 -586)" gradientUnits="userSpaceOnUse" id=":sbo" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -176 407.5 3.5 -130 1051)" gradientUnits="userSpaceOnUse" id=":sbp" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(241,241,241)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -2299.25 2311 0 -7681 405)" gradientUnits="userSpaceOnUse" id=":sbq" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(31,33,32)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(88,92,90)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -180.5 407.5 3.5 -92 726)" gradientUnits="userSpaceOnUse" id=":sbr" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(239,239,239)" stop-opacity="1.000"/><stop offset="0.3843137254901961" stop-color="rgb(247,247,247)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(1.5 -193.25 407.5 3.75 -92 380)" gradientUnits="userSpaceOnUse" id=":sbs" x1="-1" x2="1" y1="0" y2="0"><stop offset="0.3843137254901961" stop-color="rgb(244,244,244)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(255,255,255)" stop-opacity="1.000"/></linearGradient><linearGradient gradientTransform="matrix(0 -230 639 0 -126 -470)" gradientUnits="userSpaceOnUse" id=":sbt" x1="-1" x2="1" y1="0" y2="0"><stop offset="0" stop-color="rgb(0,0,0)" stop-opacity="1.000"/><stop offset="0.8470588235294118" stop-color="rgb(20,22,21)" stop-opacity="1.000"/><stop offset="1" stop-color="rgb(33,35,33)" stop-opacity="1.000"/></linearGradient></defs>';
      parts = [
        {
          x: mouse.x,
          y: mouse.y,
          z: 43,
          data: '<path d="M -3910 5327 Q -4485 5547 -4894 5547 Q -4901 5547 -4909 5547 L -4909 5547 Q -4756 5589 -4564 5590 Q -4293 5590 -4100 5506 Q -3927 5431 -3910 5327 Z M 3489 5312 Q 3494 5425 3681 5506 Q 3873 5590 4142 5590 Q 4394 5589 4579 5517 L 4579 5517 Q 4528 5535 4446 5535 Q 4159 5535 3489 5312 Z" fill="url(#:s98)"/><path d="M 4142 5017 Q 3873 5017 3681 5101 Q 3488 5185 3489 5304 L 3489 5312 Q 4159 5535 4446 5535 Q 4528 5535 4579 5517 L 4594 5511 L 4605 5506 Q 4797 5423 4797 5304 Q 4797 5185 4605 5101 Q 4413 5017 4142 5017 Z M -4564 5017 Q -4833 5017 -5025 5101 Q -5217 5185 -5217 5304 Q -5217 5423 -5025 5506 Q -4970 5530 -4909 5547 Q -4902 5547 -4894 5547 Q -4484 5547 -3910 5327 L -3908 5304 Q -3908 5185 -4100 5101 Q -4293 5017 -4564 5017 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 42,
          data: '<path d="M -3264 5399 Q -3519 5399 -3793 5422 Q -4490 5480 -4611 5725 L -4606 5743 Q -4248 5860 -3744 5860 Q -3239 5860 -2879 5743 Q -2523 5626 -2523 5462 L -2523 5454 Q -2871 5399 -3264 5399 Z M 3064 5433 Q 2620 5433 2105 5444 Q 2103 5453 2104 5462 Q 2103 5626 2460 5743 Q 2818 5860 3322 5860 Q 3825 5860 4184 5743 L 4281 5709 Q 4304 5450 3638 5438 Q 3367 5433 3064 5433 Z" fill="url(#:s99)"/><path d="M 3322 5063 Q 2816 5063 2460 5180 Q 2123 5290 2105 5444 Q 2620 5433 3064 5433 Q 3368 5433 3638 5438 Q 4304 5450 4281 5709 Q 4542 5602 4543 5462 Q 4542 5297 4184 5180 Q 3827 5063 3322 5063 Z M -3744 5063 Q -4250 5063 -4606 5180 Q -4963 5297 -4963 5462 Q -4963 5619 -4638 5733 L -4606 5743 L -4611 5725 Q -4490 5480 -3793 5422 Q -3519 5399 -3264 5399 Q -2871 5399 -2523 5454 Q -2532 5294 -2879 5180 Q -3236 5063 -3744 5063 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 41,
          data: '<path d="M 1218 4847 Q 771 4847 459 4858 Q 323 4862 230 4862 Q 108 4862 58 4855 L -575 4875 Q -1167 4852 -1671 4852 Q -2946 4911 -3739 5066 Q -4551 5215 -4551 5408 Q -4551 5518 -4276 5614 Q -4055 5450 -3116 5340 Q -2176 5230 -240 5227 Q -184 5227 -129 5227 Q 1727 5227 2790 5358 Q 3884 5493 3943 5594 Q 4160 5506 4160 5408 Q 4160 5215 3332 5060 Q 2522 4905 1218 4847 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -129 5227 Q -184 5227 -240 5227 Q -2176 5230 -3116 5340 Q -4055 5450 -4276 5614 Q -4073 5685 -3720 5748 Q -2908 5904 -1606 5963 Q -1238 5962 -796 5813 Q -355 5663 -130 5663 Q 94 5663 479 5813 Q 860 5962 1247 5963 Q 2536 5904 3340 5748 Q 3738 5676 3943 5594 Q 3884 5493 2790 5358 Q 1727 5227 -129 5227 Z" fill="url(#:s9a)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 40,
          data: '<path d="M -157 4535 Q -278 4535 -363 4667 Q -448 4798 -448 4985 Q -448 5168 -363 5300 Q -278 5432 -157 5432 Q -38 5432 47 5300 Q 131 5168 132 4985 Q 131 4798 47 4667 Q -38 4535 -157 4535 Z" fill="url(#:s9b)"/><path d="M 993 4765 L 418 4769 L 79 4793 Q 13 4802 -122 4802 Q -285 4802 -549 4789 Q -960 4769 -1340 4769 Q -1406 4769 -1471 4770 Q -2559 4825 -3236 4970 Q -3928 5110 -3928 5291 Q -3928 5413 -3603 5517 Q -3654 5327 -2814 5185 Q -2104 5065 -702 5065 Q -445 5065 -165 5069 Q 1644 5094 2406 5188 Q 3168 5281 3233 5500 Q 3503 5403 3504 5291 Q 3503 5110 2796 4965 Q 2105 4820 993 4765 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -702 5065 Q -2104 5065 -2814 5185 Q -3654 5327 -3603 5517 Q -3449 5566 -3220 5611 Q -2527 5756 -1415 5812 Q -1102 5811 -724 5671 Q -349 5531 -159 5531 Q 34 5531 361 5671 Q 688 5811 1020 5812 Q 2118 5756 2804 5611 Q 3068 5558 3233 5500 Q 3168 5281 2406 5188 Q 1644 5094 -165 5069 Q -445 5065 -702 5065 Z" fill="url(#:s9c)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 39,
          data: '<path d="M -159 3679 Q -376 3679 -530 3920 Q -683 4164 -682 4510 Q -683 4852 -530 5096 Q -375 5340 -157 5340 Q 61 5340 214 5096 Q 366 4852 366 4510 Q 366 4164 214 3920 Q 62 3679 -155 3679 Q -156 3679 -157 3679 Q -158 3679 -159 3679 Z" fill="url(#:s9d)"/><path d="M -78 4497 Q -141 4497 -194 4498 L -620 4504 L -1235 4516 Q -2127 4586 -2678 4748 Q -3244 4920 -3244 5127 Q -3244 5280 -2943 5409 Q -2986 5196 -2473 5079 Q -2038 4979 -874 4979 Q -667 4979 -436 4982 Q 1088 5002 1795 5121 Q 2502 5239 2588 5384 Q 2836 5263 2837 5127 Q 2837 4914 2257 4742 Q 1696 4580 781 4510 Q 467 4510 216 4502 Q 49 4497 -78 4497 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -874 4979 Q -2039 4979 -2473 5079 Q -2986 5196 -2943 5409 Q -2826 5459 -2664 5506 Q -2094 5672 -1188 5738 Q -931 5738 -622 5571 Q -313 5409 -159 5409 Q -5 5409 266 5571 Q 532 5738 804 5738 Q 1706 5667 2262 5506 Q 2458 5447 2588 5384 Q 2502 5239 1795 5121 Q 1088 5002 -436 4982 Q -666 4979 -874 4979 Z" fill="url(#:s9e)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 38,
          data: '<path d="M -157 2921 Q -425 2921 -614 3256 Q -804 3591 -804 4063 Q -804 4537 -614 4872 Q -425 5207 -157 5208 Q 112 5207 299 4872 Q 488 4537 488 4063 Q 488 3591 299 3256 Q 109 2921 -157 2921 Z" fill="url(#:s9f)"/><path d="M 494 4212 Q 339 4212 84 4404 Q 17 4469 -38 4505 L -136 4588 Q 592 4655 1177 4837 Q 1762 5018 1843 5048 Q 1920 4959 1920 4863 Q 1920 4639 1518 4458 Q 1129 4287 494 4212 Z M -903 4218 Q -1522 4293 -1904 4463 Q -2296 4644 -2296 4863 Q -2296 4980 -2186 5084 Q -2184 5029 -1576 4831 Q -968 4633 -237 4586 Q -667 4218 -903 4218 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -459 4383 L -237 4586 Q -968 4633 -1576 4831 Q -2184 5029 -2186 5084 L -2160 5107 Q -2062 5189 -1894 5263 Q -1499 5439 -870 5509 Q -692 5508 -478 5332 Q -233 5280 -126 5280 Q -19 5280 138 5332 Q 322 5508 510 5509 Q 1136 5434 1522 5263 Q 1717 5174 1817 5076 L 1843 5048 Q 1762 5018 1177 4837 Q 592 4655 -136 4588 L -38 4505 L 38 4440 L -49 4512 Q -111 4548 -157 4549 Q -261 4548 -459 4383 Z" fill="url(#:s9g)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 37,
          data: '<path d="M -157 2341 Q -474 2341 -694 2761 Q -917 3181 -917 3774 Q -917 4367 -694 4784 Q -474 5204 -157 5204 Q 157 5204 381 4784 Q 604 4367 604 3774 Q 604 3181 381 2761 Q 157 2341 -157 2341 Z" fill="url(#:s9h)"/><path d="M 194 4290 Q 84 4290 -5 4403 Q -24 4430 -231 4472 L -295 4408 Q -401 4295 -524 4295 Q -839 4344 -1034 4462 Q -1234 4584 -1234 4732 Q -1234 4821 -1158 4902 Q -961 4651 -298 4642 Q -269 4642 -240 4642 Q 700 4642 851 4896 Q 921 4818 922 4732 Q 922 4580 718 4457 Q 517 4339 194 4290 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -240 4642 Q -269 4642 -298 4642 Q -961 4651 -1158 4902 Q -1110 4954 -1030 5002 Q -830 5119 -507 5168 Q -418 5168 -307 5051 Q -197 4932 -141 4932 Q -86 4932 8 5051 Q 101 5168 199 5168 Q 518 5119 718 5002 Q 802 4951 851 4896 Q 700 4642 -240 4642 Z" fill="url(#:s9i)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 36,
          data: '<g transform="matrix(1 0 0 1 0 0)"><path d="M -157 1765 Q -527 1765 -790 2254 Q -1050 2743 -1050 3433 Q -1050 4125 -790 4614 Q -527 5103 -157 5103 Q 213 5103 474 4614 Q 736 4125 737 3433 Q 736 2743 474 2254 Q 213 1765 -157 1765 Z" fill="url(#:s9j)"/></g>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 35,
          data: '<g transform="matrix(0.9073028564453125 0 0 0.900634765625 -16 821)"><path d="M -159 584 Q -619 584 -945 1180 Q -1273 1779 -1273 2622 Q -1273 3467 -945 4063 Q -620 4660 -157 4660 Q 307 4660 632 4063 Q 960 3467 960 2622 Q 960 1779 632 1180 Q 308 584 -155 584 Q -156 584 -157 584 Q -158 584 -159 584 Z" fill="url(#:s9k)"/></g>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 34,
          data: '<path d="M -157 953 Q -592 953 -900 1526 Q -1208 2099 -1208 2909 Q -1208 3719 -900 4292 Q -592 4865 -157 4865 Q 278 4865 587 4292 Q 895 3719 895 2909 Q 895 2099 587 1526 Q 278 953 -157 953 Z" fill="url(#:s9l)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 33,
          data: '<g transform="matrix(1 0 0 1 0 0)"><path d="M -159 584 Q -619 584 -945 1180 Q -1273 1779 -1273 2622 Q -1273 3467 -945 4063 Q -620 4660 -157 4660 Q 307 4660 632 4063 Q 960 3467 960 2622 Q 960 1779 632 1180 Q 308 584 -155 584 Q -156 584 -157 584 Q -158 584 -159 584 Z" fill="url(#:s6y)"/></g>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 32,
          data: '<g transform="matrix(1.077484130859375 0 0 1.0547027587890625 11 -448)"><path d="M -159 584 Q -619 584 -945 1180 Q -1273 1779 -1273 2622 Q -1273 3467 -945 4063 Q -620 4660 -157 4660 Q 307 4660 632 4063 Q 960 3467 960 2622 Q 960 1779 632 1180 Q 308 584 -155 584 Q -156 584 -157 584 Q -158 584 -159 584 Z" fill="url(#:s9m)"/></g>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 31,
          data: '<path d="M -157 -171 Q -425 -171 -674 3 Q -911 170 -1095 476 Q -1286 793 -1320 869 Q -1404 1057 -1437 1339 Q -1175 955 -809 830 Q -560 744 -62 743 Q 335 744 545 801 Q 848 884 1076 1131 Q 1004 906 947 780 Q 894 663 782 476 Q 598 170 361 3 Q 114 -171 -157 -171 Z" fill="url(#:s9n)"/><path d="M -62 743 Q -560 744 -809 830 Q -1175 955 -1437 1339 Q -1470 1619 -1480 1965 Q -1393 1800 -1356 1746 Q -807 931 -63 931 Q -62 931 -61 931 Q -60 931 -59 931 Q 545 931 1153 1710 Q 1097 1230 1076 1131 Q 848 884 545 801 Q 335 744 -62 743 Z" fill="rgb(255,255,255)" fill-opacity="1.000"/><path d="M -59 931 Q -60 931 -61 931 Q -62 931 -63 931 Q -807 931 -1356 1746 Q -1393 1800 -1480 1965 L -1482 2039 Q -1482 2953 -1095 3603 Q -911 3908 -674 4076 Q -425 4249 -157 4249 Q 391 4249 782 3603 Q 1169 2953 1169 2039 Q 1169 1846 1153 1710 Q 545 931 -59 931 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 30,
          data: '<path d="M -160 -535 Q -452 -535 -719 -352 Q -979 -175 -1180 151 Q -1528 715 -1590 1471 L -1584 1471 Q -1290 1064 -889 892 Q -548 745 -62 745 Q 370 745 646 836 Q 995 949 1259 1243 Q 1224 913 1126 655 Q 1044 439 867 151 Q 666 -172 406 -352 Q 138 -535 -154 -535 Q -156 -535 -157 -535 Q -159 -535 -160 -535 Z" fill="url(#:s9o)"/><path d="M -1590 1471 L -1595 1544 Q -1590 1478 -1584 1471 Z M -62 1113 Q -840 1113 -1389 1872 Q -1499 2024 -1587 2189 Q -1519 2920 -1180 3468 Q -979 3793 -719 3970 Q -451 4153 -157 4154 Q 137 4153 406 3970 Q 666 3790 867 3468 Q 1073 3135 1175 2782 Q 1280 2415 1290 1968 L 1235 1894 Q 670 1113 -62 1113 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -62 745 Q -548 745 -889 892 Q -1290 1064 -1584 1471 Q -1590 1478 -1595 1544 L -1601 1646 L -1604 1811 Q -1604 2005 -1587 2189 Q -1499 2024 -1389 1872 Q -840 1113 -62 1113 Q 670 1113 1235 1894 L 1290 1968 L 1291 1811 Q 1289 1542 1259 1243 Q 995 949 646 836 Q 370 745 -62 745 Z" fill="rgb(255,255,255)" fill-opacity="1.000"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 29,
          data: '<path d="M -66 1143 Q -440 1143 -757 1352 Q -1051 1546 -1337 1948 Q -1417 2062 -1581 2384 Q -1536 2684 -1446 2918 Q -1367 3123 -1211 3382 Q -1005 3723 -736 3910 Q -525 4057 -299 4092 L -313 4075 Q -393 3969 -393 3820 Q -393 3670 -313 3565 Q -232 3460 -120 3460 Q -7 3460 73 3565 Q 154 3670 154 3819 Q 154 3968 74 4074 Q 253 4028 423 3910 Q 691 3721 898 3382 Q 1101 3047 1188 2819 Q 1300 2523 1330 2154 L 1215 1980 Q 923 1569 634 1368 Q 310 1143 -59 1143 Q -61 1143 -62 1143 Q -64 1143 -66 1143 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -160 -818 Q -460 -818 -736 -626 Q -1005 -440 -1211 -98 Q -1603 548 -1642 1440 Q -984 553 -62 553 Q 718 553 1310 1188 Q 1233 455 898 -98 Q 691 -440 423 -626 Q 146 -818 -154 -818 Q -156 -818 -157 -818 Q -159 -818 -160 -818 Z" fill="url(#:s9p)"/><path d="M -62 553 Q -984 553 -1642 1440 L -1646 1641 Q -1646 1955 -1581 2384 Q -1417 2062 -1337 1948 Q -1051 1546 -757 1352 Q -440 1143 -66 1143 Q -64 1143 -62 1143 Q -60 1143 -59 1143 Q 310 1143 634 1368 Q 923 1569 1215 1980 L 1330 2154 Q 1353 1860 1353 1636 Q 1353 1540 1337 1408 L 1310 1188 Q 718 553 -62 553 Z M -120 3460 Q -232 3460 -313 3565 Q -393 3670 -393 3820 Q -393 3969 -313 4075 L -299 4092 L -157 4103 Q -40 4102 74 4074 Q 154 3968 154 3819 Q 154 3670 73 3565 Q -7 3460 -120 3460 Z" fill="url(#:s9q)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 28,
          data: '<path d="M -65 1399 Q -806 1399 -1328 2096 Q -1519 2350 -1640 2644 Q -1529 2936 -1358 3192 Q -1123 3545 -818 3738 Q -579 3889 -323 3927 Q -355 3852 -355 3761 Q -355 3611 -270 3506 Q -184 3401 -66 3401 Q 53 3401 138 3506 Q 223 3611 223 3760 Q 223 3824 208 3880 Q 359 3830 505 3738 Q 810 3545 1045 3192 Q 1281 2836 1405 2412 Q 1316 2247 1203 2096 Q 681 1399 -61 1399 Q -62 1399 -63 1399 Q -64 1399 -65 1399 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -160 -1157 Q -503 -1157 -818 -957 Q -1123 -765 -1358 -411 Q -1855 335 -1855 1389 L -1850 1601 L -1775 1498 Q -1067 553 -62 553 Q 867 553 1542 1359 Q 1535 324 1045 -411 Q 810 -765 505 -957 Q 190 -1157 -154 -1157 Q -156 -1157 -157 -1157 Q -159 -1157 -160 -1157 Z" fill="url(#:s9r)"/><path d="M -62 553 Q -1067 553 -1775 1498 L -1850 1601 Q -1822 2170 -1640 2644 Q -1519 2350 -1328 2096 Q -806 1399 -65 1399 Q -64 1399 -63 1399 Q -62 1399 -61 1399 Q 681 1399 1203 2096 Q 1316 2247 1405 2412 Q 1542 1942 1542 1389 L 1542 1359 Q 867 553 -62 553 Z M -66 3401 Q -184 3401 -270 3506 Q -355 3611 -355 3761 Q -355 3852 -323 3927 L -157 3939 Q 29 3938 208 3880 Q 223 3824 223 3760 Q 223 3611 138 3506 Q 53 3401 -66 3401 Z" fill="url(#:s9s)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 27,
          data: '<path d="M -66 1399 Q -906 1399 -1500 2096 Q -1712 2344 -1848 2630 Q -1725 2908 -1553 3144 Q -1279 3517 -925 3724 Q -653 3881 -361 3922 Q -394 3849 -394 3761 Q -394 3611 -298 3506 Q -201 3401 -67 3401 Q 68 3401 165 3506 Q 261 3611 262 3760 Q 261 3824 244 3881 Q 432 3827 612 3724 Q 965 3517 1240 3144 Q 1471 2826 1615 2432 Q 1509 2256 1373 2096 Q 780 1399 -62 1399 Q -63 1399 -64 1399 Q -65 1399 -66 1399 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -157 -1477 Q -558 -1477 -925 -1265 Q -1282 -1059 -1553 -683 Q -1827 -310 -1977 177 Q -2132 679 -2132 1231 Q -2132 1432 -2111 1627 L -2006 1498 Q -1202 553 -61 553 Q 1031 553 1815 1419 L 1819 1231 Q 1819 679 1664 177 Q 1514 -310 1240 -683 Q 968 -1059 612 -1265 Q 244 -1477 -157 -1477 Z" fill="url(#:s9t)"/><path d="M -61 553 Q -1202 553 -2006 1498 L -2111 1627 Q -2076 1966 -1977 2285 Q -1921 2465 -1848 2630 Q -1712 2344 -1500 2096 Q -907 1399 -66 1399 Q -65 1399 -64 1399 Q -63 1399 -62 1399 Q 779 1399 1373 2096 Q 1509 2256 1615 2432 L 1664 2285 Q 1792 1868 1815 1419 Q 1031 553 -61 553 Z M -67 3401 Q -201 3401 -298 3506 Q -394 3611 -394 3761 Q -394 3849 -361 3922 L -157 3936 Q 48 3935 244 3881 Q 261 3824 262 3760 Q 261 3611 165 3506 Q 68 3401 -67 3401 Z" fill="url(#:s9u)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 26,
          data: '<path d="M -63 1505 Q -869 1505 -1436 2172 Q -1667 2443 -1804 2760 Q -1713 2936 -1601 3094 Q -1318 3492 -951 3710 Q -682 3870 -395 3917 Q -423 3845 -423 3761 Q -423 3598 -318 3483 Q -213 3369 -66 3369 Q 80 3369 185 3483 Q 291 3598 291 3761 Q 291 3819 277 3871 Q 461 3815 638 3710 Q 1002 3492 1285 3094 Q 1457 2850 1582 2565 Q 1468 2357 1310 2172 Q 742 1505 -63 1505 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -157 -1819 Q -572 -1819 -951 -1593 Q -1318 -1375 -1601 -977 Q -1884 -578 -2036 -61 Q -2198 473 -2197 1058 Q -2198 1375 -2150 1678 L -2006 1498 Q -1202 553 -61 553 Q 1065 553 1863 1473 Q 1884 1269 1884 1058 Q 1884 473 1723 -61 Q 1567 -578 1285 -977 Q 1002 -1375 638 -1593 Q 259 -1819 -157 -1819 Z" fill="url(#:s9v)"/><path d="M -61 553 Q -1202 553 -2006 1498 L -2150 1678 Q -2110 1934 -2036 2181 Q -1944 2491 -1804 2760 Q -1667 2443 -1436 2172 Q -869 1505 -63 1505 Q 742 1505 1310 2172 Q 1468 2357 1582 2565 Q 1662 2381 1723 2181 Q 1826 1837 1863 1473 Q 1065 553 -61 553 Z M -66 3369 Q -213 3369 -318 3483 Q -423 3598 -423 3761 Q -423 3845 -395 3917 L -157 3936 Q 65 3936 277 3871 Q 291 3819 291 3761 Q 291 3598 185 3483 Q 80 3369 -66 3369 Z" fill="url(#:s9w)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 25,
          data: '<path d="M -63 1644 Q -819 1644 -1352 2270 Q -1615 2579 -1748 2952 L -1674 3060 Q -1378 3472 -993 3701 Q -746 3847 -483 3902 Q -501 3836 -501 3762 Q -501 3563 -373 3424 Q -246 3285 -69 3285 Q -68 3285 -66 3285 Q -65 3285 -63 3285 Q 113 3285 240 3424 Q 368 3563 369 3761 Q 369 3806 362 3848 Q 523 3792 680 3701 Q 1064 3472 1361 3060 Q 1461 2920 1546 2766 Q 1422 2500 1226 2270 Q 693 1644 -63 1644 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -161 -2056 Q -597 -2056 -993 -1822 Q -1378 -1593 -1674 -1177 Q -1974 -765 -2135 -225 Q -2305 329 -2305 940 Q -2305 1371 -2220 1776 Q -2122 1633 -2006 1498 Q -1202 553 -61 553 Q 1080 553 1884 1498 L 1944 1570 Q 1992 1262 1992 940 Q 1992 329 1822 -225 Q 1658 -765 1361 -1177 Q 1064 -1593 680 -1822 Q 280 -2056 -153 -2056 Q -155 -2056 -157 -2056 Q -159 -2056 -161 -2056 Z" fill="url(#:s9x)"/><path d="M -61 553 Q -1202 553 -2006 1498 Q -2122 1633 -2220 1776 L -2174 1970 L -2135 2107 Q -2022 2484 -1841 2801 L -1748 2952 Q -1615 2579 -1352 2270 Q -819 1644 -63 1644 Q 693 1644 1226 2270 Q 1422 2500 1546 2766 L 1627 2609 Q 1741 2373 1822 2107 Q 1873 1938 1909 1765 L 1944 1570 L 1884 1498 Q 1080 553 -61 553 Z M -69 3285 Q -246 3285 -373 3424 Q -501 3563 -501 3762 Q -501 3836 -483 3902 L -383 3920 L -157 3936 Q 58 3936 266 3878 L 362 3848 Q 369 3806 369 3761 Q 368 3563 240 3424 Q 113 3285 -63 3285 Q -64 3285 -66 3285 Q -67 3285 -69 3285 Z" fill="url(#:s9y)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 24,
          data: '<path d="M -63 1644 Q -819 1644 -1352 2270 Q -1651 2621 -1782 3054 Q -1470 3462 -1067 3690 Q -783 3850 -481 3906 Q -501 3838 -501 3762 Q -501 3563 -373 3424 Q -246 3285 -69 3285 Q -68 3285 -66 3285 Q -65 3285 -63 3285 Q 113 3285 240 3424 Q 368 3563 369 3761 Q 368 3812 360 3859 Q 561 3798 754 3690 Q 1174 3452 1497 3017 L 1595 2878 Q 1463 2548 1226 2270 Q 693 1644 -63 1644 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -157 -2328 Q -632 -2328 -1067 -2082 Q -1488 -1845 -1810 -1412 Q -2135 -977 -2313 -414 Q -2497 168 -2497 804 Q -2497 1403 -2334 1954 Q -2192 1715 -2006 1498 Q -1202 553 -61 553 Q 1080 553 1884 1498 Q 1985 1617 2074 1742 Q 2181 1289 2181 804 Q 2181 165 1997 -414 Q 1819 -977 1497 -1412 Q 1172 -1845 754 -2082 Q 318 -2328 -157 -2328 Z" fill="url(#:s9z)"/><path d="M -61 553 Q -1202 553 -2006 1498 Q -2192 1715 -2334 1954 L -2313 2022 Q -2135 2585 -1810 3017 L -1782 3054 Q -1651 2621 -1352 2270 Q -819 1644 -63 1644 Q 693 1644 1226 2270 Q 1463 2548 1595 2878 Q 1847 2496 1997 2022 Q 2041 1883 2074 1742 Q 1985 1617 1884 1498 Q 1080 553 -61 553 Z M -69 3285 Q -246 3285 -373 3424 Q -501 3563 -501 3762 Q -501 3838 -481 3906 Q -322 3935 -157 3936 Q 108 3935 360 3859 Q 368 3812 369 3761 Q 368 3563 240 3424 Q 113 3285 -63 3285 Q -64 3285 -66 3285 Q -67 3285 -69 3285 Z" fill="url(#:sa0)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 23,
          data: '<path d="M -64 1859 Q -744 1859 -1223 2422 Q -1554 2810 -1656 3310 Q -1419 3534 -1140 3684 Q -870 3829 -584 3891 Q -596 3829 -596 3762 Q -596 3520 -440 3349 Q -284 3180 -69 3180 Q -68 3180 -66 3180 Q -65 3180 -63 3180 Q 153 3180 308 3349 Q 464 3519 465 3761 L 460 3840 Q 647 3781 827 3684 Q 1193 3485 1492 3157 Q 1375 2749 1097 2422 Q 617 1859 -64 1859 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -161 -2494 Q -673 -2494 -1140 -2243 Q -1595 -1997 -1943 -1553 Q -2294 -1107 -2486 -530 Q -2684 66 -2683 722 Q -2684 1375 -2486 1974 L -2431 2128 Q -2258 1793 -2006 1498 Q -1202 553 -61 553 Q 1080 553 1884 1498 Q 2055 1699 2191 1918 Q 2370 1345 2370 722 Q 2370 66 2173 -530 Q 1980 -1110 1630 -1553 Q 1279 -1997 827 -2243 Q 360 -2494 -153 -2494 Q -155 -2494 -157 -2494 Q -159 -2494 -161 -2494 Z" fill="url(#:sa1)"/><path d="M -61 553 Q -1202 553 -2006 1498 Q -2258 1793 -2431 2128 Q -2246 2611 -1943 2995 Q -1808 3167 -1656 3310 Q -1554 2810 -1223 2422 Q -744 1859 -64 1859 Q 617 1859 1097 2422 Q 1375 2749 1492 3157 L 1630 2995 Q 1980 2551 2173 1974 L 2191 1918 Q 2055 1699 1884 1498 Q 1080 553 -61 553 Z M -69 3180 Q -284 3180 -440 3349 Q -596 3520 -596 3762 Q -596 3829 -584 3891 Q -374 3936 -157 3936 Q 160 3936 460 3840 L 465 3761 Q 464 3519 308 3349 Q 153 3180 -63 3180 Q -64 3180 -66 3180 Q -67 3180 -69 3180 Z" fill="url(#:sa2)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 22,
          data: '<path d="M -63 2213 Q -618 2213 -1009 2673 Q -1333 3053 -1389 3566 Q -1297 3625 -1199 3676 Q -924 3818 -634 3882 Q -644 3824 -644 3762 Q -644 3498 -474 3312 Q -303 3127 -66 3127 L -62 3127 Q 173 3128 342 3312 Q 513 3498 513 3761 L 510 3831 Q 701 3771 886 3676 Q 1075 3577 1247 3447 Q 1169 3009 884 2673 Q 492 2213 -63 2213 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -161 -2641 Q -704 -2641 -1199 -2384 Q -1680 -2133 -2050 -1678 Q -2424 -1223 -2627 -635 Q -2836 -24 -2836 646 Q -2836 1315 -2627 1926 Q -2504 2283 -2318 2591 Q -2139 2124 -1806 1734 Q -1085 887 -62 887 Q 962 887 1683 1734 Q 1944 2041 2111 2395 Q 2225 2172 2311 1926 Q 2520 1313 2520 646 Q 2520 -24 2311 -635 Q 2107 -1223 1737 -1678 Q 1367 -2133 886 -2384 Q 391 -2641 -153 -2641 Q -155 -2641 -157 -2641 Q -159 -2641 -161 -2641 Z" fill="url(#:sa3)"/><path d="M -62 887 Q -1085 887 -1806 1734 Q -2139 2124 -2318 2591 Q -2197 2790 -2050 2969 Q -1755 3332 -1389 3566 Q -1333 3053 -1009 2673 Q -618 2213 -63 2213 Q 492 2213 884 2673 Q 1169 3009 1247 3447 Q 1512 3245 1737 2969 Q 1952 2704 2111 2395 Q 1944 2041 1683 1734 Q 962 887 -62 887 Z M -66 3127 Q -303 3127 -474 3312 Q -644 3498 -644 3762 Q -644 3824 -634 3882 L -611 3887 L -611 3891 L -610 3897 L -389 3923 L -305 3929 L -182 3933 L 20 3924 Q 332 3895 490 3858 L 491 3847 L 491 3837 L 510 3831 L 513 3761 Q 513 3498 342 3312 Q 173 3128 -62 3127 Z" fill="url(#:sa4)"/><path d="M -85 -3927 Q -192 -3927 -265 -3575 Q -341 -3222 -341 -2726 Q -341 -2230 -265 -1877 Q -191 -1525 -84 -1524 Q 21 -1525 94 -1877 Q 170 -2230 171 -2726 Q 170 -3222 94 -3575 Q 21 -3927 -83 -3927 Q -83 -3927 -84 -3927 Q -84 -3927 -85 -3927 Z" fill="url(#:sa5)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 21,
          data: '<path d="M -62 2567 Q -493 2567 -796 2923 Q -1087 3265 -1098 3746 Q -871 3840 -633 3887 Q -644 3827 -644 3762 Q -644 3498 -474 3312 Q -303 3127 -66 3127 Q 172 3127 342 3312 Q 513 3498 513 3761 L 509 3842 Q 734 3778 951 3670 L 970 3661 Q 936 3235 671 2923 Q 368 2567 -62 2567 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -157 -2749 Q -736 -2749 -1264 -2486 Q -1776 -2232 -2172 -1771 Q -2565 -1308 -2782 -708 Q -3006 -89 -3006 592 Q -3006 1270 -2782 1892 Q -2565 2491 -2172 2955 Q -2049 3098 -1916 3221 Q -1795 2644 -1413 2196 Q -854 1540 -61 1540 Q 732 1540 1291 2196 Q 1621 2584 1756 3069 L 1859 2955 Q 2251 2491 2469 1892 Q 2692 1270 2693 592 Q 2692 -89 2469 -708 Q 2251 -1308 1859 -1771 Q 1463 -2232 951 -2486 Q 422 -2749 -157 -2749 Z" fill="url(#:sa6)"/><path d="M -61 1540 Q -854 1540 -1413 2196 Q -1795 2644 -1916 3221 Q -1618 3494 -1264 3670 L -1098 3746 Q -1087 3265 -796 2923 Q -493 2567 -62 2567 Q 368 2567 671 2923 Q 936 3235 970 3661 Q 1405 3440 1756 3069 Q 1621 2584 1291 2196 Q 732 1540 -61 1540 Z M -66 3127 Q -303 3127 -474 3312 Q -644 3498 -644 3762 Q -644 3827 -633 3887 L -611 3891 L -610 3897 L -389 3923 L -164 3933 L -157 3933 L 170 3913 Q 332 3895 490 3858 L 491 3847 L 509 3842 L 513 3761 Q 513 3498 342 3312 Q 172 3127 -66 3127 Z" fill="url(#:sa7)"/><path d="M -84 -7340 Q -239 -7340 -346 -6036 Q -457 -4736 -457 -2890 Q -457 -1046 -346 255 Q -239 1559 -84 1560 Q 69 1559 179 255 Q 286 -1049 287 -2890 Q 286 -4732 179 -6036 Q 69 -7340 -84 -7340 Q -84 -7340 -84 -7340 Q -84 -7340 -84 -7340 Z" fill="url(#:sa8)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 20,
          data: '<path d="M -62 2567 Q -493 2567 -796 2923 Q -1095 3274 -1099 3770 Q -859 3856 -610 3897 Q -618 3842 -618 3785 Q -618 3514 -455 3324 Q -293 3133 -63 3133 Q 168 3133 330 3324 Q 492 3514 493 3785 L 490 3858 Q 736 3798 972 3694 Q 947 3247 671 2923 Q 368 2567 -62 2567 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -157 -2808 Q -779 -2808 -1346 -2542 Q -1892 -2288 -2316 -1822 Q -2737 -1356 -2969 -748 Q -3209 -123 -3209 564 Q -3209 1248 -2969 1875 Q -2737 2480 -2316 2947 Q -2081 3204 -1809 3398 Q -1717 2777 -1317 2308 Q -798 1698 -61 1698 Q 676 1698 1196 2308 Q 1553 2728 1665 3268 Q 1843 3122 2003 2947 Q 2424 2480 2656 1875 Q 2896 1248 2896 564 Q 2896 -123 2656 -748 Q 2424 -1356 2003 -1822 Q 1579 -2288 1031 -2542 Q 465 -2808 -157 -2808 Z" fill="url(#:sa9)"/><path d="M -61 1698 Q -798 1698 -1317 2308 Q -1717 2777 -1809 3398 Q -1590 3553 -1346 3667 Q -1224 3724 -1099 3770 Q -1095 3274 -796 2923 Q -493 2567 -62 2567 Q 368 2567 671 2923 Q 947 3247 972 3694 L 1031 3667 Q 1372 3509 1665 3268 Q 1553 2728 1196 2308 Q 676 1698 -61 1698 Z M -63 3133 Q -293 3133 -455 3324 Q -618 3514 -618 3785 Q -618 3842 -610 3897 Q -387 3933 -157 3933 Q 174 3933 490 3858 L 493 3785 Q 492 3514 330 3324 Q 168 3133 -63 3133 Z" fill="url(#:saa)"/><path d="M -84 -9897 Q -273 -9897 -403 -7891 Q -536 -5893 -536 -3051 Q -536 -210 -402 1603 Q -270 3384 -86 3384 Q -83 3384 -79 3383 Q 109 3350 238 1569 Q 366 -214 366 -3051 Q 366 -5889 236 -7891 Q 103 -9897 -84 -9897 Z" fill="url(#:sab)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 19,
          data: '<path d="M -157 -2862 Q -801 -2862 -1386 -2596 Q -1955 -2336 -2392 -1867 Q -2828 -1398 -3068 -787 Q -3317 -157 -3317 536 Q -3317 1228 -3068 1858 Q -2828 2469 -2392 2938 Q -2034 3322 -1587 3566 Q -1531 2957 -1148 2508 Q -699 1980 -61 1980 Q 576 1980 1026 2508 Q 1375 2917 1453 3461 Q 1793 3244 2076 2938 Q 2514 2469 2755 1858 Q 3003 1228 3003 536 Q 3003 -157 2755 -787 Q 2514 -1398 2076 -1867 Q 1641 -2336 1073 -2596 Q 485 -2862 -157 -2862 Z" fill="url(#:sac)"/><path d="M -61 1980 Q -699 1980 -1148 2508 Q -1531 2957 -1587 3566 Q -1489 3620 -1386 3667 Q -801 3933 -157 3933 Q 485 3933 1073 3667 Q 1271 3576 1453 3461 Q 1375 2917 1026 2508 Q 576 1980 -61 1980 Z" fill="url(#:sad)"/><path d="M -84 -8538 Q -242 -8538 -355 -6919 Q -468 -5300 -468 -3014 Q -468 -724 -355 896 Q -242 2515 -84 2515 Q 74 2515 185 896 Q 298 -727 298 -3014 Q 298 -5297 185 -6919 Q 74 -8538 -84 -8538 Q -84 -8538 -84 -8538 Q -84 -8538 -84 -8538 Z" fill="url(#:sae)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 18,
          data: '<path d="M -1386 3584 L -1375 3588 L -1374 3585 L -1386 3584 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -125 -2858 Q -784 -2858 -1386 -2595 Q -1968 -2341 -2415 -1877 Q -2865 -1411 -3110 -811 Q -3365 -187 -3365 494 Q -3365 1175 -3110 1800 Q -2865 2399 -2415 2866 Q -1968 3329 -1386 3584 L -1374 3585 Q -1324 3067 -997 2685 Q -610 2230 -61 2230 Q 488 2230 876 2685 Q 1185 3048 1247 3534 Q 1762 3285 2167 2866 Q 2616 2399 2862 1800 Q 3116 1175 3117 494 Q 3116 -187 2862 -811 Q 2616 -1413 2167 -1877 Q 1720 -2341 1138 -2595 Q 536 -2858 -125 -2858 Z" fill="url(#:saf)"/><path d="M -61 2230 Q -610 2230 -997 2685 Q -1324 3067 -1374 3585 L -1375 3588 Q -778 3846 -125 3847 Q 536 3846 1138 3584 L 1247 3534 Q 1185 3048 876 2685 Q 488 2230 -61 2230 Z" fill="url(#:sag)"/><path d="M -84 -4568 Q -205 -4568 -293 -4123 Q -378 -3678 -378 -3041 Q -378 -2408 -293 -1963 Q -205 -1518 -84 -1518 Q 38 -1518 123 -1963 Q 207 -2411 208 -3041 Q 207 -3674 123 -4123 Q 38 -4568 -84 -4568 Z" fill="url(#:sah)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 17,
          data: '<path d="M -125 -2889 Q -795 -2889 -1406 -2623 Q -1997 -2369 -2454 -1902 Q -2910 -1436 -3158 -834 Q -3419 -207 -3418 477 Q -3419 1161 -3158 1789 Q -2910 2394 -2454 2860 Q -1997 3326 -1406 3581 Q -1288 3632 -1167 3674 Q -1137 3204 -846 2862 Q -522 2481 -61 2481 Q 399 2481 724 2862 Q 1000 3187 1042 3629 L 1158 3581 Q 1748 3326 2206 2860 Q 2661 2394 2910 1789 Q 3170 1161 3170 477 Q 3170 -207 2910 -834 Q 2661 -1436 2206 -1902 Q 1748 -2369 1158 -2623 Q 544 -2889 -125 -2889 Z" fill="url(#:sai)"/><path d="M -61 2481 Q -522 2481 -846 2862 Q -1137 3204 -1167 3674 Q -666 3846 -125 3847 Q 481 3846 1042 3629 Q 1000 3187 724 2862 Q 399 2481 -61 2481 Z" fill="url(#:saj)"/><path d="M -84 -3654 Q -203 -3654 -284 -3441 Q -369 -3229 -369 -2925 Q -369 -2624 -284 -2408 Q -203 -2196 -84 -2195 Q 32 -2196 117 -2408 Q 199 -2624 199 -2925 Q 199 -3226 117 -3441 Q 32 -3654 -84 -3654 Z" fill="url(#:sak)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 16,
          data: '<path d="M 1158 3578 L 1153 3579 L 1153 3580 L 1158 3578 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -125 -2974 Q -795 -2974 -1406 -2705 Q -1997 -2448 -2454 -1976 Q -2910 -1504 -3158 -891 Q -3419 -258 -3418 435 Q -3419 1130 -3158 1764 Q -2910 2374 -2454 2849 Q -1997 3321 -1406 3578 L -1281 3630 Q -1240 3131 -928 2766 Q -571 2345 -64 2345 Q -63 2345 -62 2345 Q -61 2345 -60 2345 Q 447 2345 806 2766 Q 1101 3112 1153 3579 L 1158 3578 Q 1748 3321 2206 2849 Q 2661 2377 2910 1764 Q 3170 1130 3170 435 Q 3170 -258 2910 -891 Q 2661 -1504 2206 -1976 Q 1748 -2448 1158 -2705 Q 544 -2974 -125 -2974 Z" fill="url(#:sal)"/><path d="M -64 2345 Q -570 2345 -928 2766 Q -1240 3131 -1281 3630 Q -727 3847 -125 3847 Q 542 3847 1153 3580 L 1153 3579 Q 1101 3112 806 2766 Q 448 2345 -60 2345 Q -61 2345 -62 2345 Q -63 2345 -64 2345 Z" fill="url(#:sam)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 15,
          data: '<path d="M -125 -3030 Q -795 -3030 -1406 -2759 Q -1997 -2499 -2454 -2024 Q -2910 -1546 -3158 -930 Q -3419 -292 -3418 407 Q -3419 1105 -3158 1747 Q -2910 2363 -2454 2838 Q -1997 3315 -1406 3575 L -1358 3596 Q -1310 3080 -985 2699 Q -604 2251 -62 2251 Q 480 2251 862 2699 Q 1169 3060 1229 3543 Q 1776 3286 2206 2838 Q 2661 2363 2910 1747 Q 3170 1105 3170 407 Q 3170 -292 2910 -930 Q 2661 -1546 2206 -2024 Q 1748 -2499 1158 -2759 Q 544 -3030 -125 -3030 Z" fill="url(#:san)"/><path d="M -62 2251 Q -604 2251 -985 2699 Q -1310 3080 -1358 3596 Q -770 3846 -125 3847 Q 544 3846 1158 3575 L 1229 3543 Q 1169 3060 862 2699 Q 480 2251 -62 2251 Z" fill="url(#:sao)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 14,
          data: '<path d="M -125 -3030 Q -778 -3030 -1372 -2759 Q -1949 -2499 -2392 -2024 Q -2836 -1546 -3079 -930 Q -3331 -292 -3331 407 Q -3331 1105 -3079 1747 Q -2836 2363 -2392 2838 Q -1973 3289 -1434 3546 Q -1374 3022 -1042 2633 Q -637 2157 -62 2157 Q 513 2157 918 2633 Q 1231 3000 1302 3487 Q 1768 3239 2141 2838 Q 2585 2363 2828 1747 Q 3080 1108 3080 407 Q 3080 -292 2828 -930 Q 2585 -1546 2141 -2024 Q 1697 -2499 1124 -2759 Q 527 -3030 -125 -3030 Z" fill="url(#:sap)"/><path d="M -62 2157 Q -637 2157 -1042 2633 Q -1374 3022 -1434 3546 L -1372 3575 Q -778 3846 -125 3847 Q 527 3846 1124 3575 L 1302 3487 Q 1231 3000 918 2633 Q 513 2157 -62 2157 Z" fill="url(#:saq)"/><path d="M 4131 3257 Q 4116 3257 4106 3265 Q 4050 3307 4183 3561 Q 4313 3817 4557 4136 Q 4799 4455 5011 4649 Q 5182 4810 5252 4810 Q 5267 4810 5277 4802 Q 5333 4759 5200 4505 Q 5069 4250 4826 3931 Q 4583 3611 4372 3417 Q 4200 3257 4131 3257 Z M -4295 3262 Q -4364 3262 -4535 3424 Q -4746 3618 -4988 3939 Q -5230 4259 -5359 4515 Q -5491 4769 -5435 4812 Q -5425 4819 -5410 4819 Q -5342 4819 -5170 4658 Q -4959 4463 -4717 4143 Q -4476 3822 -4346 3567 Q -4214 3312 -4270 3270 Q -4280 3262 -4295 3262 Z" fill="url(#:sar)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 13,
          data: '<path d="M -125 -3030 Q -767 -3030 -1355 -2759 Q -1923 -2499 -2358 -2024 Q -2797 -1546 -3037 -930 Q -3286 -292 -3285 407 Q -3286 1105 -3037 1747 Q -2797 2363 -2358 2838 Q -2008 3222 -1570 3466 Q -1494 2922 -1144 2512 Q -698 1987 -64 1987 Q -63 1987 -62 1987 Q -61 1987 -60 1987 Q 573 1987 1020 2512 Q 1347 2897 1436 3399 Q 1804 3171 2110 2838 Q 2548 2363 2789 1747 Q 3037 1105 3037 407 Q 3037 -292 2789 -930 Q 2548 -1546 2110 -2024 Q 1672 -2499 1107 -2759 Q 519 -3030 -125 -3030 Z" fill="url(#:sas)"/><path d="M -64 1987 Q -697 1987 -1144 2512 Q -1494 2922 -1570 3466 Q -1465 3525 -1355 3575 Q -767 3846 -125 3847 Q 519 3846 1107 3575 Q 1277 3497 1436 3399 Q 1347 2897 1020 2512 Q 574 1987 -60 1987 Q -61 1987 -62 1987 Q -63 1987 -64 1987 Z" fill="url(#:sat)"/><path d="M 3500 2419 Q 3482 2419 3470 2428 Q 3386 2491 3647 2960 Q 3909 3425 4364 4023 Q 4819 4621 5198 4997 Q 5525 5319 5634 5319 Q 5652 5319 5664 5310 Q 5747 5247 5486 4778 Q 5224 4313 4769 3715 Q 4313 3117 3935 2741 Q 3608 2419 3500 2419 Z M -3666 2422 Q -3774 2422 -4100 2746 Q -4478 3123 -4930 3723 Q -5383 4322 -5643 4789 Q -5902 5258 -5818 5322 Q -5806 5331 -5789 5331 Q -5682 5331 -5354 5007 Q -4977 4629 -4524 4029 Q -4072 3429 -3812 2964 Q -3553 2494 -3636 2431 Q -3648 2422 -3666 2422 Z" fill="url(#:sau)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 12,
          data: '<path d="M -130 -2962 Q -769 -2962 -1355 -2696 Q -1923 -2437 -2358 -1965 Q -2797 -1496 -3037 -884 Q -3286 -252 -3285 442 Q -3286 1133 -3037 1768 Q -2797 2378 -2358 2850 Q -2129 3097 -1864 3286 Q -1752 2706 -1369 2259 Q -829 1628 -63 1628 Q 704 1628 1244 2259 Q 1601 2675 1722 3207 Q 1927 3047 2110 2850 Q 2548 2378 2789 1768 Q 3037 1133 3037 442 Q 3037 -252 2789 -884 Q 2548 -1496 2110 -1965 Q 1672 -2437 1107 -2696 Q 521 -2962 -120 -2962 Q -122 -2962 -125 -2962 Q -127 -2962 -130 -2962 Z" fill="url(#:sav)"/><path d="M -63 1628 Q -829 1628 -1369 2259 Q -1752 2706 -1864 3286 Q -1624 3457 -1355 3580 Q -767 3847 -125 3847 Q 519 3847 1107 3580 Q 1436 3430 1722 3207 Q 1601 2675 1244 2259 Q 704 1628 -63 1628 Z" fill="url(#:saw)"/><path d="M 3061 1844 Q 3043 1844 3031 1853 Q 2939 1923 3275 2499 Q 3610 3074 4178 3821 Q 4746 4567 5212 5043 Q 5619 5461 5741 5461 Q 5759 5461 5771 5452 Q 5863 5381 5527 4804 Q 5189 4226 4623 3482 Q 4056 2738 3589 2259 Q 3183 1844 3061 1844 Z M -3230 1845 Q -3352 1845 -3756 2262 Q -4222 2742 -4785 3489 Q -5349 4235 -5683 4815 Q -6018 5394 -5925 5464 Q -5913 5473 -5896 5473 Q -5775 5473 -5368 5053 Q -4904 4574 -4339 3826 Q -3774 3077 -3441 2500 Q -3108 1923 -3200 1854 Q -3212 1845 -3230 1845 Z" fill="url(#:sax)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 11,
          data: '<path d="M -125 -2874 Q -762 -2874 -1344 -2611 Q -1904 -2358 -2336 -1897 Q -2769 -1436 -3006 -840 Q -3252 -221 -3251 457 Q -3252 1134 -3006 1753 Q -2769 2350 -2336 2811 Q -2238 2916 -2133 3010 Q -1983 2421 -1592 1953 Q -960 1195 -62 1195 Q 835 1195 1468 1953 Q 1828 2384 1984 2917 L 2088 2811 Q 2520 2350 2758 1753 Q 3003 1134 3004 457 Q 3003 -221 2758 -840 Q 2520 -1436 2088 -1897 Q 1655 -2358 1093 -2611 Q 510 -2874 -125 -2874 Z" fill="url(#:say)"/><path d="M -62 1195 Q -960 1195 -1592 1953 Q -1983 2421 -2133 3010 Q -1776 3329 -1344 3526 Q -762 3787 -125 3787 Q 510 3787 1093 3526 Q 1588 3302 1984 2917 Q 1828 2384 1468 1953 Q 835 1195 -62 1195 Z" fill="url(#:saz)"/><path d="M 411 -1627 Q 396 -1627 387 -1620 Q 275 -1535 944 -488 Q 1616 562 2675 1954 Q 3734 3345 4568 4273 Q 5329 5119 5489 5119 Q 5504 5119 5513 5112 Q 5625 5026 4955 3978 Q 4284 2933 3221 1538 Q 2158 142 1330 -783 Q 570 -1627 411 -1627 Z M -595 -1637 Q -754 -1637 -1511 -789 Q -2335 139 -3392 1539 Q -4450 2939 -5116 3987 Q -5782 5038 -5669 5123 Q -5660 5130 -5645 5130 Q -5487 5130 -4728 4280 Q -3898 3348 -2844 1953 Q -1791 557 -1123 -496 Q -459 -1545 -571 -1630 Q -581 -1637 -595 -1637 Z" fill="url(#:sb0)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 10,
          data: '<path d="M -125 -2752 Q -753 -2752 -1324 -2500 Q -1878 -2255 -2305 -1812 Q -2732 -1367 -2963 -790 Q -3207 -194 -3206 460 Q -3207 1113 -2963 1710 Q -2748 2245 -2365 2667 Q -2184 2139 -1817 1704 Q -1092 844 -63 844 Q 967 844 1693 1704 Q 2024 2097 2205 2566 Q 2525 2182 2715 1710 Q 2958 1113 2958 460 Q 2958 -194 2715 -790 Q 2483 -1367 2057 -1812 Q 1630 -2255 1076 -2500 Q 502 -2752 -125 -2752 Z" fill="url(#:sb1)"/><path d="M -63 844 Q -1092 844 -1817 1704 Q -2184 2139 -2365 2667 L -2305 2732 Q -1878 3174 -1324 3420 Q -753 3671 -125 3671 Q 502 3671 1076 3420 Q 1630 3174 2057 2732 L 2205 2566 Q 2024 2097 1693 1704 Q 967 844 -63 844 Z" fill="url(#:sb2)"/><path d="M 1070 -773 Q 1051 -773 1039 -764 Q 927 -679 1430 150 Q 1932 978 2757 2062 Q 3580 3143 4245 3848 Q 4837 4476 4991 4476 Q 5010 4476 5022 4467 Q 5134 4381 4632 3553 Q 4128 2729 3303 1646 Q 2476 560 1816 -144 Q 1223 -773 1070 -773 Z M -1250 -780 Q -1404 -780 -1993 -149 Q -2651 558 -3473 1648 Q -4294 2734 -4793 3561 Q -5293 4391 -5180 4476 Q -5168 4485 -5149 4485 Q -4996 4485 -4406 3853 Q -3744 3145 -2925 2061 Q -2105 974 -1605 144 Q -1106 -686 -1219 -771 Q -1231 -780 -1250 -780 Z" fill="url(#:sb3)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 9,
          data: '<path d="M -130 -2603 Q -744 -2603 -1304 -2363 Q -1847 -2132 -2265 -1707 Q -2684 -1284 -2912 -732 Q -3150 -165 -3150 460 Q -3150 1085 -2912 1653 Q -2767 2004 -2544 2304 Q -2349 1860 -2025 1479 Q -1214 526 -63 526 Q 1088 526 1900 1479 Q 2185 1814 2370 2199 Q 2543 1944 2664 1653 Q 2901 1085 2902 460 Q 2901 -165 2664 -732 Q 2435 -1284 2017 -1707 Q 1596 -2132 1053 -2363 Q 493 -2603 -120 -2603 Q -123 -2603 -125 -2603 Q -128 -2603 -130 -2603 Z" fill="url(#:sb4)"/><path d="M -63 526 Q -1214 526 -2025 1479 Q -2349 1860 -2544 2304 Q -2417 2474 -2265 2628 Q -1844 3052 -1304 3284 Q -742 3524 -125 3524 Q 491 3524 1053 3284 Q 1596 3052 2017 2628 Q 2215 2428 2370 2199 Q 2185 1814 1900 1479 Q 1088 526 -63 526 Z" fill="url(#:sb5)"/><path d="M 1688 19 Q 1662 19 1645 32 Q 1533 117 1863 719 Q 2192 1320 2773 2082 Q 3353 2844 3843 3320 Q 4260 3725 4404 3725 Q 4430 3725 4447 3712 Q 4559 3626 4229 3025 Q 3899 2428 3319 1666 Q 2738 904 2249 424 Q 1832 19 1688 19 Z M -1864 14 Q -2008 14 -2424 422 Q -2911 903 -3488 1668 Q -4066 2432 -4393 3031 Q -4721 3633 -4608 3718 Q -4591 3731 -4565 3731 Q -4421 3731 -4005 3324 Q -3518 2846 -2940 2081 Q -2364 1317 -2036 715 Q -1709 112 -1821 27 Q -1838 14 -1864 14 Z" fill="url(#:sb6)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 8,
          data: '<path d="M -125 -2510 Q -716 -2510 -1256 -2281 Q -1776 -2064 -2177 -1662 Q -2579 -1261 -2796 -741 Q -3025 -201 -3025 390 Q -3025 978 -2796 1518 L -2727 1670 Q -2529 1275 -2233 928 Q -1336 -126 -63 -126 Q 1210 -126 2108 928 Q 2350 1213 2527 1530 L 2548 1518 Q 2777 980 2777 390 Q 2777 -201 2548 -741 Q 2330 -1261 1929 -1662 Q 1525 -2064 1005 -2281 Q 465 -2510 -125 -2510 Z" fill="url(#:sb7)"/><path d="M 2548 1518 L 2527 1530 L 2536 1546 L 2548 1518 Z" fill="url(#:sb8)"/><path d="M -63 -126 Q -1336 -126 -2233 928 Q -2529 1275 -2727 1670 Q -2519 2098 -2177 2439 Q -1776 2840 -1256 3058 Q -716 3287 -125 3287 Q 465 3287 1005 3058 Q 1525 2840 1929 2439 Q 2319 2049 2536 1546 L 2527 1530 Q 2350 1213 2108 928 Q 1210 -126 -63 -126 Z" fill="url(#:sb9)"/><path d="M 2311 845 Q 2288 845 2273 857 Q 2194 917 2359 1254 Q 2527 1591 2842 2005 Q 3156 2418 3437 2670 Q 3665 2871 3760 2871 Q 3783 2871 3798 2860 Q 3877 2800 3709 2463 Q 3542 2127 3226 1712 Q 2909 1296 2631 1047 Q 2406 845 2311 845 Z M -2484 843 Q -2579 843 -2803 1046 Q -3081 1296 -3395 1713 Q -3710 2130 -3875 2466 Q -4042 2804 -3963 2864 Q -3948 2875 -3926 2875 Q -3831 2875 -3602 2672 Q -3323 2419 -3010 2004 Q -2697 1589 -2530 1252 Q -2367 914 -2446 855 Q -2461 843 -2484 843 Z" fill="url(#:sba)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 7,
          data: '<path d="M -126 -2413 Q -680 -2413 -1187 -2199 Q -1675 -1994 -2053 -1619 Q -2431 -1245 -2637 -760 Q -2853 -258 -2853 292 Q -2853 842 -2637 1345 L -2597 1434 Q -2439 1169 -2233 928 Q -1336 -126 -63 -126 Q 1210 -126 2108 928 Q 2268 1116 2400 1319 Q 2605 828 2605 292 Q 2605 -258 2389 -760 Q 2182 -1245 1805 -1619 Q 1427 -1994 938 -2199 Q 428 -2413 -126 -2413 Z" fill="url(#:sbb)"/><path d="M -63 -126 Q -1336 -126 -2233 928 Q -2439 1169 -2597 1434 Q -2395 1864 -2053 2204 Q -1675 2578 -1187 2783 Q -680 2994 -126 2994 Q 428 2994 938 2783 Q 1427 2578 1805 2204 Q 2182 1829 2389 1345 L 2400 1319 Q 2268 1116 2108 928 Q 1210 -126 -63 -126 Z" fill="url(#:sbc)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 6,
          data: '<path d="M -2361 -1010 Q -2447 -866 -2513 -710 L -2513 -679 Q -2271 -597 -2142 -597 Q -1979 -597 -1999 -729 Q -2034 -967 -2361 -1010 Z M 2109 -1020 Q 1757 -962 1765 -715 Q 1769 -588 1902 -588 Q 2028 -588 2268 -702 L 2268 -710 Q 2198 -872 2109 -1020 Z" fill="rgb(255,255,255)" fill-opacity="1.000"/><path d="M -129 -2281 Q -654 -2281 -1132 -2078 Q -1599 -1883 -1956 -1527 Q -2194 -1291 -2361 -1010 Q -2034 -967 -1999 -729 Q -1979 -597 -2142 -597 Q -2271 -597 -2513 -679 L -2524 -684 L -2526 -679 Q -2716 -216 -2716 291 Q -2716 813 -2513 1291 L -2509 1294 Q -2384 1105 -2233 928 Q -1336 -126 -63 -126 Q 1210 -126 2108 928 Q 2215 1054 2310 1187 Q 2470 756 2470 291 Q 2470 -228 2271 -702 L 2270 -703 L 2268 -702 Q 2027 -588 1902 -588 Q 1769 -588 1765 -715 Q 1757 -962 2109 -1020 Q 1944 -1296 1710 -1527 Q 1353 -1883 886 -2078 Q 405 -2281 -119 -2281 Q -122 -2281 -124 -2281 Q -127 -2281 -129 -2281 Z" fill="url(#:sbd)"/><path d="M 2268 -710 L 2268 -702 L 2270 -703 L 2268 -710 Z M -2513 -710 L -2524 -684 L -2513 -679 L -2513 -710 Z M -2513 1291 L -2511 1297 L -2509 1294 L -2513 1291 Z" fill="url(#:sbe)"/><path d="M -63 -126 Q -1336 -126 -2233 928 Q -2384 1105 -2509 1294 L -2511 1297 Q -2314 1754 -1956 2108 Q -1599 2464 -1132 2659 Q -652 2860 -124 2860 Q 403 2860 886 2659 Q 1353 2464 1710 2108 Q 2070 1752 2268 1291 L 2310 1187 Q 2215 1054 2108 928 Q 1210 -126 -63 -126 Z" fill="url(#:sbf)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 5,
          data: '<path d="M 1969 -950 Q 1537 -731 1573 -505 Q 1590 -395 1732 -395 Q 1881 -395 2166 -515 L 2093 -704 Q 2038 -832 1969 -950 Z M -2224 -939 Q -2289 -826 -2341 -704 Q -2387 -600 -2421 -494 Q -2128 -341 -1976 -341 Q -1843 -341 -1819 -459 Q -1769 -711 -2224 -939 Z" fill="rgb(255,255,255)" fill-opacity="1.000"/><path d="M -125 -2137 Q -614 -2137 -1061 -1953 Q -1493 -1775 -1827 -1450 Q -2065 -1219 -2224 -939 Q -1769 -711 -1819 -459 Q -1843 -341 -1976 -341 Q -2128 -341 -2421 -494 Q -2531 -155 -2531 209 Q -2531 665 -2358 1082 L -2233 928 Q -1336 -126 -63 -126 Q 1210 -126 2108 928 L 2150 979 Q 2282 608 2283 209 Q 2282 -166 2166 -515 Q 1881 -395 1732 -395 Q 1590 -395 1573 -505 Q 1537 -731 1969 -950 Q 1810 -1224 1579 -1450 Q 1245 -1775 813 -1953 Q 366 -2137 -125 -2137 Z" fill="url(#:sbg)"/><path d="M -63 -126 Q -1336 -126 -2233 928 L -2358 1082 L -2341 1122 Q -2158 1543 -1827 1868 Q -1493 2193 -1061 2371 Q -614 2555 -125 2555 Q 364 2555 813 2371 Q 1245 2193 1579 1868 Q 1909 1543 2093 1122 L 2150 979 L 2108 928 Q 1210 -126 -63 -126 Z" fill="url(#:sbh)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 4,
          data: '<path d="M -2234 928 L -2234 929 L -2233 928 Z" fill="rgb(0,0,0)" fill-opacity="1.000"/><path d="M -2072 -908 Q -2144 -790 -2200 -662 Q -2260 -527 -2300 -388 Q -2183 -321 -1837 -213 Q -1731 -180 -1653 -180 Q -1477 -180 -1443 -347 Q -1394 -588 -1930 -843 L -2072 -908 Z M 1815 -920 Q 1754 -893 1698 -866 Q 1161 -610 1165 -336 Q 1168 -149 1342 -149 Q 1423 -149 1541 -189 Q 1913 -316 2044 -407 Q 2005 -537 1949 -662 Q 1890 -796 1815 -920 Z" fill="rgb(255,255,255)" fill-opacity="1.000"/><path d="M -130 -1973 Q -586 -1973 -1002 -1804 Q -1406 -1643 -1717 -1346 Q -1927 -1147 -2072 -908 L -1930 -843 Q -1394 -588 -1443 -347 Q -1477 -180 -1653 -180 Q -1731 -180 -1837 -213 Q -2183 -321 -2300 -388 Q -2378 -116 -2378 172 Q -2378 566 -2234 928 L -2233 928 Q -1336 -126 -63 -126 Q 1148 -126 2020 829 Q 2127 512 2127 172 Q 2127 -128 2044 -407 Q 1913 -316 1541 -189 Q 1423 -149 1342 -149 Q 1167 -149 1165 -336 Q 1161 -610 1698 -866 Q 1754 -893 1815 -920 Q 1672 -1152 1469 -1346 Q 1158 -1643 754 -1804 Q 334 -1973 -120 -1973 Q -123 -1973 -125 -1973 Q -128 -1973 -130 -1973 Z" fill="url(#:sbi)"/><path d="M -63 -126 Q -1336 -126 -2233 928 L -2234 929 L -2200 1009 Q -2031 1393 -1717 1690 Q -1406 1987 -1002 2148 Q -584 2317 -125 2317 Q 332 2317 754 2148 Q 1158 1987 1469 1690 Q 1779 1393 1949 1009 L 2020 829 Q 1148 -126 -63 -126 Z" fill="url(#:sbj)"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 3,
          data: '<path d="M 1648 -842 Q 1163 -604 1191 -385 Q 1205 -275 1364 -275 Q 1522 -275 1823 -383 Q 1767 -626 1648 -842 Z M -1899 -828 L -1963 -698 Q -2034 -535 -2072 -363 L -2062 -361 Q -1916 -308 -1804 -279 Q -1679 -248 -1594 -248 Q -1434 -248 -1421 -359 Q -1398 -561 -1804 -779 L -1899 -828 Z" fill="rgb(255,255,255)" fill-opacity="1.000"/><path d="M -125 -1874 Q -951 -1874 -1533 -1309 Q -1755 -1096 -1897 -832 L -1899 -828 L -1804 -779 Q -1398 -561 -1421 -359 Q -1435 -248 -1594 -248 Q -1678 -248 -1804 -279 Q -1916 -308 -2062 -361 L -2072 -363 L -2073 -362 Q -2103 -227 -2113 -88 L -2118 53 Q -2118 382 -2009 687 Q -1181 -126 -63 -126 Q 993 -126 1790 599 Q 1870 335 1870 53 Q 1870 -175 1823 -383 Q 1522 -275 1364 -275 Q 1205 -275 1191 -385 Q 1163 -604 1648 -842 Q 1510 -1093 1285 -1309 Q 703 -1874 -125 -1874 Z" fill="url(#:sbk)"/><path d="M -63 -126 Q -1181 -126 -2009 687 L -1963 805 Q -1810 1150 -1533 1416 Q -951 1981 -125 1981 Q 703 1981 1285 1416 Q 1562 1150 1715 805 Q 1759 703 1790 599 Q 993 -126 -63 -126 Z" fill="url(#:sbl)"/><path d="M -2050 -549 Q -2096 -549 -2133 -511 Q -2175 -470 -2181 -405 Q -2187 -341 -2154 -291 Q -2121 -242 -2067 -237 Q -2061 -236 -2056 -236 Q -2010 -236 -1972 -274 Q -1930 -316 -1924 -381 Q -1918 -445 -1951 -494 Q -1984 -543 -2037 -548 Q -2044 -549 -2050 -549 Z" fill="url(#:sbm)"/><path d="M 1802 -574 Q 1796 -574 1789 -573 Q 1735 -568 1702 -519 Q 1669 -470 1675 -406 Q 1681 -341 1723 -299 Q 1761 -261 1808 -261 Q 1814 -261 1819 -262 Q 1872 -267 1905 -316 Q 1938 -366 1933 -430 Q 1926 -495 1884 -536 Q 1847 -574 1802 -574 Z" fill="url(#:sbn)"/><path d="M 1506 637 Q 711 74 -20 60 Q -779 46 -1586 616 L -1695 694" fill="none" stroke="rgb(156,156,156)" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="1.000" stroke-width="63.481906870545906"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 2,
          data: '<path d="M -1635 -685 Q -1714 -524 -1753 -346 Q -1554 -268 -1429 -268 Q -1283 -268 -1240 -376 Q -1161 -576 -1635 -685 Z M 1383 -695 Q 1017 -578 983 -363 Q 966 -258 1091 -258 Q 1220 -258 1500 -370 Q 1459 -540 1383 -695 Z" fill="rgb(255,255,255)" fill-opacity="1.000"/><path d="M -128 -1625 Q -816 -1625 -1304 -1142 Q -1516 -932 -1635 -685 Q -1161 -576 -1240 -376 Q -1283 -268 -1429 -268 Q -1553 -268 -1753 -346 Q -1793 -166 -1793 31 Q -1793 251 -1742 452 Q -1006 -126 -63 -126 Q 813 -126 1512 374 Q 1545 209 1545 31 Q 1545 -179 1500 -370 Q 1220 -258 1091 -258 Q 966 -258 983 -363 Q 1017 -578 1383 -695 Q 1263 -937 1056 -1142 Q 568 -1625 -122 -1625 Q -123 -1625 -125 -1625 Q -126 -1625 -128 -1625 Z" fill="url(#:sbo)"/><path d="M -63 -126 Q -1006 -126 -1742 452 Q -1635 872 -1304 1201 Q -815 1684 -125 1684 Q 567 1684 1056 1201 Q 1418 841 1512 374 Q 813 -126 -63 -126 Z" fill="url(#:sbp)"/><path d="M 1439 601 Q -59 -471 -1704 699" fill="none" stroke="rgb(170,170,170)" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="1.000" stroke-width="63.481906870545906"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 1,
          data: '<path d="M -125 -1327 Q -648 -1327 -1021 -947 Q -1392 -568 -1392 -32 Q -1392 77 -1376 181 Q -779 -161 -63 -161 Q 585 -161 1136 119 L 1144 -32 Q 1143 -568 773 -947 Q 400 -1327 -125 -1327 Z" fill="url(#:sbq)"/><path d="M -63 -161 Q -779 -161 -1376 181 Q -1316 584 -1021 886 Q -648 1265 -125 1266 Q 400 1265 773 886 Q 1090 561 1136 119 Q 585 -161 -63 -161 Z" fill="url(#:sbr)"/><path d="M 1100 355 Q -231 -283 -1319 453" fill="none" stroke="rgb(177,177,177)" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="1.000" stroke-width="63.481906870545906"/>'
        },
        {
          x: mouse.x,
          y: mouse.y,
          z: 0,
          data: '<path d="M -63 -206 Q -430 -206 -765 -110 L -770 -18 Q -770 274 -580 482 Q -391 689 -125 689 Q 143 689 333 482 Q 522 277 522 -18 Q 522 -82 513 -142 Q 235 -206 -63 -206 Z" fill="url(#:sbs)"/><path d="M -125 -729 Q -394 -729 -580 -521 Q -740 -346 -765 -110 Q -430 -206 -63 -206 Q 235 -206 513 -142 Q 481 -358 333 -521 Q 143 -729 -125 -729 Z" fill="url(#:sbt)"/><path d="M 535 158 Q -69 23 -821 183" fill="none" stroke="rgb(188,188,188)" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="1.000" stroke-width="63.481906870545906"/>'
        }
      ]

      function init() {
        document.addEventListener('mousemove', mousemove);
        setInterval(loop, 1000 / fps);
      }

      function mousemove(e) {
        mouse = {x: e.clientX, y: e.clientY}
      }

      function loop() {
        for (var i = 0; i < parts.length; i++) {
          var params = {mouse: mouse, part: parts[i]};
          setTimeout(transform, parts[i].z * delay, params);
        }
        ;
        element.innerHTML = svg();
      }

      function svg() {
        var svg = '<svg color-interpolation-filters="sRGB" style="fill-rule: evenodd; pointer-events: none; -moz-user-select: none; width: 100%; height: 100%;">';
        for (var i = 0; i < parts.length; i++) {
          svg += '<g transform="matrix(0.05 0 0 0.05 ' + parts[i].x + " " + parts[i].y + ')">';
          svg += parts[i].data;
          svg += '</g>';
        }
        ;
        svg += defs;
        svg += '</svg>';
        return svg;
      }

      function transform(params) {
        params.part.x = definemaxspeed(params.mouse.x - params.part.x) / easy + params.part.x;
        params.part.y = definemaxspeed(params.mouse.y - params.part.y) / easy + params.part.y;
      }

      function definemaxspeed(speed) {
        if (speed > 0 && speed > maxspeed) return maxspeed;
        if (speed < 0 && speed < -maxspeed) return -maxspeed;
        return speed;
      }

      return {init: init};
    })();

    whale.init();
  })
</script>
</#assign>

<#include "../layout/layout-main.ftl" />
