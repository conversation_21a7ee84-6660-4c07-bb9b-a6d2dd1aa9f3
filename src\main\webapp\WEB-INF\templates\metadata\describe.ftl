<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
  <h1>对象查询</h1>
  <ol class="breadcrumb">
    <li><a href="${ctx}"><i class="fa fa-dashboard"></i>任务主页</a></li>
    <li><a href=""><i class="fa fa-dashboard"></i>对象查询</a></li>
  </ol>
  <div id="warning" class="alert alert-warning alert-dismissible hide" role="alert">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
            aria-hidden="true">&times;</span></button>
    <strong>警告!</strong> 请输入tenantId.
  </div>
  <div id="warningInfo" class="alert alert-warning hide">
    <span id="closeInfo" href="#" class="close">&times;</span>
    <strong id="ajaxInfo"></strong>
  </div>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <div class="box-header with-border">
          <form class="form-inline" method="post" id="findForm" role="form" data-toggle="validator">
            <div class="form-group">
              <label for="tenantId">tenant_id</label>
              <input type="text" style="border-radius:5px;" name="tenantId" class="form-control" id="tenantId" value="${tenant_id!}"
                     placeholder="tenant_id(必填)" required>
            </div>
            <div class="form-group">
              <label for="apiName">api_name</label>
              <input type="text" style="border-radius:5px;" name="apiName" class="form-control" id="apiName" value="${describe_api_name!}"
                     placeholder="api_name(选填)">
            </div>
            <div class="form-group">
              <label for="displayName">display_name</label>
              <input type="text" style="border-radius:5px;" name="displayName" class="form-control" id="displayName" value="${describe_display_name!}"
                     placeholder="display_name(选填)">
            </div>
            <button type="button" id="findSubmit" class="btn btn-primary">查询对象描述</button>
          </form>
        </div>
        <div class="box-body hide" id="container" style="min-width:400px;height:400px;">
        <#--展示图-->
        </div>
        <div class="message"></div>
        <div class="box-body" id="tableBody">
          <table id="datatable" class="table table-hover table-bordered" cellspacing="0" width="100%">
            <thead>
            <tr>
              <th>对象ID</th>
              <th>企业ID</th>
              <th>api_name</th>
              <th>storeTableName</th>
              <th>索引名</th>
              <th>对象名称</th>
              <th>修改时间</th>
              <th>详情</th>
            </tr>
            </thead>
          </table>
        </div>
        <div class="box-footer clearfix">
        </div>
      </div>
    </div>
  </div>
</section>
<#--模态框-->
<div class="modal fade" id="infoModal" tabindex="-1" role="dialog" aria-labelledby="infoModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                class="sr-only">Close</span></button>
        <h4 class="modal-title" id="stopModalLabel">describe详情</h4>
      </div>
      <div class="modal-body">
                <pre id="describeInfo" style="height: 400px" readonly>
                </pre>
      </div>
      <div class="modal-footer">
      </div>
    </div>
  </div>
</div>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/js/highcharts.js"></script>
<script src="https://img.hcharts.cn/highcharts/modules/data.js"></script>
<#--高亮显示-->
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<#--表单验证插件-->
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<script type="application/javascript">
  $(document).ready(function () {

    var table = $("#datatable").DataTable({
      "deferRender": true,
      "ajax": "",
      "language": {
        "url": "${ctx}/static/js/datatables-zh_CN.json"
      },
      "columnDefs": [
        {
          "targets": 2,
          "render": function (data, type, row, meta) {
            if (type === 'display') {
              return "<a href='${ctx}/metadata/describe-query?tenantId=" + row[1] + "&describeId=" + row[0] + "'>" + data + "</a>";
            }
            return data;
          }
        },
        {
          "targets": 7,
          "render": function (data, type, row, meta) {
            if (type === 'display') {
              return "<button value='" + row + "' onclick='describeInfo(value)' class='btn btn-xs btn-info'>详情信息</button>";
            }
            return data;
          }
        }
      ],
      "mark": true,
      "iDisplayLength": 50,
      "bSortClasses": true,
      "order": [[4, 'desc']],
      "bAutoWidth": false
    });

    //查询
    $('#findSubmit').on('click', function () {
      var tenantId = $('#tenantId').val();
      var apiName = $('#apiName').val();
      var displayName = $('#displayName').val();
      if (tenantId === null || tenantId === "") {
        $('#warning').removeClass("hide");
        return;
      } else {
        $('#warning').addClass("hide");
        if (apiName !== "") {
          ajaxChart(tenantId, apiName);
        } else if(displayName==""){
          // findChart(tenantId, apiName);
          alert("apiName和displayName不能都有空");
          return;
        }
      }
      $('#datatable').dataTable().fnClearTable(false);
      table.ajax.url("${ctx}/metadata/describe/list?tenantId=" + tenantId + "&apiName=" + apiName + "&displayName=" + displayName).load();
    });

    //        点击统计柱状图跳转后
    if ($('#apiName').val() !== "") {
      var tenantId = $('#tenantId').val();
      var apiName = $('#apiName').val();
      $('#container').removeClass('hide');
      ajaxChart(tenantId, apiName);
    }

    function findChart(tenantId, describeApiName) {
      $('#warningInfo').addClass('hide');
      var dataObject = {
        tenantId: tenantId,
        describeApiName: describeApiName,
        startTime: "",
        endTime: ""
      };
      $.ajax({
        url: "${CONTEXT_PATH}/metadata/stat/find",
        contentType: "application/json",
        data: JSON.stringify(dataObject),
        dataType: "json",
        type: "POST",
        traditional: true,
        success: function (data) {
          if (data.code === 200) {
            var xNameNum = data.xNameNum;
            var xName = data.xName;
            $('#container').removeClass('hide');
            var chartName = "";
            var chartType = "";
            var chartColor = "";
            if ($('#apiName').val() !== "") {
              chartName = "近三个月对象数据量变化图";
              chartType = "line";
              chartColor = "#47B648";
            } else {
              chartName = "对象数据概况统计图";
              chartType = "column";
              chartColor = "#3985FF"
            }
            showChart(xNameNum, xName, chartName, chartType, chartColor);
          } else {
            $('#ajaxInfo').html(data.error);
            $('#warningInfo').removeClass('hide');
            console.log(data.errorInfo);
          }
        }
      });
    }

//        ajax获取图表
    function ajaxChart(tenantId, apiName) {
      $('#container').removeClass('hide');
      var chart = null;
      $.getJSON('${ctx}/metadata/describe/ajax-chart?tenant_id=' + tenantId + "&describe_api_name=" + apiName, function (data) {
        if (data.mapList === null) {
          return;
        }
        chart = Highcharts.chart('container', {
          chart: {
            zoomType: 'x'
          },
          title: {
            text: '近三个月对象数据变化走势图'
          },
          subtitle: {
            text: document.ontouchstart === undefined ? '' : ''
//                                '鼠标拖动可以进行缩放' : '手势操作进行缩放'
          },
          xAxis: {
            type: 'datetime',
            dateTimeLabelFormats: {
              millisecond: '%H:%M:%S.%L',
              second: '%H:%M:%S',
              minute: '%H:%M',
              hour: '%H:%M',
              day: '%m-%d',
              week: '%m-%d',
              month: '%Y-%m',
              year: '%Y'
            }
          },
          credits: {
            enabled: false
          },
          tooltip: {
            dateTimeLabelFormats: {
              millisecond: '%H:%M:%S.%L',
              second: '%H:%M:%S',
              minute: '%H:%M',
              hour: '%H:%M',
              day: '%Y-%m-%d',
              week: '%m-%d',
              month: '%Y-%m',
              year: '%Y'
            }
          },
          yAxis: {
            title: {
              text: ''
            }
          },
          legend: {
            enabled: false
          },
          plotOptions: {
            area: {
              fillColor: {
                linearGradient: {
                  x1: 0,
                  y1: 0,
                  x2: 0,
                  y2: 1
                },
                stops: [
//                                    趋势模块阴影
//                                    [0, Highcharts.getOptions().colors[0]],
//                                    [1, Highcharts.Color(Highcharts.getOptions().colors[0]).setOpacity(0).get('rgba')]
                ]
              },
              marker: {
                radius: 2
              },
              lineWidth: 1,
              states: {
                hover: {
                  lineWidth: 1
                }
              },
              threshold: null
            }
          },
          series: [{
            type: 'spline',
            name: '数据量',
            data: data.mapList
          }]
        });
      });

    }

  });

  //模态框
  function describeInfo(row) {
    $('#describeInfo').text("");
    var rows = row.split(",");
    // console.log("rows:" + rows + "tenantId:" + rows[1] + ",describeId:" + rows[0]);
    $.ajax({
      url: "${ctx}/metadata/describe/info?tenantId=" + rows[1] + "&describeId=" + rows[0],
      contentType: "application/json",
      type: "GET",
      traditional: true,
      success: function (data) {
        // var info = JSON.stringify(data.describeInfo);
        $('#describeInfo').JSONView(data.describeInfo, {
          collapsed: false,
          nl2br: true,
          recursive_collapser: true
        });
      }
    });
    $('#infoModal').modal('show');
  };

  //    展示示意图
  function showChart(dataList, xName, chartName, chartType, chartColor) {
    $('#container').highcharts({
      chart: {
        type: chartType
      },
      title: {
        text: chartName
      },
      subtitle: {
//                数据来源:
        text: ''
      },
      xAxis: {
        categories: xName,
        // 生成可以点击的横坐标----失败！
        labels: {
          formatter: function () {
            return "<span id='xNameBtn'>" + this.value + "</span>";
          }
        },
//        categories:pNameList,
        crosshair: true,
        title: {
//                    横坐标名称
          text: ''
        }
      },
      yAxis: {
//                禁用刻度值
        labels: {
          enabled: false
        },
        min: 0,
        tickInterval: 2, // 刻度值
        title: {
          text: '数据量变化'
        }
      },
      credits: {
        enabled: false, // 禁用版权信息
        href: 'https://www.fxiaoke.com/',
        text: 'Copyright:纷享销客',
        style: {
          cursor: 'pointer',
          color: '#909090',
          fontSize: '10px'
        }
      },
//      自定义提示内容------------将柱状图信息合并
      tooltip: {
        shared: true,
        useHTML: true,
        borderRadius: 10,             // 边框圆角
      },
      plotOptions: {
        column: {
          borderWidth: 0,
//                    柱状图点击事件
          cursor: 'pointer',
          point: {
            events: {
              click: function () {
                $('#apiName').val(this.category);
                $('#findSubmit').trigger('click');
              }
            }
          }
        }
      },
      series: [{
        name: '数量',
        data: dataList,
        color: chartColor
      }
      ]
    });
  };

  /**
   * 信息提示栏关闭
   */
  $('#closeInfo').on('click', function () {
    $('#warningInfo').addClass('hide');
  });
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
