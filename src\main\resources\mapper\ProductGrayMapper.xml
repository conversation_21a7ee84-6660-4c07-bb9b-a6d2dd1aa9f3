<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fxiaoke.paas.console.license.mapper.ModuleParaMapper">

  <resultMap id="masterLicense" type="com.facishare.paas.license.pojo.MasterLicensePojo">
    <result property="moduleName" column="module_name"/>
    <result property="moduleCode" column="module_code"/>
    <result property="paraKey" column="para_key"/>
    <result property="paraValue" column="para_value"/>
  </resultMap>

  <select id="selectAllPara" resultMap="masterLicense">
    select distinct mi.module_name ,mi.module_code ,mp.para_key ,mp.para_value
    from module_info AS mi inner join module_para AS mp on mi.id = mp.module_id
    where mp.tenant_id = #{tenantId} AND mi.tenant_id = #{tenantId} AND mp.del_flag = FALSE AND mi.del_flag = FALSE
    <if test="productIds != null">
      AND mi.product_id IN
      <foreach collection="productIds" item="productId" open="(" separator="," close=")">
        #{productId}
      </foreach>
    </if>
  </select>

  <select id="selectAllParaByModuleId" resultMap="masterLicense">
    select distinct mi.module_name ,mi.module_code ,mp.para_key ,mp.para_value
    from module_info AS mi inner join module_para AS mp on mi.id = mp.module_id
    where mp.tenant_id = #{tenantId} AND mi.tenant_id = #{tenantId} AND mp.del_flag = FALSE AND mi.del_flag = FALSE
    and  mi.id  = #{moduleId}
  </select>

  <select id="selectPara" resultType="com.facishare.paas.license.pojo.ModuleParaPojo">
    select distinct mi.module_name, mi.module_code, mp.para_key, mp.para_value
    from module_info AS mi
           inner join module_para AS mp on mi.id = mp.module_id
    where mp.tenant_id = #{tenantId}
      AND mi.tenant_id = #{tenantId}
      AND mp.del_flag = FALSE
      AND mi.del_flag = FALSE
      AND mi.product_id = #{productId}  limit 1
  </select>

</mapper>
