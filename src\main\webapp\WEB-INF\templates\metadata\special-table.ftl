<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css" xmlns="http://www.w3.org/1999/html" xmlns="http://www.w3.org/1999/html"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
    <h1>专表统计管理</h1>
    <ol class="breadcrumb">
        <li><a href="../"><i class="fa fa-dashboard"></i>任务主页</a></li>
        <li><a href=""><i class="fa fa-dashboard"></i>专表统计管理</a></li>
    </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-header with-border">
                    <ul class="nav nav-tabs" id="myTab">
                        <li role="presentation"><a href="#createDesigned" data-toggle="tab">创建</a></li>
                        <li role="presentation" class="active"><a href="#findDesigned" data-toggle="tab">列表</a></li>
                    </ul>
                </div>
                <div class="box-body">
                    <div class="tab-content">
                        <div class="tab-pane fade" id="createDesigned">
                            <form class="form-horizontal" action="${ctx}/table/special/created" method="post" id="myForm" role="form" data-toggle="validator">
                                <div class="form-group">
                                    <label for="describeApiName" class="col-sm-2 control-label">DescribeApiName</label>
                                    <div class="col-sm-4">
                                        <input type="text" class="form-control" style="border-radius:5px;" id="describeApiName" name="describeApiName" value="" placeholder="必填" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="label" class="col-sm-2 control-label">标签</label>
                                    <div class="col-sm-4">
                                        <input type="text" class="form-control" style="border-radius:5px;" id="label" name="label" value="" placeholder="必填" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="storeTableName" class="col-sm-2 control-label">专表名称</label>
                                    <div class="col-sm-4">
                                        <input type="text" class="form-control" style="border-radius:5px;" id="storeTableName" name="storeTableName" value="" placeholder="必填" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="team" class="col-sm-2 control-label">团队</label>
                                    <div class="col-sm-4">
                                        <input type="text" class="form-control" style="border-radius:5px;" id="team" name="team" value="" placeholder="必填" required>
                                    </div>
                                </div>
                                <div class="col-sm-offset-3">
                                    <button id="createdBtn" type="submit" class="btn btn-primary">创建</button>
                                <#--<button id="nextStep" type="button" class="btn btn-warning">下一步</button>-->
                                </div>
                                <br><br>
                            <#--下一步区域-->
                                <div class="form-group nextStep hide">
                                    <div class="form-group">
                                        <label for="tenantId" class="col-sm-2 control-label">指标</label>
                                        <div class="col-sm-4">
                                            <input type="text" class="form-control" style="border-radius:5px;" id="isIndex" name="isIndex" value="" placeholder="必填">
                                        </div>
                                    </div>
                                    <div class="col-sm-offset-3 col-sm-3">
                                        <button id="createdBtn" type="submit" class="btn btn-primary">创建</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="tab-pane fade in active" id="findDesigned">
                            <table id="datatables" class="table table-hover table-bordered" cellpadding="0" width="100%">
                                <thead>
                                <tr>
                                    <th>Id</th>
                                    <th>DescribeApiName</th>
                                    <th>标签</th>
                                    <th>专表名称</th>
                                    <th>状态</th>
                                    <th>团队</th>
                                    <th>创建时间</th>
                                    <th>修改时间</th>
                                    <th>修改人</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                            </table>
                        </div>

                        <div class="tab-pane fade" id="">
                        </div>
                    </div>
                </div>
                <div class="box-footer clearfix">
                    <div class="clearfix"></div>
                </div>
            </div>
        </div>
    </div>
</section>
<#--编辑页面-->
<div class="modal fade" id="editStoreTableModal" tabindex="-1" role="dialog" aria-labelledby="infoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                    class="sr-only">Close</span></button>
                <h4 class="modal-title" id="stopModalLabel">编辑</h4>
            </div>
            <form class="form-horizontal" action="${ctx}/table/special/edit" method="post" role="form" data-toggle="validator">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="id" class="col-sm-3 control-label">ID</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" style="border-radius:5px;" id="idEdit" name="id" value="" placeholder="必填" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="describeApiName" class="col-sm-3 control-label">DescribeApiName</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" style="border-radius:5px;" id="describeApiNameEdit" name="describeApiName" value="" placeholder="必填" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="label" class="col-sm-3 control-label">标签</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" style="border-radius:5px;" id="labelEdit" name="label" value="" placeholder="必填" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="storeTableName" class="col-sm-3 control-label">专表名称</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" style="border-radius:5px;" id="storeTableNameEdit" name="storeTableName" value="" placeholder="必填" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="status" class="col-sm-3 control-label">状态</label>
                        <div class="col-sm-4">
                            <select id="status" name="status" style="border-radius:5px;" class="selectpicker show-tick form-control" title="" data-live-search="false" required>
                                <option id="using">using</option>
                                <option id="deprecated">deprecated</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="team" class="col-sm-3 control-label">团队</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" style="border-radius:5px;" id="teamEdit" name="team" value="" placeholder="必填" required>
                        </div>
                    </div>

                </div>
                <div class="modal-footer">
                    <a type="button" class="btn btn-default" data-dismiss="modal">取消</a>
                    <button type="submit" class="btn btn-primary">提交</button>
                </div>
            </form>
        </div>
    </div>
</div>
<#--获取sql-->
<div class="modal fade" id="getSqlModal" tabindex="-1" role="dialog" aria-labelledby="infoModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="width: 50%">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                    class="sr-only">Close</span></button>
                <h4 class="modal-title" id="stopModalLabel">建表SQL</h4>
            </div>
            <div class="modal-body">
                    <pre id="theSql" style="height: 450px">
                    </pre>
            </div>
        </div>
    </div>
</div>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script>

    function checkStatus(data) {
        if (data === "using") {
            return "<span class='btn btn-success btn-xs'>using</span>"
        } else {
            return "<span class='btn btn-danger btn-xs'>deprecated</span>"
        }
    }

    $(document).ready(function () {
        // 下一步
        $('#nextStep').on('click', function () {
            $('.nextStep').removeClass('hide');
        });

        var table = $('#datatables').DataTable({
            "processing": true,
            "ajax": "${ctx}/table/special/get-store-table",
            "columnDefs": [
                {
                    "targets": 4,
                    "render": function (data, type, row, meta) {
                        return checkStatus(data);
                    }
                },
                {
                    "targets": 9,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return "<button value='" + data + "' onclick='editStoreTable(value)' class='btn btn-info btn-xs'>编辑</button>";
                                    // + " <button value='" + data + "' onclick='getSql(value)' class='btn btn-danger btn-xs'>建表</button>";
                        }
                        return data;
                    }
                }
            ],
            "language": {
                "url": "${ctx}/static/js/datatables-zh_CN.json"
            },
            // "dom": "<''<'col-sm-2'l><'col-sm-8'B><'col-sm-2'f>><''<'col-sm-12'tr>><''<'col-sm-5'i><'col-sm-7'p>>",
            // "buttons": [
            //     {
            //         "className": "btn btn-danger",
            //         "text": '<span style="color:white">业务方创建专表，请在此页面维护；点击操作栏对应的【建表】按钮生成对应的创建数据权限表的SQL语句，和专表的建表SQL（业务方自行准备）申请一起提交给DBA。</span>',
            //         "action": function (e, dt, node, config) {
            //         }
            //     }
            // ],
            "displayLength": 25,
            "mark": true,
            "order": [[6, 'desc']]
        });

    });

    function editStoreTable(id) {
        $.getJSON("${CONTEXT_PATH}/table/special/findById", {
            id: id
        }, function (storeTable) {
            var it = storeTable[1];
            $('#idEdit').val(it.id);
            $('#describeApiNameEdit').val(it.describeApiName);
            $('#labelEdit').val(it.label);
            $('#storeTableNameEdit').val(it.storeTableName);
            $('#teamEdit').val(it.team);
            if (it.status === "using") {
                $('#using').attr('selected', true);
            } else {
                $('#deprecated').attr('selected', true);
            }

            $('#editStoreTableModal').modal('show');
        });

    }

    function getSql(id) {
        $.getJSON("${CONTEXT_PATH}/table/special/getSql", {
            id: id
        }, function (sql) {
            let creatsTableSql = sql[1];
            $('#theSql').html(creatsTableSql);
            $('#getSqlModal').modal('show');
        })
    }

</script>
</#assign>
<#include "../layout/layout-main.ftl" />
