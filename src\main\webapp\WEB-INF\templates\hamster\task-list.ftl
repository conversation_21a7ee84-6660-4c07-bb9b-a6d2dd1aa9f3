<#assign headContent>
    <link href="//static.foneshare.cn/oss/AdminLTE/plugins/datatables/dataTables.bootstrap.css" rel="stylesheet"
          type="text/css"/>
    <link type="text/css" href="//static.foneshare.cn/oss/datatables-checkboxes/dataTables.checkboxes.css"
          rel="stylesheet"/>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
    <style>
        #datatable td {
            text-align: left;
        }


        .modal-content {

            overflow-y: scroll;

            position: absolute;

            top: 0;

            bottom: 0;

            left: -150px;

            width: 1000px;

            height: 800px;
        }

        .modal-body {

            overflow-y: scroll;

            position: absolute;

            top: 55px;

            bottom: 65px;

            width: 100%;
        }

        .modal-header .close {
            margin-right: 15px;
        }

        .modal-footer {

            position: absolute;

            width: 100%;

            bottom: 0;

        }


    </style>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>任务列表</h1>
        <ol class="breadcrumb">
            <li><a href="../"><i class="fa fa-dashboard"></i>任务主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>Schema迁移任务查询</a></li>
        </ol>
    </section>
</#assign>

<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div id="warningInfo" class="alert alert-warning hide">
                    <span id="closeInfo" href="#" class="close">&times;</span>
                    <strong id="hitInfo"></strong>
                </div>
                <div class="box box-info" style="margin-bottom: 1px;">
                    <div class="box-body" style="padding: 20px;">
                        <form class="form-inline">
                            <button type="button" id="createTask" onclick="batchMigration()" class="btn btn-primary">批量迁移</button>
                            <button type="button" class="btn btn-default" style="float: right" onclick="history.back()">返回</button>
                        </form>
                    </div>
                    <div class="box-footer clearfix" style="padding: 2px;">
                        <div class="clearfix"></div>
                    </div>
                </div>
                <div class="box box-info">
                    <div class="box-body">
                        <table id="datatable" class="table table-hover table-bordered" cellspacing="0" width="100%">
                            <thead>
                            <tr>
                                <th><input type="checkbox" name="head" id="checkAll" class="checkAll">
                                    <label for="checkAll" class="text-center"></label></th>
                                <th id="taskId" class="col-sm-1" style="table-layout: fixed;">ID</th>
                                <th>计划Id</th>
                                <th>模板Id</th>
                                <th>name</th>
                                <th>EI</th>
                                <th>fromSchema</th>
                                <th>toSchema</th>
                                <th>当前库所在云</th>
                                <th>目标库所在云</th>
                                <th>当前服务云环境</th>
                                <th>目标服务云环境</th>
                                <th>状态</th>
                                <th>当前运行步骤</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="box-footer clearfix">
                    </div>
                </div>
            </div>
        </div>
    </section>
<#--任务详情-->
    <div class="modal fade" id="taskInfoModal" tabindex="-1" role="dialog" aria-labelledby="infoModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                                class="sr-only">Close</span></button>
                    <h4 class="modal-title" id="stopModalLabel">Schema任务详情</h4>
                </div>
                <div class="modal-body">
                <pre id="taskInfo" style="height: 600px">
                </pre>
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>

<#--编辑页面-->
    <div class="modal fade" id="editTaskModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                                class="sr-only">Close</span></button>
                    <h4 class="modal-title" id="stopModalLabel">编辑</h4>
                </div>
                <form class="form-horizontal" action="${ctx}/hamster/edit_task" method="post" role="form" data-toggle="validator">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="taskIdEdit" class="col-sm-3 control-label">ID</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="taskIdEdit" name="taskId" value="" placeholder="必填" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="planIdEdit" class="col-sm-3 control-label">planId</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px;  width: 500px" id="planIdEdit" name="planId" value="" placeholder="必填" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="templateIdEdit" class="col-sm-3 control-label">templateId</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px;  width: 500px" id="templateIdEdit" name="templateId" value="" placeholder="必填" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="taskNameEdit" class="col-sm-3 control-label">taskName</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px;  width: 500px" id="taskNameEdit" name="taskName" value="" placeholder="必填">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="tenantIdEdit" class="col-sm-3 control-label">EI</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="tenantIdEdit" name="tenantId" value="" placeholder="必填" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="tenantNameEdit" class="col-sm-3 control-label">企业名称</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="tenantNameEdit" name="tenantName" value="" placeholder="必填" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="fromSchemaEdit" class="col-sm-3 control-label">fromSchema</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="fromSchemaEdit" name="fromSchema" value="" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="toSchemaEdit" class="col-sm-3 control-label">toSchema</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="toSchemaEdit" name="toSchema" value="" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="serviceCloudEdit" class="col-sm-3 control-label">当前服务所在云</label>
                            <div class="col-sm-4">
                                <select id="serviceCloudEdit" name="serviceCloud" data-width="400px" class="selectpicker show-tick form-control" title="" data-live-search="false" placeholder="必填" required>
                                    <option id="serviceFsTest" value="fstest">fstest</option>
                                    <option id="serviceFoneshare" value="foneshare">foneshare</option>
                                    <option id="serviceAlePublicProd" value="ale-public-prod">ale-public-prod</option>
                                    <option id="serviceCloudModulePublicProd" value="cloudmodel-public-prod">cloudmodel-public-prod</option>
                                    <option id="serviceForceecrmPublicProd" value="forceecrm-public-prod">forceecrm-public-prod</option>
                                    <option id="serviceHisensePublicProd" value="hisense-public-prod">hisense-public-prod</option>
                                    <option id="serviceHuaweiCloudPublicProd" value="huaweicloud-public-prod">huaweicloud-public-prod</option>
                                    <option id="serviceHuaweiCloudSbtProd" value="huaweicloud-sbt-prod">huaweicloud-sbt-prod</option>
                                    <option id="serviceHwsPublicProd" value="hws-public-prod">hws-public-prod</option>
                                    <option id="serviceKsdKscProd" value="ksc-ksc-prod">ksc-ksc-prod</option>
                                    <option id="serviceUcdPublicProd" value="ucd-public-prod">ucd-public-prod</option>
                                    <option id="serviceUcdPublicTest" value="ucd-public-test">ucd-public-test</option>
                                    <option id="serviceXjgcPublicProd" value="xjgc-public-prod">xjgc-public-prod</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="currentCloudEdit" class="col-sm-3 control-label">当前库所在云</label>
                            <div class="col-sm-4">
                                <select id="currentCloudEdit" name="currentCloud" data-width="400px" class="selectpicker show-tick form-control" title="" data-live-search="false" placeholder="必填" required>
                                    <option id="currentFsTest" value="fstest">fstest</option>
                                    <option id="currentFoneshare" value="foneshare">foneshare</option>
                                    <option id="currentAlePublicProd" value="ale-public-prod">ale-public-prod</option>
                                    <option id="currentCloudModulePublicProd" value="cloudmodel-public-prod">cloudmodel-public-prod</option>
                                    <option id="currentForceecrmPublicProd" value="forceecrm-public-prod">forceecrm-public-prod</option>
                                    <option id="currentHisensePublicProd" value="hisense-public-prod">hisense-public-prod</option>
                                    <option id="currentHuaweiCloudPublicProd" value="huaweicloud-public-prod">huaweicloud-public-prod</option>
                                    <option id="currentHuaweiCloudSbtProd" value="huaweicloud-sbt-prod">huaweicloud-sbt-prod</option>
                                    <option id="currentHwsPublicProd" value="hws-public-prod">hws-public-prod</option>
                                    <option id="currentKsdKscProd" value="ksc-ksc-prod">ksc-ksc-prod</option>
                                    <option id="currentUcdPublicProd" value="ucd-public-prod">ucd-public-prod</option>
                                    <option id="currentUcdPublicTest" value="ucd-public-test">ucd-public-test</option>
                                    <option id="currentXjgcPublicProd" value="xjgc-public-prod">xjgc-public-prod</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="targetCloudEdit" class="col-sm-3 control-label">目标库所在云</label>
                            <div class="col-sm-4">
                                <select id="targetCloudEdit" name="targetCloud" data-width="400px" class="selectpicker show-tick form-control" title="" data-live-search="false" placeholder="必填" required>
                                    <option id="targetFsTest" value="fstest">fstest</option>
                                    <option id="targetFoneshare" value="foneshare">foneshare</option>
                                    <option id="targetAlePublicProd" value="ale-public-prod">ale-public-prod</option>
                                    <option id="targetCloudModulePublicProd" value="cloudmodel-public-prod">cloudmodel-public-prod</option>
                                    <option id="targetForceecrmPublicProd" value="forceecrm-public-prod">forceecrm-public-prod</option>
                                    <option id="targetHisensePublicProd" value="hisense-public-prod">hisense-public-prod</option>
                                    <option id="targetHuaweiCloudPublicProd" value="huaweicloud-public-prod">huaweicloud-public-prod</option>
                                    <option id="targetHuaweiCloudSbtProd" value="huaweicloud-sbt-prod">huaweicloud-sbt-prod</option>
                                    <option id="targetHwsPublicProd" value="hws-public-prod">hws-public-prod</option>
                                    <option id="targetKsdKscProd" value="ksc-ksc-prod">ksc-ksc-prod</option>
                                    <option id="targetUcdPublicProd" value="ucd-public-prod">ucd-public-prod</option>
                                    <option id="targetUcdPublicTest" value="ucd-public-test">ucd-public-test</option>
                                    <option id="targetXjgcPublicProd" value="xjgc-public-prod">xjgc-public-prod</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="jdbcOldPaasEdit" class="col-sm-3 control-label">PaaS老地址</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="jdbcOldPaasEdit" name="jdbcOldPaas" value="" placeholder="必填" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="jdbcNewPaasMasterEdit" class="col-sm-3 control-label">PaaS新地址(主库)</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="jdbcNewPaasMasterEdit" name="jdbcNewPaasMaster" value="" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="jdbcNewPaasSlaveEdit" class="col-sm-3 control-label">PaaS新地址(从库)</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="jdbcNewPaasSlaveEdit" name="jdbcNewPaasSlave" value="" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="jdbcOldBiEdit" class="col-sm-3 control-label">BI老地址</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="jdbcOldBiEdit" name="jdbcOldBi" value="" placeholder="必填" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="jdbcNewBiMasterEdit" class="col-sm-3 control-label">BI新地址(主库)</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="jdbcNewBiMasterEdit" name="jdbcNewBiMaster" value="" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="jdbcNewBiSlaveEdit" class="col-sm-3 control-label">BI新地址(从库)</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="jdbcNewBiSlaveEdit" name="jdbcNewBiSlave" value="" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="statusEdit" class="col-sm-3 control-label">状态</label>
                            <div class="col-sm-4">
                                <select id="statusEdit" name="status" size="400px" data-width="400px" class="selectpicker show-tick form-control" title="" data-live-search="false" placeholder="必填" readonly>
                                    <option id="ready" value="0">ready</option>
                                    <option id="running" value="1">running</option>
                                    <option id="success" value="2">success</option>
                                    <option id="error" value="3">error</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="paasPgbouncerOldEdit" class="col-sm-3 control-label">Paas老PGBOUNCER</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="paasPgbouncerOldEdit" name="paasPgbouncerOld" value="" placeholder="必填" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="paasPgbouncerNewMasterEdit" class="col-sm-3 control-label">Paas新PGBOUNCER（主）</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="paasPgbouncerNewMasterEdit" name="paasPgbouncerNewMaster" value="" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="paasPgbouncerNewSlaveEdit" class="col-sm-3 control-label">Paas新PGBOUNCER（从）</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="paasPgbouncerNewSlaveEdit" name="paasPgbouncerNewSlave" value="" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="biPgbouncerOldEdit" class="col-sm-3 control-label">BI老PGBOUNCER</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="biPgbouncerOldEdit" name="biPgbouncerOld" value="" placeholder="必填" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="biPgbouncerNewMasterEdit" class="col-sm-3 control-label">BI新PGBOUNCER（主）</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="biPgbouncerNewMasterEdit" name="biPgbouncerNewMaster" value="" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="biPgbouncerNewSlaveEdit" class="col-sm-3 control-label">BI新PGBOUNCER（从）</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px; width: 500px" id="biPgbouncerNewSlaveEdit" name="biPgbouncerNewSlave" value="" placeholder="必填" required>
                            </div>
                        </div>
                        <a type="button" class="btn btn-default" style="float: right" data-dismiss="modal">取消</a>
                        <button type="button" class="btn btn-primary" style="float: right" onclick="commitEditSchemaTask()">提交</button>
                    </div>

                </form>
            </div>
        </div>
    </div>

</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script type="application/javascript">
        var isRunning = false;
        $('#statusEdit').attr("disabled", "readonly");

        function checkStatus(data) {
            if (data == 0) {
                return "<span class='btn btn-success btn-xs'>ready</span>"
            } else if (data == 1) {
                isRunning = true;
                return "<span class='btn btn-warning btn-xs'>running</span>"
            } else if (data == 2) {
                return "<span class='btn btn-success btn-xs'>success</span>"
            } else if (data == 3) {
                return "<span class='btn btn-danger btn-xs'>error</span>"
            }
        }

        function checkStatusAction(data, status) {
            if (status == 0) {
                return "<span class='btn btn-success btn-xs'>任务未运行</span>"
            } else if (status == 2) {
                return "<span class='btn btn-success btn-xs'>任务成功</span>"
            } else {
                if (data == null) {
                    return "<span class='btn btn-success btn-xs'>无runningItem</span>"
                } else {
                    return "<span class='btn btn-success btn-xs'>" + data + "</span>"
                }
            }
        }

        $(document).ready(function () {
            var table = $("#datatable").DataTable({
                "processing": true,
                "ajax": {
                    url: "${ctx}/hamster/hamster-task-list?taskId=default",
                    type: 'GET'
                },
                "order":
                    [[14, "desc"]]
                ,
                "columnDefs": [
                    {
                        "orderable": false,
                        "targets": [0]
                    },
                    {
                        "targets": 0,
                        "data": null,
                        "render": function (data, type, row) {
                            return data = '<input type="checkbox" class="checkOne" onclick="add_Goods(this)" id="check_item_' + row[1] + '" >' +
                                '<label for="check_item_' + row[1] + '" id="checkOne" ></label>';
                        },
                        "sClass": "text-center"
                    },
                    {
                        "targets": 1,
                        "visible": false
                    },
                    {
                        "targets": 2,
                        "visible": false
                    },
                    {
                        "targets": 3,
                        "visible": false
                    },
                    {
                        "targets": 12,
                        "render": function (data, type, row, meta) {
                            return checkStatus(data);
                        }
                    },
                    {
                        "targets": 13,
                        "render": function (data, type, row, meta) {
                            return checkStatusAction(row[13], row[12]);
                        }
                    },
                    {
                        // 定义操作列,######以下是重点########
                        "targets": 15,//操作按钮目标列
                        "render": function (data, type, row, meta) {
                            var html = "<button class='btn btn-info btn-xs' onclick='editHamsterTask(\"" + row[1] + "\")'>编辑</button>"
                            var url = "${ctx}/hamster/task-item-list?taskId=" + row[1];
                            html += "<a href='" + url + " ' class='btn btn-info btn-xs'>查看步骤</a>"
                            if(row[15] == 0) {
                                html += "<button class='btn btn-secondary btn-xs' onclick='softDeleteHamsterTask(\"" + row[1] + "\")'>作废</button>";
                            }else if(row[15] == 1){
                                html += "<button class='btn btn-danger btn-xs' onclick='deleteHamsterTask(\"" + row[1] + "\")'>删除</button>";
                            }
                            return html;

                        }
                    }
                ],
                "language": {
                    "url": "${ctx}/static/js/datatables-zh_CN.json"
                },
                "mark": true,
                "displayLength": 25
            });
//        提交
            $('#findSub').on('click', function () {
                $('#warningInfo').addClass('hide');
                $("#datatable").dataTable().fnClearTable();
                var tenantId = $('#tenantId').val();
                if (tenantId === "") {
                    $('#warningInfo').removeClass('hide');
                    $('#hitInfo').html('关键字段不能为空！');
                    return;
                }
//            将对象转化为json字符串放在url中传递到后端
                table.ajax.url("${ctx}/migration/find-schema-task?tenantId=" + tenantId).load();
            });
            //定时器
            var timer = setInterval(myrefresh, 10000); //指定 秒刷新一次
            function myrefresh() {
                reload(timer);
            };

            //重新加载
            function reload(timer) {
                if (isRunning) {
                    ids = '';
                    var dataTable = $("#datatable").DataTable();
                    dataTable.ajax.reload();
                }
            };

        });

        function editHamsterTask(taskId) {
            $.getJSON("${CONTEXT_PATH}/hamster/hamster-task", {
                taskId: taskId
            }, function (data) {
                var taskPojo = data.data;
                $('#taskIdEdit').val(taskPojo.id);
                $('#planIdEdit').val(taskPojo.planId);
                $('#templateIdEdit').val(taskPojo.templateId);
                $('#taskNameEdit').val(taskPojo.name);
                $('#tenantIdEdit').val(taskPojo.tenantId);
                $('#tenantNameEdit').val(taskPojo.tenantName);
                $('#fromSchemaEdit').val(taskPojo.fromSchema);
                $('#toSchemaEdit').val(taskPojo.toSchema);
                $('#jdbcOldPaasEdit').val(taskPojo.jdbcOldPaas);
                $('#jdbcNewPaasMasterEdit').val(taskPojo.jdbcNewPaasMaster);
                $('#jdbcNewPaasSlaveEdit').val(taskPojo.jdbcNewPaasSlave);
                $('#jdbcOldBiEdit').val(taskPojo.jdbcOldBi);
                $('#jdbcNewBiMasterEdit').val(taskPojo.jdbcNewBiMaster);
                $('#jdbcNewBiSlaveEdit').val(taskPojo.jdbcNewBiSlave);
                $('#paasPgbouncerOldEdit').val(taskPojo.paasPgbouncerOld);
                $('#paasPgbouncerNewMasterEdit').val(taskPojo.paasPgbouncerNewMaster);
                $('#paasPgbouncerNewSlaveEdit').val(taskPojo.paasPgbouncerNewSlave);
                $('#biPgbouncerOldEdit').val(taskPojo.biPgbouncerOld);
                $('#biPgbouncerNewMasterEdit').val(taskPojo.biPgbouncerNewMaster);
                $('#biPgbouncerNewSlaveEdit').val(taskPojo.biPgbouncerNewSlave);
                $('#statusEdit.selectpicker').selectpicker('val', taskPojo.status);
                $('#serviceCloudEdit.selectpicker').selectpicker('val', taskPojo.serviceCloud);
                $('#currentCloudEdit.selectpicker').selectpicker('val', taskPojo.currentCloud);
                $('#targetCloudEdit.selectpicker').selectpicker('val', taskPojo.targetCloud);


                $('#editTaskModal').on('hidden.bs.modal', centerModals('editTaskModal')).modal();
            });
        }

        function softDeleteHamsterTask(taskId) {
            var SchemaTaskObject = {
                taskId: taskId,
                soft: "1"
            }
            if (confirm("确定作废吗")) {
                $.ajax({
                    url: "${ctx}/hamster/delete-task",
                    contentType: "application/json",
                    data: JSON.stringify(SchemaTaskObject),
                    dataType: "json",
                    type: "POST",
                    success: function (data) {
                        console.log(data)
                        if (data.code !== 200) {
                            alert(data.info)
                        }
                    }
                });
            }
        }

        function deleteHamsterTask(taskId) {
            var SchemaTaskObject = {
                taskId: taskId,
                soft: "0"
            }
            if (confirm("确定删除吗")) {
                $.ajax({
                    url: "${ctx}/hamster/delete-task",
                    contentType: "application/json",
                    data: JSON.stringify(SchemaTaskObject),
                    dataType: "json",
                    type: "POST",
                    success: function (data) {
                        console.log(data)
                        if (data.code !== 200) {
                            alert(data.info)
                        }
                    }
                });
            }
        }

        function commitEditSchemaTask() {
            var SchemaTaskObject = {
                id: $('#taskIdEdit').val(),
                name: $('#taskNameEdit').val(),
                serviceCloud: $('#serviceCloudEdit').val(),
                planId: $('#planIdEdit').val(),
                templateId: $('#templateIdEdit').val(),
                tenantId: $('#tenantIdEdit').val(),
                tenantName: $('#tenantNameEdit').val(),
                fromSchema: $('#fromSchemaEdit').val(),
                toSchema: $('#toSchemaEdit').val(),
                currentCloud: $('#currentCloudEdit').val(),
                targetCloud: $('#targetCloudEdit').val(),
                jdbcOldPaas: $('#jdbcOldPaasEdit').val(),
                jdbcNewPaasMaster: $('#jdbcNewPaasMasterEdit').val(),
                jdbcNewPaasSlave: $('#jdbcNewPaasSlaveEdit').val(),
                jdbcOldBi: $('#jdbcOldBiEdit').val(),
                jdbcNewBiMaster: $('#jdbcNewBiMasterEdit').val(),
                jdbcNewBiSlave: $('#jdbcNewBiSlaveEdit').val(),
                paasPgbouncerOld: $('#paasPgbouncerOldEdit').val(),
                paasPgbouncerNewMaster: $('#paasPgbouncerNewMasterEdit').val(),
                paasPgbouncerNewSlave: $('#paasPgbouncerNewSlaveEdit').val(),
                biPgbouncerOld: $('#biPgbouncerOldEdit').val(),
                biPgbouncerNewMaster: $('#biPgbouncerNewMasterEdit').val(),
                biPgbouncerNewSlave: $('#biPgbouncerNewSlaveEdit').val(),
                status: $('#statusEdit').val(),
            }
            $.ajax({
                url: '${ctx}/hamster/edit_task',
                contentType: "application/json",
                data: JSON.stringify(SchemaTaskObject),
                dataType: 'json',
                type: 'POST',
                traditional: true,
                success: function (data) {
                    if (data.code === 200) {
                        alert(data.info)
                        $('#editTaskModal').modal('hide');
                        location.reload();  //实现页面重新加载
                    } else {
                        alert(data.info)
                    }
                }
            })
        }

        var ids = '';  //任务id


        $("#checkAll").click(function () {
            var table = $("#datatable").dataTable();
            if ($(this).is(":checked")) {
                $(".checkOne").prop("checked", $(this).prop("checked"));
                $(".checkOne").each(function (o) {
                    ids += table.api().rows({page: 'current'}).data()[o][1] + ',';
                });
            } else {
                $(".checkOne").prop("checked", $(this).prop("checked"));
                $(".checkOne").each(function (o) {
                    ids = ids.replace(table.api().rows({page: 'current'}).data()[o][1] + ',', "");
                });
            }
        });

        //获得单选，选中数据
        function add_Goods(_this) {
            var flag = $(".checkOne:checked").length == $(".checkOne").length;
            $(".checkAll").prop("checked", flag);
            var nRow = $(_this).parents('tr')[0];//得到这一行
            var table = $("#datatable").dataTable();
            var aData = table.fnGetData(nRow);//得到这一行的json数据
            if (_this.checked) {
                ids += aData[1] + ',';
            } else {
                $(_this).removeAttr('checked');
                //当取消选中,把参数设置为空
                ids = ids.replaceAll(aData[1] + ',', "");
            }
        }

        $('#createTask').on('click', function () {
            $('#createTaskModal').on('hidden.bs.modal', centerModals('createTaskModal')).modal();
        });

        /**
         * 信息提示栏关闭
         */
        $('#closeInfo').on('click', function () {
            $('#warningInfo').addClass('hide');
        });

        function batchMigration() {
            if (ids == '') {
                alert("未选中数据");
                return;
            }
            if (confirm("确定迁移吗")) {
                $.ajax({
                    url: '${ctx}/hamster/start-migration-by-task?taskIds=' + ids,
                    contentType: "application/json",
                    dataType: 'json',
                    traditional: true,
                    success: function (data) {
                        if (data.code === 200) {
                            alert("开始迁移！");
                            window.location.reload();
                        } else {
                            alert("执行失败！");
                        }
                    }
                })
            }
        }

        function theTaskInfo(taskId) {
            // var tenantId = $('#tenantId').val();
            $.getJSON("${CONTEXT_PATH}/migration/find-task-info", {
                taskId: taskId,
                // tenantId: tenantId
            }, function (taskInfo) {
                $('#taskInfo').JSONView(taskInfo.taskMap, {
                    collapsed: false,
                    nl2br: true,
                    recursive_collapser: true
                });
                $('#taskInfoModal').on('hidden.bs.modal', centerModals('taskInfoModal')).modal();
            });
        }

        function centerModals(s) {
            var windows = '\#' + s;
            $(windows).each(function (i) {
                $(this).find('.modal-content').css("margin-top", -250);
            });
        }

        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
            var r = window.location.search.substr(1).match(reg); //匹配目标参数
            if (r != null)
                return unescape(r[2]);
            return null; //返回参数值
        }
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
