package com.fxiaoke.paas.console.service.organization

import com.fxiaoke.paas.console.bean.organization.arg.DepartmentArg
import com.fxiaoke.paas.console.bean.organization.arg.DeptUserArg
import com.fxiaoke.paas.console.bean.organization.arg.EmployeeArg
import com.fxiaoke.paas.console.bean.organization.object.DepartmentObject
import com.fxiaoke.paas.console.bean.organization.object.EmployeeObject

/**
 * Created by wangxing on 2018/03/06
 */
interface OrganizationService {

  /**
   * 通过企业ID查询所有部门
   * @param tenantId 企业ID
   * @return 部门集合
   */
  List<DepartmentObject> getDepartmentIdByCompanyId(String tenantId)

  /**
   * 通过企业ID和部门ID查询部门信息
   * @param departmentArg 参数
   * @return 部门信息集合
   */
  List<DepartmentObject> getDepartment(DepartmentArg departmentArg)

  /**
   * 通过企业ID和父部门ID查询部门信息
   * @param departmentArg
   * @param page
   * @return
   */
  List<DepartmentObject> getSubordinateDepartment(DepartmentArg departmentArg)

  /**
   * 通过企业ID、员工ID、员工部门关系类型查询其所在部门信息
   * @param deptUserArg
   * @param page
   * @return
   */
  List<DepartmentObject> getRelationalDepartment(DeptUserArg deptUserArg)

  /**
   * 通过企业ID查询所有员工
   * @param tenantId
   * @return
   */
  List<EmployeeObject> getEmployeeIdByCompanyId(String tenantId)

  /**
   * 通过企业ID和员工ID查询员工信息
   * @param employeeArg
   * @param page
   * @return
   */
  List<EmployeeObject> getEmployee(EmployeeArg employeeArg)

  /**
   * 通过企业ID、部门ID、员工部门关系类型查询其所有的员工信息
   * @param deptUserArg
   * @param page
   * @return
   */
  List<EmployeeObject> getRelationalEmployee(DeptUserArg deptUserArg)

  /**
   * 通过手机号查询员工信息
   * @param tel
   * @return
   */
  List<EmployeeObject> getEmployeeByTel(String tel)

  /**
   * 获取部门树
   */
  List<DepartmentObject> departmentTree(String tenantId, String parentId)

  /**
   * 根据企业ID和用户ID查询所属部门
   * @param tenantId
   * @param userId
   * @return
   */
  List<Map<String,String>> getDeptByTenantIdAndUserId(String tenantId,List<String> userIds)
}