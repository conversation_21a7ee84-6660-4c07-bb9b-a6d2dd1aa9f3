<#assign title="员工信息">
<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
<style>
    #dataTable2 th {
        vertical-align: middle;
        align-items: center;
    }

    #dataTable2 td {
        vertical-align: middle;
        align-items: center
    }

    .companyId {
        padding-left: 10px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #ccc
    }
</style>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
    <h1>员工信息列表查询</h1>
    <ol class="breadcrumb">
        <li><a href="../../"><i class="fa fa-dashboard"></i>任务主页</a></li>
        <li><a href=""><i class="fa fa-dashboard"></i>员工信息列表查询</a></li>
    </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-header with-border">
                    <form class="form-inline" action="${ctx}/describe/list" method="post" id="findForm" role="form" data-toggle="validator">
                        <div class="form-group">
                            <label for="tenantId">企业ID</label>
                            <input type="text" name="companyId" class="companyId" id="companyId" placeholder="企业ID必填" required>
                        </div>
                        <div class="form-group">
                            <label for="apiName">员工ID</label>
                            <select class="selectpicker" data-size="5" multiple data-actions-box="true" data-live-search="true" id="userId">
                            </select>
                        </div>
                        <button type="button" class="btn btn-primary" id="doSearch">查询</button>
                    </form>
                </div>

                <div class="box-body">
                    <table class="table table-striped table-bordered table-condensed dataTable no-footer" id="dataTable2">
                        <thead>
                        <tr>
                            <th>企业ID</th>
                            <th>员工ID</th>
                            <th>员工昵称</th>
                            <th>员工姓名</th>
                            <th>所在部门</th>
                            <th>职位</th>
                            <th>负责人ID</th>
                            <th>状态</th>
                            <th>创建人</th>
                            <th>创建时间</th>
                            <th>最后更新时间</th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="box-footer clearfix">
                </div>
            </div>
        </div>
    </div>
</section>
<#--提示信息模态框-->
<div class="modal fade" id="hintModal" tabindex="-1" role="dialog" aria-labelledby="sqlModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                        class="sr-only">Close</span></button>
                <h4 class="modal-title" id="stopModalLabel">提示</h4>
            </div>
            <div class="modal-body">
                <h4>企业ID和员工ID不能为空！</h4>
            </div>
            <div class="modal-footer">
                <a type="button" class="btn btn-default" data-dismiss="modal">返回</a>
            </div>
        </div>
    </div>
</div>
</#assign>
<#assign scriptContent>
<!--搜索高亮显示-->
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script>
    $(document).ready(function () {

        $("#userId").selectpicker({
//            noneSelectedText: '请选择',
            selectAllText: '全选',
            deselectAllText: '取消选择',
            actionsBox: true,
            countSelectedText: "已选{0}项",
            selectedTextFormat: "count > 2"
        });

        var table = $("#dataTable2").DataTable({
            "searching": true,
            "ajax": "",
            "columnDefs": [
                {"width": "8.5%", "targets": 0},
                {"width": "8.5%", "targets": 1},
                {"width": "8.5%", "targets": 2},
                {"width": "8.5%", "targets": 3},
                {"width": "8.5%", "targets": 4},
                {"width": "8.5%", "targets": 5},
                {"width": "8.5%", "targets": 6},
                {"width": "8.5%", "targets": 7},
                {"width": "9%", "targets": 8},
                {"width": "11%", "targets": 9},
                {"width": "11%", "targets": 10}
            ],
            columns: [
                {data: "tenantId"},
                {data: "userId"},
                {data: "nickname"},
                {data: "name"},
                {data: "department"},
                {data: "position"},
                {data: "supervisorId"},
                {data: "status"},
                {data: "createdBy"},
                {data: "createTime"},
                {data: "lastModifiedTime"}
            ],
            "iDisplayLength": 10,
            "sPaginationType": "full_numbers",
            "language": {
                "url": "${ctx}/static/js/datatables-zh_CN.json"
            }
        });
        $("#companyId").bind("propertychange input", function () {
            getEmployeeIdByCompanyId();
        });
        $("#doSearch").click(function () {
            var tenantId = $("#companyId").val();
            var userIds = $("#userId").val();
            userIds = userIds.join(',');
            if (tenantId.length !== 0 && userIds !== null) {
                var data = {
                    tenantId: tenantId,
                    userIds: userIds
                };
                $.ajax ({
                    url: "${CONTEXT_PATH}/organization/employee",
                    type: "POST",
                    data: JSON.stringify(data),
                    contentType: "application/json",
                    dataType: "json",
                    success:function (data) {
                        console.log(data);
                        $("#dataTable2").dataTable().fnClearTable();
                        $("#dataTable2").dataTable().fnAddData(data.data);
                    }
                });

            } else {
                $('#hintModal').modal("show");
            }
        });
    });

    function getEmployeeIdByCompanyId() {
        var companyId = {
            tenantId: $('#companyId').val()
        };
        $.ajax({
            type: "get",
            url: "${CONTEXT_PATH}/organization/employeeId",
            async: true,
            dataType: "json",
            data: companyId,
            contentType: "application/x-www-form-urlencoded; charset=utf-8",
            success: function (data) {
                for (var i = 0; i < data.length; i++) {
                    $('#userId').get(0).options.add(new Option(data[i].name + "(" + data[i].userId + ")", data[i].userId))
                }
                $('#userId').selectpicker('refresh');
                $('#userId').selectpicker('render');
            }
        });
    }
</script>
</#assign>
<#include "../layout/layout-main.ftl"/>
