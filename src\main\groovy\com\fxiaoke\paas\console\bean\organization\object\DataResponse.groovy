package com.fxiaoke.paas.console.bean.organization.object

import groovy.transform.ToString
import lombok.Getter
import lombok.Setter


/**
 * Created by wangxing on 2018/03/06
 * @param < T >
 */
@Getter
@Setter
@ToString
class DataResponse<T extends Serializable> {

  String draw

  long recordsTotal

  long recordsFiltered

  List<T> data

  String error

  DataResponse() {

  }

  DataResponse(long recordsFiltered, List data) {
    this.recordsFiltered = recordsFiltered
    this.data = data
  }
}
