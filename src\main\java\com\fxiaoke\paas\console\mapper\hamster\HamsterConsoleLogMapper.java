package com.fxiaoke.paas.console.mapper.hamster;

import com.fxiaoke.paas.console.log.HamsterConsoleAuditLog;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface HamsterConsoleLogMapper {

  @Insert("INSERT INTO hm_console_log(id, executor, description, argument, execute_time) values(#{auditLog.id}, #{auditLog.executor}, #{auditLog.description}, #{auditLog.argument,typeHandler=com.github.mybatis.handler2.JSONBStringTypeHandler}, #{auditLog.executeTime})")
  Integer addAuditLog(@Param("auditLog") HamsterConsoleAuditLog auditLog);

  @Select("SELECT * FROM hm_console_log ORDER BY execute_time DESC LIMIT #{num}")
  List<HamsterConsoleAuditLog> findAuditLogByNum(@Param("num") int num);
}
