package com.fxiaoke.paas.console.service

import com.alibaba.druid.pool.DruidDataSource
import com.github.autoconf.ConfigFactory
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import javax.sql.DataSource
import java.sql.Connection
import java.sql.SQLException
/**
 * <AUTHOR>
 * Created on 2018/5/30.
 */
@Slf4j
@Service
class PgConnectionService {

//  private final Map<String, DataSource> dataSourceMap = Maps.newConcurrentMap()
//  private String username
//  private String password
//
//  @PostConstruct
//  void init() {
//    ConfigFactory.getConfig("db-paas-console", { config ->
//      username = config.get("pg_username")
//      password = config.get("pg_password")
//    })
//  }
//  /**
//   * 建立连接
//   * @param jdbcUrl
//   */
//  Connection getConnection(String jdbcUrl) throws SQLException {
//    DataSource dataSource = dataSourceMap.computeIfAbsent(jdbcUrl, { ds -> createDruidPool(jdbcUrl) })
//    return dataSource.getConnection()
//  }
//
//  private DataSource createDruidPool(String jdbcUrl) {
//    log.info("create datasource:" + jdbcUrl)
//
//    DruidDataSource ds = new DruidDataSource()
//    ds.setUrl(jdbcUrl)
//    ds.setUsername(username)
//    ds.setPassword(password)
//    try {
//      ds.setFilters("slf4j")
//    } catch (Exception e) {
//      log.error("cannot add slf4j filter with {}", jdbcUrl, e)
//    }
//
//    // 设置连接池的各种属性
//    ds.setMaxActive(100)
//    // 设置成0,不会立即连接
//    ds.setInitialSize(0)
//    ds.setMinIdle(0)
//    ds.setMaxWait(20000)
//    ds.setTimeBetweenEvictionRunsMillis(30000)
//    ds.setMinEvictableIdleTimeMillis(30000)
//    ds.setValidationQuery("SELECT 'x'")
//    ds.setTestWhileIdle(true)
//    ds.setTestOnReturn(false)
//    ds.setTestOnBorrow(false)
//
//    ds.setPoolPreparedStatements(true)
//    ds.setMaxPoolPreparedStatementPerConnectionSize(100)
//
//    try {
//      ds.init()
//    } catch (Exception e) {
//      log.error("init datasource failed {}, ", jdbcUrl, e)
//    }
//    return ds
//  }
}
