<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/aop
        http://www.springframework.org/schema/aop/spring-aop.xsd">
  <bean class="com.github.mybatis.util.SpringUtil"/>
  <context:component-scan base-package="com.fxiaoke.paas.console.service"/>
  <context:component-scan base-package="com.fxiaoke.template.server"/>
  <context:component-scan base-package="com.fxiaoke.paas.console.aoplog"/>
  <context:component-scan base-package="com.fxiaoke.paas.console.aop"/>
  <context:component-scan base-package="com.fxiaoke.paas.console.mq"/>
  <context:component-scan base-package="com.fxiaoke.paas.console.proxy"/>
  <context:component-scan base-package="com.fxiaoke.appcenter.restapi.service"/>

  <!--option服务-->
  <import resource="classpath*:/META-INF/spring/option-api-client.xml"/>

  <bean id="httpSupport" name="httpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean" p:configName="paas-console-remotecall"/>
  <bean class="com.fxiaoke.paas.console.util.HttpClientUtil" lazy-init="false" p:client-ref="httpSupport"/>

  <!--审计日志未读消息redis-->
  <bean id="redisCache" class="com.github.jedis.support.JedisFactoryBean" p:configName="fs-paas-metadata-service-redis"/>
  <!--jvm监控-->
  <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

  <!--通知spring使用cglib而不是jdk的来生成代理方法 AOP可以拦截到Controller
      默认的jdk动态代理是监听接口级别的，当 proxy-target-class 设置为true，则通知spring使用cglib的动态代理，拦截类级别的-->
  <aop:aspectj-autoproxy proxy-target-class="true"/>

  <import resource="classpath:spring/paas-console-spring-cms.xml"/>
  <import resource="classpath:spring/paas-console-spring-guava.xml"/>
  <import resource="classpath:spring/paas-console-spring-shiro.xml"/>
  <import resource="classpath:spring/paas-console-spring-db.xml"/>
  <!--<import resource="classpath:spring/paas-console-dubbo-consumer.xml"/>-->
  <import resource="classpath:spring/paas-console-spring-scheduler.xml"/>
  <import resource="classpath:spring/paas-console-special-table-sql.xml"/>
  <import resource="classpath:spring/paas-console-spring-xxl.xml"/>
  <import resource="classpath:spring/paas-console-spring-redis.xml"/>
  <import resource="classpath*:paas-common/rest/dubbo-rest.xml"/>
  <import resource="classpath*:spring/spring-org-cms.xml"/>
  <bean id="schemaHelper" class="com.fxiaoke.paas.common.service.SchemaHelper"/>
  <import resource="classpath:table-transfer-client.xml"/>
  <import resource="classpath:appcenterrest/appcenterrest.xml"/>

  <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
    <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>
  </bean>

  <!--license-->
  <import resource="classpath:spring/license-client.xml"/>
  <!--<bean id="licenseClient" class="com.facishare.paas.license.factory.LicenseFactoryBean"/>-->
  <!--<bean id="licenseFactoryService" class="com.facishare.paas.license.factory.LicenseFactoryService"/>-->

  <!--mongo-->
  <!--<bean id="dataStore" class="com.github.mongo.support.MongoDataStoreFactoryBean" p:configName="mongo-support-workflow"/>-->
  <!-- 启用@AspectJ注解 -->
  <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"/>
  <aop:config>
    <aop:aspect ref="serviceProfiler">
      <aop:around method="profile" pointcut="execution(* com.fxiaoke.paas.console.service.*.*(..))"/>
    </aop:aspect>
  </aop:config>

</beans>
