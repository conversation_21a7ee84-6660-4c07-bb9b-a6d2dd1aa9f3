<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>添加灰度产品</h1>
        <ol class="breadcrumb">
            <li><a href=".."><i class="fa fa-dashboard"></i>任务主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i></a></li>
        </ol>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title"></h3>
                    </div>
                    <form class="form-horizontal" action="${ctx}/license-write/gray-price" method="post" id="roleForm" >
                        <div class="box-body">
                            <div class="form-group">
                                <label for="tenantId" class="col-sm-2 control-label" style="width: 120px">企业ID</label>
                                <div class="col-sm-6">
                                    <input type="text" style="border-radius:5px;width:180px;" class="form-control" id="tenantId" name="tenantId" width="60px" value="${tenantId!}" placeholder="必填" required>
                                    <p>1年<input type="radio" id="oneYear" style="border-radius:5px;" name="totalPara" value="1" onclick="selectYear(1)"/>
                                        2年<input type="radio" style="border-radius:5px;" name="totalPara" value="2" onclick="selectYear(2)"/>
                                        3年<input type="radio" style="border-radius:5px;" name="totalPara" value="3" onclick="selectYear(3)"/>
                                    </p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="host" class="col-sm-1 control-label" style="width: 120px">开始时间</label>
                                <span class="glyphicon glyphicon-calendar"
                                      onclick="WdatePicker({skin:'whyGreen',errDealMode:-1, dateFmt:'yyyy-MM-dd HH:mm:ss',vel:'00:00:00',el:'startTime', onpicked:function(dp){$('#startTime').change()}})"></span>
                                <div class="col-sm-4" style="width: 200px">
                                    <input type="text" style="width: 180px" class="form-control" id="startTime" name="startTime" value="" width="120px" onblur="checkStartTime()"/>
                                    <span id="startTimeSpan"  style="color: #ff2d21"></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="host" class="col-sm-1 control-label" style="width: 120px">结束时间</label>
                                <span class="glyphicon glyphicon-calendar"
                                      onclick="WdatePicker({skin:'whyGreen',errDealMode:-1, dateFmt:'yyyy-MM-dd HH:mm:ss',el:'endTime', onpicked:function(dp){$('#endTime').change()}})"></span>
                                <div class="col-sm-4" style="width: 200px">
                                    <input type="text" style="width: 180px" class="form-control" id="endTime" name="endTime"value="" width="120px" onblur="checkEndTime()"/>
                                    <span id="endTimeSpan" style="color: #ff2d21" ></span>
                                </div>
                            </div>
                        </div>
                        <div class="box-footer clearfix">
                            <div class="form-group">
                                <div class="col-sm-offset-2 col-sm-2">
                                    <button type="submit" class="btn btn-primary">提交</button>
                                    <button type="button" class="btn btn-default" onclick="history.back()">返回</button>
                                </div>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script src="//static.foneshare.cn/oss/my97datepicker/WdatePicker.js"></script>
    <script type="js"></script>
    <script>
        function selectYear(sy) {
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth();
            var day = date.getDate();
            var hour = date.getHours();
            var min = date.getMinutes();
            var second = date.getSeconds();
            if (month < 10) {
                month = "0" + month;
            }
            if (day < 10) {
                day = "0" + day;
            }
            if (hour < 10) {
                hour = "0" + hour;
            }
            if (min < 10) {
                min = "0" + min;
            }
            if (second < 10) {
                second = "0" + second;
            }
            var stTime = year + "-" + month + "-" + day + " " + hour + ":" + min + ":" + second;
            if (sy==1){
                $('#startTime').val(stTime);
                year = year + 1;
                var enTime = year + "-" + month + "-" + day + " " + hour + ":" + min + ":" + second;
                $('#endTime').val(enTime);
            }
            if (sy==2){
                $('#startTime').val(stTime);
                year = year + 2;
                var enTime = year + "-" + month + "-" + day + " " + hour + ":" + min + ":" + second;
                $('#endTime').val(enTime);
            }
            if (sy==3){
                $('#startTime').val(stTime);
                year = year + 3;
                var enTime = year + "-" + month + "-" + day + " " + hour + ":" + min + ":" + second;
                $('#endTime').val(enTime);
            }
        }
        var date_Format = /^((\d{2}(([02468][048])|([13579][26]))[\-\/\s]?((((0?[13578])|(1[02]))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\-\/\s]?((0?[1-9])|([1-2][0-9])))))|(\d{2}(([02468][1235679])|([13579][01345789]))[\-\/\s]?((((0?[13578])|(1[02]))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\-\/\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\s((([0-1][0-9])|(2?[0-3]))\:([0-5]?[0-9])((\s)|(\:([0-5]?[0-9])))))?$/
        function checkStartTime(){
            var stTime = $('#startTime').val();
            var startNum = new Date($('#startTime').val()).getTime();
            var endNum = new Date($('#endTime').val()).getTime();
            if (startNum>=endNum){
                $('#startTimeSpan').html("时间错误");
                return ;
            }else{
                $('#startTimeSpan').html("正确");
            }
            if(date_Format.test(stTime)){
                $('#startTimeSpan').html("正确");
            } else {
                $('#startTimeSpan').html("错误");
            }
        }
        function checkEndTime(){
            var startNum = new Date($('#startTime').val()).getTime();
            var endNum = new Date($('#endTime').val()).getTime();
            var edTime = $('#endTime').val();
            if (startNum>=endNum){
                $('#endTimeSpan').html("时间错误");
                return ;
            }else{
                $('#endTimeSpan').html("正确");
            }
            if(date_Format.test(edTime)){
                $('#endTimeSpan').html("正确");
            } else {
                $('#endTimeSpan').html("错误");
            }
        }
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
