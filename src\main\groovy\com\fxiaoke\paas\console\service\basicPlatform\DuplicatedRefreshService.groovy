package com.fxiaoke.paas.console.service.basicPlatform

import com.alibaba.fastjson.JSON
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData
import com.facishare.uc.api.service.EnterpriseEditionService
import com.fxiaoke.paas.console.bean.organization.object.EnterpriseObject
import com.fxiaoke.paas.console.service.OKHttpService
import com.fxiaoke.paas.console.service.metadata.SqlQueryService
import com.fxiaoke.paas.console.util.organization.typeconvert.EnterpriseConvert
import com.fxiaoke.release.FsGrayRelease
import com.fxiaoke.release.FsGrayReleaseBiz
import com.github.autoconf.ConfigFactory
import com.github.autoconf.api.IConfig
import com.google.common.base.Splitter
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import javax.ws.rs.core.MediaType
import java.util.stream.Collectors

@Service
class DuplicatedRefreshService {

    @Autowired
    SqlQueryService sqlQueryService
    @Autowired
    private EnterpriseEditionService enterpriseEditionService

    @Autowired
    private OKHttpService okHttpService


    private static Set<String> CUSTOM_MULTI_RULE = Sets.newHashSet()

    private static final Splitter CONFIG_SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();
    private FsGrayReleaseBiz gray = FsGrayRelease.getInstance("udobj");

    private static String serviceUrl = "";


    private String searchQueryUrl;

    boolean isAllow(String rule, String euid) {
        return gray.isAllow(rule, euid);
    }

    private boolean isAllowSystemDuplicate(String tenantId) {
        return isAllow("multi_duplicate_rule_support_pre_obj_gray", tenantId)
    }


    @PostConstruct
    static init() {
        ConfigFactory.getConfig("fs-paas-appframework-config", { iConfig ->
            CUSTOM_MULTI_RULE = getSetFromConfig(iConfig, "multi_duplicate_rule_and_support_filter_gray_ei")
        })

        ConfigFactory.getConfig("paas-console-base", { config ->
            searchQueryUrl = config.get("fs-udobj-app-web-url")
        })
    }

    private static Set<String> getSetFromConfig(IConfig config, String key) {
        return Sets.newHashSet(CONFIG_SPLITTER.split(config.get(key, "")));
    }


    List<String> checkRefreshTenant(String refreshType, List<String> tenantList, Map<String, Object> failTenant) {
        tenantList = checkTenantStatus(tenantList, failTenant)
        if (CollectionUtils.isEmpty(tenantList)) {
            return Lists.newArrayList()
        }
        if (StringUtils.equals(refreshType, "custom")) {
            return checkCustomTenant(tenantList, failTenant)
        } else if (StringUtils.equals(refreshType, "system")) {
            return checkSystemTenant(tenantList, failTenant)
        }
        return Lists.newArrayList()
    }


    List<String> checkCustomTenant(List<String> tenantList, Map<String, Object> failTenant) {
        if (!tenantList) {
            return Lists.newArrayList()
        }
        //校验企业是否已经灰度
        List<String> exitsList = tenantList.findAll({ x -> CUSTOM_MULTI_RULE.contains(x) })
        if (CollectionUtils.isNotEmpty(exitsList)) {
            exitsList.each { x -> failTenant.put(x, "该企业已经灰度多规则") }
            tenantList = tenantList.findAll({ x -> !CUSTOM_MULTI_RULE.contains(x) })
        }
        return tenantList;
    }


    List<String> checkSystemTenant(List<String> tenantList, Map<String, Object> failTenant) {
        if (!tenantList) {
            return Lists.newArrayList()
        }
        //校验企业是否已经灰度多规则
        List<String> unExitsMultiRuleList = tenantList.findAll({ x -> !CUSTOM_MULTI_RULE.contains(x) })
        if (CollectionUtils.isNotEmpty(unExitsMultiRuleList)) {
            unExitsMultiRuleList.each { x -> failTenant.put(x, "该企业没有灰度多规则") }
            tenantList = tenantList.findAll({ x -> CUSTOM_MULTI_RULE.contains(x) })
        }

        List<String> unExistTenant = tenantList.findAll({ x -> !isAllowSystemDuplicate(x) })
        if (CollectionUtils.isNotEmpty(unExistTenant)) {
            unExistTenant.each { x -> failTenant.put(x, "该企业没有灰度三大对象多规则") }
        }
        return tenantList.findAll({ x -> !failTenant.containsKey(x) })
    }

    List<String> checkTenantStatus(List<String> tenantList, Map<String, Object> failTenant) {
        if (CollectionUtils.isEmpty(tenantList)) {
            return Lists.newArrayList()
        }
        //校验企业状态
        Map<String, EnterpriseObject> enterpriseInfo = getEnterpriseInfo(tenantList, failTenant)
        if (enterpriseInfo == null || enterpriseInfo.size() == 0) {
            tenantList.each { x -> failTenant.put(x, "该企业不存在") }
        }
        tenantList.findAll({ x -> Objects.isNull(enterpriseInfo.get(x)) || !"已开通".equals(enterpriseInfo.get(x).getRunStatus()) })?.each { x -> failTenant.put(String.valueOf(x), Objects.isNull(enterpriseInfo.get(x)) ? "企业不存在" : "企业状态异常:" + enterpriseInfo.get(x).getRunStatus()) }
        return tenantList.findAll({ x -> !failTenant.containsKey(x) })
    }

    Map<String, EnterpriseObject> getEnterpriseInfo(List<String> tenantList, Map<String, Object> failTenant) {
        tenantList = tenantList.findAll({ x -> x.isNumber() })
        tenantList.findAll({ x -> !x.isNumber() })?.each { x -> failTenant.put(x, "请输入企业id") }
        List<Integer> tenants = tenantList?.stream()?.map { x -> Integer.valueOf(x) }?.collect(Collectors.toList())
        BatchGetSimpleEnterpriseDataArg batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(
                enterpriseIds: tenants,
                enterpriseAccounts: null
        )
        BatchGetSimpleEnterpriseDataResult batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
        List<SimpleEnterpriseData> enterpriseList = batchGetSimpleEnterpriseDataResult.simpleEnterpriseList
        List<EnterpriseObject> enterpriseObjectList = enterpriseList?.collect { x -> EnterpriseConvert.enterpriseDataConvertEnterpriseObject(x) }
        return enterpriseObjectList.collectEntries { x -> [String.valueOf(x.getEnterpriseId()), x] }
    }


    Map<String, List<String>> refreshTenant(String refreshType, List<String> tenantList, HashMap<String, Object> failTenant) {
        String url = serviceUrl + "duplicate_search/service/batchRefresh"
        Map<String, String> headers = Maps.newHashMap()
        headers.put("x-fs-userInfo", "-10000")
        headers.put("x-fs-ei", tenantList.get(0))
        Map<String, List<String>> params = Maps.newHashMap()
        if ("custom".equals(refreshType)) {
            tenantList.each { x -> params.put(x, []) }
        } else if ("system".equals(refreshType)) {
            tenantList.each { x -> params.put(x, ["AccountObj", "LeadsObj", "ContactObj"]) }
        }

        Map<String, Object> result = okHttpService.post(url, headers, ["grayTenant": params], MediaType.APPLICATION_JSON)
        if (result.get("code") != 0) {
            throw new RuntimeException(result.get("message") as String)
        }
        return result.get("data")?.getAt("finalTenant") as Map<String, List<String>>
    }
}
