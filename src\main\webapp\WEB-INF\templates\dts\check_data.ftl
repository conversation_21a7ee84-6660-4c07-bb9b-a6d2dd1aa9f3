<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
  <h1>数据管理</h1>
  <ol class="breadcrumb">
    <li><a href="${ctx}"><i class="fa fa-dashboard"></i>主页</a></li>
    <li><a href=""><i class="fa fa-dashboard"></i>数据管理</a></li>
  </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info row">
        <div class="box-header with-border">
        </div>
        <form class="form-horizontal" action="" method="post" id="infoFrom" role="form" data-toggle="validator">
          <div class="box-body col-xs-6">
            <div class="form-group">
              <label for="serverType" class="col-sm-3 control-label">服务类型</label>
              <div class="col-sm-6">
                <select id="serverType" name="serverType" class="selectpicker show-tick form-control" title="Nothing selected" data-live-search="false">
                  <option value="1" selected>直连库</option>
                  <option value="2">基于文件</option>
                  <option value="3">更新dts配置</option>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label for="opType" class="col-sm-3 control-label">操作类型</label>
              <div class="col-sm-6">
                <select id="opType" name="opType" class="selectpicker show-tick form-control" title="请选择操作" data-live-search="false">
                  <option value="init_db">初始化数据库</option>
                  <option value="init_all_describe">全量初始化对象表</option>
                  <option value="init_all_data">全量初始化企业数据</option>
                  <option value="sync_describe">同步对象描述</option>
                  <option value="sync_data">同步对象数据</option>
                  <option value="sync_city_data">同步城市数据</option>
                  <option value="sync_config_table">同步配置表结构</option>
                  <option value="sync_config_data">同步配置表数据</option>
                  <option value="sync_data_id">同步对象数据(ID)</option>
                  <option value="query_id">根据ID查明细</option>
                  <option value="table_count">查表数据总数</option>
                  <option value="table_info">查表明细</option>
                  <option value="query_sql">执行查询sql</option>
                  <option value="update_sql">执行变更sql</option>
                  <option value="sync_1n_object_data">同步1+N对象数据按对象</option>
                  <option value="sync_1n_ei_data">同步1+N对象数据按N企业</option>
                  <option value="sync_n_config_data">同步1+N配置数据按N企业</option>
                  <option value="table_drop">删除表</option>

                  <option value="sync_log_object">同步审计日志按对象</option>
                  <option value="sync_log_ei">同步审计日志按企业</option>
                  <option value="sync_log_group">审计日志分组数据</option>
                  <option value="sync_login_log">同步登录日志</option>

                  <option value="table_refresh">更新表元数据缓存</option>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label for="ei" class="col-sm-3 control-label">企业ID</label>
              <div class="col-sm-6">
                <input type="text" class="form-control" style="border-radius:5px;" id="ei" name="ei" placeholder="企业ID(必填)" required>
              </div>
            </div>
            <div class="form-group">
              <label for="objectApiName" class="col-sm-3 control-label">对象ApiName / 表名</label>
              <div class="col-sm-6">
                <input type="text" class="form-control" style="border-radius:5px;" id="objectApiName" name="objectApiName" placeholder="对象ApiName / 表名">
              </div>
            </div>
            <div class="form-group">
              <label for="serviceHost" class="col-sm-3 control-label">服务地址</label>
              <div class="col-sm-6">
                <input type="text" class="form-control" style="border-radius:5px;" id="serviceHost" name="serviceHost" placeholder="服务地址">
              </div>
            </div>
            <div class="form-group">
              <label for="queryData" class="col-sm-3 control-label">数据</label>
              <div class="col-sm-6">
                <textarea id="queryData" name="queryData" class="form-control" style="height: 450px;border-radius:5px;"  placeholder="数据"></textarea>
                <div class="help-block with-errors"></div>
              </div>
            </div>
            <div class="col-sm-offset-8">
              <button type="button" id="submitData" class="btn btn-primary">执行</button>
            </div>
          </div>
          <div class="box-body col-xs-6">
            <h4>Result <i class="fa fa-hand-o-down"></i></h4>
            <pre style=""><div id="dataInfo"></div></pre>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<#--表单验证插件-->
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script>
  $('#submitData').on('click', function () {
    $('#dataInfo').text("");

    const postBody = {
      "serverType": $('#serverType').val(),
      "opType": $('#opType').val(),
      "ei": $('#ei').val(),
      "objectApiName": $('#objectApiName').val(),
      "queryData": $('#queryData').val(),
      "serviceHost": $('#serviceHost').val()
    };

    $.ajax({
      url: "${CONTEXT_PATH}/dts/check/data/do",
      contentType: "application/json",
      data: JSON.stringify(postBody),
      dataType: "json",
      type: "POST",
      traditional: true,
      success: function (data) {
        result = data.data
        if(isJSON(result)){
          $('#dataInfo').JSONView(result, {
            collapsed: false,
            nl2br: true,
            recursive_collapser: true
          });
        }else {
          $('#dataInfo').text(result)
        }
      }
    });
  });
  function isJSON(str) {
    try {
      JSON.parse(str);
    } catch (e) {
      return false;
    }
    return true;
  }
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
