package com.fxiaoke.paas.console.license.mapper;

import com.facishare.paas.license.pojo.MasterLicensePojo
import com.fxiaoke.paas.console.entity.license.ModuleInfoEntity
import com.fxiaoke.paas.console.entity.license.ModuleInfoIdCodeEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

interface ModuleInfoMapper {

  List<String> queryModuleCodeByModuleCode(@Param("tenantId") String tenantId, @Param("moduleCodes") List<String> moduleCodes);

  List<MasterLicensePojo> queryModuleInfoByProId2(@Param("tenantId") String tenantId, @Param("ids") List<String> ids);

  @Select("select distinct module_code  from module_info mi where tenant_id = 'default'")
  List<String> queryModuleCodeByDefault();

  @Select("select id,module_code,module_name from module_info mi where tenant_id = 'default' and product_id = 'default_app_id'")
  List<ModuleInfoIdCodeEntity> queryModuleCodeAndIdByDefault();

  @Select("select distinct module_code  from module_info mi where tenant_id = 'default' and product_id = #{productId}")
  List<String> queryModuleCodeByProductId(@Param("productId") String productId);

  List<ModuleInfoEntity> queryModuleByProductId(@Param("productId") String productId);
}
