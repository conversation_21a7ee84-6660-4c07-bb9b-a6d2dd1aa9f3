<#assign title="企业信息树形图">
<#assign headContent>
<#--<link href="http://www.jq22.com/jquery/bootstrap-3.3.4.css" rel="stylesheet" type="text/css">-->
<style>
  .companyId {
    padding-left: 10px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #ccc
  }
</style>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
  <h1>部门树状图</h1>
  <ol class="breadcrumb">
    <li><a href="../../"><i class="fa fa-dashboard"></i>任务主页</a></li>
    <li><a href=""><i class="fa fa-dashboard"></i>部门树状图</a></li>
  </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <div class="box-header with-border">
          <form class="form-inline" action="" method="post" id="findForm" onsubmit="return false;" role="form" data-toggle="validator">
            <div class="form-group">
              <label for="tenantId">企业ID</label>
              <input type="text" name="companyId" class="companyId" id="companyId" placeholder="企业ID必填" required>
            </div>
            <button type="button" class="btn btn-primary" id="doSearch">查询</button>
          </form>
        </div>

        <div class="box-body">
          <div class="jq22-container">
            <div class="col-sm-4">
              <h2>部门树</h2>
              <div id="treeview-selectable" class=""></div>
            </div>
            <div class="col-sm-4" id="treeDepartment">
              <h2>部门信息</h2>
              <div id="selectable-output"></div>
            </div>
          </div>
        </div>

        <div class="box-footer clearfix">
        </div>
      </div>
    </div>
  </div>
</section>
<#--提示信息模态框-->
<div class="modal fade" id="hintModal" tabindex="-1" role="dialog" aria-labelledby="sqlModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                class="sr-only">Close</span></button>
        <h4 class="modal-title" id="stopModalLabel">提示</h4>
      </div>
      <div class="modal-body">
        <h4>暂无信息！！！</h4>
      </div>
      <div class="modal-footer">
        <a type="button" class="btn btn-default" data-dismiss="modal">返回</a>
      </div>
    </div>
  </div>
</div>
<#--提示信息模态框-->
<div class="modal fade" id="hintModal2" tabindex="-1" role="dialog" aria-labelledby="sqlModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                class="sr-only">Close</span></button>
        <h4 class="modal-title" id="stopModalLabel">提示</h4>
      </div>
      <div class="modal-body">
        <h4>企业ID不能为空！</h4>
      </div>
      <div class="modal-footer">
        <a type="button" class="btn btn-default" data-dismiss="modal">返回</a>
      </div>
    </div>
  </div>
</div>
</#assign>
<#assign scriptContent>
<script src="${ctx}/static/js/organization/bootstrap-treeview.js"></script>
<script type="text/javascript">
  $(document).ready(function () {
    $("#doSearch").click(function () {
      $('#selectable-output').empty();
      if ($('#companyId').val() != null && $('#companyId').val().length !== 0) {
        var companyId = {
          tenantId: $('#companyId').val(),
          parentId: null
        };
        $.ajax({
          type: "post",
          url: "${CONTEXT_PATH}/organization/departmentTree",
          dataType: "json",
          data: companyId,
          contentType: "application/x-www-form-urlencoded; charset=utf-8",
          language: {
            "url": "${ctx}/static/js/datatables-zh_CN.json"
          },
          success: function (data) {
            if (data.length > 0) {
              var defaultData = data;
              $('#treeview-selectable').treeview({
                levels: 1,
                data: defaultData,
                dataType: "json",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                onNodeSelected: function (event, node) {
                  $('#selectable-output').empty();
                  $('#selectable-output').prepend(
                          '<p> <b>部门ID：</b>' + node.deptId + ' </p>',
                          '<p> <b>部门名称：</b>' + node.name + ' </p>',
                          '<p><b> 状态：</b>' + node.status + ' </p>',
                          '<p><b> 负责人ID：</b>' + node.managerId + ' </p>',
                          '<p><b> 创建人：</b>' + node.createBy + ' </p>',
                          '<p><b> 创建时间：</b>' + node.createTime + ' </p>',
                          '<p><b> 最后更新人：</b>' + node.lastModifiedBy + ' </p>',
                          '<p><b> 最后更新时间：</b>' + node.lastModifiedTime + ' </p>'
                  );
                },
                onNodeExpanded: function (event, node) {
                  if (node.nodes.length === 0) {
                    companyId.parentId = node.deptId;
                    $.ajax({
                      type: "Post",
                      url: "${CONTEXT_PATH}/organization/departmentTree",
                      data: companyId,
                      dataType: "json",
                      contentType: "application/x-www-form-urlencoded; charset=utf-8",
                      success: function (result) {
                        console.log("result:");
                        console.log(result);
                        for (var num in result) {
                          $("#treeview-selectable").treeview("addNode", [
                            node.nodeId,
                            {
                              node: {
                                "createTime": result[num].createTime,
                                "deptId": result[num].deptId,
                                "id": result[num].id,
                                "isDeleted": result[num].isDeleted,
                                "lastModifiedTime": result[num].lastModifiedTime,
                                "managerId": result[num].managerId,
                                "name": result[num].name,
                                "nodes": result[num].nodes,
                                "status": result[num].status,
                                "tenantId": result[num].tenantId,
                                "text": result[num].text
                              }, silent: true
                            }
                          ]);
                        }
                      }
                    });
                  }
                }
              });
            } else {
              $('#hintModal').modal("show");
              $('#selectable-output').empty();
              $('#treeview-selectable').empty();
            }
          }
        });
      } else {
        $('#hintModal2').modal("show");
      }
    });
    $(window).scroll(function () {
      var offsetTop = $(window).scrollTop() + "px";//scrollTop()返回匹配元素的滚动条的垂直位置
      $("#treeDepartment").animate({top: offsetTop}, {duration: 600, queue: false});
    });
  });
</script>
</#assign>
<#include "../layout/layout-main.ftl"/>

