package com.fxiaoke.paas.console.mapper.organization

import com.fxiaoke.paas.console.entity.organization.DepartmentEntity
import com.fxiaoke.paas.console.service.organization.OrganizationService
import lombok.extern.slf4j.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(locations = ["classpath:mapperContext.xml"])
@Slf4j
class DepartmentMapperTest extends Specification{

    @Autowired
    OrganizationService organizationService


    def "测试Service---departmentTree"() {
        given:
        List<DepartmentEntity> list = organizationService.departmentTree("7",null)
        expect:
        list.size()
    }
}
