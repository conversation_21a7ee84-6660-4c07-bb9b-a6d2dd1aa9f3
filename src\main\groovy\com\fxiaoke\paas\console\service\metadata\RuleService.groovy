package com.fxiaoke.paas.console.service.metadata

import com.fxiaoke.paas.console.mapper.metadata.RuleMapper
import com.fxiaoke.paas.console.util.DateFormatUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 *  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/3/24.
 */
@Service
class RuleService {
  @Autowired
  RuleMapper ruleMapper

  /**
   * 根据企业ID查询映射规则
   * @param tenantId
   */
  def findRuleByTenantId(String tenantId) {
    ruleMapper.setTenantId(tenantId).findRuleByTenantId(tenantId)
  }

  /**
   * 根据id查询映射规则详情
   * @param tenantId 企业ID
   * @param ruleId 映射规则id
   */
  def findRuleByRuleId(String tenantId, String ruleId) {
    Map<String, Object> ruleMap = ruleMapper.setTenantId(tenantId).findRuleByRuleId(ruleId, tenantId)
    ruleMap["create_time"] = DateFormatUtil.formatLong(ruleMap.get("create_time") as Long)
    ruleMap["last_modified_time"] = DateFormatUtil.formatLong(ruleMap.get("last_modified_time") as Long)
    ruleMap
  }
}
