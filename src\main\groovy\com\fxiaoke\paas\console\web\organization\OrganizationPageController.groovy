package com.fxiaoke.paas.console.web.organization

import lombok.extern.slf4j.Slf4j
import org.apache.shiro.SecurityUtils
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping

/**
 * Created by wangxing on 2018/03/06
 */
@Controller
@Slf4j
@RequestMapping("/org")
class OrganizationPageController {

  @RequestMapping("/department")
  def department() {
    "organization/department"
  }

  @RequestMapping("/subordinateDepartment")
  def subordinateDepartment() {
    "organization/subordinateDepartment"
  }

  @RequestMapping("/relationalDepartment")
  def relationalDepartment() {
    "organization/relationalDepartment"
  }

  @RequestMapping("/employee")
  def employee() {
    "organization/employee"
  }

  @RequestMapping("/relationalEmployee")
  def relationalEmployee() {
    "organization/relationalEmployee"
  }

  @RequestMapping("tel")
  def employeeByTel() {
    "organization/employeeByTel"
  }

  @RequestMapping("enterprise")
  def enterprise() {
    "organization/enterprise"
  }

  @RequestMapping("treeDepartment")
  def testTree() {
    "organization/departmentTree"
  }

  @RequestMapping("tenantMsg")
  def tenantMsg() {
    "organization/tenantMsg"
  }
}
