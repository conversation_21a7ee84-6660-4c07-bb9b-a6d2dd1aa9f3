package com.fxiaoke.paas.console.service.datarights

import com.alibaba.fastjson.JSONObject
import com.facishare.paas.pod.client.DbRouterClient
import com.facishare.paas.pod.dto.RouterInfo
import com.facishare.paas.pod.util.DialectUtil
import com.fxiaoke.common.http.SimpleHttpClient
import com.fxiaoke.paas.console.entity.datarights.DepartmentMsg
import com.fxiaoke.paas.console.entity.datarights.DepartmentUser
import com.fxiaoke.paas.console.entity.datarights.DtAuth
import com.fxiaoke.paas.console.entity.datarights.EntityShareGroup
import com.fxiaoke.paas.console.entity.datarights.EntityShareReceive
import com.fxiaoke.paas.console.entity.datarights.GroupUser
import com.fxiaoke.paas.console.entity.datarights.Personnel
import com.fxiaoke.paas.console.entity.datarights.Principal
import com.fxiaoke.paas.console.entity.datarights.RoleMember
import com.fxiaoke.paas.console.mapper.datarights.DataRightsDataMapper
import com.fxiaoke.paas.console.mapper.datarights.DataRightsDepartmentMapper
import com.fxiaoke.paas.console.mapper.datarights.PersonnelMapper
import com.fxiaoke.paas.console.mapper.datarights.TemporaryRightsMapper
import com.fxiaoke.paas.console.service.functional.FunctionalService
import com.fxiaoke.paas.console.util.ApiNameToStoreTableNameUtil
import com.github.autoconf.ConfigFactory
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import groovy.util.logging.Log4j
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import java.util.stream.Collectors

/**
 *
 * <AUTHOR>
 * @date 2019/4/1 下午4:16
 *
 */
@Service
@Log4j
class DataRightsDataService {
  @Autowired
  private DataRightsDataMapper dataMapper
  @Autowired
  private PersonnelMapper personnelMapper
  @Autowired
  private DataRightsDepartmentMapper dataRightsDepartmentMapper
  @Autowired
  private TemporaryRightsMapper temporaryRightsMapper
  @Autowired
  private DataRightsStoreTableNameService storeTableNameService
  @Autowired
  private FunctionalService functionalService

  private String roleListUrl
  @Autowired
  private DbRouterClient dbRouterClient

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("paas-console-base", { config ->
      roleListUrl = config.get("roleListUrl")
    })
  }


  def queryReferenceData(String tenantId, String apiName, Collection<String> dataIds) {
    Map<String, List<Map<String, String>>> result = Maps.newTreeMap()
    if (StringUtils.isAnyBlank(tenantId, apiName) || CollectionUtils.isEmpty(dataIds)) {
      return result
    }
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, "CRM", "application", DialectUtil.POSTGRESQL)
    List<Map<String, String>> relationData = dataMapper.setTenantId(tenantId).queryDataRelation(routerInfo.standalone ? "sch_" + tenantId : "public", tenantId, apiName, dataIds)
    Map<String, List<Map<String, Object>>> myRelationData = Maps.newHashMap()
    Map<String, List<Map<String, Object>>> relationMeData = Maps.newHashMap()
    relationData.forEach({ relation ->
      String sourceApiName = relation.get("source_api_name")
      String sourceDataId = relation.get("source_data_id")
      String targetApiName = relation.get("target_api_name")
      String targetDataId = relation.get("target_data_id")

      if (dataIds.contains(sourceDataId)) {
        String storeTableName = storeTableNameService.byDescribeApiName(tenantId, targetApiName)
        if (StringUtils.isBlank(storeTableName)) {
          return
        }
        Map<String, Object> targetData = dataMapper.setTenantId(tenantId).queryData(tenantId, targetApiName, (routerInfo.standalone ? ("sch_" + tenantId) : "public") + "." + storeTableName, targetDataId)
        myRelationData.computeIfAbsent(sourceDataId, { key -> Lists.newArrayList() }).add(targetData)

      }

      if (dataIds.contains(targetDataId)) {
        String storeTableName = storeTableNameService.byDescribeApiName(tenantId, sourceApiName)
        if (StringUtils.isBlank(storeTableName)) {
          return
        }
        Map<String, Object> sourceData = dataMapper.setTenantId(tenantId).queryData(tenantId, sourceApiName, (routerInfo.standalone ? ("sch_" + tenantId) : "public") + "." + storeTableName, sourceDataId)
        relationMeData.computeIfAbsent(targetDataId, { key -> Lists.newArrayList() }).add(sourceData)
      }
    })

    result.put("15.我引用的数据", myRelationData)
    result.put("16.引用我的数据", relationMeData)
    return result
  }

  /**
   * 通过数据ID查询数据临时权限
   * @param tenantId
   * @param objectId
   * @return
   */
  List<Map<String, Object>> getTemporaryRightsByObjectId(String tenantId, String objectId) {
    if (isAllNotBlank(tenantId, objectId)) {
      return temporaryRightsMapper.setTenantId(tenantId).queryTemporaryRightsByObjectId(tenantId, objectId)
    }
    return Lists.newArrayList()
  }

  /**
   * 通过数据ID和用户ID查询人员的上级、下级、用户组、角色、所属部门
   * @param tenantId
   * @param userId
   * @param objectId
   * @return
   */
  Personnel getPersonnelByObjectId(String tenantId, String userId, String objectId) {
    if (isAllNotBlank(tenantId, userId, objectId)) {
      return personnelMapper.setTenantId(tenantId).queryPersonnelByObjectId(tenantId, userId, objectId)
    }
    return null
  }
  /**
   * 通过部门ID获取部门的：负责人、成员、上级部门、下级部门
   * @param tenantId
   * @param deptId
   * @return
   */
  DepartmentMsg getDepartmentMsgByDeptId(String tenantId, String deptId) {
    if (isAllNotBlank(tenantId, deptId)) {
      return dataRightsDepartmentMapper.setTenantId(tenantId).queryDepartmentMsgByDeptId(tenantId, deptId)
    }
    return null
  }
  /**
   *  通过apiName查询基于条件的规则目标方
   * @param tenantId
   * @param apiName
   * @return
   */
  List<EntityShareReceive> getEntityShareReceiveByApiName(String tenantId, String apiName) {
    if (isAllNotBlank(tenantId, apiName)) {
      return dataMapper.setTenantId(tenantId).queryEntityShareReceiveByApiName(tenantId, apiName)
    }
    return null
  }

  /**
   * 通过apiName查询基于条件的规则规则组
   * @param tenantId
   * @param apiName
   * @return
   */
  List<EntityShareGroup> getEntityShareGroupByApiName(String tenantId, String apiName) {

    if (isAllNotBlank(tenantId, apiName)) {
      return dataMapper.setTenantId(tenantId).queryEntityShareGroupByApiName(tenantId, apiName)
    }
    return null
  }

  /**
   * 通过部门ID查找部门下全部成员
   * @param tenantId
   * @param deptId
   * @return
   */
  List<DepartmentUser> getDepartmentUserByDeptId(String tenantId, String deptId) {

    if (isAllNotBlank(tenantId, deptId)) {
      return dataMapper.setTenantId(tenantId).queryDepartmentUserByDeptId(tenantId, deptId)
    }
    return null
  }

  /**
   * 获取基于来源的共享规则
   * @param tenantId
   * @param objectId
   * @return
   */
  List<Map<String, Object>> getEntityShares(String tenantId, String objectId) {
    if (isAllNotBlank(tenantId, objectId)) {
      List<Principal> principal = dataMapper.setTenantId(tenantId).queryTeamMember(tenantId, objectId, DataRightsDataMapper.PRINCIPAL)
      if (CollectionUtils.isEmpty(principal)) {
        return Lists.newArrayList()
      }
      Personnel personnel = personnelMapper.setTenantId(tenantId).queryPersonnelByObjectId(tenantId, principal.get(0).getUserId(), objectId)
      return dataMapper.setTenantId(tenantId).queryEntityShare(tenantId, objectId, personnelIdsConverter(personnel))
    }
    return Lists.newArrayList()
  }

  /**
   * 获取相关团队成员数据
   * @param tenantId
   * @param objectId
   * @return
   */
  List<Principal> getTeamMember(String tenantId, String objectId) {
    List<Principal> principals = Lists.newArrayList()
    if (isAllNotBlank(tenantId, objectId)) {
      List<Principal> PRINCIPAL = dataMapper.setTenantId(tenantId).queryTeamMember(tenantId, objectId, DataRightsDataMapper.PRINCIPAL)
      List<Principal> JOINT_FOLLOWER = dataMapper.setTenantId(tenantId).queryTeamMember(tenantId, objectId, DataRightsDataMapper.JOINT_FOLLOWER)
      List<Principal> ORDINARY_MEMBER = dataMapper.setTenantId(tenantId).queryTeamMember(tenantId, objectId, DataRightsDataMapper.ORDINARY_MEMBER)
      List<Principal> SERVICE_PERSONNEL = dataMapper.setTenantId(tenantId).queryTeamMember(tenantId, objectId, DataRightsDataMapper.SERVICE_PERSONNEL)
      PRINCIPAL.forEach({ s -> s.setType("负责人") })
      JOINT_FOLLOWER.forEach({ s -> s.setType("联合跟进人") })
      SERVICE_PERSONNEL.forEach({ s -> s.setType("售后人员") })
      ORDINARY_MEMBER.forEach({ s -> s.setType("普通成员") })
      principals.addAll(PRINCIPAL)
      principals.addAll(JOINT_FOLLOWER)
      principals.addAll(ORDINARY_MEMBER)
      principals.addAll(SERVICE_PERSONNEL)
      return principals
    }
    return principals
  }

  /**
   * 通过数据ID和角色类型查询该角色类型下全部成员信息
   * @param tenantId
   * @param objectId
   * @param roleType
   * @return
   */
  List<RoleMember> getRoleMemberByRoleTypeAndObjectId(String tenantId, String objectId, String roleType) {
    if (isAllNotBlank(tenantId, objectId, roleType)) {
      return dataMapper.setTenantId(tenantId).queryRoleMemberByRoleTypeAndObjectId(tenantId, roleType, objectId)
    }
    return null
  }

  /**
   * 获取数据归属部门数据
   * @param tenantId
   * @param objectId
   * @return
   */
  Map<String, String> getDataOwnDepartment(String tenantId, String objectId) {
    if (isAllNotBlank(tenantId, objectId)) {
      return dataMapper.setTenantId(tenantId).queryDataOwnDepartment(tenantId, objectId)
    }
    return Maps.newHashMap()
  }

  /**
   * 获取数据归属部门数据（专表）
   * @param tenantId
   * @param objectId
   * @return
   */
  Map<String, String> getDataOwnDepartmentSpecial(String tenantId, String objectId, String storeTableName) {
    if (isAllNotBlank(tenantId, objectId)) {
      return dataMapper.setTenantId(tenantId).queryDataOwnDepartmentSpecial(tenantId, objectId, storeTableName, storeTableName + "_id")
    }
    return Maps.newHashMap()
  }
  /**
   * 获取数据归属部门数据（专表预置对象）
   * @param tenantId
   * @param objectId
   * @return
   */
  Map<String, String> getDataOwnDepartmentSpecialBiz(String tenantId, String objectId, String storeTableName) {
    if (isAllNotBlank(tenantId, objectId)) {
      return dataMapper.setTenantId(tenantId).queryDataOwnDepartmentSpecialBiz(tenantId, objectId, storeTableName)
    }
    return Maps.newHashMap()
  }
  /**
   * 获取用户组成员数据
   * @param tenantId
   * @param groupId
   * @return
   */
  GroupUser getGroupUserByGroupId(String tenantId, String groupId) {
    if (isAllNotBlank(tenantId, groupId)) {
      return dataMapper.setTenantId(tenantId).queryGroupUserByGroupId(tenantId, groupId)
    }
    return null
  }


  private boolean isAllNotBlank(String... strs) {
    boolean flag = true
    for (String str : strs) {
      flag = flag && StringUtils.isNotBlank(str)
      if (!flag) {
        break
      }
    }
    return flag
  }

  private Set<String> personnelIdsConverter(Personnel personnel) {
    Set<String> ids = Sets.newHashSet()
    if (personnel != null) {
      ids.add(personnel.userId)
      ids.add(personnel.superior)
      ids.addAll(personnel.subordinates.stream().map({ s -> s.get("下级ID") }).collect(Collectors.toSet()))
      ids.add(personnel.role)
      ids.add(personnel.deptId)
      ids.add(personnel.groupId)
      ids = ids.stream().filter({ s -> StringUtils.isNotBlank(s) }).collect(Collectors.toSet())
    }
    return ids
  }

  DtAuth getDtAuth(String authTable, String apiName, String tenantId, String objectId) {
    if (StringUtils.isBlank(apiName) || StringUtils.isBlank(tenantId) || StringUtils.isBlank(objectId)) {
      return Lists.newArrayList()
    }
    String table = storeTableNameService.byDescribeApiName(tenantId, apiName)
    if (StringUtils.isNotBlank(table)) {
      return dataMapper.setTenantId(tenantId).queryDtAuthByObjectId(authTable, ApiNameToStoreTableNameUtil.toNewStoreTableName(table), objectId, tenantId)
    }
    return Lists.newArrayList()
  }


  List<Map<String, Object>> getEntityShareGroupByRuleId(String tenantId, Set<String> ruleId) {
    if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(ruleId)) {
      return Lists.newArrayList()
    }
    return dataMapper.setTenantId(tenantId).queryEntityShareGroupByRuleId(tenantId, ruleId)
  }

  List<Map<String, Object>> getReceiveShareGroupByRuleId(String tenantId, Set<String> ruleId) {
    if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(ruleId)) {
      return Lists.newArrayList()
    }
    return dataMapper.setTenantId(tenantId).queryReceiveShareGroupByRuleId(tenantId, ruleId)
  }

  JSONObject getRoleList(String tenantId) {
    if (StringUtils.isEmpty(tenantId)) {
      return null
    }
    JSONObject dataObject = new JSONObject()
    dataObject.put("tenantId", tenantId)
    dataObject.put("appId", "CRM")
    dataObject.put("userId", "-10000")
    dataObject.put("pageInfo", new HashMap() {
      {
        put("total", null)
        put("pageSize", null)
        put("currentPage", null)
        put("totalPage", null)
      }
    })

    def body = functionalService.checkBody(dataObject.toJSONString())
    return JSONObject.parseObject(SimpleHttpClient.postJson(roleListUrl, JSONObject.toJSONString(body)))
  }
}
