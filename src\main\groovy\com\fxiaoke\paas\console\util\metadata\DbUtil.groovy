package com.fxiaoke.paas.console.util.metadata

import com.google.common.base.Throwables
import groovy.util.logging.Slf4j

import java.sql.*

/**
 * <AUTHOR>
 * Created on 2018/5/30.
 */
@Slf4j
class DbUtil {

  static Connection createConnect(String jdbcUrl, String user, String password) {
    Properties prop = new Properties()
    prop.put("user", user)
    if (password != null) {
      prop.put("password", password)
    }
    try {
      Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver")
      DriverManager.setLoginTimeout(15)
      return DriverManager.getConnection(jdbcUrl, prop)
    } catch (Exception e) {
      log.error("connect failed.", e)
      Throwables.propagate(e)
    }
    //UnReached codes
    return null
  }

  /**
   * a wrapped method to execute select-like sql statement .
   *
   * @param conn Database connection .
   * @param sql sql statement to be executed
   * @return a {@link java.sql.ResultSet}
   * @throws java.sql.SQLException if occurs SQLException.
   */
  static ResultSet query(Connection conn, String sql)
          throws SQLException {
    // 默认3600 s 的query Timeout
    return query(conn, sql, 1000)
  }

  /**
   * a wrapped method to execute select-like sql statement .
   *
   * @param conn Database connection .
   * @param sql sql statement to be executed
   * @return a {@link ResultSet}
   * @throws SQLException if occurs SQLException.
   */
  static ResultSet query(Connection conn, String sql, int fetchSize)
          throws SQLException {
    // 默认3600 s 的query Timeout
    return query(conn, sql, fetchSize, 172800)
  }

  /**
   * a wrapped method to execute select-like sql statement .
   *
   * @param conn Database connection .
   * @param sql sql statement to be executed
   * @param fetchSize
   * @param queryTimeout unit:second
   * @return
   * @throws SQLException
   */
  static ResultSet query(Connection conn, String sql, int fetchSize, int queryTimeout)
          throws SQLException {
    // make sure autocommit is off
    conn.setAutoCommit(false)
    Statement stmt = conn.createStatement(ResultSet.TYPE_FORWARD_ONLY,
            ResultSet.CONCUR_READ_ONLY)
    stmt.setFetchSize(fetchSize)
    stmt.setQueryTimeout(queryTimeout)
    return query(stmt, sql)
  }

  /**
   * a wrapped method to execute select-like sql statement .
   *
   * @param stmt {@link Statement}
   * @param sql sql statement to be executed
   * @return a {@link ResultSet}
   * @throws SQLException if occurs SQLException.
   */
  static ResultSet query(Statement stmt, String sql) throws SQLException {
    return stmt.executeQuery(sql)
  }
}
