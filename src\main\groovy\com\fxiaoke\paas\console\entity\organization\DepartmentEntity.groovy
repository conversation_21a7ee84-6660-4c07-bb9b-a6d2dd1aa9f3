package com.fxiaoke.paas.console.entity.organization

import com.fxiaoke.paas.console.util.organization.constant.OrgConstants
import groovy.transform.ToString
import lombok.Getter
import lombok.Setter

import javax.persistence.Table

/**
 * 部门信息实体类
 * Created by wangxing on 2018/03/15
 */
@Table(name = OrgConstants.TABLE_DEPARTMENT)
@Getter
@Setter
@ToString
class DepartmentEntity {

  String id
  String deptId
  String tenantId
  String name
  String managerId
  String parentId
  Integer status
  String description
  String createBy
  Long createTime
  String lastModifiedBy
  Long lastModifiedTime
  Integer isDeleted
}
