package com.fxiaoke.paas.console.service.hamster;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.hamster.arg.CreateHamsterTaskArg;
import com.facishare.hamster.arg.HamsterLogArg;
import com.facishare.hamster.arg.QuarantineMigrationArg;
import com.facishare.hamster.arg.SkipHamsterTaskItemArg;
import com.facishare.hamster.common.Result;
import com.facishare.hamster.pojo.HamsterTaskItemPojo;
import com.facishare.hamster.pojo.HamsterTaskPojo;
import com.facishare.hamster.result.HamsterLogResult;
import com.facishare.hamster.result.HamsterTaskItemResult;
import com.facishare.hamster.result.HamsterTaskResult;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.paas.console.constant.ConstantString;
import com.fxiaoke.paas.console.entity.hamster.HamsterTemplateItem;
import com.fxiaoke.paas.console.proxy.CmsProxy;
import com.fxiaoke.paas.console.proxy.HamsterTaskProxy;
import com.fxiaoke.paas.console.proxy.OkHttpClient;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j
public class HamsterTaskService {

  @Autowired
  private DbRouterClient dbRouterClient;
  @Autowired
  private EnterpriseEditionService enterpriseEditionService;
  @Autowired
  private HamsterTemplateService hamsterTemplateService;
  @Autowired
  private HamsterTaskProxy hamsterTaskProxy;

  @Autowired
  private CmsProxy cmsProxy;

  private final String token = "746afb412ddd0a5e0f1f68833c42da1f";
  private String SUPPLEMENT_MIGRATION_DATA_URL;

  private String REFRESH_FOREST_BASE_URL;

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("paas-console-base", iConfig -> {
      SUPPLEMENT_MIGRATION_DATA_URL = iConfig.get("supplement-migration-data");
    });

    ConfigFactory.getConfig("fs-paas-console-url", iConfig -> {
      REFRESH_FOREST_BASE_URL = iConfig.get("refresh-url");
    });
  }

  public HamsterTaskResult queryHamsterTask(String taskId) {
    QuarantineMigrationArg arg = new QuarantineMigrationArg();
    arg.setTaskId(taskId);
    return hamsterTaskProxy.queryHamsterTask(arg);
  }

  public HamsterTaskItemResult queryHamsterTaskItem(String taskId) {
    QuarantineMigrationArg arg = new QuarantineMigrationArg();
    arg.setTaskId(taskId);
    return hamsterTaskProxy.queryHamsterTaskItem(arg);
  }

  public HamsterTaskItemResult queryHamsterTaskItem(String taskId, String action) {
    QuarantineMigrationArg arg = new QuarantineMigrationArg();
    arg.setTaskId(taskId);
    arg.setAction(action);
    return hamsterTaskProxy.queryHamsterTaskItem(arg);
  }

  public HamsterLogResult queryHamsterLog(String taskId, String action, String type) {
    HamsterLogArg arg = new HamsterLogArg();
    arg.setTaskId(taskId);
    arg.setAction(action);
    arg.setType(type);
    return hamsterTaskProxy.queryHamsterLog(arg);
  }


  public void saveTask(HamsterTaskPojo task) {
    String tenantId;
    tenantId = task.getTenantId().trim();

    RouterInfo routerInfoPaas = dbRouterClient.queryRouterInfo(tenantId, "CRM", "application", "postgresql");
    RouterInfo routerInfoBi = dbRouterClient.queryRouterInfo(tenantId, "BI", "application", "postgresql");
    task.setId(IdGenerator.get());
    task.setTenantName(getEnterpriseName(tenantId));

    if (routerInfoPaas.getStandalone()) {
      task.setFromSchema("sch_" + tenantId);
    } else {
      task.setFromSchema("public");
    }


    task.setToSchema("sch_" + tenantId);

    task.setJdbcOldPaas(routerInfoPaas.getJdbcUrl());
    task.setJdbcOldBi(routerInfoBi.getJdbcUrl());

    if (StringUtils.isEmpty(routerInfoPaas.getMasterProxyUrl())) {
      task.setPaasPgbouncerOld(cmsProxy.getPgbouncerPaasMaster());
    } else {
      task.setPaasPgbouncerOld(routerInfoPaas.getMasterProxyUrl());
    }

    if (StringUtils.isEmpty(routerInfoBi.getMasterProxyUrl())) {
      task.setBiPgbouncerOld(cmsProxy.getPgbouncerBiMaster());
    } else {
      task.setBiPgbouncerOld(routerInfoBi.getMasterProxyUrl());
    }

    if (StringUtils.isEmpty(task.getPlanId())) {
      task.setPlanId(ConstantString.DEFAULT_PLAN_ID);
    }

    if (StringUtils.isNotEmpty(task.getTemplateId())) {
      List<HamsterTemplateItem> hamsterTemplateIds = hamsterTemplateService.getHamsterTemplateIds(task.getTemplateId());
      List<HamsterTaskItemPojo> hamsterTaskItemPojoList = Lists.newArrayList();
      for (HamsterTemplateItem templateItem : hamsterTemplateIds) {
        HamsterTaskItemPojo hamsterTaskItemPojo = new HamsterTaskItemPojo();
        hamsterTaskItemPojo.setAction(templateItem.getAction());
        hamsterTaskItemPojo.setActionType(templateItem.getActionType());
        hamsterTaskItemPojo.setOperation(templateItem.getOperation());
        hamsterTaskItemPojo.setName(templateItem.getName());
        hamsterTaskItemPojo.setOrder(templateItem.getOrder());
        hamsterTaskItemPojo.setId(IdGenerator.get());
        hamsterTaskItemPojo.setTaskId(task.getId());
        hamsterTaskItemPojo.setCreateBy(task.getCreateBy());
        hamsterTaskItemPojo.setUpdateBy(task.getUpdateBy());
        hamsterTaskItemPojo.setStatus(0);
        hamsterTaskItemPojoList.add(hamsterTaskItemPojo);
      }
      task.setHamsterTaskItemPojoList(hamsterTaskItemPojoList);
    }

    formatJdbcUrl(task);
    hamsterTaskProxy.createHamsterTask(task);
  }

  public Result<String> softDeleteTask(HamsterTaskPojo pojo) {
    Result<String> ret = hamsterTaskProxy.softDeleteHamsterTask(pojo);
    return ret;
  }

  public Result<String> deleteTask(HamsterTaskPojo pojo) {
    Result<String> ret = hamsterTaskProxy.deleteHamsterTask(pojo);
    return ret;
  }

  private void formatJdbcUrl(HamsterTaskPojo task) {
    if (task == null) {
      return;
    }
    task.setJdbcNewBiMaster(formatJdbcUrl(task.getJdbcNewBiMaster()));
    task.setJdbcNewBiSlave(formatJdbcUrl(task.getJdbcNewBiSlave()));
    task.setJdbcOldPaas(formatJdbcUrl(task.getJdbcOldPaas()));
    task.setJdbcOldBi(formatJdbcUrl(task.getJdbcOldBi()));
    task.setJdbcNewPaasMaster(formatJdbcUrl(task.getJdbcNewPaasMaster()));
    task.setJdbcNewPaasSlave(formatJdbcUrl(task.getJdbcNewPaasSlave()));
  }

  private String formatJdbcUrl(String jdbcUrl) {
    if (StringUtils.isEmpty(jdbcUrl)) {
      return jdbcUrl;
    }
    return jdbcUrl.replace("jdbc:postgresql://", "");
  }

  private String getEnterpriseName(String tenantId) {

    List<SimpleEnterpriseData> enterpriseList = getSimpleEnterpriseData(tenantId, enterpriseEditionService);
    if (enterpriseList == null)
      return "--";
    return enterpriseList.get(0).getEnterpriseName();
  }

  public String getEnterpriseEA(String tenantId) {

    List<SimpleEnterpriseData> enterpriseList = getSimpleEnterpriseData(tenantId, enterpriseEditionService);
    if (enterpriseList == null) {
      return "--";
    }
    return enterpriseList.get(0).getEnterpriseAccount();
  }

  private List<SimpleEnterpriseData> getSimpleEnterpriseData(String tenantId, EnterpriseEditionService enterpriseEditionService) {
    BatchGetSimpleEnterpriseDataArg arg = new BatchGetSimpleEnterpriseDataArg();
    Collection<Integer> enterpriseIds = Lists.newArrayList(Integer.valueOf(tenantId));
    arg.setEnterpriseIds(enterpriseIds);

    BatchGetSimpleEnterpriseDataResult batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(arg);

    List<SimpleEnterpriseData> enterpriseList = batchGetSimpleEnterpriseDataResult.getSimpleEnterpriseList();
    if (CollectionUtils.isEmpty(enterpriseList)) {
      return null;
    }
    return enterpriseList;
  }

  public JSONObject supplementMigrateData(String tenantId, String tables, String biz, boolean isCompareData) throws InterruptedException {
    String url = SUPPLEMENT_MIGRATION_DATA_URL + "?token=" + token + "&tenantIds=" + tenantId + "&tables=" + tables + "&service=" + biz + "&isCompareData=" + isCompareData;
    Map<String, Object> headers = Maps.newHashMap();

    return OkHttpClient.get(url, headers);
  }

  public Set<String> queryAllMigrateTable(String tenantId, String biz) {
    try {
      Set<String> result = Sets.newHashSet();

      String url = REFRESH_FOREST_BASE_URL + "/api/v1/inner/schema/query_all_migrate_table?token=" + token + "&tenantId=" + tenantId + "&service=" + biz;
      Map<String, Object> headers = Maps.newHashMap();
      JSONArray jsonArray = OkHttpClient.executeGetArray(url, headers);

      if (jsonArray == null) {
        result.add("all");
        log.info("queryAllMigrateTable is null, tenantId:{}, biz:{}", tenantId, biz);
        return result;
      }

      for (Object v : jsonArray) {
        if (v instanceof String) {
          result.add((String) v);
        }
      }

      return result;
    } catch (Exception e) {
      log.error("queryAllMigrateTable error, tenantId:{}, biz:{}", tenantId, biz, e);
      return Sets.newHashSet("all");
    }
  }

  public Result skipHamsterTaskItem(String taskId, String action, Integer operation, String itemId) {
    SkipHamsterTaskItemArg arg = new SkipHamsterTaskItemArg();
    arg.setTaskId(taskId);
    arg.setAction(action);
    arg.setOperation(operation);
    arg.setItemId(itemId);
    return hamsterTaskProxy.skipHamsterTaskItem(arg);
  }

  public Result<String> startMigration(String taskId, String action, String operation, String name, String itemId) {
    QuarantineMigrationArg arg = new QuarantineMigrationArg();
    arg.setTaskId(taskId);
    arg.setAction(action);
    arg.setOperation(operation);
    arg.setName(name);
    arg.setItemId(itemId);
    return hamsterTaskProxy.startMigration(arg);
  }

  public Result updateHamsterTask(HamsterTaskPojo pojo) {
    CreateHamsterTaskArg arg = new CreateHamsterTaskArg();
    arg.setHamsterTaskPojo(pojo);
    return hamsterTaskProxy.updateHamsterTask(arg);
  }

  public String paasBiCopierDump(String tenantId) {
    return hamsterTaskProxy.paasBiCopierDump(tenantId);
  }

  public String paasBiCopierRecheck(String tenantId) {
    return hamsterTaskProxy.paasBiCopierRecheck(tenantId);
  }

}
