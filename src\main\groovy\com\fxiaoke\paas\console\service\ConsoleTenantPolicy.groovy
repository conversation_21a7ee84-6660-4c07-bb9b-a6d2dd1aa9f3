package com.fxiaoke.paas.console.service

import com.facishare.paas.foundation.persistence.PersistenceConfiguration
import com.facishare.paas.pod.client.DbRouterClient
import com.facishare.paas.pod.dto.RouterInfo
import com.facishare.paas.pod.util.DialectUtil
import com.fxiaoke.release.FsGrayRelease
import com.fxiaoke.release.FsGrayReleaseBiz
import com.github.mybatis.tenant.TenantContext
import com.github.mybatis.tenant.TenantPolicy
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.BooleanUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * <AUTHOR> @date 2020/5/14 18:07
 *
 */
@Slf4j
class ConsoleTenantPolicy implements TenantPolicy {

  @Autowired
  private DbRouterClient dbRouterClient

  private static final FsGrayReleaseBiz GRAY = FsGrayRelease.getInstance("metadata");

  @Override
  TenantContext get(String tenantId, boolean readOnly) {
    boolean usePgBouncer = true;
    String describeApiName = PersistenceConfiguration.DescribeHolder.getDescribeApiName();

    RouterInfo routerInfo = null;
    if(StringUtils.isNotEmpty(describeApiName)) {
      try {
        routerInfo = dbRouterClient.queryRouterInfo(tenantId, "CRM", "fs-metadata-service", DialectUtil.POSTGRESQL, usePgBouncer,describeApiName)
      } catch (Exception e) {
        log.warn("unassigned big object routing")
      }
    }
    if(routerInfo == null) {
      routerInfo = dbRouterClient.queryRouterInfo(tenantId, "CRM", "paas-console", DialectUtil.POSTGRESQL, usePgBouncer)
    }

    if (BooleanUtils.isTrue(ReadThreadLocal.booleanThreadLocal.get()) && routerInfo != null && routerInfo.slaveUrl != null) {
      ReadThreadLocal.booleanThreadLocal.set(false)
      return TenantContext.builder().username(routerInfo.userName).password(routerInfo.passWord).url(routerInfo.slaveUrl).build()
    } else {
      TenantContext.builder().username(routerInfo.userName).password(routerInfo.passWord).url(routerInfo.jdbcUrl).schema(routerInfo.standalone ? "sch_" +
              tenantId : "public").build()
    }

  }
}
