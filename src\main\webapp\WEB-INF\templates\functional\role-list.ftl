<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
    <h1>查询角色列表</h1>
    <ol class="breadcrumb">
        <li><a href="../"><i class="fa fa-dashboard"></i>任务主页</a></li>
        <li><a href=""><i class="fa fa-dashboard"></i></a></li>
    </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info row">
                <div id="warningInfo" class="alert alert-warning hide">
                    <span id="closeInfo" href="#" class="close">&times;</span>
                    <strong id="hitInfo"></strong>
                </div>
                <div class="box-header with-border">
                    <h3 class="box-title"></h3>
                </div>
                <form class="form-horizontal" action="" method="post" id="myForm" role="form"
                      data-toggle="validator">
                    <div class="box-body col-xs-6" style="margin-right: -190px">
                        <div class="form-group">
                            <label for="tenantId" class="col-sm-2 control-label">企业ID</label>
                            <div class="col-sm-6">
                                <input type="text" style="border-radius:5px;" class="form-control" id="tenantId" name="tenantId" value="" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="appId" class="col-sm-2 control-label">AppId</label>
                            <div class="col-sm-6">
                                <select id="appId" name="appId" class="selectpicker show-tick form-control" title="Nothing selected" data-live-search="false">
                                    <#list appIdList! as appId>
                                        <option value="${appId}" <#if ((appId) == "CRM")>selected="selected"</#if>>${appId!}</option>
                                    </#list>
                                </select>
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="userId" class="col-sm-2 control-label">用户ID</label>
                            <div class="col-sm-6">
                                <input type="text" style="border-radius:5px;" class="form-control" id="userId" name="userId" value="" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="key" class="col-sm-2 control-label">键</label>
                            <div class="col-sm-6">
                                <input type="text" style="border-radius:5px;" class="form-control" id="key" name="key" value="" placeholder="选填">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="roleCode" class="col-sm-2 control-label">角色Code</label>
                            <div class="col-sm-6">
                                <textarea id="roleCode" name="roleCode" class="form-control" style="height: 80px;border-radius:5px;"></textarea>
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="key" class="col-sm-2 control-label">角色类型</label>
                            <div class="col-sm-6">
                                <select id="roleType" name="roleType" class="selectpicker show-tick form-control" title="Nothing selected(选填)" data-live-search="false">
                                    <option value="">Nothing selected</option>
                                    <option value="0">0</option>
                                    <option value="1">1</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="total" class="col-sm-2 control-label">Total</label>
                            <div class="col-sm-6">
                                <input type="text" style="border-radius:5px;" class="form-control" id="total" name="total" value="" placeholder="选填">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="pageSize" class="col-sm-2 control-label">PageSize</label>
                            <div class="col-sm-6">
                                <input type="text" style="border-radius:5px;" class="form-control" id="pageSize" name="pageSize" value="" placeholder="选填">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="currentPage" class="col-sm-2 control-label">CurrentPage</label>
                            <div class="col-sm-6">
                                <input type="text" style="border-radius:5px;" class="form-control" id="currentPage" name="currentPage" value="" placeholder="选填">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="totalPage" class="col-sm-2 control-label">TotalPage</label>
                            <div class="col-sm-6">
                                <input type="text" style="border-radius:5px;" class="form-control" id="totalPage" name="totalPage" value="" placeholder="选填">
                            </div>
                        </div>
                        <div class="col-sm-offset-3 col-sm-2">
                            <button type="button" id="findSub" class="btn btn-primary">查询</button>
                        </div>
                    </div>
                    <div class="box-body col-xs-6">
                        <table id="datatable" class="table table-hover table-bordered" cellspacing="0" width="110%">
                            <thead>
                            <tr>
                                <th>角色编号</th>
                                <th>角色名称</th>
                                <th>角色类型</th>
                                <th>AppId</th>
                                <th>企业ID</th>
                                <th>删除标记</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<script>
    $(document).ready(function () {
        var _table = $("#datatable").DataTable({
//            "deferRender": true,
            "processing": true,
            "columnDefs": [],
            "language": {
                "url": "${ctx}/static/js/datatables-zh_CN.json"
            },
            "mark": true
//            "paging":false
        });

//        查询
        $('#findSub').on('click', function () {
            $('#warningInfo').addClass('hide');
            var tenantId = $('#tenantId').val();
            var appId = $('#appId').val();
            var userId = $('#userId').val();
            if (tenantId === "" || appId === "" || userId === "") {
                $('#warningInfo').removeClass('hide');
                $('#hitInfo').html('关键字段不能为空！');
                return;
            }

            var pageInfo = {
                total: $('#total').val(),
                pageSize: $('#pageSize').val(),
                currentPage: $('#currentPage').val(),
                totalPage: $('#totalPage').val()
            };
            var dataObject = {
                tenantId: tenantId,
                appId: appId,
                userId: userId,
                key: $('#key').val(),
                roleCode: $('#roleCode').val(),
                roleType: $('#roleType').val(),
                pageInfo: pageInfo
            };

            $.ajax({
                url: "${CONTEXT_PATH}/func/role-list",
                contentType: "application/json",
                data: JSON.stringify(dataObject),
                dataType: "json",
                type: "POST",
                traditional: true,
                success: function (data) {
                    if (data.code === 200) {
                        $("#datatable").dataTable().fnClearTable();
                        $("#datatable").dataTable().fnAddData(data.data);
                        console.log(data.data);
                    } else {
                        $('#warningInfo').removeClass('hide');
                        $('#hitInfo').html(data.error);
                    }
                }
            });


        });

    });
    /**
     * 信息提示栏关闭
     */
    $('#closeInfo').on('click', function () {
        $('#warningInfo').addClass('hide');
    });

</script>
</#assign>
<#include "../layout/layout-main.ftl" />
