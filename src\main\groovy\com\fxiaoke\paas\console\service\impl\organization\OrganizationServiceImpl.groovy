package com.fxiaoke.paas.console.service.impl.organization

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.common.http.SimpleHttpClient
import com.fxiaoke.paas.console.bean.organization.arg.DepartmentArg
import com.fxiaoke.paas.console.bean.organization.arg.DeptUserArg
import com.fxiaoke.paas.console.bean.organization.arg.EmployeeArg
import com.fxiaoke.paas.console.bean.organization.object.DepartmentObject
import com.fxiaoke.paas.console.bean.organization.object.EmployeeObject
import com.fxiaoke.paas.console.entity.organization.DepartmentEntity
import com.fxiaoke.paas.console.entity.organization.EmployeeEntity
import com.fxiaoke.paas.console.mapper.organization.DepartmentMapper
import com.fxiaoke.paas.console.mapper.organization.PaasConsoleEmployeeMapper
import com.fxiaoke.paas.console.service.organization.OrganizationService
import com.fxiaoke.paas.console.util.organization.typeconvert.DepartmentConvert
import com.fxiaoke.paas.console.util.organization.typeconvert.EmployeeConvert
import com.google.common.base.Strings
import com.google.common.collect.Lists
import lombok.extern.slf4j.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * Created by wangxing on 2018/03/06
 */
@Service
@Slf4j
class OrganizationServiceImpl implements OrganizationService {

  @Autowired
  DepartmentMapper departmentMapper

  @Autowired
  PaasConsoleEmployeeMapper employeeMapper

  @Override
  List<DepartmentObject> getDepartmentIdByCompanyId(String tenantId) {
    List<DepartmentObject> departmentObjects = Lists.newArrayList()
    List<DepartmentEntity> departmentEntities = departmentMapper.setTenantId(tenantId).getDepartmentIdByCompanyId(tenantId)
    for (DepartmentEntity departmentEntity : departmentEntities) {
      DepartmentObject departmentObject = DepartmentConvert.departmentEntityConvertDepartmentObject(departmentEntity)
      departmentObjects.add(departmentObject)
    }
    return departmentObjects
  }

  @Override
  List<DepartmentObject> getDepartment(DepartmentArg departmentArg) {
    List<DepartmentObject> departmentList = Lists.newArrayList()
    List<DepartmentEntity> departmentEntities = departmentMapper.setTenantId(departmentArg.getTenantId()).getDepartment(departmentArg.getTenantId(), departmentArg.getDeptIds())
    for (DepartmentEntity departmentEntity : departmentEntities) {
      DepartmentObject departmentObject = DepartmentConvert.departmentEntityConvertDepartmentObject(departmentEntity)
      departmentList.add(departmentObject)
    }
    return departmentList
  }

  @Override
  List<DepartmentObject> getSubordinateDepartment(DepartmentArg departmentArg) {
    List<DepartmentObject> departmentObjects = Lists.newArrayList()
    List<DepartmentEntity> departmentEntities = departmentMapper.setTenantId(departmentArg.getTenantId()).getSubordinateDepartment(departmentArg.getTenantId(), departmentArg.getParentId())
    for (DepartmentEntity departmentEntity : departmentEntities) {
      DepartmentObject departmentObject = DepartmentConvert.departmentEntityConvertDepartmentObject(departmentEntity)
      departmentObjects.add(departmentObject)
    }
    return departmentObjects
  }

  @Override
  List<DepartmentObject> getRelationalDepartment(DeptUserArg deptUserArg) {
    List<DepartmentObject> departmentList = Lists.newArrayList()
    List<DepartmentEntity> departmentEntities = departmentMapper.setTenantId(deptUserArg.getTenantId()).getRelationalDepartment(deptUserArg.getTenantId(), deptUserArg.getUserId(), deptUserArg.getType())
    for (DepartmentEntity departmentEntity : departmentEntities) {
      DepartmentObject departmentObject = DepartmentConvert.departmentEntityConvertDepartmentObject(departmentEntity)
      departmentList.add(departmentObject)
    }
    return departmentList
  }

  /**
   * 通过企业ID查询所有员工
   * @param tenantId
   * @return
   */
  @Override
  List<EmployeeObject> getEmployeeIdByCompanyId(String tenantId) {
    List<EmployeeObject> employeeList = Lists.newArrayList()
    List<EmployeeEntity> employeeEntities = employeeMapper.setTenantId(tenantId).getEmployeeIdByCompanyId(tenantId)
    for (EmployeeEntity employeeEntity : employeeEntities) {
      EmployeeObject employeeObject = EmployeeConvert.employeeEntityConvertEmployeeObject(employeeEntity)
      employeeList.add(employeeObject)
    }
    return employeeList
  }

  /**
   * 通过企业ID和员工ID查询员工信息
   * @param employeeArg
   * @return
   */
  @Override
  List<EmployeeObject> getEmployee(EmployeeArg employeeArg) {
    List<EmployeeObject> employeeList = Lists.newArrayList()
    List<EmployeeEntity> employeeEntities = employeeMapper.setTenantId(employeeArg.getTenantId()).getEmployee(employeeArg.getTenantId(), employeeArg.getUserIds())
    for (EmployeeEntity employeeEntity : employeeEntities) {
      EmployeeObject employeeObject = EmployeeConvert.employeeEntityConvertEmployeeObject(employeeEntity)
      employeeList.add(employeeObject)
    }
    return employeeList
  }

  /**
   * 根据企业ID和用户ID获取所属部门
   * @return
   */
  @Override
  List<Map<String, String>> getDeptByTenantIdAndUserId(String tenantId, List<String> userIds) {
    employeeMapper.setTenantId(tenantId).getDeptByTenantIdAndUserId(tenantId, userIds)
  }

  @Override
  List<EmployeeObject> getRelationalEmployee(DeptUserArg deptUserArg) {
    List<EmployeeObject> employeeList = Lists.newArrayList()
    List<EmployeeEntity> employeeEntities = employeeMapper.setTenantId(deptUserArg.getTenantId()).getRelationalEmployee(deptUserArg.getTenantId(), deptUserArg.getDepartmentId(), deptUserArg.getType())
    for (EmployeeEntity employeeEntity : employeeEntities) {
      EmployeeObject employeeObject = EmployeeConvert.employeeEntityConvertEmployeeObject(employeeEntity)
      employeeList.add(employeeObject)
    }
    return employeeList
  }

  /**
   * 根据电话号码查信息
   * @param tel 电话号码
   * @return
   */
  @Override
  List<EmployeeObject> getEmployeeByTel(String tel) {
    def s = SimpleHttpClient.post("http://oss.foneshare.cn/flog/admin/user/info/search", ["telId": tel])
    List<EmployeeObject> employeeList = Lists.newArrayList()
    if (!Strings.isNullOrEmpty(s)) {
      def userInfos = JSONObject.parseObject(s)["userInfos"]
      if (userInfos == null) {
        return Lists.newArrayList()
      }
      JSONObject.parseArray(userInfos.toString()).forEach { item ->
        def user = JSONObject.parseObject(item.toString())
        EmployeeObject employeeObject = new EmployeeObject()
        employeeObject.tenantId = user["ei"] as String
        employeeObject.enterpriseId = user["enterpriseId"] as String
        employeeObject.userId = user["userId"] as String
        employeeObject.department = user["enterpriseName"] as String
        employeeObject.name = user["name"] as String
        employeeObject.nickname = user["pinyin"] as String
        employeeList.add(employeeObject)
      }
    }
    return employeeList
  }

  /**
   * 组织架构树状图
   * @param tenantId
   * @param parentId
   * @return
   */
  @Override
  List<DepartmentObject> departmentTree(String tenantId, String parentId) {
    List<DepartmentObject> departmentObjectList = Lists.newArrayList()
    if (parentId == null || parentId.size() == 0) {
      departmentTreeUtil(departmentObjectList, tenantId, null)
    } else {
      departmentTreeUtil(departmentObjectList, tenantId, parentId)
    }
    return departmentObjectList
  }

  void departmentTreeUtil(List<DepartmentObject> departmentObjectList, String tenantId, String parentId) {
    //通过企业ID和部门ID查询子部门信息
    List<DepartmentEntity> departmentEntityList = departmentMapper.setTenantId(tenantId).getSubordinateDepartment(tenantId, parentId)
    List<String> deptIds = departmentEntityList.stream().map({ it -> it.getDeptId() }).collect()
    if (deptIds.size() > 0) {
      //查询部门下的子部门
      List<String> parentIds = departmentMapper.setTenantId(tenantId).listParentId(tenantId, deptIds)
      for (DepartmentEntity departmentEntity : departmentEntityList) {
        DepartmentObject departmentObject = DepartmentConvert.departmentEntityConvertDepartmentObject(departmentEntity)
        if (parentIds.contains(departmentObject.deptId)) {
          List<DepartmentObject> list = Lists.newArrayList()
          departmentObject.setNodes(list)
        }
        departmentObjectList.add(departmentObject)
      }
    }
  }
}
