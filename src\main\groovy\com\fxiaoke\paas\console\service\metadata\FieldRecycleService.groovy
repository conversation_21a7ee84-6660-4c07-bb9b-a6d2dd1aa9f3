package com.fxiaoke.paas.console.service.metadata;

import com.facishare.paas.license.proxy.DescribeProxy;
import com.fxiaoke.paas.common.service.SchemaHelper;
import com.facishare.paas.pod.client.DbRouterClient;
import com.fxiaoke.paas.console.entity.metadata.MtDescribe;
import com.fxiaoke.paas.console.mapper.metadata.DataExtraMapper;
import com.fxiaoke.paas.console.mapper.metadata.DataMapper;
import com.fxiaoke.paas.console.mapper.metadata.DescribeMapper;
import com.fxiaoke.paas.console.mapper.metadata.FieldMapper
import com.fxiaoke.paas.console.mapper.metadata.MtIndexMapper;
import com.fxiaoke.paas.console.mapper.metadata.MtRelationMapper;
import com.fxiaoke.paas.console.mapper.metadata.MtUniqueMapper;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2024/2/29 11:49
 */
@Service
@Slf4j
public class FieldRecycleService {
  @Resource
  private SchemaHelper schemaHelper;
  @Resource
  private FieldMapper fieldMapper;
  @Resource
  private DescribeMapper describeMapper;
  @Resource
  private DataExtraMapper dataExtraMapper;
  @Resource
  private MtUniqueMapper  mtUniqueMapper;
  @Resource
  private MtRelationMapper mtRelationMapper;
  @Resource
  private MtIndexMapper mtIndexMapper;
  @Resource
  private DbRouterClient dbRouterClient;
  @Resource
  private PurgeCacheService purgeCacheService;

  private Map<String, RateLimiter> rateLimiterMap = Maps.newConcurrentMap();


  public Map<String, Integer> recycleField(String tenantId, String describeApiName, String fieldApiNames) {
    Map<String, Integer> result = new HashMap()
    MtDescribe mtDescribe = describeMapper.setTenantId(tenantId).findDescribeByTenantIdAndApiName(tenantId, describeApiName);
    fieldApiNames = "'" + fieldApiNames.replaceAll(",","','") +  "'";
    //回收mt_data_extra
    int affect = -1;
    int total = 0;
    while (affect !=0) {
      affect = dataExtraMapper.setTenantId(tenantId).recycleDataExtra(tenantId, describeApiName, fieldApiNames);
      total += affect
      limit(tenantId, affect)
    }
    result.put("mt_data_extra", total)

    //回收mt_unique
    affect = -1;
    total = 0;
    while (affect !=0) {
      affect = mtUniqueMapper.setTenantId(tenantId).recycleUnique(tenantId, describeApiName, fieldApiNames);
      total += affect
      limit(tenantId, affect)
    }
    result.put("mt_unique", total)


    //回收mt_relation
    affect = -1;
    total = 0;
    while (affect !=0) {
      affect = mtRelationMapper.setTenantId(tenantId).recycleRelation(tenantId, describeApiName, fieldApiNames);
      total += affect
      limit(tenantId, affect)
    }
    result.put("mt_relation", total);

    //回收mt_index
    affect = -1;
    total = 0;
    while (affect !=0) {
      affect = mtIndexMapper.setTenantId(tenantId).recycleMtIndex(tenantId, describeApiName, fieldApiNames);
      total += affect
      limit(tenantId, affect)
    }
    result.put("mt_index", total);

    //清空字段对应槽位数据（仅public）
    if (!schemaHelper.isSpecialSchema(tenantId)) {
      List<String> fieldNums = fieldMapper.setTenantId(tenantId).queryDeletedFieldNum(tenantId, mtDescribe.getDescribeId(), fieldApiNames);
      if (CollectionUtils.isNotEmpty(fieldNums)) {
        String table = StringUtils.isNotBlank(mtDescribe.getStoreTableName()) ? mtDescribe.getStoreTableName() : "mt_data";
        String id = null;
        affect = -1;
        total = 0;
        while (affect != 0) {
          List<String> ids = fieldMapper.setTenantId(tenantId).queryDataIds(tenantId, describeApiName, table,  id, 100);
          if (CollectionUtils.isEmpty(ids)) {
            break;
          }
          id = ids.get(ids.size() - 1);
          affect = ids.size();
          total += affect
          fieldMapper.setTenantId(tenantId).clearFieldData(tenantId, fieldNums, ids);
          limit(tenantId, affect)
        }
        result.put("data", total)
      }
    }


    //删除字段
    int count = fieldMapper.setTenantId(tenantId).recycleField(tenantId, mtDescribe.getDescribeId(), fieldApiNames);
    result.put("mt_field", count)
    purgeCacheService.purgeCacheByTenantIdS(tenantId);
    return result;
  }

  private void limit(String tenantId, int affect) {
    if (affect > 0) {
      getRateLimiter(tenantId).acquire(Math.min(affect, 100));
    }
  }

  private RateLimiter getRateLimiter(String tenantId) {
    String jdbcUrl = dbRouterClient.queryJdbcUrl(tenantId, "CRM", "postgresql");
    return rateLimiterMap.computeIfAbsent(jdbcUrl, { v -> RateLimiter.create(100D) });
  }

}
