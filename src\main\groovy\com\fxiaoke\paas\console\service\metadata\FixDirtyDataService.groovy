package com.fxiaoke.paas.console.service.metadata

import com.clickhouse.client.internal.google.common.collect.Maps
import com.clickhouse.client.internal.google.common.util.concurrent.RateLimiter
import com.facishare.paas.pod.client.DbRouterClient
import com.fxiaoke.paas.console.entity.metadata.MtDescribe
import com.fxiaoke.paas.console.mapper.metadata.DataMapper
import com.fxiaoke.paas.console.mapper.metadata.DescribeMapper
import com.fxiaoke.paas.console.mapper.metadata.FieldMapper
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Service

import javax.annotation.Resource
import java.util.stream.Collectors

@groovy.util.logging.Slf4j
@Service
class FixDirtyDataService {

  @Resource
  private DataMapper dataMapper;
  @Resource
  private FieldMapper fieldMapper;
  @Resource
  private DescribeMapper describeMapper;
  @Resource
  private DbRouterClient dbRouterClient;

  private Map<String, RateLimiter> rateLimiterMap = Maps.newConcurrentMap();

  private static String QUERY_FIELD_NUM_SQL = "select field_num from mt_field where tenant_id = '%s' and describe_id = '%s' and api_name =any('%s') and define_type='custom' and status='new'";



  public String fixDirtyData(String tenantId, String describeApiName, String fieldApiNames, String dirtyValue, String id) {
    try {
      MtDescribe mtDescribe = describeMapper.setTenantId(tenantId).findDescribeByTenantIdAndApiName(tenantId, describeApiName);
      fieldApiNames = "{\""+fieldApiNames.replaceAll(",","\",\"") + "\"}"
      List<Map<String, String>> fieldNums = fieldMapper.setTenantId(tenantId).findFieldByDescribeId2(String.format(QUERY_FIELD_NUM_SQL, tenantId, mtDescribe.getDescribeId(), fieldApiNames));
      if (StringUtils.isNotEmpty(id)) {
        for (Map map : fieldNums) {
          String fieldNum = map.get("field_num");
          dataMapper.clearDirtyData(tenantId, describeApiName, "value" + fieldNum, dirtyValue, "{\"" + id + "\"}");
        }
      } else {
        int affect = -1;
        int affectRow = 0;
        String lastId = null;
        while (affect != 0) {
          List<String> idList = fieldMapper.setTenantId(tenantId).queryDataIds(tenantId, describeApiName, "mt_data", lastId, 10);
          if (CollectionUtils.isEmpty(idList)) {
            break;
          }
          lastId = idList.get(idList.size() - 1);
          affect = idList.size();
          String ids = "{\"" + idList.join("\",\"") + "\"}";
          for (Map map : fieldNums) {
            String fieldNum = map.get("field_num");
            affectRow = dataMapper.setTenantId(tenantId).clearDirtyData(tenantId, describeApiName, "value" + fieldNum, dirtyValue, ids)
            limit(tenantId, affectRow)
          }
        }
      }
      return "SUCCESS";
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return e.getMessage();
    }
  }

  private void limit(String tenantId, int affect) {
    if (affect > 0) {
      getRateLimiter(tenantId).acquire(Math.min(affect, 100));
    }
  }

  private RateLimiter getRateLimiter(String tenantId) {
    String jdbcUrl = dbRouterClient.queryJdbcUrl(tenantId, "CRM", "postgresql");
    return rateLimiterMap.computeIfAbsent(jdbcUrl, { v -> RateLimiter.create(10D) });
  }
}
