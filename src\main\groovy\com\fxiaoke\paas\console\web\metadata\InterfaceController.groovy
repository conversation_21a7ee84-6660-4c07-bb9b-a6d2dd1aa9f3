package com.fxiaoke.paas.console.web.metadata

import com.alibaba.fastjson.JSON
import com.fxiaoke.paas.console.entity.log.SpecialTable
import com.fxiaoke.paas.console.mapper.log.SpecialTableMapper
import com.google.common.collect.Lists
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody

/**
 * 对外部提供接口
 * <AUTHOR>
 * Created on 2018/8/1.
 */
@RequestMapping(value = "/interface")
@Controller
@Slf4j
class InterfaceController {

  @Autowired
  SpecialTableMapper specialTableMapper

  /**
   * 查询专表接口
   */
//  @SystemControllerLog(description = "元数据 -- 接口查询专表")
  @RequestMapping(value = "/findSpecialTable")
  @ResponseBody
  def findSpecialTable() {
    List<SpecialTable> specialTableList = Lists.newArrayList()
    try {
      specialTableList = specialTableMapper.findAll()
    } catch (Exception e) {
      log.error("查询专表接口失败，error:", e)
    }
    return JSON.toJSONString(specialTableList)
  }


}
