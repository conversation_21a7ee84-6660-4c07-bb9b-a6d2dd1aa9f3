<#assign headContent>
<link href="//static.foneshare.cn/oss/AdminLTE/plugins/datatables/dataTables.bootstrap.css" rel="stylesheet" type="text/css"/>
<link type="text/css" href="//static.foneshare.cn/oss/datatables-checkboxes/dataTables.checkboxes.css" rel="stylesheet"/>
<style>

</style>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
    <h1>企业下字段列表</h1>
    <ol class="breadcrumb">
        <li><a href="../"><i class="fa fa-dashboard"></i>主页</a></li>
        <li><a href="javascript:history.go(-1)"><i class="fa fa-dashboard"></i>统计概况</a></li>
        <li><a href=""><i class="fa fa-dashboard"></i>字段列表</a></li>
    </ol>
</section>
</#assign>

<#assign bodyContent>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div id="container"></div>
            <div class="box box-info">
                <div class="box-body">
                    <table id="datatable" class="table table-hover table-bordered" cellspacing="0" width="100%">
                        <thead>
                        <tr>
                            <th>fieldId</th>
                            <th>tenantId</th>
                            <th>describeId</th>
                            <th>apiName</th>
                            <th>type</th>
                            <th>fieldLabel</th>
                            <th>createTime</th>
                            <th>详情</th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="box-footer clearfix">
                </div>
            </div>
        </div>
    </div>
</section>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script type="application/javascript">
    var data = ${data!};
    $(document).ready(function () {
        console.log(data);
        var table = $("#datatable").DataTable({
            "data": data,
            "deferRender": true,
            "language": {
                "url": "${ctx}/static/js/datatables-zh_CN.json"
            },
            "columnDefs": [{
                "targets": 7,
                "render": function (data, type, row, meta) {
                    if (type === 'display') {
                        return "<a href='${ctx}/metadata/describe-query?tenantId=" + row[1] + "&describeId=" + data + "'>详情</a>";
                    }
                    return data;
                }
            }
            ],
            "mark": true,
            "iDisplayLength": 25,
            "bSortClasses": true,
            "order": [[6, 'desc']]
        });
    });

</script>
</#assign>
<#include "../layout/layout-main.ftl" />
