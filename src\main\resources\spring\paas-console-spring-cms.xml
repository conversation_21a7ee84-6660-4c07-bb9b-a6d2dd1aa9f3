<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:c="http://www.springframework.org/schema/c"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

  <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor" c:placeholderConfigurer-ref="paasConsoleAutoConf"/>

  <bean id="paasConsoleAutoConf" class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
        p:fileEncoding="UTF-8"
        p:ignoreResourceNotFound="true"
        p:ignoreUnresolvablePlaceholders="false"
        p:location="classpath:application.properties"
        p:configName="db-paas-console,fs-pod-client-config,fs-paas-tissue,paas-console-xxl-job,paas-console-remotecall,mongo-support-workflow,dubbo-common"/>

</beans>
