package com.fxiaoke.paas.console.web.dmlmanager

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.service.OKHttpService
import com.github.autoconf.ConfigFactory
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.PostConstruct
import java.security.MessageDigest


/**
 * <AUTHOR> @date 2019/9/16 4:34 PM
 */
@Controller
@Slf4j
@RequestMapping("/dmlmanager")
class DmlManagerController {
  @Autowired
  private OKHttpService okHttpService
  private String dmlManagerUrl
  private String copierUrl
  private String refreshUrl

  @RequestMapping("/page")
  String index(ModelMap model) {
    "metadata/dmlmanager"
  }
  private final String token = "149afd631693c895f81e508eb5aaef37"


  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-console-url", { iConfig ->
      this.dmlManagerUrl = iConfig.get("dmlmanager-url")
      this.refreshUrl = iConfig.get("refresh-url")
      this.copierUrl = iConfig.get("copier-url")
    })
  }


  @GetMapping(path = "/init", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- Schema创建")
  def create(String tenantId, String paasJdbcUrl, String biJdbcUrl) {
    if (StringUtils.isAllBlank(paasJdbcUrl, biJdbcUrl)) {
      if (StringUtils.isBlank(tenantId)) {
        return ["code": 200, "info": "tenantId、paasJdbcUrl、biJdbcUrl不允许全部为空"]
      }
      JSONObject bothPara = new JSONObject()
      bothPara.put("tenantId", tenantId)
      new Thread({ -> okHttpService.postJSON(this.dmlManagerUrl + "/init", bothPara) }, "create-both-schema-thread").start()
      return ["code": 200, "info": "success"]
    }

    if (StringUtils.isAnyBlank(tenantId, paasJdbcUrl, biJdbcUrl)) {
      return ["code": 200, "info": "tenantId、paasJdbcUrl、biJdbcUrl均不能为空"]
    }
    JSONObject passParam = new JSONObject()
    passParam.put("tenantId", tenantId)
    passParam.put("targetJdbcUrl", paasJdbcUrl)

    JSONObject biParam = new JSONObject()
    biParam.put("tenantId", tenantId)
    biParam.put("targetJdbcUrl", biJdbcUrl)

    new Thread({ -> okHttpService.postJSON(this.dmlManagerUrl + "/init", passParam) }, "create-paas-schema-thread").start()
    new Thread({ -> okHttpService.postJSON(this.dmlManagerUrl + "/init", biParam) }, "create-bi-schema-thread").start()
    return ["code": 200, "info": "success"]

  }


  @GetMapping(path = "/run", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  @SystemControllerLog(description = "元数据--执行schema全量迁移")
  def run(String key, String tenantIds) {
    if (token.equalsIgnoreCase(getMD5Str(key)) && StringUtils.isNotBlank(tenantIds)) {
      okHttpService.getForm(refreshUrl + "api/v1/inner/schema/run?token=746afb412ddd0a5e0f1f68833c42da1f&tenantIds=" + tenantIds)
      return ["code": 200, "info": "success"]
    } else {
      return ["code": 200, "info": "key is not matched"]
    }
  }

  @GetMapping(path = "/stop", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  @SystemControllerLog(description = "元数据--停止schema全量迁移")
  def stop(String key) {
    if (token.equalsIgnoreCase(getMD5Str(key))) {
      okHttpService.getForm(refreshUrl + "api/v1/inner/schema/stop?token=746afb412ddd0a5e0f1f68833c42da1f")
      return ["code": 200, "info": "success"]
    } else {
      return ["code": 200, "info": "key is not matched"]
    }
  }

  private static String getMD5Str(String str) throws Exception {
    try {
      MessageDigest md = MessageDigest.getInstance("MD5")
      md.update(str.getBytes())
      return new BigInteger(1, md.digest()).toString(16)
    } catch (Exception e) {
      throw new Exception("MD5加密出现错误，" + e.toString())
    }
  }

  @GetMapping(path = "/copier/run", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  @SystemControllerLog(description = "元数据--开始schema增量同步")
  def copierRun() {
    okHttpService.getForm(this.copierUrl + "/run")
    return ["code": 200, "info": "success"]
  }

  @GetMapping(path = "/copier/stop", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  @SystemControllerLog(description = "元数据--关闭schema增量同步")
  def copierStop() {
    okHttpService.getForm(this.copierUrl + "/stop")
    return ["code": 200, "info": "success"]
  }

}