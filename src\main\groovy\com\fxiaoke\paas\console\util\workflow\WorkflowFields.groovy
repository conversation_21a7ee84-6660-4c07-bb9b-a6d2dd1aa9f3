package com.fxiaoke.paas.console.util.workflow

/**
 * Created by yangxw on 2018/3/5.
 */
final class WorkflowFields {

  static final String NAME = "name"
  static final String DESC = "description"
  static final String ORGANIZATION_ID = "organizationId"
  static final String DEPLOYED_BY = "deployedBy"
  static final String WORKFLOW_ID = "workflowId"
  static final String SOURCE_WORKFLOW_ID = "sourceWorkflowId"
  static final String WORKFLOW_INSTANCE_ID = "workflowInstanceId"
  static final String CREATOR = "creator"


  static final String CREATE_TIME = "createTime"
  static final String MODIFIER = "modifier"
  static final String MODIFY_TIME = "modifyTime"
  static final String ENABLE = "enable"
  static final String TRIGGER = "trigger"
  static final String VARIABLES = "variables"
  static final String OBJECT_TYPE = "entityName"
  static final String OBJECT_FIELDS = "objectFields"
  static final String TYPE = "type"
  static final String TENANT_ID = "tenantId"
  static final String ENTITY_ID = "entityId"
  static final String ENTITY_NAME = "entityName"
  static final String APP_ID = "appId"
  static final String APP_NAME = "appName"
  static final String OBJECT_ID = "objectId"
  static final String APPLICANT_ID = "applicantId"
  static final String FLOW_TYPE = "flowType"
  /**
   * 以下是新加的字段
   */
  static final String DELETED = "deleted"
  static final String REPLY_TIME = "replyTime"
  static final String USER_ID = "userId"
  static final String ACTION_TYPE = "actionType"
  static final String OPINION = "opinion"
  static final String STATE = "state"
  static final String ID = "_id"
  static final String AGREE_BIZ_OPERATION_ID = "agreeBizOperationId"
  static final String REJECT_BIZ_OPERATION_ID = "rejectBizOperationId"


//     static final class VersionsLock {
//
//         static final String OWNER = "owner"
//         static final String TIME = "time"
//
//        private VersionsLock() {
//
//        }
//    }
//
//     static final class Versions {
//
//         static final String WORKFLOW_NAME = "workflowName"
//         static final String VERSION_IDS = "versionIds"
//         static final String LOCK = "lock"
//
//        private Versions() {
//
//        }
//    }

}
