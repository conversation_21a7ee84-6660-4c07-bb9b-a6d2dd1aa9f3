package com.fxiaoke.paas.console.service.metadata


import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification
/**
 * <AUTHOR>
 * Created on 2018/3/20.
 */
@ContextConfiguration(locations = ["classpath:mapperContext.xml"])
class SqlQueryServiceTest extends Specification {
  @Autowired
  SqlQueryService sqlQueryService

  /**
   * 测试sql
   * @param sql
   * @return
   */
  def checkSql(String sql) {
    sql = sql.trim().toLowerCase()
    String[] str = ["delete ", "drop ", "truncate ", "update ", "insert "]
    for (int i = 0; i < str.size(); i++) {
      if (sql.indexOf(str[i]) != -1) {
        return false
      }
    }
    return true
  }

  def "sqlQuery"() {
    given:
    def sql = "select * from mt_describe where tenant_id = '2' limit 2"
    def tenantId = "2"
    def module = "CRM"
    def pkg = "CRM"
    def resourceType = "pg"
    expect:
    sqlQueryService.sqlQuery(sql, module, pkg, resourceType, tenantId)
  }

  def "checkSql"() {
    given:
    def sql = "select * from table"
    expect:
    checkSql(sql)
  }

  def "tenantIdPattern"() {
    given:
    def sql = "SELECT * FROM mt_data WHERE tenant_id= '55732' LIMIT 10"
    expect:
    sqlQueryService.tenantIdPattern(sql)
  }

  def "explainQuery"() {
    given:
    def sql = "select object_id from dt_team where tenant_id = '556015' and package = 'CRM' and object_describe_api_name = 'object_1tOi7__c' and member_type = 0 and member_id = '1870' and is_deleted = 0  union  select object_id from dt_team team inner join dt_user_leader_cache leader  on leader.tenant_id = team.tenant_id and leader.user_id = team.member_id  and team.package = 'CRM' and team.object_describe_api_name = 'object_1tOi7__c' and team.member_type = 0 and team.is_deleted = 0  where leader.tenant_id = '556015' and leader.leader_id = '1870' union  select object_id from dt_team as team  inner join dt_dept_user_cache as dept_user on  team.tenant_id = dept_user.tenant_id  and team.package = 'CRM' and team.object_describe_api_name = 'object_1tOi7__c' and team.member_id = dept_user.user_id and team.member_type = 0 and team.is_deleted = 0  where dept_user.tenant_id = '556015' and dept_user.dept_id  in   ( select dt_dept_user_cache.dept_id from dt_dept_user_cache where dt_dept_user_cache.tenant_id = '556015' and dt_dept_user_cache.user_id = '1870' and relation_type = 1) and dept_user.relation_type = 0  union  select object_id from dt_team as team inner join dt_entity_share_cache as entity_share  on team.tenant_id = entity_share.tenant_id  and team.package = entity_share.app_id  and team.object_describe_api_name = entity_share.entity_id  and team.member_id = entity_share.share_user   and team.member_type = 0 and team.role_type = '1' and team.is_deleted = 0  where entity_share.tenant_id = '556015' and entity_share.app_id='CRM' and entity_share.entity_id = 'object_1tOi7__c' and  ( (entity_share.receive_user = '1870' and entity_share.receive_type = 0)    or (entity_share.receive_user in (select group_user.group_id from org_group_user group_user where group_user.tenant_id = '556015' and group_user.package = 'CRM' and  group_user.user_id = '1870' and group_user.is_deleted = 0 ) and entity_share.receive_type = 1)    or (entity_share.receive_user in (select dept_user.dept_id from org_dept_user dept_user where dept_user.tenant_id = '556015'  and dept_user.user_id = '1870' and dept_user.is_deleted = 0) and entity_share.receive_type = 2) or ( entity_share.receive_user in ('59b17aaef88eaa8305a825ff','00000000000000000000000000000015') and entity_share.receive_type =4));"
    def tenantId = "2"
    expect:
    sqlQueryService.explainQuery(sql, tenantId)
  }


  def "sqlFindBi"() {
    given:
    def sql = "SELECT * FROM attach WHERE ei = '2' LIMIT 10"
    def module = "BI"
    def pkg = "BI"
    def resourceType = "pg"
    def tenantId = "2"
    expect:
    sqlQueryService.sqlQueryForBi(sql, module, pkg, resourceType, tenantId)
  }

}
