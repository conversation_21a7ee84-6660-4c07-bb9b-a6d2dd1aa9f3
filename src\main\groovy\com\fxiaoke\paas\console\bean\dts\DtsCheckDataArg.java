package com.fxiaoke.paas.console.bean.dts;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/25 11:36
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DtsCheckDataArg implements Serializable {
    private String ei;
    private String opType;
    private String serverType;
    private String objectApiName;
    private String queryData;
    private String serviceHost;

}
