package com.fxiaoke.paas.console.web.metadata

import com.alibaba.excel.EasyExcel
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.facishare.paas.pod.client.DbRouterClient
import com.facishare.stone.sdk.StoneProxyApi
import com.facishare.stone.sdk.request.StoneFileUploadRequest
import com.facishare.stone.sdk.response.StoneFileUploadResponse
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData
import com.facishare.uc.api.service.EnterpriseEditionService
import com.fxiaoke.common.http.HttpClient
import com.fxiaoke.helper.CollectionHelper
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.paas.console.entity.metadata.DictExcelData
import com.fxiaoke.paas.console.service.metadata.JdbcService
import com.fxiaoke.template.InternUtils
import com.google.common.base.CharMatcher
import com.google.common.base.Joiner
import com.google.common.base.Splitter
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import groovy.util.logging.Slf4j
import lombok.Cleanup
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.io.IOUtils
import org.apache.commons.lang3.SerializationUtils
import org.apache.commons.lang3.StringUtils
import org.postgresql.util.PGobject
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.Resource
import javax.servlet.http.HttpServletResponse
import java.nio.file.Path
import java.nio.file.Paths

/**
 * 获取db存储词典信息（存储列、option等）
 *
 * @date 2021/10/8 17:59
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/rest/metadata/dict")
@Controller
class MetadataDictController {
    @Autowired
    DbRouterClient dbRouterClient
    @Autowired
    JdbcService jdbcService
    @Resource
    private StoneProxyApi stoneProxyApi
    @Autowired
    private EnterpriseEditionService enterpriseEditionService
    private Map<String, DictExcelData> systemFieldMap = systemFieldDict()

    @GetMapping(value = "/paas-dict", produces = "application/json; charset=UTF-8")
    @ResponseBody
    def queryByObjectApiNames(String tenantId,
                              @RequestParam(defaultValue = "*") String objectApiNames,
                              @RequestParam(defaultValue = "true") boolean checkDataExists,
                              @RequestParam(defaultValue = "false") boolean isDatax
    ) {
        return query(tenantId, objectApiNames, checkDataExists, isDatax)
    }


    @GetMapping(value = "/export-paas-dict", produces = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8')
    def exportExcel(String tenantId,
                    @RequestParam(defaultValue = "*") String objectApiNames,
                    @RequestParam(defaultValue = "true") boolean checkDataExists,
                    @RequestParam(defaultValue = "false") boolean isDatax,
                    HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8")
        response.setContentType('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8')
        String enterpriseName = getEnterpriseName(tenantId)
        enterpriseName = StringUtils.isBlank(enterpriseName) ? "${tenantId}-数据字典" : enterpriseName
        String fileName = CharMatcher.anyOf('+ ').replaceFrom(URLEncoder.encode(enterpriseName, 'UTF-8'), '%20')
        response.setHeader("Content-disposition", "attachment;filename=${fileName}.xlsx")

        List<DictExcelData> lines = query(tenantId, objectApiNames, checkDataExists, isDatax)
        EasyExcel.write(response.getOutputStream(), DictExcelData.class).sheet("数据字典").doWrite(lines)
    }

    @PostMapping(value = "/func-paas-dict", produces = "application/json; charset=UTF-8")
    @ResponseBody
    String handlePaasDictByFunction(@RequestBody JSONObject dataObject) {
        log.info("func paas dict params: {}",dataObject)
        String tenantId = dataObject.getString("tenantId")
        String uploadEa = dataObject.getString("uploadEa")
        String cloudName = dataObject.getString("cloudName")
        try{
            boolean isDatax = org.apache.commons.lang3.BooleanUtils.isTrue(dataObject.getBoolean("isDatax"))
            String enterpriseName = getEnterpriseName(tenantId)
            if(StringUtils.isBlank(enterpriseName)){
                return new JSONObject().fluentPut("code", 400).fluentPut("message", "cannot found enterprise: "+tenantId).fluentPut("data", null).toString()
            }
            enterpriseName = StringUtils.isBlank(enterpriseName) ? "${tenantId}-数据字典" : enterpriseName
            String fileName = CharMatcher.anyOf('+ ').replaceFrom(URLEncoder.encode(enterpriseName, 'UTF-8'), '%20')
            Path filePath = Paths.get("/tmp/"+new Date().getTime()).resolve(fileName+".xlsx")
            File dataFile = filePath.toFile()
            //如果目标存在，则删除当前的文件
            if(dataFile.exists() || dataFile.isFile()){
                dataFile.delete()
            }
            //文件夹不存在，则创建
            if(!dataFile.getParentFile().exists()){
                dataFile.getParentFile().mkdirs()
            }
            if(StringUtils.contains(cloudName,"oss.foneshare.cn")){
                log.info("fs account, create dict file from foneshare,tenantId: {}",tenantId)
                createPaasDictFile(tenantId,cloudName,isDatax,dataFile)
            }else{
                log.info("cloud account, download dict file from {},tenantId: {}",cloudName,tenantId)
                downLoadPaasDictFromCloud(tenantId,cloudName,isDatax,dataFile)
            }
            if(dataFile.exists() && dataFile.isFile()){
                String npath = uploadData2FileSystem(dataFile,uploadEa)
                log.info("upload dict file to fs,tenantId:{},npath:{}",tenantId,npath)
                return new JSONObject().fluentPut("code", 200).fluentPut("message", "success").fluentPut("data", npath).toString()
            }
            log.info("create dict file fail,tenantId:{}, uploadEa: {}, cloudName: {}",tenantId,uploadEa,cloudName)
            return new JSONObject().fluentPut("code", 400).fluentPut("message", "cloudName: "+cloudName+" 无法识别").fluentPut("data", null).toString()
        }catch (Exception e){
            log.error("handle paas dict fail, tenantId: {},error ",tenantId,e)
            return new JSONObject().fluentPut("code", 400).fluentPut("message", "handle paas dict fail, tenantId: "+tenantId+",error: "+e.getMessage()).fluentPut("data", null).toString()
        }
    }

    @GetMapping(value = "/enterprise_name", produces = "application/json; charset=UTF-8")
    @ResponseBody
    String enterpriseNameRest(String tenantId) {
        try{
            return getEnterpriseInfo(tenantId)
        }catch (Exception e){
            log.error("handle paas dict fail, tenantId: {},error ",tenantId,e)
            return "fail: "+e.getMessage()
        }
    }

    private void downLoadPaasDictFromCloud(String tenantId,String cloudName,boolean isDatax,File filePath){
        String urlFormat = "%s/paas-console/rest/metadata/dict/export-paas-dict?tenantId=%s&checkDataExists=false&isDatax=%b"
        String downloadUrl = String.format(urlFormat,cloudName,tenantId,isDatax)
        log.info("export paas dict,tenantId:{},cloudName:{},url: {}",tenantId,cloudName,downloadUrl)
        HttpClient.defaultClient().downloadFile(downloadUrl,filePath)
    }

    private void createPaasDictFile(String tenantId,String cloudName,boolean isDatax,File filePath){
        try{
            List<DictExcelData> lines = query(tenantId, "*", false, isDatax)
            if(CollectionHelper.isEmpty(lines)){
                log.warn("cannot found any describe,tenantId: {}, cloudName: {}, isDatax: {}",tenantId,cloudName,isDatax)
                return
            }
            EasyExcel.write(filePath, DictExcelData.class).sheet("数据字典").doWrite(lines)
        }catch (Exception e){
            log.error("handle paas dict fail, tenantId={},cloudname={}, error ",tenantId,cloudName)
        }
    }

    private String uploadData2FileSystem(File filePath, String ea) {
        try {
            log.info("upload file: {}, ea: {}",filePath, ea)
            byte[] bytes = IOUtils.toByteArray(new FileInputStream(filePath))
            StoneFileUploadRequest stoneFileUploadRequest = new StoneFileUploadRequest()
            stoneFileUploadRequest.setEa(ea)
            stoneFileUploadRequest.setBusiness("TENANT-DATA-UPLOAD")
            stoneFileUploadRequest.setEmployeeId(1000)
            stoneFileUploadRequest.setFileSize(bytes.length)
            stoneFileUploadRequest.setExtensionName("csv")
            stoneFileUploadRequest.setExpireDay(2)
            StoneFileUploadResponse response = stoneProxyApi.uploadByStream("n", stoneFileUploadRequest, new ByteArrayInputStream(bytes));
            return response.getPath()
        } catch (Exception e) {
            log.error("upload file fail,error ", e);
        }
        return null;
    }

    private String getEnterpriseName(String tenantId) {
        BatchGetSimpleEnterpriseDataArg batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(
                enterpriseIds: Arrays.asList(Integer.parseInt(tenantId)),
                enterpriseAccounts: null
        )
        BatchGetSimpleEnterpriseDataResult batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
        List<SimpleEnterpriseData> enterpriseList = batchGetSimpleEnterpriseDataResult.simpleEnterpriseList
        if (CollectionUtils.isEmpty(enterpriseList)) {
            return ""
        }
        SimpleEnterpriseData enterpriseData = enterpriseList.get(0)
        log.info("enterprise info: {}",JSON.toJSONString(enterpriseData))
        return tenantId + "-" + enterpriseList.get(0).getEnterpriseName()
    }

    private String getEnterpriseInfo(String tenantId) {
        BatchGetSimpleEnterpriseDataArg batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(
                enterpriseIds: Arrays.asList(Integer.parseInt(tenantId)),
                enterpriseAccounts: null
        )
        BatchGetSimpleEnterpriseDataResult batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
        List<SimpleEnterpriseData> enterpriseList = batchGetSimpleEnterpriseDataResult.simpleEnterpriseList
        if (CollectionUtils.isEmpty(enterpriseList)) {
            return ""
        }
        SimpleEnterpriseData enterpriseData = enterpriseList.get(0)
        return JSON.toJSONString(enterpriseData)
    }

    private List<DictExcelData> query(String tenantId, String objectApiNames, boolean checkDataExists, boolean isDatax) {
        // 查询租户所在数据库（确认是否做了schema隔离）
        def pair = jdbcService.conn(tenantId)
        @Cleanup def jdbc = pair.first
        def schema = pair.second

        if ('*' == objectApiNames) {
            // 查询有数据的表
            def size = checkDataExists ? 0 : -1
            def query = "SELECT relname FROM pg_stat_user_tables WHERE schemaname='$schema' AND n_live_tup>$size"
            List<String> hasDataTables = jdbc.findMany(query)
            def ids = Joiner.on(',').join(hasDataTables)
//      log.info("check pg_stat_user_tables, found tables: $ids")
            // 检查含有tenant_id的表
            query = "SELECT table_name FROM information_schema.columns WHERE table_schema='$schema' AND column_name='tenant_id' AND table_name=any('{$ids}')"
            hasDataTables = jdbc.findMany(query)
//      log.info("check information_schema, found tables: $hasDataTables")
            // 检查企业数据
            Set<String> rechecked = Sets.newLinkedHashSet()
            if (checkDataExists) {
                hasDataTables.each {
                    def sql = "SELECT * FROM ${schema}.\"${it}\" WHERE tenant_id='${tenantId}' LIMIT 1"
                    String id = jdbc.findOne(sql)
                    if (id != null) {
                        rechecked.add(it)
                    }
                }
            } else {
                rechecked = Sets.newLinkedHashSet(hasDataTables)
            }
//      log.info("check data exists, found tables: $rechecked")

            // 检查租户库对象存储表
            List<String> names = Lists.newArrayList()
            query = "SELECT describe_api_name,store_table_name FROM ${schema}.mt_describe WHERE tenant_id='$tenantId' AND is_deleted=false"
            jdbc.cursor(query) {
                while (it.next()) {
                    def describe_api_name = it.getString(1)
                    def store_table_name = it.getString(2)
                    if (it.wasNull() && schema == 'public') {
                        store_table_name = 'mt_data'
                    }
                    if (rechecked.contains(store_table_name) || "mt_data".equals(store_table_name)) {
                        log.info("found-0 describe_api_name:${describe_api_name}, store_table_name:${store_table_name}")
                        rechecked.remove(store_table_name)
                        names += describe_api_name
                    } else {
                        log.warn("skip-0 describe_api_name:${describe_api_name}, store_table_name:${store_table_name}")
                    }
                }
            }
            names += 'AccountObj'
            objectApiNames = Joiner.on(',').join(names)
//      log.info("got objectApiNames from tenantDB: $objectApiNames")
            def lines = queryByObjectApiNames(schema, tenantId, objectApiNames, jdbc, isDatax)

            // 检查系统库-100租户预制信息
            if (!rechecked.isEmpty()) {
                log.info("try load from SystemDB, tables: $rechecked")
                def pair2 = jdbcService.conn('-100')
                @Cleanup def system = pair2.first
                //query 追加describe_api_name不包含 org_dept表
                query = "SELECT describe_api_name,store_table_name FROM public.mt_describe WHERE tenant_id='-100' AND is_deleted=false and describe_api_name not in ('org_dept')"
                names.clear()
                system.cursor(query) {
                    while (it.next()) {
                        def describe_api_name = it.getString(1)
                        def store_table_name = it.getString(2)
                        if (it.wasNull() && schema == 'public') {
                            store_table_name = 'mt_data'
                        }
                        if (rechecked.contains(store_table_name)) {
//              log.info("found-1 describe_api_name:${describe_api_name}, store_table_name:${store_table_name}")
                            rechecked.remove(store_table_name)
                            names += describe_api_name
                        } else {
                            log.warn("skip-1 describe_api_name:${describe_api_name}, store_table_name:${store_table_name}")
                        }
                    }
                }
                objectApiNames = Joiner.on(',').join(names)
//        log.info("got objectApiNames from SystemDB: $objectApiNames")
                def fromSystem = queryByObjectApiNames('public', '-100', objectApiNames, system, isDatax)
//        log.info("load {} fields from SystemDB", fromSystem.size())
                lines.addAll(fromSystem)
            }
            return lines
        } else {
            return queryByObjectApiNames(schema, tenantId, objectApiNames, jdbc, isDatax)
        }
    }

    List<DictExcelData> queryByObjectApiNames(String schema, String tenantId, String objectApiNames, JdbcConnection jdbc, boolean isDatax) {
        def clean = CharMatcher.anyOf('\'\"\\;-').removeFrom(objectApiNames)
        def query = "SELECT describe_api_name,display_name,store_table_name FROM ${schema}.mt_describe WHERE tenant_id='$tenantId' AND is_deleted=false AND describe_api_name=any('{$clean}')"
        Map<String, List<String>> rows = Maps.newHashMap()
        jdbc.query(query) {
            while (it.next()) {
                def describe_api_name = it.getString(1)
                def display_name = it.getString(2)
                def store_table_name = isDatax ? InternUtils.toTableName(describe_api_name) : it.getString(3)
                rows[describe_api_name] = Lists.newArrayList(display_name, store_table_name)
            }
        }

        // 查询option信息
        query = "SELECT distinct option_id FROM ${schema}.mt_field WHERE tenant_id='$tenantId' AND describe_api_name=any('{$clean}') AND is_active='t' AND option_id is not null and status!='deleted'"
        List<String> optionIds = jdbc.findMany(query)
        // 查询option信息
        Map<String, Object> optionMappings = Maps.newConcurrentMap()
        if (!optionIds.isEmpty()) {
            // 分批查询
            Lists.partition(Lists.newArrayList(optionIds), 200).parallelStream().each {
                def md5Set = Joiner.on(',').join(it)
                def q = "SELECT md5,option_json FROM metadata_option WHERE md5=any('{$md5Set}')"
                JdbcConnection db = null
                try {
                    db = jdbcService.connectOptionDb()
                    db.query(q) {
                        while (it.next()) {
                            def md5 = it.getString(1)
                            def json = it.getObject(2)
                            optionMappings[md5] = json
                        }
                    }
                } finally {
                    if (db != null) {
                        db.close()
                    }
                }
            }
        }

        // 通用选项集option信息
        Map<String, Object> optionSetMappings = Maps.newConcurrentMap()
        //查询通用选项集
        if(!StringUtils.equals(tenantId,"-100")){
            //系统库没有通用选项集，不用查询
            query = "select api_name,options from ${schema}.mt_option_set where tenant_id='$tenantId' and is_deleted=0"
            jdbc.query(query) {
                while (it.next()) {
                    def api_name = it.getString(1)
                    def json = it.getString(2)
                    optionSetMappings[api_name] = json
                }
            }
        }

        // 查询field信息
        List<DictExcelData> lines = Lists.newArrayList()
        query = "SELECT describe_api_name,api_name,field_label,type,field_num,is_single,is_required,option_id,select_option,target_api_name,quote_field,max_length,option_api_name FROM ${schema}.mt_field WHERE tenant_id='$tenantId' AND describe_api_name=any('{$clean}') AND is_active='t' and status!='deleted' ORDER BY describe_api_name"
        Map<String, String> targetObjectMap = Maps.newHashMap()
        Map<String, String> quoteFieldMap = Maps.newHashMap()

        String objectApiName = "";
        String tableName = ""
        String objectDisplayName = ""
        List<String> filedApiNameList = Lists.newArrayList();
        jdbc.cursor(query) {
            while (it.next()) {
                def api_name = it.getString(2)
                if (systemFieldMap.containsKey(api_name)) {
                    //不处理系统字段(后面会补)
                    continue
                }
                def describe_api_name = it.getString(1)
                if (StringUtils.isNotBlank(objectApiName) && !StringUtils.endsWith(objectApiName, describe_api_name)) {
                    //说明换对象了，判断是否需要补系统字段
                    lines.addAll(fillSystemField(objectApiName, objectDisplayName, tableName,isDatax))
                }
                objectApiName = describe_api_name
                filedApiNameList.add(api_name)

                def field_label = it.getString(3)
                def type = it.getString(4)
                def targetRef = it.getString(10)
                if ("master_detail".equals(type) || "object_reference".equals(type)) {
                    targetObjectMap.put(describe_api_name + api_name, it.getString(10))
                    targetRef = "object reference [" + targetRef + "]"
                }
                if ("quote".equals(type)) {
                    def quoteFieldInfo = it.getString(11)
                    if (StringUtils.isNotBlank(quoteFieldInfo)) {
                        quoteFieldMap.put(describe_api_name + api_name, StringUtils.replace(quoteFieldInfo, "__r.", "."))
                    }
                }
                def field_num = it.getInt(5)
                if (it.wasNull()) {
                    field_num = -1
                }
                def is_single = it.getBoolean(6)
                def is_required = it.getBoolean(7)
                def option_id = it.getString(8)
                def select_option = it.getObject(9)
                def json = option_id == null ? parseOptions(select_option) : firstNotNullOptions(optionMappings[option_id], select_option)

                def option_api_name = it.getString(13)
                if(StringUtils.isNotBlank(option_api_name)){
                    //说明使用了通用选项集
                    json = parseOptions(optionSetMappings.get(option_api_name))
                }

                if (StringUtils.isNotBlank(json) && StringUtils.length(json) > 32766) {
                    json = StringUtils.substring(json, 0, 32766)
                }

                def common = rows[describe_api_name]
                def display_name = ''
                def store_table_name = ''
                if (common != null) {
                    display_name = common[0]
                    store_table_name = common[1]
                    if (store_table_name == null && 'public' == schema) {
                        store_table_name = isDatax ? describe_api_name.toLowerCase() : 'mt_data'
                    }
                }
                objectDisplayName = display_name
                tableName = store_table_name
                String slot = isDatax ? InternUtils.toColumnName(api_name) : (field_num != -1 ? 'value' + field_num : null)
                def required = is_required ? 'Y' : 'N'
                def isOption = StringUtils.isAllBlank(option_id, option_api_name, json)?"N":"Y"
                def columnType = InternUtils.getColumnType(type)
                def maxLength = java.util.Objects.toString(it.getObject(12), "")
                //原始paas对象库
                if(!isDatax){
                    if(StringUtils.endsWithIgnoreCase(type,"_many")){
                        columnType = "varchar[]"
                    }else if(StringUtils.equalsIgnoreCase(columnType,"string")){
                       if(StringUtils.isNotBlank(maxLength) && StringUtils.isNumeric(maxLength)){
                            columnType = "varchar("+maxLength+")"
                        }else {
                            columnType = "varchar"
                        }
                    }
                }

                def line = DictExcelData.builder().describeApiName(describe_api_name).displayName(display_name).fieldLabel(field_label).fieldRef(targetRef).fieldApiName(api_name).storeSlotName(StringUtils.defaultIfBlank(slot, api_name)).storeTableName(store_table_name).type(type).columnType(columnType).isOption(isOption).required(required).fieldLength(maxLength).options(json).build()
                lines.add(line)
            }
        }

        //处理master_detail、quote、object_reference关系备注信息
        for (DictExcelData obj : lines) {
            String key = obj.getDescribeApiName() + obj.getFieldApiName()
            if (quoteFieldMap.containsKey(key)) {
                Iterator<String> fieldIt = Splitter.on(".").split(quoteFieldMap.get(key)).iterator()
                def targetFieldApiName = fieldIt.hasNext() ? fieldIt.next() : ""
                def targetObjectField = fieldIt.hasNext() ? fieldIt.next() : ""
                def refKey = obj.getDescribeApiName() + targetFieldApiName
                if (targetObjectMap.containsKey(refKey)) {
                    obj.setFieldRef("field reference " + targetObjectMap.get(refKey) + "." + targetObjectField)
                }
            }
        }
        return lines
    }

    List<DictExcelData> fillSystemField(String objectApiName, String displayName, String tableName,boolean  isDatax) {
        List<DictExcelData> dataList = Lists.newArrayList()
        systemFieldMap.forEach {
            k, v ->
                DictExcelData d = SerializationUtils.clone(v)
                d.setDescribeApiName(objectApiName)
                d.setDisplayName(displayName)
                d.setStoreTableName(tableName)
                if(!isDatax){
                    def columnType = d.getColumnType()
                    if(StringUtils.endsWithIgnoreCase(columnType,"string")){
                        d.setColumnType(StringUtils.isNumeric(d.getFieldLength())?"varchar("+d.getFieldLength()+")":"varchar")
                    }
                }
                dataList.add(d)
        }
        return dataList
    }

    static Map<String, DictExcelData> systemFieldDict() {
        Map<String, DictExcelData> map = Maps.newHashMap()
        map.put("id", DictExcelData.builder().isOption("N").fieldLabel("数据唯一ID").fieldApiName("id").storeSlotName("id").columnType("string").required("Y").type("text").fieldLength("64").build())
        map.put("created_by", DictExcelData.builder().isOption("N").fieldLabel("创建人").fieldApiName("created_by").storeSlotName("created_by").columnType("string").required("Y").type("text").fieldLength("128").build())
        map.put("last_modified_by", DictExcelData.builder().isOption("N").fieldLabel("最后修改人").fieldApiName("last_modified_by").storeSlotName("last_modified_by").columnType("string").required("Y").type("text").fieldLength("128").build())
        map.put("tenant_id", DictExcelData.builder().isOption("N").fieldLabel("租户ID").fieldApiName("tenant_id").storeSlotName("tenant_id").columnType("string").required("Y").type("text").fieldLength("16").build())
        map.put("object_describe_api_name", DictExcelData.builder().isOption("N").fieldLabel("对象apiName").fieldApiName("object_describe_api_name").storeSlotName("object_describe_api_name").columnType("string").required("Y").type("text").fieldLength("255").build())
        map.put("create_time", DictExcelData.builder().isOption("N").fieldLabel("创建时间").fieldApiName("create_time").storeSlotName("create_time").required("Y").columnType("long").type("date_time").build())
        map.put("last_modified_time", DictExcelData.builder().isOption("N").fieldLabel("最后修改时间").fieldApiName("last_modified_time").storeSlotName("last_modified_time").columnType("long").required("Y").type("date_time").build())
        map.put("is_deleted", DictExcelData.builder().isOption("N").fieldLabel("数据删除状态位").fieldApiName("is_deleted").storeSlotName("is_deleted").required("Y").columnType("int").type("true_or_false").build())
        map.put("out_tenant_id", DictExcelData.builder().isOption("N").fieldLabel("外部租户ID").fieldApiName("out_tenant_id").storeSlotName("out_tenant_id").required("F").columnType("string").type("text").fieldLength("16").build())
        map.put("out_owner", DictExcelData.builder().isOption("N").fieldLabel("外部负责人").fieldApiName("out_owner").storeSlotName("out_owner").type("text").columnType("string").required("F").fieldLength("128").build())

        return map
    }

    static def firstNotNullOptions(Object... items) {
        for (def s : items) {
            if (s != null) {
                def opt = parseOptions(s)
                if (opt != null) {
                    return opt
                }
            }
        }
    }

    static String parseOptions(def raw) {
        if(raw == null){
            return raw
        }
        String json
        if (raw instanceof PGobject) {
            json = ((PGobject) raw).getValue()
        } else {
            json = (String) raw
        }
        // [{"label": "办公地址", "value": "1"}, {"label": "仓储地址", "value": "2"}, {"label": "注册地址", "value": "3"}, {"label": "门店地址", "value": "4"}, {"label":"家庭地址", "value": "5"}, {"label": "其他", "value": "0"}]
        if (json != '[]') {
            Map<String, String> mappings = Maps.newHashMap()
            JSON.parseArray(json).each {
                JSONObject obj = (JSONObject) it
                if (obj.containsKey("label")) {
                    String val = obj.containsKey("value") ? obj.getString("value") : obj.getString("api_name")
                    mappings.put(obj.getString("label"), val)
                }
            }
            return mappings.isEmpty() ? null : JSON.toJSONString(mappings);
        }
    }
}