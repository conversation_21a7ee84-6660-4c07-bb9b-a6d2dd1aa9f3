package com.fxiaoke.paas.console.service.license

import com.facishare.paas.license.Result.LicenseVersionResult
import com.facishare.paas.license.arg.QueryProductArg
import com.facishare.paas.license.common.LicenseContext
import com.facishare.paas.license.constant.LicenseConstant
import com.facishare.paas.license.http.LicenseClient
import com.facishare.paas.license.pojo.ProductVersionPojo
import com.facishare.uc.api.model.enterprise.arg.BatchGetEnterpriseRunStatusArg
import com.facishare.uc.api.model.enterprise.result.BatchGetEnterpriseRunStatusResult
import com.facishare.uc.api.model.fscore.RunStatus
import com.facishare.uc.api.service.EnterpriseEditionService
import com.fxiaoke.common.Pair
import com.fxiaoke.common.PasswordUtil
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.paas.console.bean.license.PvInfo
import com.github.autoconf.ConfigFactory
import com.google.common.base.CharMatcher
import com.google.common.base.Splitter
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import groovy.sql.Sql
import groovy.util.logging.Slf4j
import org.apache.commons.lang.math.NumberUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.util.CollectionUtils

import javax.annotation.PostConstruct
import javax.annotation.Resource
import java.sql.SQLException

/**
 * 将license中所有企业对应的最高CRM版本同步到系统库的tenant_license表中
 */
@Service
@Slf4j
class LicenseSyncJobService {

  private static final String QUERY_SQL = """ SELECT tenant_id,product_id,product_version,start_time,expired_time,create_time FROM product_license WHERE product_type = '0' AND del_flag is false """
  private List<String> srcJdbcUrls
  private String srcUser
  private String srcPwd

  private String destJdbcUrl
  private String destUser
  private String destPwd

  private String destJdbcUrl2
  private String destUser2
  private String destPwd2
  private List<String> toMyqlEditions
  private boolean isMysql

  @Resource(name = "licenseClient")
  LicenseClient licenseClient
  @Autowired
  EnterpriseEditionService enterpriseEditionService
  @Autowired
  LicenseService licenseService

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("db-paas-console", { config ->
      srcJdbcUrls = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(config.get("srcJdbcUrls"))
      srcUser = config.get("srcUser")
      srcPwd = config.get("srcPwd")
      boolean srcEncrypt = config.getBool("srcPwd.encrypt", false)
      if(srcEncrypt) {
        srcPwd = PasswordUtil.decode(srcPwd)
      }

      destJdbcUrl = config.get("destJdbcUrl")
      destUser = config.get("destUser")
      destPwd = config.get("destPwd")
      boolean destEncrypt = config.getBool("destPwd.encrypt", false)
      if (destEncrypt) {
        destPwd = PasswordUtil.decode(destPwd)
      }

      destJdbcUrl2 = config.get("destJdbcUrl2")
      destUser2 = config.get("destUser2")
      destPwd2 = config.get("destPwd2")
      boolean destEncrypt2 = config.getBool("destPwd2.encrypt", false)
      if (destEncrypt2) {
        destPwd2 = PasswordUtil.decode(destPwd2)
      }

      toMyqlEditions = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(config.get("toMyqlEditions", "enterpriseEdition"))
      isMysql = config.getBool("isMysql", false)
    })
  }

  /**
   * 开始全量
   */
  void sync() {
    srcJdbcUrls.forEach({ it -> this.syncDB(it) })
  }

  /**
   * 增量企业
   * @param tenantId
   */
  void addSync(String tenantId) {
    log.info("<<<<< tenantId=${tenantId} addSync is start")
    PvInfo pvInfo = queryPvInfoByTenantId(tenantId)
    insertTenantLicense(pvInfo)
  }


  private void syncDB(String srcJdbcUrl) {
    log.info("<<<< srcJdbcUrl = ${srcJdbcUrl} syncDB is start!")
    long startTime = System.currentTimeMillis()
    Map<String, List<PvInfo>> pvMap = Maps.newHashMap()

    JdbcConnection srcConn = new JdbcConnection(srcJdbcUrl, srcUser, srcPwd)
    try {
      srcConn.cursor(QUERY_SQL, { rs ->
        while (rs.next()) {
          PvInfo pvInfo = new PvInfo();
          pvInfo.setTenantId(rs.getString("tenant_id"))
          pvInfo.setProductId(rs.getString("product_id"))
          pvInfo.setProductVersion(rs.getString("product_version"))
          pvInfo.setStartTime(rs.getLong("start_time"))
          pvInfo.setExpiredTime(rs.getLong("expired_time"))
          pvInfo.setCreateTime(rs.getLong("create_time"))
          putPvInfo(rs.getString("tenant_id"), pvMap, pvInfo)
        }
      })
    } catch (SQLException e) {
      log.error("sync {} is error: ", srcJdbcUrl, e)
      throw new RuntimeException(e)
    } finally {
      srcConn?.close()
    }

    //排除已删除的企业
    Map<String, List<PvInfo>> pvInfoMap = batchCheckTenant(pvMap)
    def sqlInstance = Sql.newInstance(destJdbcUrl, destUser, destPwd, "org.postgresql.Driver")
    sqlInstance.withBatch(100, """ INSERT INTO tenant_license VALUES(?,?,?,?,?,?,?) on conflict (tenant_id) do UPDATE set product_version = ?,start_time = ?,expired_time = ?,modify_time = ?,product_id = ?,is_expired = ? """) {
      ps ->
        pvInfoMap.each {
          String tenantId, List<PvInfo> pvInfos ->
            PvInfo pv = getPvInfo(pvInfos)
            ps.addBatch(tenantId, pv.getProductVersion(), pv.getStartTime(), pv.getExpiredTime(), System.currentTimeMillis(), getIsExpired(pv.getStartTime(), pv.getExpiredTime(), System.currentTimeMillis()), pv.getProductId(), pv.getProductVersion(), pv.getStartTime(), pv.getExpiredTime(), System.currentTimeMillis(), pv.getProductId(), getIsExpired(pv.getStartTime(), pv.getExpiredTime(), System.currentTimeMillis()))
        }
    }
    sqlInstance?.close()

//    if (isMysql) {
//      this.syncMysql(pvInfoMap)
//    }
    log.info("<<<< ${srcJdbcUrl} time consuming is ${System.currentTimeMillis() - startTime}")
  }

  void syncLicensePg(Map<String, List<PvInfo>> pvInfoMap) {
    def sqlInstance = Sql.newInstance(destJdbcUrl2, destUser2, destPwd2, "org.postgresql.Driver")
    sqlInstance.withBatch(100, """ INSERT INTO tenant_license VALUES(?,?,?,?,?,?,?) on conflict (tenant_id) do UPDATE set product_version = ?,start_time = ?,expired_time = ?,modify_time = ?,product_id = ?,is_expired = ? """) {
      ps ->
        pvInfoMap.each {
          String tenantId, List<PvInfo> pvInfos ->
            PvInfo pv = getPvInfo(pvInfos)
            ps.addBatch(tenantId, pv.getProductVersion(), pv.getStartTime(), pv.getExpiredTime(), System.currentTimeMillis(), getIsExpired(pv.getStartTime(), pv.getExpiredTime(), System.currentTimeMillis()), pv.getProductId(), pv.getProductVersion(), pv.getStartTime(), pv.getExpiredTime(), System.currentTimeMillis(), pv.getProductId(), getIsExpired(pv.getStartTime(), pv.getExpiredTime(), System.currentTimeMillis()))
        }
    }
    sqlInstance?.close()
    log.info(">>>>> syncLicensePg is end")
  }

  /**
   * 写入mysql
   * @param pvInfoMap
   */
  @Async
  void syncMysql(Map<String, List<PvInfo>> pvInfoMap) {
    Map<String, PvInfo> pvInfoMap2 = getMapByEdition(pvInfoMap)
    def sqlInstance2 = Sql.newInstance(destJdbcUrl2, destUser2, destPwd2, "com.mysql.jdbc.Driver")
    sqlInstance2.withBatch(100, """ INSERT INTO tenant_license VALUES(?,?,?,?,?,?,?,?) on DUPLICATE KEY UPDATE product_version = ?,start_time = ?,expired_time = ?,modify_time = ?,product_id = ?,create_time = ?,is_expired = ? """) {
      ps ->
        pvInfoMap2.each {
          String tenantId, PvInfo pv ->
            ps.addBatch(tenantId, pv.getProductVersion(), pv.getStartTime(), pv.getExpiredTime(), System.currentTimeMillis(), getIsExpired(pv.getStartTime(), pv.getExpiredTime(), System.currentTimeMillis()), pv.getProductId(), pv.getCreateTime(), pv.getProductVersion(), pv.getStartTime(), pv.getExpiredTime(), System.currentTimeMillis(), pv.getProductId(), pv.getCreateTime(), getIsExpired(pv.getStartTime(), pv.getExpiredTime(), System.currentTimeMillis()))
        }
    }
    sqlInstance2?.close()
    log.info("<<<<< syncMysql is end")
  }

  /**
   * 获取写入mysql的记录
   * @param pvInfoMap
   * @return
   */
  private Map<String, PvInfo> getMapByEdition(Map<String, List<PvInfo>> pvInfoMap) {
    Map<String, PvInfo> pvMap = Maps.newHashMap()
    pvInfoMap.each { String tenantId, List<PvInfo> pvInfoList ->
      PvInfo pvInfo = getPvInfo(pvInfoList)
//      if (toMyqlEditions.contains(pvInfo.productVersion)) {
      pvMap.put(tenantId, pvInfo)
//      }
    }
    return pvMap
  }

  /**
   * 将指定企业的license信息插入系统库
   * @param pv
   */
  void insertTenantLicense(PvInfo pv) {
    def sqlInstance = Sql.newInstance(destJdbcUrl, destUser, destPwd, "org.postgresql.Driver")
    sqlInstance.execute(""" INSERT INTO tenant_license VALUES(?,?,?,?,?,?,?) on conflict (tenant_id) do UPDATE set product_version = ?,start_time = ?,expired_time = ?,modify_time = ?,product_id = ? """, [pv.getTenantId(), pv.getProductVersion(), pv.getStartTime(), pv.getExpiredTime(), System.currentTimeMillis(), getIsExpired(pv.getStartTime(), pv.getExpiredTime(), System.currentTimeMillis()), pv.getProductId(), pv.getProductVersion(), pv.getStartTime(), pv.getExpiredTime(), System.currentTimeMillis(), pv.getProductId()])
    sqlInstance?.close()
  }

  /**
   * jar包获取企业license(返回pvInfo)
   * @param tenantId
   * @return
   */
  private PvInfo queryPvInfoByTenantId(String tenantId) {
    LicenseContext context = new LicenseContext()
    context.setTenantId(tenantId)
    context.setUserId("1000")
    context.setAppId("CRM")

    QueryProductArg arg = new QueryProductArg()
    arg.setLicenseContext(context)
//    LicenseVersionResult result = licenseClient.queryProductVersion(arg)
    LicenseVersionResult result = licenseService.queryProductVersion(arg)
    List<ProductVersionPojo> pojoList = result.getResult()

    PvInfo pvInfo = PvInfo.builder().tenantId(tenantId).build()
    if (CollectionUtils.isEmpty(pojoList)) {
      return pvInfo
    }
    pojoList.forEach({ pojo ->
      if (LicenseConstant.ProductType.MAIN.equals(pojo.getProductType())) {
        pvInfo.setProductVersion(pojo.getCurrentVersion())
        pvInfo.setStartTime(pojo.getStartTime())
        pvInfo.setExpiredTime(pojo.getExpiredTime())
        pvInfo.setProductId(pojo.getProductId())
      }
    })
    return pvInfo
  }

  /**
   * 处理pvMap中的Value
   * @param tenantId
   * @param pvMap
   * @param pvInfo
   */
  private static void putPvInfo(String tenantId, Map<String, List<PvInfo>> pvMap, PvInfo pvInfo) {
    List<PvInfo> oldInfoList = pvMap.get(tenantId)
    if (oldInfoList == null) {
      pvMap.put(tenantId, Lists.newArrayList(pvInfo))
    } else {
      //排除还没生效的
      if (pvInfo.getStartTime() <= System.currentTimeMillis()) {
        oldInfoList.add(pvInfo)
        pvMap.put(tenantId, oldInfoList)
      }
    }
  }

  /**
   * 获取是否失效
   * @param startTime
   * @param expiredTime
   * @return
   */
  private static boolean getIsExpired(long startTime, long expiredTime, long time) {
    if (expiredTime <= time || startTime >= time) {
      return true
    }
    return false
  }

  /**
   * 获取企业下最高版本
   * 1. 判断是否有没到期的，有则把失效的排除，没有则在失效的产品中选择最高的以及时间最远的
   * @param pvInfoList
   * @return
   */
  private PvInfo getPvInfo(List<PvInfo> pvInfoList) {
    //有效产品
    List<PvInfo> pvInfos = Lists.newArrayList()
    long time = System.currentTimeMillis()
    pvInfoList.forEach({ pvInfo ->
      if (pvInfo.getExpiredTime() > time) {
        pvInfos.add(pvInfo)
      }
    })

    if (!CollectionUtils.isEmpty(pvInfos)) {
      //存在有效产品
      return getTop(pvInfos)
    } else {
      //不存在有效产品
      return getTop(pvInfoList)
    }
  }

  /**
   * 获取最高版本
   * @param pvInfoList
   * @return
   */
  private static PvInfo getTop(List<PvInfo> pvInfoList) {
    Map<String, PvInfo> pvInfoMap = Maps.newConcurrentMap()
    Map<String, String> productMap = Maps.newConcurrentMap()
    for (PvInfo pv : pvInfoList) {
      //获取productVersion对应的PvInfo
      PvInfo pvInfo = pvInfoMap.get(pv.getProductVersion())
      if (pvInfo == null || pvInfo.getExpiredTime() < pvInfo.getExpiredTime()) {
        pvInfoMap.put(pv.getProductVersion(), pv)
      }
      //获取最高版本的version
      String productVersion = productMap.get("productVersion")
      if (productVersion == null || LicenseConstant.LICENSE_GRADE.get(productVersion) < LicenseConstant.LICENSE_GRADE.get(pv.getProductVersion())) {
        productMap.put("productVersion", pv.getProductVersion())
      }
    }
    return pvInfoMap.get(productMap.get("productVersion"))
  }

  /**
   * 批量排除已删除企业
   * @param pvInfoMap
   * @return
   */
  Map<String, List<PvInfo>> batchCheckTenant(Map<String, List<PvInfo>> pvMap) {
    Set<Integer> tenantIdList = Sets.newHashSet()
    pvMap.each { tenantId, pvInfos ->
      if (!NumberUtils.isDigits(tenantId) || tenantId.size() > "**********".size()) {
        log.error(">>>> ${tenantId} as Integer is error")
        return
      }
      tenantIdList.add(tenantId as Integer)
    }

    tenantIdList.collate(2000).each { tenantIds ->
      BatchGetEnterpriseRunStatusArg arg = new BatchGetEnterpriseRunStatusArg()
      arg.setEnterpriseIds(tenantIds)
      BatchGetEnterpriseRunStatusResult result = enterpriseEditionService.batchGetEnterpriseRunStatus(arg)
      result.getEiRunStatus().each { tenantId, flag ->
        if (flag != RunStatus.RUN_STATUS_NORMAL) {
          pvMap.remove(tenantId as String)
        }
      }
    }

    return pvMap
  }

  /**
   * 获取专属云企业
   * @param pair
   * @return
   */
  List<Pair<String, List<String>>> queryCloudTenantScope() {

    Map<String, Pair<Integer, Integer>> pairMap = new HashMap<String, Pair<Integer, Integer>>() {
      {
        put("HWC_PROD", Pair.build(60000001, 61000000))
        put("SBT_PROD", Pair.build(40010001, 40020000))
        put("UCD_TEST", Pair.build(40000001, 40010000))
        put("UCD_PROD", Pair.build(61000001, 62000000))
        put("KSC_PROD", Pair.build(40020001, 40030000))
        put("ALI_CLOUD", Pair.build(62000001, 63000000))
        put("TEN_CLOUD", Pair.build(63000001, 64000000))
        put("AWS_CLOUD", Pair.build(64000001, 65000000))
        put("AZSURE_CLOUD", Pair.build(65000001, 66000000))
        put("GCLOUD", Pair.build(66000001, 67000000))
      }
    }

    List<Pair<String, List<String>>> result = Lists.newArrayList()
    JdbcConnection connection = new JdbcConnection(destJdbcUrl, destUser, destPwd)
    try {
      pairMap.forEach({ name, pair ->
        List<String> tenantIds = Lists.newArrayList()
        connection.cursor(String.format("SELECT DISTINCT tenant_id FROM tenant_license WHERE cast(tenant_id AS NUMERIC) BETWEEN %s AND %s", pair.first, pair.second), { rs ->
          while (rs.next()) {
            tenantIds.add(rs.getString("tenant_id"))
          }
        })
        result.add(Pair.build(name, tenantIds))
      })
    } catch (Exception e) {
      throw new Exception(e)
    }
    return result
  }

}
