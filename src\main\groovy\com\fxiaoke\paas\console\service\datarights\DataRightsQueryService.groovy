package com.fxiaoke.paas.console.service.datarights

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.common.http.spring.OkHttpSupport
import com.fxiaoke.paas.console.entity.datarights.DtAuth
import com.fxiaoke.paas.console.entity.datarights.DtTeam
import com.fxiaoke.paas.console.mapper.datarights.DataRightsQueryMapper
import com.github.autoconf.ConfigFactory
import com.google.common.base.Charsets
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import groovy.util.logging.Slf4j
import okhttp3.MediaType
import okhttp3.Request
import okhttp3.RequestBody
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.collections.MapUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import javax.annotation.Resource

/**
 * <AUTHOR>
 * @date 2019/7/8 下午5:20
 */
@Slf4j
@Service
class DataRightsQueryService {
  @Autowired
  private DataRightsQueryMapper dataRightsQueryMapper
  private String GET_DATA_AUTH_MSG_URL
  private String DATA_AUTH_QUERY_URL

  @Resource(name = "httpSupport")
  private OkHttpSupport client

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-console-datarights", {
      iconfig ->
        this.GET_DATA_AUTH_MSG_URL = iconfig.get("dataAuthServiceUrl") + "/datarights/objectspermission"
        this.DATA_AUTH_QUERY_URL = iconfig.get("dataAuthServiceUrl") + "/dataAuthQuery/obtainDataAuth"
    })
  }

  String getDataAuthMsg(String tenantId, String objectId, String newStoreTableName) {
    String dataAuthCode = dataRightsQueryMapper.setTenantId(tenantId).getDataAuthCode(tenantId, objectId, newStoreTableName)
    if (StringUtils.isBlank(dataAuthCode)) {
      log.warn("dataAuthCode is blank!\n tenantId:{}, objectId:{}, newStoreTableName:{}", tenantId, objectId, newStoreTableName)
      return "{}"
    }

    DtAuth dtAuth = dataRightsQueryMapper.setTenantId(tenantId).getDataAuthCodeByDataAuthCodeAndTenantId(dataAuthCode, tenantId)
    return JSONObject.toJSONString(dtAuth)
  }


  JSONObject checkDataAuth(String tenantId, String objectId, String newStoreTableName, String apiName) {
    JSONObject jsonObject = new JSONObject()
    Map<String, String> map = dataRightsQueryMapper.setTenantId(tenantId).getDataAuthCodeAndOwner(tenantId, objectId, newStoreTableName)
    if (MapUtils.isEmpty(map)) {
      log.warn("map is Empty!\n tenantId:{}, objectId:{}, newStoreTableName:{}", tenantId, objectId, newStoreTableName)
      jsonObject.put("msg", "权限表为空或data_auth_code为空")
      return jsonObject
    }
    String dataAuthCode = map.get("dataauthcode")
    String dataOwner = map.get("owner")
    if (StringUtils.isBlank(dataAuthCode)) {
      log.warn("dataAuthCode is blank!\n tenantId:{}, objectId:{}, newStoreTableName:{}", tenantId, objectId, newStoreTableName)
      jsonObject.put("msg", "数据对应的data_auth_code为空")
      return jsonObject
    }
    jsonObject.put("msg", "该数据对应的权限数据全部正确")

    DtAuth dtAuth = dataRightsQueryMapper.setTenantId(tenantId).getDataAuthCodeByDataAuthCodeAndTenantId(dataAuthCode, tenantId)
    if (dtAuth == null) {
      jsonObject.put("msg", "查不到对应数据的权限数据")
      log.warn("can't get data auth, data_auth_code:{} ,tenantId:{}", dataAuthCode, tenantId)
      return jsonObject
    }
    List<String> readPermissionUsers = dtAuth.readPermissionUsers
    List<String> writePermissionUsers = dtAuth.writePermissionUsers
    String owner = dtAuth.owner
    List<DtTeam> dtTeamList = dataRightsQueryMapper.setTenantId(tenantId).getDtTeamMsg(tenantId, objectId, apiName)
    if (CollectionUtils.isEmpty(dtTeamList)) {
      jsonObject.put("msg", "没有查到该数据对应的dt_team")
      log.warn("can't get dt_team, objectId:{} ,tenantId:{} ,ApiName:{}", objectId, tenantId, apiName)
      return jsonObject
    }
    Set<String> msgList = Sets.newHashSet()
    if (owner == null || !owner.equals(dataOwner)) {
      msgList.add("该数据的团队成员类型为：用户，数据负责人校验错误,原因:dt_auth表和数据表中的数据所有人不一致。 dtAuthId:" + dtAuth.id + ", dtAuthOwner:" + owner + ", dataOwner:" + dataOwner + ", tenantId:" + tenantId + ", objectId:" + objectId)
    }
    dtTeamList.forEach({ dtTeam ->
      String dtTeamId = dtTeam.id
      String memberId = dtTeam.memberId
      int memberType = dtTeam.memberType
      String roleType = dtTeam.roleType
      int permission = dtTeam.permission
      //团队成员类型是用户的
      if (memberType == 0) {
        String realMemberId = tenantId + "." + memberId
        //数据归属人权限
        if (roleType != null && roleType.equals("1") && (owner == null || !owner.equals(memberId))) {
          msgList.add("该数据的团队成员类型为：用户，数据负责人校验错误,原因:dt_auth表和dt_team中的数据所有人不一致。dtTeamId: " + dtTeamId + ", memberId: " + memberId + ", dtAuthId:" + dtAuth.id + ", dtAuthOwner:" + owner)
        }
        if (roleType != null && roleType.equals("1") && (memberId == null || !memberId.equals(dataOwner))) {
          msgList.add("该数据的团队成员类型为：用户，数据负责人校验错误,原因:dt_team表和数据表中的数据所有人不一致。dtTeamId: " + dtTeamId + ", memberId: " + memberId + ", objectId: " + objectId + ", dataOwner:" + dataOwner)
        }
        //只读权限
        if (permission == 1) {
          if (readPermissionUsers == null || !readPermissionUsers.contains(realMemberId)) {
            msgList.add("该数据的团队成员类型为：用户，读权限校验错误。dtTeamId: " + dtTeamId + ", memberId: " + memberId)
          }
        }
        //读写权限
        if (permission == 2) {
          if (readPermissionUsers == null || !readPermissionUsers.contains(realMemberId)) {
            msgList.add("该数据的团队成员类型为：用户，读权限校验错误。dtTeamId: " + dtTeamId + ", memberId: " + memberId)
          }
          if (writePermissionUsers == null || !writePermissionUsers.contains(realMemberId)) {
            msgList.add("该数据的团队成员类型为：用户，写权限校验错误。 dtTeamId: " + dtTeamId + ", memberId: " + memberId)
          }
        }
      } else {
        msgList.add("目前不支持对成员类型为非用户的权限数据查询")
      }


    })

    if (CollectionUtils.isNotEmpty(msgList)) {
      jsonObject.put("msg", msgList)
    }
    return jsonObject
  }


  String getDataAuthMsgByTenantIdAndUserIdAndApiNameAndObjectIds(String tenantId, String userId, String apiName, List<String> objectIds, String outerTenantId, String outerUserId, String outerAppId) {
    JSONObject jsonObject = new JSONObject()
    jsonObject.put("context", new HashMap() {
      {
        put("appId", "CRM")
        put("objectProperties", Maps.newHashMap())
        Map<String, String> propertiesMap = Maps.newHashMap()
        if (StringUtils.isNotBlank(outerAppId)) {
          propertiesMap.put("bizAppId", outerAppId)
        }
        put("properties", propertiesMap)
        put("tenantId", tenantId)
        put("userId", userId)
        if (StringUtils.isNotBlank(outerTenantId)) {
          put("outerTenantId", outerTenantId)
        }

        if (StringUtils.isNotBlank(outerUserId)) {
          put("outerUserId", outerUserId)
        }
      }
    })

    jsonObject.put("entityId", apiName)
    jsonObject.put("roleType", "1")
    jsonObject.put("cascadeDept", 'false')
    jsonObject.put("cascadeSubordinates", "false")
    jsonObject.put("objects", Sets.newHashSet(objectIds))
    String result = null
    Request request = new Request.Builder()
            .post(RequestBody.create(MediaType.parse("application/json;charset=utf-8"), jsonObject.toJSONString()))
            .url(this.GET_DATA_AUTH_MSG_URL).build()
    client.syncExecute(request, {
      response ->
        result = response.body().string()
    })
    return result
  }

  String queryDataAuth(String tenantId, String userId, String describeApiName, String dataId, String outerTenantId) {
    JSONObject jsonObject = new JSONObject()
    jsonObject.put("tenantId", tenantId)
    if (StringUtils.isNotBlank(outerTenantId)) {
      jsonObject.put("outTenantId", outerTenantId)
      jsonObject.put("outUserId", userId)
    } else {
      jsonObject.put("userId", userId)
    }
    jsonObject.put("describeApiName", describeApiName)
    jsonObject.put("dataId", dataId)
    Request request = new Request.Builder()
            .post(RequestBody.create(MediaType.parse("application/json;charset=utf-8"), jsonObject.toJSONString().getBytes(Charsets.UTF_8)))
            .url(this.DATA_AUTH_QUERY_URL).build()

    return client.syncExecute(request, {
      response -> return new String(response.body().bytes(), Charsets.UTF_8)
    })
  }

}

