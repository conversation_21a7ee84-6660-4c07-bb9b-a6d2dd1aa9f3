package com.fxiaoke.paas.console.service.basicPlatform

import com.alibaba.fastjson.JSON
import com.fxiaoke.paas.console.service.OKHttpService
import com.github.autoconf.ConfigFactory
import com.google.common.collect.Maps
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import javax.ws.rs.core.MediaType

/**
 * <AUTHOR> create by liy on 2025/8/19
 */
@Service
public class SchedulerTaskService {

    //计划任务服务地址
    private static String QUERY_URL
    //方法映射
    private static METHOD_MAP = Maps.newHashMap("ids": "Ids", "tenantId": "TenantId", "apiName": "ApiName")

    @Autowired
    private OKHttpService okHttpService

    @PostConstruct
    void init() {
        ConfigFactory.getConfig("variables_qixin", { config ->
            QUERY_URL = config.get("schedule_task_url")
        })
    }

    /**
     * 批量取消
     */
    def batchCancel(String method, Map arg) {
        String url = QUERY_URL + "/API/task/cancelTaskBy" + METHOD_MAP.get(method)
        //
        Map<String, String> headers = Maps.newHashMap()
        headers.put("x-fs-ei", "0")
        headers.put("x-fs-userInfo", "-10000")
        headers.put("x-tenant-id", "0")
        headers.put("x-user-id", "-10000")
        headers.put("x-fs-trace-id", "000123456789111")

        //
        Map body = Maps.newHashMap(arg)
        //
        //return okHttpService.post(url, headers, body, MediaType.APPLICATION_JSON)
        return okHttpService.post(url, JSON.toJSONString(body), Map.class)
    }

    /**
     * 批量禁用
     */
    def batchDisable(String method, Map arg) {
        String url = QUERY_URL + "/API/task/disableTaskBy" + METHOD_MAP.get(method)
        //
        Map<String, String> headers = Maps.newHashMap()
        //
        Map body = Maps.newHashMap(arg)
        //
        //return okHttpService.post(url, headers, body, MediaType.APPLICATION_JSON)
        return okHttpService.post(url, JSON.toJSONString(body), List.class)
    }

    /**
     * 批量启用
     */
    def batchEnable(String method, Map arg) {
        String url = QUERY_URL + "/API/task/enableTaskBy" + METHOD_MAP.get(method)
        //
        Map<String, String> headers = Maps.newHashMap()
        //
        Map body = Maps.newHashMap(arg)
        //
        //return okHttpService.post(url, headers, body, MediaType.APPLICATION_JSON)
        return okHttpService.post(url, JSON.toJSONString(body), List.class)
    }

    /**
     * 批量删除
     */
    def batchDelete(String method, Map arg) {
        String url = QUERY_URL + "/API/task/deleteTaskBy" + METHOD_MAP.get(method)
        //
        Map<String, String> headers = Maps.newHashMap()
        //
        Map body = Maps.newHashMap(arg)
        //
        return okHttpService.post(url, headers, body, MediaType.APPLICATION_JSON)
    }

}
