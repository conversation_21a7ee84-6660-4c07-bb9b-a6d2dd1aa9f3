package com.fxiaoke.paas.console.bean.dts;

import groovy.transform.builder.Builder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/23 19:17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DtsConfigArg {
    private String ei;
    private String ea;
    private String dbType;
    private String jdbcUrl;
    private String dbName;
    private String dbHost;
    private String dbUser;
    private String dbPwd;
    private String dbPort;
    private String dispatchTime;
    private String config;
    private String checkJdbcUrl;
    private String dateTimeToLong;
    private String syncAllCity;
    private String syncRelevantTeam;
    private String tombstone;
}
