package com.fxiaoke.paas.console.entity.metadata

import groovy.transform.ToString
import groovy.transform.builder.Builder
import lombok.Getter
import lombok.Setter

import javax.persistence.Transient

/**
 * <AUTHOR>
 * Created on 2018/3/6.
 */
@ToString
@Getter
@Setter
@Builder
class MtDescribe {
  String describeId
  String tenantId
  String describeApiName
  String createdBy
  Long createTime
  String displayName
  String lastModifiedBy
  Long lastModifiedTime
  String pkg
  String pluralName
  String defineType
  String resourceBundleKey
  String privilegeHandler
  String recordType
  boolean isActive
  String iconPath
  Integer indexVersion
  Integer version
  Integer revision
  String storeTableName
  boolean isDeleted
  String module
  Integer iconIndex
  String description
  String visibleScope
  boolean isCurrent
  String config
  @Transient
  Integer describeApiNameNum

}
