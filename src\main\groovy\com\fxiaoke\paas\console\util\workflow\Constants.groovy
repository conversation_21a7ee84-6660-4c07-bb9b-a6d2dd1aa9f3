package com.fxiaoke.paas.console.util.workflow

/**
 * Created by yangxw on 2018/3/5.
 */
final class Constants {
     static final String FLOW_TYPE_APPROVAL_FLOW = "approvalflow";
     static final String FLOW_TYPE_WORKFLOW = "workflow";
     static final String FLOW_TYPE_WORKFLOW_BPM = "workflow_bpm";

     static final String RECEIVER_TYPE_PERSON = "PERSON";
     static final String RECEIVER_TYPE_DEPT = "DEPT";
     static final String RECEIVER_TYPE_ROLE = "ROLE";

     static final String APP_ID_CRM = "CRM";
     static final String APP_NAME_CRM = "CRM";

     static final String APP_ID_XT = "facishare-xt";
     static final String APP_NAME_XT = "协同";

    static final String APP_ID_BPM = "BPM";
    static final String APP_NAME_BPM = "BPM";

     static final String ENTITY_ID_CRM_CUSTOMER = "2";
     static final String ENTITY_NAME_CRM_CUSTOMER = "客户";

     Constants() {

    }

//    Set<String> FIELD_TYPES = Sets.newHashSet("string", "number", "boolean");

     static final class Operator {

         static final String STRING_EQUALS = "equals";
         static final String STRING_NOT_EQUALS = "notEquals";
         static final String STRING_STARTS_WITH = "startsWith";
         static final String STRING_ENDS_WITH = "endsWith";
         static final String STRING_NOT_STARTS_WITH = "notStartsWith";
         static final String STRING_NOT_ENDS_WITH = "notEndsWith";
         static final String STRING_CONTAINS = "contains";
         static final String STRING_NOT_CONTAINS = "notContains";

         static final String COMMON_EMPTY = "empty";
         static final String COMMON_NOT_EMPTY = "notEmpty";

         static final String NUMBER_LESS_THAN = "<";
         static final String NUMBER_GREATER_THAN = ">";
         static final String NUMBER_LESS_THAN_OR_EQUAL = "<=";
         static final String NUMBER_GREATER_THAN_OR_EQUAL = ">=";
         static final String NUMBER_EQUALS = "==";
         static final String NUMBER_NOT_EQUALS = "!=";

         static final String BOOLEAN_EQUALS = "==";
         static final String BOOLEAN_NOT_EQUALS = "!=";

         Operator() {

        }

    }


//     static final class  FieldType {
//
//         static final String STRING = "string";
//         static final String NUMBER = "number";
//         static final String BOOLEAN = "boolean";
//
//         FieldType() {
//
//        }
//    }
//
//     static final class  FieldSrc {
//
//         static final String OBJECT_FIELD = "objectField";
//         static final String SYSTEM_FIELD = "systemField";
//
//         FieldSrc() {
//
//        }
//    }
//
//     static final class  SystemField {
//
//         static final String TENANT_ID = "@TENANTID@";
//         static final String APP_ID = "APPID";
//         static final String USERID = "@USERID@";
//         static final String DEPT = "@DEPT@";
//         static final String GROUP = "@GROUP@";
//         static final String ROLE = "@ROLE@";
//
//         SystemField() {
//
//        }
//    }
//    @Deprecated
//    interface Action {
//        String CREATE = "create";
//        String CREATE_OR_UPDATE = "createOrUpdate";
//        String UPDATE = "update";
//        String DELETE = "delete";
//    }
//
//     static final class  ActionType {
//
//         static final int CREATE = 1;//表示创建新的规则
//         static final int EDIT = 2;//表示编辑规则
//         static final int UPDATE = 3;//表示更新规则
//         static final int CANCEL = 4;//作废
//         static final int DELETE = 5;//表示删除规则
//
//         ActionType() {
//
//        }
//    }

}
