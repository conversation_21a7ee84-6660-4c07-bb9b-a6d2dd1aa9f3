<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
    <style>
        .spinner {
            margin: 100px auto;
            width: 40px;
            height: 40px;
            position: relative;
        }

        .container1 > div, .container2 > div, .container3 > div {
            width: 9px;
            height: 9px;
            background-color: #67CF22;

            border-radius: 100%;
            position: absolute;
            -webkit-animation: bouncedelay 1.2s infinite ease-in-out;
            animation: bouncedelay 1.2s infinite ease-in-out;
            -webkit-animation-fill-mode: both;
            animation-fill-mode: both;
        }

        .spinner .spinner-container {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .container2 {
            -webkit-transform: rotateZ(45deg);
            transform: rotateZ(45deg);
        }

        .container3 {
            -webkit-transform: rotateZ(90deg);
            transform: rotateZ(90deg);
        }

        .circle1 {
            top: 0;
            left: 0;
        }

        .circle2 {
            top: 0;
            right: 0;
        }

        .circle3 {
            right: 0;
            bottom: 0;
        }

        .circle4 {
            left: 0;
            bottom: 0;
        }

        .container2 .circle1 {
            -webkit-animation-delay: -1.1s;
            animation-delay: -1.1s;
        }

        .container3 .circle1 {
            -webkit-animation-delay: -1.0s;
            animation-delay: -1.0s;
        }

        .container1 .circle2 {
            -webkit-animation-delay: -0.9s;
            animation-delay: -0.9s;
        }

        .container2 .circle2 {
            -webkit-animation-delay: -0.8s;
            animation-delay: -0.8s;
        }

        .container3 .circle2 {
            -webkit-animation-delay: -0.7s;
            animation-delay: -0.7s;
        }

        .container1 .circle3 {
            -webkit-animation-delay: -0.6s;
            animation-delay: -0.6s;
        }

        .container2 .circle3 {
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }

        .container3 .circle3 {
            -webkit-animation-delay: -0.4s;
            animation-delay: -0.4s;
        }

        .container1 .circle4 {
            -webkit-animation-delay: -0.3s;
            animation-delay: -0.3s;
        }

        .container2 .circle4 {
            -webkit-animation-delay: -0.2s;
            animation-delay: -0.2s;
        }

        .container3 .circle4 {
            -webkit-animation-delay: -0.1s;
            animation-delay: -0.1s;
        }

        @-webkit-keyframes bouncedelay {
            0%, 80%, 100% {
                -webkit-transform: scale(0.0)
            }
            40% {
                -webkit-transform: scale(1.0)
            }
        }

        @keyframes bouncedelay {
            0%, 80%, 100% {
                transform: scale(0.0);
                -webkit-transform: scale(0.0);
            }
            40% {
                transform: scale(1.0);
                -webkit-transform: scale(1.0);
            }
        }
    </style>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>SQL查询</h1>
        <ol class="breadcrumb">
            <li><a href="../"><i class="fa fa-dashboard"></i>主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>Sql查询</a></li>
        </ol>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info row">
                    <div class="box-header with-border" style="padding: 0px">
                    </div>
                    <form class="form-horizontal" action="" onsubmit="return false;" method="post" id="myForm" role="form" data-toggle="validator">
                        <div class="box-body col-xs-6" style="margin-right: -127px">
                            <div class="form-group">
                                <label for="lines" class="col-sm-2 control-label">Sql</label>
                                <div class="col-sm-6">
                                    <textarea id="sql" name="sql" class="form-control" style="height: 450px;border-radius:5px;" required></textarea>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>
                            <div class="col-sm-offset-3 col-sm-2">
                                <button type="button" id="queryBut" class="btn btn-primary">查询</button>
                            </div>
                        </div>
                        <#--result展示-->
                        <div class="box-body col-xs-6">
                            <h4>Result <i class="fa fa-hand-o-down"></i></h4>
                            <pre id="queryResult" style="">
                        </pre>
                            <#--loading。。。-->
                            <div class="spinner hide" id="loading">
                                <div class="spinner-container container1">
                                    <div class="circle1"></div>
                                    <div class="circle2"></div>
                                    <div class="circle3"></div>
                                    <div class="circle4"></div>
                                </div>
                                <div class="spinner-container container2">
                                    <div class="circle1"></div>
                                    <div class="circle2"></div>
                                    <div class="circle3"></div>
                                    <div class="circle4"></div>
                                </div>
                                <div class="spinner-container container3">
                                    <div class="circle1"></div>
                                    <div class="circle2"></div>
                                    <div class="circle3"></div>
                                    <div class="circle4"></div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#queryBut').on('click', function () {

                $('#queryResult').html('');
                $('#loading').removeClass('hide');
                $('#queryBut').attr("disabled", "disabled");

                var sql = $('#sql').val();
                $.post("${CONTEXT_PATH}/metadata/log-query/query", {
                    sql: sql,
                }, function (result) {
                    $('#queryBut').removeAttr("disabled");
                    $('#loading').addClass('hide');
                    var info = null;
                    if (result.code === 200) {
                        info = result.info
                    } else {
                        info = result
                    }
                    $('#queryResult').JSONView(info, {
                        collapsed: false,
                        nl2br: true,
                        recursive_collapser: true
                    });
                });
            });
        });
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
