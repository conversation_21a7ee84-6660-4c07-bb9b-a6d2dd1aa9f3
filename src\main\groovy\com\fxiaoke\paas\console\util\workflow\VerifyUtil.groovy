package com.fxiaoke.paas.console.util.workflow

import com.fxiaoke.paas.console.exception.workflow.WorkflowAdminException
import groovy.util.logging.Slf4j
import org.springframework.stereotype.Component

/**
 * Created by yangxw on 2018/3/7.
 */
@Slf4j
class VerifyUtil {
    static void verifyNotNull(Object object, String name) {

        if (null == object) {
            log.info("Parameter {} id null", name);
            throw new WorkflowAdminException("参数" + name + "为空");
        }

    }

}
