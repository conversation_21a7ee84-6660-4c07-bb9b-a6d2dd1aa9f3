package com.fxiaoke.paas.console.entity.log

import com.github.mybatis.entity.IdEntity
import groovy.transform.ToString
import groovy.transform.builder.Builder
import lombok.Getter
import lombok.Setter

import javax.persistence.Table

/**
 * <AUTHOR>
 * Created on 2018/8/29.
 */
@Setter
@Getter
@ToString
@Builder
@Table(name = "describe_statistics")
class DescribeStatistics extends IdEntity {
  String count
  Date createTime
}
