<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fxiaoke.paas.console.mapper.datarights.DataRightsDataMapper">
  <select id="queryDataRelation" resultType="java.util.Map">
    select source_api_name,target_api_name,source_data_id,target_data_id
    FROM ${schema}.mt_relation WHERE tenant_id=#{tenantId}
    and (source_api_name=#{apiName} or target_api_name=#{apiName})
    and (
    <foreach collection="dataIds" item="dataId" open="(" separator="or" close=")">
      source_data_id=#{dataId} or target_data_id=#{dataId}
    </foreach>
    )
  </select>

  <select id="queryRoleMemberByRoleTypeAndObjectId" resultType="com.fxiaoke.paas.console.entity.datarights.RoleMember">
SELECT
  dt_team.object_describe_api_name     as apiName,
  dt_team.member_id                    as userId,
  org_employee_user.name               as name,
  org_employee_user.create_time        as createTime,
  org_employee_user.last_modified_time as modifiedTIme,
  org_employee_user.user_name          as userName,
  org_employee_user.position           as position,
  org_employee_user.tenant_id          as tenantId,
  org_employee_user.leader             as leaderID,
  dt_team.object_id                    as objectId,
  dt_team.role_type                    as type
FROM dt_team
  inner join org_employee_user
    on dt_team.tenant_id = org_employee_user.tenant_id
       and dt_team.member_id = org_employee_user.user_id
where dt_team.object_id = #{objectId}
      and org_employee_user.tenant_id = #{tenantId}
      and org_employee_user.is_deleted = 0
      and dt_team.role_type = #{roleType};
  </select>


  <select id="queryDataOwnDepartment" resultType="java.util.Map">
SELECT
  org_dept.dept_id                 as depId,
  mt_data.id                       as objectId,
  mt_data.object_describe_api_name as apiName,
  org_dept.name                    as name,
  org_dept.tenant_id               as tenantId
FROM mt_data
  inner join org_dept
    on mt_data.data_own_department = org_dept.dept_id
       and mt_data.tenant_id = org_dept.tenant_id
where mt_data.tenant_id = #{tenantId}
and org_dept.tenant_id = #{tenantId}
and mt_data.id = #{objectId}
</select>

  <select id="queryDataOwnDepartmentSpecial" resultType="java.util.Map">
SELECT
  org_dept.dept_id                 as depId,
  ${storeTableName}.${objectIdName}    as objectId,
  org_dept.name                    as name,
  org_dept.tenant_id               as tenantId
FROM ${storeTableName}
  inner join org_dept
    on ${storeTableName}.data_own_department = org_dept.dept_id
       and ${storeTableName}.ei = org_dept.tenant_id ::int
where ${storeTableName}.ei  = #{tenantId} ::int
and org_dept.tenant_id = #{tenantId}
and ${storeTableName}.${objectIdName} = #{objectId}

</select>

  <select id="queryDataOwnDepartmentSpecialBiz" resultType="java.util.Map">
SELECT
  org_dept.dept_id                 as depId,
  ${storeTableName}.id                       as objectId,
  ${storeTableName}.object_describe_api_name as apiName,
  org_dept.name                    as name,
  org_dept.tenant_id               as tenantId
FROM ${storeTableName}
  inner join org_dept
    on ${storeTableName}.data_own_department = org_dept.dept_id
       and ${storeTableName}.tenant_id = org_dept.tenant_id
where ${storeTableName}.tenant_id = #{tenantId}
and org_dept.tenant_id = #{tenantId}
and ${storeTableName}.id = #{objectId}

</select>

  <select id="queryTeamMember" resultType="com.fxiaoke.paas.console.entity.datarights.Principal">
 Select
  dt_team.object_describe_api_name as apiName,
  employee.owner                   as owner,
  employee.leader                  as leaderID,
  employee.user_name               as userName,
  employee.create_time             as createTime,
  employee.last_modified_time      as modifiedTime,
  employee.name                    as name,
  employee.user_id                 as userId,
  dt_team.role_type                as type
FROM dt_team
  INNER JOIN org_employee_user AS employee
    ON dt_team.member_id = employee.user_id
       and dt_team.tenant_id = employee.tenant_id
WHERE dt_team.object_id = #{objectId}
AND dt_team.tenant_id = #{tenantId}
and employee.tenant_id = #{tenantId}
and dt_team.role_type = #{roleType}
</select>


  <select id="queryEntityShare" resultType="java.util.Map">
    SELECT
    dt_entity_share.share_id as shareId,
    dt_entity_share.share_type as shareType,
    dt_entity_share.receive_id as receiveId,
    dt_entity_share.receive_type as receiveType
    from mt_data
    inner join dt_entity_share
    on mt_data.tenant_id = dt_entity_share.tenant_id
    and mt_data.object_describe_api_name = dt_entity_share.entity_id
    where mt_data.tenant_id =#{tenantId} and mt_data.is_deleted=0 and mt_data.id =#{objectId}
    <if test="ids!=null and ids.size()>0">
      and dt_entity_share.share_id in
      <foreach collection="ids" separator="," open="(" close=")" item="id">
        #{id}
      </foreach>
    </if>
  </select>

  <select id="queryGroupUserByGroupId" resultMap="queryGroupUserByGroupIdResultMap">
SELECT
  org_group_user.tenant_id   as tenantId,
  org_group_user.group_id    as groupId,
  org_employee_user.user_id  as userId,
  org_employee_user.name     as name,
  org_employee_user.position as position
from org_group_user
  inner join org_employee_user
    on org_group_user.user_id = org_employee_user.user_id
       and org_group_user.tenant_id = org_employee_user.tenant_id
where org_group_user.group_id = #{groupId}
and org_group_user.tenant_id = #{tenantId}
and org_group_user.is_deleted = 0
  </select>


  <select id="queryDepartmentUserByDeptId" resultType="com.fxiaoke.paas.console.entity.datarights.DepartmentUser">
   SELECT
  org_dept.name      as deptName,
  org_dept.dept_id   as deptId,
  org_dept.tenant_id as tenantId,
  a.name             as userName,
  a.user_id          as userId,
  a.position         as position,
  a.owner            as ownerId,
  a.leader           as leaderId,
  b.name             as leader,
  c.name             as owner
FROM org_dept
  inner join org_dept_user
    on org_dept.tenant_id = org_dept_user.tenant_id and org_dept.dept_id = org_dept_user.dept_id
  inner join org_employee_user as a
    on org_dept.tenant_id = a.tenant_id and org_dept_user.user_id = a.user_id
  left join org_employee_user as b
    on org_dept.tenant_id = b.tenant_id and a.leader = b.user_id
  left join org_employee_user as c
    on org_dept.tenant_id = c.tenant_id and a.owner = c.user_id
where org_dept.tenant_id = #{tenantId} and org_dept.dept_id = #{deptId} and org_dept_user.is_deleted = 0

  </select>

  <select id="queryEntityShareGroupByApiName" resultType="com.fxiaoke.paas.console.entity.datarights.EntityShareGroup">

  </select>

  <select id="queryEntityShareReceiveByApiName" resultType="com.fxiaoke.paas.console.entity.datarights.EntityShareReceive">
 SELECT
  r.tenant_id         as tenantId,
  g.app_id            as appId,
  r.entity_id         as entityId,
  r.rule_code         as ruleCode,
  g.rule_name         as ruleName,
  g.rule_parse        as ruleParse,
  g.rule_expression   as ruleExpression,
  g.rule_sql          as ruleSql,
  g.sql_select_fields as sqlSelectFields,
  g.scene             as scene,
  r.rule_order        as ruleOrder,
  r.field_type        as fieldType,
  r.field_name        as fieldName,
  r.field_value       as fieldValue
from
  rule_rule as r left join rule_rule_group as g
    on r.tenant_id = g.tenant_id and r.rule_code = g.rule_code
where g.is_deleted = 0 and r.tenant_id =#{tenantId} and r.entity_id=#{apiName}
</select>
  <select id="queryDtAuthByObjectId" resultType="com.fxiaoke.paas.console.entity.datarights.DtAuth">
    SELECT a.id,
    a.tenant_id as tenantId,
    a.pkg,
    a.object_describe_api_name as objectDescribeApiName,
    a.owner,
    a.last_modified_time as lastModifiedTime,
    a.read_permission_users as readPermissionUsers,
    a.write_permission_users as writePermissionUsers,
    a.participants,
    a.shared_users_depts_roles as sharedUsersDeptsRoles,
    a.relate_rules as relateRules,
    a.sales_waiters as salesWaiters,
    a.followers,
    a.data_auth_code as dataAuthCode
    from ${authTable} as a join ${table} as b on a.tenant_id=b.tenant_id and a.object_describe_api_name=b.object_describe_api_name
    <if test="authTable == 'dt_auth'">
      and a.data_auth_code=b.data_auth_code
    </if>
    <if test="authTable == 'dt_auth_out'">
      and a.data_auth_code=b.out_data_auth_code
    </if>
    where a.tenant_id=#{tenantId} and b.id=#{objectId}
  </select>

  <select id="queryEntityShareGroupByRuleId" resultType="java.util.Map">
    select
    id,
    tenant_id as tenantId,
    app_id as appId,
    entity_id as apiName,
    rule_name as ruleName,
    rule_code as ruleCode,
    rule_parse as ruleParse,
    rule_expression as ruleExpression,
    rule_sql as ruleSql,
    status,
    created_by as createdBy,
    create_time as createTime,
    last_modified_by as lastModifiedBy,
    last_modified_time as lastModifiedTime,
    is_deleted as isDeleted,
    sql_select_fields as sqlSelectFields,
    scene
    from rule_rule_group
    where tenant_id = #{tenantId} and rule_code in
    <foreach collection="ruleIds" item="ruleId" open="(" close=")" separator=",">
      #{ruleId}
    </foreach>
  </select>

  <select id="queryReceiveShareGroupByRuleId" resultType="java.util.Map">
    SELECT
    id,
    app_id as appId,
    entity_id as entityId,
    share_type as shareType,
    share_id as shareId,
    receive_type as receiveType,
    receive_id as receiveId,
    permission,
    status,
    creator,
    create_time as createTime,
    modifier,
    modify_time as modifyTime,
    del_flag as delFlag,
    source,
    scene,
    share_dept_cascade as shareDeptCascade
    FROM dt_entity_share
    where tenant_id = #{tenantId} and id in
    <foreach collection="ruleIds" open="(" close=")" separator="," item="ruleId">
      #{ruleId}
    </foreach>
  </select>
  <select id="queryRoleOrDeptOrUserOrGroupByTypeAndId" resultType="java.util.Map">
    <if test="type == '0'">
      SELECT * FROM org_employee_user where tenant_id=#{tenantId} and user_id=#{id}
    </if>
    <if test="type =='1'">
      SELECT * FROM org_group where tenant_id=#{tenantId} and id=#{id}
    </if>
    <if test="type=='2'">
      SELECT * FROM org_dept where tenant_id=#{tenantId} and dept_id=#{id}
    </if>
    <if test="type=='4'">

    </if>
  </select>

  <resultMap id="queryGroupUserByGroupIdResultMap" type="com.fxiaoke.paas.console.entity.datarights.GroupUser">
    <id property="groupId" column="groupId"></id>
    <result column="tenantId" property="tenantId"></result>
    <collection property="users" ofType="com.fxiaoke.paas.console.entity.datarights.Principal">
      <id column="userId" property="userId"></id>
      <result column="name" property="name"></result>
      <result column="position" property="position"></result>
    </collection>

  </resultMap>
</mapper>