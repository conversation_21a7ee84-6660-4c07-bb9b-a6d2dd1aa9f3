<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
  <h1>ES文本查询字段刷成编号类字段</h1>
  <ol class="breadcrumb">
    <li><a href="${ctx}"><i class="fa fa-dashboard"></i>主页</a></li>
  </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info row">
        <div class="box-header with-border">
        </div>
        <form class="form-horizontal" action="" method="post" id="infoFrom" role="form" data-toggle="validator">
          <div class="box-body col-xs-6">
            <div class="form-group">
              <label for="tenantId" class="col-sm-4 control-label">租户ID</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="tenantId" placeholder="TenantId(必填)" required>
              </div>
            </div>
            <div class="form-group">
              <label for="apiName" class="col-sm-4 control-label">对象名称</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="objectApiName" placeholder="Describe Api Name(必填)">
              </div>
            </div>
            <div class="form-group">
              <label for="name" class="col-sm-4 control-label">字段名称</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="fieldApiName" placeholder="Field ApiName(必填)">
              </div>
            </div>
            <div class="col-sm-offset-3">
              <button type="button" id="findData" class="btn btn-primary" disabled>执行</button>
              <input id="cancel_btn" class="btn" type="button" value="返回" onclick="history.back()"/>
            </div>
          </div>
          <div class="box-body col-xs-6">
            <h4>Result <i class="fa fa-hand-o-down"></i></h4>
            <pre id="dataInfo" style="">
                        </pre>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<#--表单验证插件-->
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script>
  $('#findData').on('click', function () {
    $('#dataInfo').text("");

    $.getJSON("${CONTEXT_PATH}/metadata/data/t2n", {
      tenantId: $('#tenantId').val(),
      objectApiName: $('#objectApiName').val(),
      fieldApiName: $('#fieldApiName').val(),
    }, function (data) {
      if (data.result !== "") {
        $('#dataInfo').JSONView(data.result, {
          collapsed: false,
          nl2br: true,
          recursive_collapser: true
        });
      }
    });

  <#--$.ajax({-->
  <#--url: "${ctx}/metadata/data/info?tenantId=" + $('#tenantId').val() + "&apiName=" + $('#apiName').val() + "&name=" + $('name').val() + "&id=" + $('#dataId').val(),-->
  <#--contentType: "application/json",-->
  <#--type: "POST",-->
  <#--data: ,-->
  <#--traditional: true,-->
  <#--success: function (data) {-->
  <#--if (data.dataInfo !== "") {-->
  <#--$('#dataInfo').JSONView(data.dataInfo, {-->
  <#--collapsed: false,-->
  <#--nl2br: true,-->
  <#--recursive_collapser: true-->
  <#--});-->
  <#--}-->
  <#--}-->
  <#--});-->
  });

  /**
   * 表单验证
   */
  var required1 = false, required2 = false, required3 = false;

  $('#tenantId').bind("input propertychange", function () {
    var tenantIdValue = $('#tenantId').val();
    if (tenantIdValue === null || tenantIdValue === "") {
      required1 = false;
    } else {
      required1 = true;
    }
  });

  $('#objectApiName').bind("input propertychange", function () {
    var apiNameValue = $('#objectApiName').val();
    if (apiNameValue === null || apiNameValue === "") {
      required2 = false;
    } else {
      required2 = true;
    }
  });

  $('#fieldApiName').bind("input propertychange", function () {
    var dataIdValue = $('#fieldApiName').val();
    if (dataIdValue === null || dataIdValue === "") {
      required3 = false;
    } else {
      required3 = true;
    }
  });


  $('#tenantId,#objectApiName,#fieldApiName').bind("input propertychange", function () {
    if (required1 && required2 && required3) {
      $('#findData').removeAttr("disabled");
    } else {
      $('#findData').attr("disabled", "disabled");
    }
  });
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
