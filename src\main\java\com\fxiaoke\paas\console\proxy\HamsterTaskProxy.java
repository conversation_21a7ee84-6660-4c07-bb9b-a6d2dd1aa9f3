package com.fxiaoke.paas.console.proxy;

import com.alibaba.fastjson.TypeReference;
import com.facishare.hamster.arg.CreateHamsterTaskArg;
import com.facishare.hamster.arg.HamsterLogArg;
import com.facishare.hamster.arg.QuarantineMigrationArg;
import com.facishare.hamster.arg.SkipHamsterTaskItemArg;
import com.facishare.hamster.common.Result;
import com.facishare.hamster.pojo.HamsterTaskPojo;
import com.facishare.hamster.result.HamsterLogResult;
import com.facishare.hamster.result.HamsterTaskItemResult;
import com.facishare.hamster.result.HamsterTaskResult;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.ws.rs.core.MediaType;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class HamsterTaskProxy {

  private String HAMSTER_SERVER_BASE_URL;
  private String PAAS_BI_COPIER_DUMP_URL;
  private String PAAS_BI_COPIER_RECHECK_URL;
  private final String FS_PAAS_AUTH_HEADER_EI = "x-fs-ei";

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("paas-hamster", config -> {
      HAMSTER_SERVER_BASE_URL = config.get("hamster_server_base_url", "http://localhost:8080/hamster-server");
      PAAS_BI_COPIER_DUMP_URL = config.get("paas_bi_copier_url_dump", "http://************:62863/api/v1/dump/tenantId");
      PAAS_BI_COPIER_RECHECK_URL = config.get("paas_bi_copier_url_recheck", "http://************:62863/api/v1/recheck/tenantId");
    });
  }

  /***
   * 创建任务
   * @param task
   */
  public void createHamsterTask(HamsterTaskPojo task) {
    String url = HAMSTER_SERVER_BASE_URL + "/hamster/create-hamster-task";
    Map<String, Object> body = Maps.newHashMap();
    body.put("hamsterTaskPojo", task);
    OkHttpClient.post(url, getHeaders(task.getTenantId()), body, MediaType.APPLICATION_JSON);
  }

  public Result<String> softDeleteHamsterTask(HamsterTaskPojo pojo) {
    String url = HAMSTER_SERVER_BASE_URL + "/hamster/soft-delete-hamster-task";
    return OkHttpClient.post(url, null, pojo, MediaType.APPLICATION_JSON, new TypeReference<Result<String>>() {
    });
  }

  public Result<String> deleteHamsterTask(HamsterTaskPojo pojo) {
    String url = HAMSTER_SERVER_BASE_URL + "/hamster/delete-hamster-task";
    return OkHttpClient.post(url, null, pojo, MediaType.APPLICATION_JSON, new TypeReference<Result<String>>() {
    });
  }

  public HamsterTaskResult queryHamsterTask(QuarantineMigrationArg arg) {
    String url = HAMSTER_SERVER_BASE_URL + "/hamster/query-hamster-task";
    return OkHttpClient.post(url, null, arg, MediaType.APPLICATION_JSON, new TypeReference<HamsterTaskResult>() {
    });
  }

  public HamsterTaskItemResult queryHamsterTaskItem(QuarantineMigrationArg arg) {
    String url = HAMSTER_SERVER_BASE_URL + "/hamster/query-hamster-task-item";
    return OkHttpClient.post(url, null, arg, MediaType.APPLICATION_JSON, new TypeReference<HamsterTaskItemResult>() {
    });
  }

  public HamsterLogResult queryHamsterLog(HamsterLogArg arg) {
    String url = HAMSTER_SERVER_BASE_URL + "/hamster/query-hamster-log";
    return OkHttpClient.post(url, null, arg, MediaType.APPLICATION_JSON, new TypeReference<HamsterLogResult>() {
    });
  }

  private Map<String, Object> getHeaders(String tenantId) {
    Map<String, Object> headers = Maps.newHashMap();
    headers.put(FS_PAAS_AUTH_HEADER_EI, tenantId);
    return headers;
  }

  public Result skipHamsterTaskItem(SkipHamsterTaskItemArg arg) {
    String url = HAMSTER_SERVER_BASE_URL + "/hamster/skip-hamster-task-item";
    return OkHttpClient.post(url, null, arg, MediaType.APPLICATION_JSON, new TypeReference<Result<String>>() {
    });
  }

  public Result<String> startMigration(QuarantineMigrationArg arg) {
    String url = HAMSTER_SERVER_BASE_URL + "/hamster/start-quarantine-migration";
    return OkHttpClient.post(url, null, arg, MediaType.APPLICATION_JSON, new TypeReference<Result<String>>() {
    });
  }

  public Result updateHamsterTask(CreateHamsterTaskArg arg) {
    String url = HAMSTER_SERVER_BASE_URL + "/hamster/update-hamster-task";
    return OkHttpClient.post(url, null, arg, MediaType.APPLICATION_JSON, new TypeReference<Result<String>>() {
    });
  }

  public String paasBiCopierDump(String tenantId){
    String url = PAAS_BI_COPIER_DUMP_URL + "?tenantId="+tenantId+"&table=.*";
    return OkHttpClient.postV2(url, null,"", MediaType.APPLICATION_JSON, new TypeReference<String>() {
    });
  }

  public String paasBiCopierRecheck(String tenantId){
    String url = PAAS_BI_COPIER_RECHECK_URL + "?tenantId="+tenantId+"&table=.*";
    return OkHttpClient.postV2(url, null,"", MediaType.APPLICATION_JSON, new TypeReference<String>() {
    });
  }
}
