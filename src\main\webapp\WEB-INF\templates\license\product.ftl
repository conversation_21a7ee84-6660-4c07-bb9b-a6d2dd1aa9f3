<#assign headContent>
<link href="//static.foneshare.cn/oss/AdminLTE/plugins/datatables/dataTables.bootstrap.css" rel="stylesheet"
      type="text/css"/>
<link type="text/css" href="//static.foneshare.cn/oss/datatables-checkboxes/dataTables.checkboxes.css"
      rel="stylesheet"/>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
<style>
  #datatable td:nth-child(6) {
    text-align: right;
  }

  #datatable td:nth-child(7) {
    text-align: right;
  }
</style>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
  <h1>查询Product</h1>
  <ol class="breadcrumb">
    <li><a href="#"><i class="fa fa-dashboard"></i>主页</a></li>
  </ol>
</section>
</#assign>

<#assign bodyContent>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div id="warningInfo" class="alert alert-warning hide">
        <span id="closeInfo" href="#" class="close">&times;</span>
        <strong id="hitInfo"></strong>
      </div>
      <div class="box box-info" style="margin-bottom: 1px;">
        <div class="box-body" style="padding: 20px;">
          <form class="form-inline">
            <div class="form-group">
              <label for="tenantId" class="control-label">企业ID</label>
              <input type="text" style="border-radius:5px;" class="form-control" id="tenantId" name="tenantId" placeholder="企业ID">
            </div>
            <button type="button" id="findSub" class="btn btn-primary">查询</button>
          </form>
        </div>
        <div class="box-footer clearfix" style="padding: 2px;">
          <div class="clearfix"></div>
        </div>
      </div>
      <div class="box box-info">
        <div class="box-body">
          <table id="datatable" class="table table-hover table-bordered" cellspacing="0" width="100%">
            <thead>
            <tr>
              <th>ID</th>
              <th>企业ID</th>
              <th>产品名称</th>
              <th>产品版本</th>
              <th>单价</th>
            </tr>
            </thead>
          </table>
        </div>
        <div class="box-footer clearfix">
        </div>
      </div>
    </div>
  </div>
</section>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script type="application/javascript">
  $(document).ready(function () {
    var table = $("#datatable").DataTable({
//            "deferRender": true,
      "processing": true,
      "ajax": "",
      "columnDefs": [],
      "language": {
        "url": "${ctx}/static/js/datatables-zh_CN.json"
      },
      "mark": true
      // "paging": false
    });

//        提交
    $('#findSub').on('click', function () {
      $('#warningInfo').addClass('hide');
      $("#datatable").dataTable().fnClearTable();
      var tenantId = $('#tenantId').val();
      if (tenantId === "") {
        $('#warningInfo').removeClass('hide');
        $('#hitInfo').html('关键字段不能为空！');
        return;
      }

//            将对象转化为json字符串放在url中传递到后端
      table.ajax.url("${ctx}/license/find-product?tenantId=" + tenantId).load();
    });


  });
  /**
   * 信息提示栏关闭
   */
  $('#closeInfo').on('click', function () {
    $('#warningInfo').addClass('hide');
  });

</script>
</#assign>
<#include "../layout/layout-main.ftl" />
