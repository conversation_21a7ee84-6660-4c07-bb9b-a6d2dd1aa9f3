package com.fxiaoke.paas.console.web.metadata

import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.bean.metadata.RestoreField
import com.fxiaoke.paas.console.entity.metadata.MtDescribe
import com.fxiaoke.paas.console.service.metadata.RestoreDataService
import com.google.common.collect.Lists
import groovy.util.logging.Slf4j
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody

@Controller
@Slf4j
@RequestMapping("/metadata-restore")
class RestoreDataController {
  @Autowired
  private RestoreDataService restoreDataService;

  /**
   * 跳转自定义对象恢复数据页面
   * @return
   */
  @RequestMapping("")
  def loadPage(){
    "metadata/restoreData"
  }

  @RequestMapping("/restoreData")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 自定义对象恢复数据")
  restoreData(String tenantId,String describeApiName,String dataId){
    if (StringUtils.isEmpty(tenantId) || StringUtils.isEmpty(describeApiName) || StringUtils.isEmpty(dataId)){
      return ["code": 200, "info": ["message":"参数不能为空"]]
    }

    MtDescribe mtDescribe = restoreDataService.findDescribe(tenantId,describeApiName)
    if (StringUtils.isNotEmpty(mtDescribe.getStoreTableName())){
      return ["code": 200, "info": ["message":"数据为预设对象"]]
    }

    List<String> dataIdList = Lists.newArrayList(dataId.split(","))

    List<String> messageList = Lists.newArrayList()

    for (String id:dataIdList){

      Map<String, Object> dataMap = restoreDataService.findMtData(tenantId, id, describeApiName)

      List<RestoreField> restoreFieldList = Lists.newArrayList()
      List<RestoreField> list= restoreDataService.findRestoreFieldList(tenantId,mtDescribe.getDescribeId())
      String lifeStatus = "normal"
      String fieldNum = null
      //查询需要恢复的mt_unique数据和mt_data的life_status_before_invalid
      for (RestoreField field:list){
        if ("life_status".equals(field.getApiName())){
          fieldNum = "value"+field.getFieldNum()
        }

        if ("life_status_before_invalid".equals(field.getApiName())){
          lifeStatus = dataMap.get("value"+field.getFieldNum())
        }

        if (field.getIsUnique()){
          restoreFieldList.add(field)
        }
      }

      if (dataMap.get("is_deleted") != -1 || fieldNum == null || !"invalid".equals(String.valueOf(dataMap.get(fieldNum)))){
        messageList.add("数据ID：" + id + "，不符合恢复数据条件")
        continue
      }

      if (CollectionUtils.isNotEmpty(restoreFieldList)){
        for (RestoreField restoreField:restoreFieldList){
          if (restoreField.getFieldNum() == null){
            restoreField.setFieldValue(dataMap.get(restoreField.getApiName()) as String)
          }else {
            restoreField.setFieldValue(dataMap.get("value"+restoreField.getFieldNum()) as String)
          }
        }
      }

      try {
        restoreDataService.restoreData(tenantId, mtDescribe.getDescribeApiName(), restoreFieldList, id, lifeStatus, fieldNum)
      } catch(Exception e){
        messageList.add("数据ID：" + id + "，" + e.getMessage())
      }
    }

    if (CollectionUtils.isNotEmpty(messageList)){
      return ["code": 200, "info": ["message":messageList]]
    }else {
      return ["code": 200, "info": ["message":"数据恢复成功"]]
    }

  }

  @RequestMapping("/batchRestoreData")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 自定义对象恢复数据")
  batchRestoreData(String tenantId,String describeApiName,Long startTime,Long endTime){
    try {
      if (StringUtils.isEmpty(tenantId) || StringUtils.isEmpty(describeApiName) || startTime == null){
        return ["code": 200, "info": ["message":"参数不能为空"]]
      }
      List<String> idList = restoreDataService.findRestoreIdList(tenantId,describeApiName,startTime,endTime)
      if (CollectionUtils.isEmpty(idList)){
        return ["code": 200, "info": ["message":"需要恢复的数据为空"]]
      }
      return restoreData(tenantId,describeApiName,idList.join(","))
    }catch(Exception e){
      return ["code": 200, "info": ["message":e.getMessage()]]
    }
  }
}
