package com.fxiaoke.paas.console.mapper.metadata

import com.github.mybatis.mapper.IBatchMapper
import com.github.mybatis.mapper.ICrudMapper
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

/**
 * <AUTHOR>
 * Created on 2018/3/5.
 */
interface DataMapper extends ICrudMapper, IBatchMapper, ITenant<DataMapper> {

  /**
   * 专表data列表
   * @param tableName
   * @param tenantId
   * @param apiName
   * @return
   */
  @Select("SELECT id,tenant_id,object_describe_id,object_describe_api_name,name,extend_obj_data_id FROM \${tableName} WHERE tenant_id = #{tenantId} AND object_describe_api_name = #{apiName} ORDER BY last_modified_time DESC LIMIT 1000")
  List<Map<String, Object>> findDataList(@Param("tableName") String tableName, @Param("tenantId") String tenantId, @Param("apiName") String apiName)

  /**
   * 查询专表详情
   * @param tableName
   * @param id
   * @param tenantId
   * @return
   */
  @Select("SELECT * FROM \${tableName} WHERE id=#{id} AND tenant_id=#{tenantId} AND object_describe_api_name=#{describeApiName}")
  Map<String, Object> dataInfo(@Param("tableName") String tableName, @Param("id") String id, @Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName)

  @Select("SELECT count(1) FROM \${tableName} WHERE tenant_id=#{tenantId} and object_describe_api_name=#{describeApiName}")
  Integer countDataByDescribeApiName(@Param("tableName") String tableName, @Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName)

  @Select("SELECT ID,object_describe_api_name FROM biz_sales_order WHERE tenant_id = #{tenantId} AND name = #{name} AND is_deleted = 0 ")
  Map<String,Objects> queryFsSalesOrderInfoByName(@Param("tenantId") String tenantId,@Param("name") String name)

  @Select("SELECT order_id,product_id FROM biz_sales_order_product WHERE tenant_id = #{tenantId} AND name = #{name} AND is_deleted = 0 ")
  Map<String,Objects> queryFsSalesOrderProductInfoByName(@Param("tenantId") String tenantId,@Param("name") String name)

  @Select("SELECT product_code FROM biz_product WHERE tenant_id = #{tenantId} AND id = #{id} AND is_deleted = 0 ")
  Map<String,Objects> queryFsProductInfoByName(@Param("tenantId") String tenantId,@Param("id") String id)

  @Update("update mt_data set \${columnName}=null where tenant_id = #{tenantId} and object_describe_api_name=#{describeApiName} and \${columnName}=#{dirtyValue}  and id =any('\${ids}')")
  int clearDirtyData(@Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName, @Param("columnName") String columnName, @Param("dirtyValue") String dirtyValue, @Param("ids") String ids);
}
