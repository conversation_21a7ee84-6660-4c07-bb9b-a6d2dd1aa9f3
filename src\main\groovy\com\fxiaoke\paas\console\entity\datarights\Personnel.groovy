package com.fxiaoke.paas.console.entity.datarights

import groovy.transform.ToString
import groovy.transform.builder.Builder
import lombok.Data


/**
 * <AUTHOR>
 * @date 2019/3/29 下午7:12
 *
 */
@Builder
@ToString
class Personnel {
  String userId
  String superior
  Set<Map<String,String>> subordinates
  Set<Map<String,String>> group
  Set<Map<String,String>>  departments
  String role
  String tenantId


}
