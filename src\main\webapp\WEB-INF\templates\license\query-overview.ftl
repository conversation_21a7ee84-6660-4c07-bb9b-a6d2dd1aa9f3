<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>查询概览信息</h1>
        <ol class="breadcrumb">
            <li><a href="../"><i class="fa fa-dashboard"></i>主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>查询概览</a></li>
        </ol>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info row">
                    <div class="box-header with-border" style="padding: 0px">
                    </div>
                    <form class="form-horizontal" action="" onsubmit="return false;" method="post" id="myForm" role="form" data-toggle="validator">
                        <div class="box-body col-xs-6" style="margin-right: -127px">
                            <div class="form-group">
                                <label for="tenantId" class="col-sm-2 control-label">租户ID</label>
                                <div class="col-sm-6">
                                    <input type="text" style="border-radius:5px;" class="form-control" id="tenantId"
                                           name="tenantId" value="" placeholder="">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="moudel" class="col-sm-2 control-label">返回总配额</label>
                                <div class="col-sm-6">
                                    <div class="col-sm-6">
                                        <p>是<input type="radio" style="border-radius:5px;" name="totalPara" value="1" checked="true"/>
                                            否<input type="radio" style="border-radius:5px;" name="totalPara" value="0"/></p>
                                    </div>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="moudel" class="col-sm-2 control-label">触发扫描</label>
                                <div class="col-sm-6">
                                    <div class="col-sm-6">
                                        <p>是<input type="radio" style="border-radius:5px;" name="needScan" value="1"/>
                                            否<input type="radio" style="border-radius:5px;" name="needScan" value="0" checked="true"/></p>
                                    </div>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>
                            <div class="col-sm-offset-3 col-sm-2">
                                <button type="button" id="queryBut" class="btn btn-primary">查询</button>
                            </div>
                        </div>
                        <#--result展示-->
                        <div class="box-body col-xs-6">
                            <h4>Result <i class="fa fa-hand-o-down"></i></h4>
                            <pre id="queryResult" style="">
                        </pre>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script>
      $(document).ready(function () {

        $('#queryBut').on('click', function () {

          $('#queryResult').html('');
          $('#queryBut').attr("disabled", "disabled");

          var tenantId = $('#tenantId').val();
          var totalPara = $("input[name='totalPara']:checked").val();
          var needScan = $("input[name='needScan']:checked").val();

          $.post("${CONTEXT_PATH}/license/query-overview", {
            tenantId: tenantId,
            totalPara: totalPara,
            needScan:needScan
          }, function (result) {
            $('#queryBut').removeAttr("disabled");
            if (result.code === 200) {
              info = result.info
            } else {
              info = result
            }
            $('#queryResult').JSONView(info, {
              collapsed: false,
              nl2br: true,
              recursive_collapser: true
            });
          });
        });

      });

    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
