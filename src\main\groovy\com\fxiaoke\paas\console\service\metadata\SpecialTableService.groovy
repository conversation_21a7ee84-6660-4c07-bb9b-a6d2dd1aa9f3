package com.fxiaoke.paas.console.service.metadata

import com.fxiaoke.paas.console.entity.log.SpecialTable
import com.fxiaoke.paas.console.mapper.log.SpecialTableMapper
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * 专表数据统计管理
 * <AUTHOR>
 * Created on 2018/7/6.
 */
//@Service
@Slf4j
class SpecialTableService {

    String createSpecialTableSql

    @Autowired
    SpecialTableMapper specialTableMapper

    /**
     * 插入专表信息
     * @param storeTable
     * @return
     */
    def insert(SpecialTable specialTable) {
        if (specialTableMapper.findByStoreName(specialTable.storeTableName) != null) {
            return 0
        }
        specialTableMapper.insertAndSetObjectId(specialTable)
    }

    /**
     * 获取建表sql
     * @param tableName
     */
    def getCreateSpecialTableSql(String id) {
        SpecialTable specialTable = specialTableMapper.findById(id as Long)
        return createSpecialTableSql.replaceAll( "SPECIAL_TABLE_NAME", specialTable.storeTableName)
    }

}
