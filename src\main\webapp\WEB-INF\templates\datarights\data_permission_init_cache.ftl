<#assign title="缓存初始化">
<#assign active_nav="data_permission_init">
<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
<style>
    .doSearch {
        width: 8%;
        margin-left: 20px
    }


    #dataPermission_table{
        margin-top:10px;
        width:97%;
        margin-left:1.5%;
        }

        input{
        margin-left:10px;
        }


        #tenants{
        width:75%;
        height:20px;
        }

        #data_init_tenants{
        margin-top:20px;
        }


    #dataTable2 th {
        vertical-align: middle;
        align-items:center;
    }


    #dataTable2 td {
        vertical-align: middle;
        align-items:center
    }

        .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
            width: 10%;
        }
        input {
            width: 10%;
            height: 34px;
            line-height: 34px;
            box-sizing: border-box;
            border-radius: 4px;
            border: 1px solid #c8cccf;
            color: #6a6f77;
            -web-kit-appearance: none;
            -moz-appearance: none;
            outline: 0;
            text-decoration: none;
        }
</style>
</#assign>
<#assign bodyContent>
    <#assign breadcrumbContent>
    <section class="content-header">
        <h1>缓存初始化</h1>
        <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i>初始化</a></li>
        </ol>
    </section>
    </#assign>
<section class="content">
<div class="row">
<div class="col-md-12">
    <div class="box box-info">
        <div class="box-header with-border">
            <h3 class="box-title"></h3>
        </div>
        <form class="form-horizontal" action="${ctx}/biz/add" method="post" id="myForm" role="form"
              data-toggle="validator">
<div class="container-fluid">
    <div id="data_init_basic">
    <span style="margin-left:10px">缓存选择</span><select class="selectpicker">
             <option value="企业共享规则缓存初始化">企业共享规则缓存初始化</option>
             <option value="企业部门用户缓存初始化">企业部门用户缓存初始化</option>
              <option value="企业用户汇报对象缓存初始化">企业用户汇报对象缓存初始化</option>
              <option value="" selected>Nothing selected</option>
     </select>
    <input placeholder="企业_ID"/>
    <input placeholder="APP_ID"/>
    <input placeholder="currentPage"/>
    </div>
    <div id="data_init_tenants">
    <span style="margin-left:10px">Tenants</span>
    <input placeholder="tenants" style="width:75%"/>
        <button type="button" class="doSearch btn btn-primary">Send</button>
</div>
    <div id="dataPermission_table">
        <table class="table table-striped table-bordered table-condensed dataTable no-footer" id="dataTable2">
            <thead>
            <tr>
                <th>errCode</th>
                <th>errMessage</th>
            </tr>
            </thead>
        </table>
    </div>
</div>
</div>
    </form>
</div>
    </div>
    </div>
</section>
</#assign>
<#assign scriptContent>
<script>
    $(document).ready(function () {
        var bootstrapDom = "<'row'<'col-sm-6'l>>" +
                "<'row'<'col-sm-12'tr>>" +
                "<'row'<'col-sm-5'i><'col-sm-7'p>>";
        var table = $("#dataTable2").DataTable({
            "dom": bootstrapDom,
            "paging": true,
            "bLengthChange": true,
            "bFilter": true,
            "bInfo": true,
            "processing": false,
  //          "serverSide": true,//服务器分页
            "search": {
                "regex": true
            },
            "ordering":false,
//            "order": [[3, "asc"],[2, "asc"],[1, 'asc'],[4, "desc"]],
            "ajax": "${CONTEXT_PATH}/data/permission/init?dataObject=",
            "columnDefs": [
                { "width": "50%", "targets": 0,},
                { "width": "50%", "targets": 1,}
            ],
            columns: [
                {data: "errCode"},
                {data: "errMessage"},
            ],
            "iDisplayLength": 25,
            "sPaginationType": "full_numbers",
            "language": {
                "processing": "加载中...",
                "lengthMenu": "显示 _MENU_ 项结果",
                "zeroRecords": "没有匹配结果",
                "emptyTable": "没有数据",
                "info": "",
                "infoEmpty": "",
                "infoFiltered": "",
                "infoPostFix": "",
                "search": "搜索:",
                "url": "",
                "paginate": {
                    "first": "首页",
                    "previous": "上页",
                    "next": "下页",
                    "last": "末页"
                }
            }
        });

        $(".doSearch").on("click", function () {
        var tenantId = $("#data_init_basic input").eq(0).val();
        var AppId = $("#data_init_basic input").eq(1).val();
        var currentPage = $("#data_init_basic input").eq(2).val();
        var initType = $("#data_init_basic select").val();
        var tenants = $("#data_init_tenants input").val();
        if(tenantId==""){
        alert("企业ID不可为空!");
        return false;
        }
        if(initType==""){
        alert("缓存选择项—不可为空");
        return false;
        }
        var dataObject = {
            "initType":initType,
            "tenantId":tenantId,
            "AppId":AppId,
            "currentPage":currentPage,
            "tenants":tenants,
        }
        table.ajax.url("${CONTEXT_PATH}/datarights_refresh/permission/init?dataObject="+encodeURIComponent(JSON.stringify(dataObject))).load();
        });

         });
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
