package com.fxiaoke.paas.console.proxy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fxiaoke.common.http.handler.AsyncCallback;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.collections.MapUtils;

import java.io.IOException;
import java.util.Map;

@Slf4j
public class OkHttpClient {

  private static OkHttpSupport client;

  public void setClient(OkHttpSupport client) {
    OkHttpClient.client = client;
  }

  public static JSONObject executeCallBack(String url, Map<String, Object> headers) {
    log.info("http get url:{},header:{}", url, headers);
    Request.Builder builder = new Request.Builder();

    if (MapUtils.isNotEmpty(headers)) {
      headers.forEach((k, v) -> builder.addHeader(k, v.toString()));
    }
    Request request = builder.url(url).build();
    return (JSONObject) client.syncExecute(request, new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {
        if (response != null) {
          if (response.isSuccessful()) {
            String res = response.body().string();
            JSONObject jsonObject = (JSONObject) JSON.parse(res, Feature.config(0, Feature.UseBigDecimal, false));
            log.debug("http get url:{},code: {} result:{}", url, response.code(), JSON.toJSONString(jsonObject));
            return jsonObject;
          } else {
            log.info("http get url:{}, code: {}, message:{}", url, response.code(), response.message());
          }
        } else {
          log.info("http get url:{}, response is null", url);
        }
        return new JSONObject();
      }
    });
  }

  public static JSONArray executeGetArray(String url, Map<String, Object> headers) {
    log.info("http get array url:{},header:{}", url, headers);
    Request.Builder builder = new Request.Builder();

    if (MapUtils.isNotEmpty(headers)) {
      headers.forEach((k, v) -> builder.addHeader(k, v.toString()));
    }
    Request request = builder.url(url).build();
    return (JSONArray) client.syncExecute(request, new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {
        if (response != null) {
          if (response.isSuccessful()) {
            return JSON.parseArray(response.body().string());
          } else {
            log.info("http get array url:{}, code: {}, message:{}", url, response.code(), response.message());
          }
        } else {
          log.info("http get array url:{}, response is null", url);
        }
        return new JSONArray();
      }
    });
  }

  /**
   * 此方法不处理异常,不打error日志，由调用方处理
   *
   * @param url
   * @param headers
   * @return
   */
  public static JSONObject simpleGet(String url, Map<String, Object> headers) {
    log.info("http simpleGet url:{}, header:{}",url, headers);
    Request.Builder builder = new Request.Builder();
    Request request = builder.url(url).build();
    if (MapUtils.isNotEmpty(headers)) {
      headers.forEach((k, v) -> builder.addHeader(k, v.toString()));
    }
    return  (JSONObject) client.syncExecute(request, new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {
        if (response == null) {
          log.warn("http simpleGet error,response is null.url:{}, header:{}",url,headers);
          throw new Exception("response is null");
        }
        String res = response.body().string();
        if (!response.isSuccessful()) {
          log.warn("http simpleGet error,url:{}, header:{}, code:{}, result:{}",url,headers,response.code(),res);
          throw new Exception(res);
        }
        log.debug("http simpleGet success, url:{}, header:{}, code:{}, result:{}",url,headers,response.code(),res);
        return JSON.parse(res, Feature.config(0, Feature.UseBigDecimal, false));
      }
    });
  }

  public static JSONObject get(String url, Map<String, Object> headers) {
    try {
      return executeCallBack(url, headers);
    } catch (Exception e) {
      log.error("OkHttpClient.get() Error, url:{}, headers:{}", url, headers);
      log.error("OkHttpClient.get() Error", e);
    }
    return new JSONObject();
  }

  public static JSONObject getException(String url, Map<String, Object> headers) throws Exception {
    return executeCallBack(url, headers);
  }

  public static JSONObject post(String configName, String key, Map<String, Object> headers, Object entity, String mediaType) {
    String url = null;
    try {
      url = ConfigFactory.getConfig(configName).get(key);
    } catch (Exception e) {
      log.error("configName:{}, key:{}", configName, key);
      log.error("OkHttpClient getConfig Error", e);
    }
    return post(url, headers, entity, mediaType);
  }


  public static JSONObject post(String url, Map<String, Object> headers, Object entity, String type) {
    log.debug("post url:{}, headers:{}, entity:{}", url, JSON.toJSON(headers), JSONObject.toJSONString(entity, SerializerFeature.WriteMapNullValue));
    try {
      MediaType mediaType = MediaType.parse(type);
      RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(entity, SerializerFeature.WriteMapNullValue));
      Request.Builder builder = new Request.Builder();
      if (MapUtils.isNotEmpty(headers)) {
        headers.forEach((k, v) -> builder.addHeader(k, v.toString()));
      }
      Request request = builder.url(url).post(body).build();
      return (JSONObject) client.syncExecute(request, new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
          if (response != null) {
            if (response.isSuccessful()) {
              String res = response.body().string();
              JSONObject jsonObject = JSONObject.parseObject(res);
              log.debug("http post url:{}, code: {}, result:{}", url, response.code(), JSON.toJSONString(jsonObject));
              return jsonObject;
            } else {
              log.debug("http post url:{}, code: {}, message:{}", url, response.code(), response.message());
            }
          } else {
            log.debug("http post url:{}, response is null", url);
          }
          return new JSONObject();
        }
      });
    } catch (Exception e) {
      log.error("OkHttpClient.post() Error, url:{}, headers:{}, entity:{}, mediaType:{}",
                url,
                headers,
                JSONObject.toJSONString(entity, SerializerFeature.WriteMapNullValue),
                type);
      log.error("OkHttpClient.post() Error", e);
    }
    return new JSONObject();
  }

  public static <T> T post(String url, Map<String, Object> headers, Object entity, String type, TypeReference<T> typeReference) {
    log.debug("post url:{}, headers:{}, entity:{}", url, JSON.toJSON(headers), JSONObject.toJSONString(entity, SerializerFeature.WriteMapNullValue));
    try {
      MediaType mediaType = MediaType.parse(type);
      RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(entity, SerializerFeature.WriteMapNullValue));

      Request.Builder builder = new Request.Builder();

      if (MapUtils.isNotEmpty(headers)) {
        headers.forEach((k, v) -> builder.addHeader(k, v.toString()));
      }
      Request request = builder.url(url).post(body).build();

      return (T) client.syncExecute(request, new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
          if (response != null) {
            if (response.isSuccessful()) {
              String responseBody = response.body().string();
              //log.info("http post url:{}, code: {}, result:{}", url, response.code(), responseBody);
              return JSON.parseObject(responseBody, typeReference);
            } else {
              log.info("http post url:{}, code: {}, message:{}", url, response.code(), response.message());
            }
          } else {
            log.info("http post url:{}, response is null", url);
          }
          return null;
        }
      });
    } catch (Exception e) {
      log.error("OkHttpClient.post() Error, url:{}, headers:{}, entity:{}, mediaType:{}",
                url,
                headers,
                JSONObject.toJSONString(entity, SerializerFeature.WriteMapNullValue),
                type);
      log.error("OkHttpClient.post() Error", e);
      throw e;
    }
  }

  /**
   * 处理返回值非json格式的接口调用
   */
  public static <T> T postV2(String url, Map<String, Object> headers, Object entity, String type, TypeReference<T> typeReference) {
    log.debug("post url:{}, headers:{}, entity:{}", url, JSON.toJSON(headers), JSONObject.toJSONString(entity, SerializerFeature.WriteMapNullValue));
    try {
      MediaType mediaType = MediaType.parse(type);
      RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(entity, SerializerFeature.WriteMapNullValue));

      Request.Builder builder = new Request.Builder();

      if (MapUtils.isNotEmpty(headers)) {
        headers.forEach((k, v) -> builder.addHeader(k, v.toString()));
      }
      Request request = builder.url(url).post(body).build();

      return (T) client.syncExecute(request, new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
          if (response != null) {
            if (response.isSuccessful()) {
              String responseBody = response.body().string();
              //log.info("http post url:{}, code: {}, result:{}", url, response.code(), responseBody);
              if (JSON.isValid(responseBody)){
                return JSON.parseObject(responseBody, typeReference);
              }
              return responseBody;
            } else {
              log.info("http post url:{}, code: {}, message:{}", url, response.code(), response.message());
            }
          } else {
            log.info("http post url:{}, response is null", url);
          }
          return null;
        }
      });
    } catch (Exception e) {
      log.error("OkHttpClient.post() Error, url:{}, headers:{}, entity:{}, mediaType:{}",
                url,
                headers,
                JSONObject.toJSONString(entity, SerializerFeature.WriteMapNullValue),
                type);
      log.error("OkHttpClient.post() Error", e);
      throw e;
    }
  }

  /**
   * 异步执行 post请求
   */
  public static void asyncPost(String url, Map<String, Object> headers, Object entity, String type) {
    log.info("post url:{}, headers:{}, entity:{}", url, JSON.toJSON(headers), JSON.toJSON(entity));
    try {

      Request.Builder builder = new Request.Builder();
      if (MapUtils.isNotEmpty(headers)) {
        headers.forEach((k, v) -> builder.addHeader(k, v.toString()));
      }
      RequestBody body = RequestBody.create(MediaType.parse(type), JSONObject.toJSONString(entity));
      Request request = builder.url(url).post(body).build();

      client.asyncExecute(request, new AsyncCallback() {
        @Override
        public void response(Response response) throws IOException {
          if (response != null) {
            if (response.isSuccessful()) {
              String res = response.body().string();
              JSONObject jsonObject = JSONObject.parseObject(res);
              log.debug("async http post url:{}, code: {}, result:{}", url, response.code(), JSON.toJSONString(jsonObject));
            } else {
              log.info("async http post url:{}, code: {}, message:{}", url, response.code(), response.message());
            }
          } else {
            log.info("async http post url:{}, response is null", url);
          }
        }
      });
    } catch (Exception e) {
      log.error("OkHttpClient.post() Error, url:{}, headers:{}, entity:{}, mediaType:{}", url, headers, entity, type);
      log.error("OkHttpClient.post() Error", e);
    }
  }

  private OkHttpClient() {
  }

}