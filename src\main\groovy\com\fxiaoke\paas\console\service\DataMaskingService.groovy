package com.fxiaoke.paas.console.service

import com.fxiaoke.paas.console.util.DataMaskingUtil
import groovy.util.logging.Slf4j
import org.springframework.stereotype.Service

/**
 * 数据脱敏过滤
 * <AUTHOR>
 * @date 2018/6/7
 */
@Service
@Slf4j
class DataMaskingService {

  private static final String NAME = "name"
  private static final String PHONE = "phone_number"
  private static final String EMAIL = "email"
  private static final String CURRENCY = "currency"
  private static final String LOCATION = "location"
  private static final String IMAGE = "image"
  private static final String FILE_ATTACHMENT = "file_attachment"

  /**
   * 元数据数据脱敏
   * @param data
   * @param type
   * @return
   */
  static Object filter(String data, String type) {
    switch (type) {
      case NAME:
        return DataMaskingUtil.chineseName(data)
      case PHONE:
        return DataMaskingUtil.mobilePhone(data)
      case IMAGE:
        return DataMaskingUtil.address(data, 10)
      case FILE_ATTACHMENT:
        return DataMaskingUtil.address(data, 10)
      case LOCATION:
        return DataMaskingUtil.address(data, 10)
      case EMAIL:
        return DataMaskingUtil.email(data)
      case CURRENCY:
        return DataMaskingUtil.password(data)
      default:
        return ""
    }
  }
}
