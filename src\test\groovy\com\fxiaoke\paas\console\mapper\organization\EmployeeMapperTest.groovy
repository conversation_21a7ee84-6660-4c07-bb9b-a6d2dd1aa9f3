package com.fxiaoke.paas.console.mapper.organization

import com.google.common.collect.Lists
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * <AUTHOR>
 * Created on 2018/5/10.
 */
@ContextConfiguration(value = ["classpath:mapperContext.xml"])
class EmployeeMapperTest extends Specification{

  @Autowired
  PaasConsoleEmployeeMapper employeeMapper

  def "getDeptByTenantIdAndUserId" () {
    given:
    def tenantId = '2'
    def userIds = Lists.newArrayList()
    userIds.add('1071')
    expect:
    employeeMapper.setTenantId(tenantId).getDeptByTenantIdAndUserId(tenantId,userIds)
  }

}
