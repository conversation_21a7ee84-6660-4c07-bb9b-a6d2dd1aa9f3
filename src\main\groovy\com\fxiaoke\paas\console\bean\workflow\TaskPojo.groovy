package com.fxiaoke.paas.console.bean.workflow

import groovy.transform.ToString

/**
 * Created by yangxw on 2018/3/12.
 */
@ToString
class TaskPojo {

     String id;
    
     String tenantId;
    
     String appName;

     String sourceWorkflowId;

     String workflowId;
    
     String workflowInstanceId;

     String objectId;
    
     String entityName;

     String taskType;
    
     String actionType;
    
     String applicantId;
    
     String applicantAccountAndDept;

//    ("申请人所在部门")
//     String dept;

     String assignee;

     String assigneeAndOpinion;

     Boolean remind;

     String state;

     String errMsg;

     String createTime;

     String modifyTime;

     String duration;
}
