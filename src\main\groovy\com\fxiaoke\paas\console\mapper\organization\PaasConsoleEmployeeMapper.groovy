package com.fxiaoke.paas.console.mapper.organization

import com.fxiaoke.paas.console.entity.organization.EmployeeEntity
import com.github.mybatis.mapper.ICrudMapper
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Param

interface PaasConsoleEmployeeMapper extends ITenant<PaasConsoleEmployeeMapper>, ICrudMapper<EmployeeEntity> {

  /**
   * 通过企业ID查询所有员工ID
   * @param tenantId 企业ID
   * @return 员工ID集合
   */
  List<EmployeeEntity> getEmployeeIdByCompanyId(@Param("tenantId") String tenantId)

  /**
   * 通过企业ID，员工ID查询员工信息
   * @param tenantId 企业ID
   * @param userIds 员工ID
   * @param page 分页信息对象
   * @return 员工对象集合
   */
  List<EmployeeEntity> getEmployee(@Param("tenantId") String tenantId, @Param("userIds") List<String> userIds)

  /**
   * 通过企业ID,部门ID,部门员工关系类型查询其所有的员工信息
   * @param tenantId 企业ID
   * @param deptId 部门ID
   * @param type 关系类型
   * @param page 分页信息对象
   * @return 员工对象集合
   */
  List<EmployeeEntity> getRelationalEmployee(@Param("tenantId") String tenantId, @Param("deptId") String deptId, @Param("type") Integer type)

  /**
   * 根据企业ID和用户ID获取所属部门
   * @param tenantId
   * @param userId
   * @return
   */
  List<Map<String,String>> getDeptByTenantIdAndUserId(@Param("tenantId") String tenantId, @Param("userIds") List<String> userIds)

}