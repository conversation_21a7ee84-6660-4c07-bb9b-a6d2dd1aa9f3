<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
  <h1>DTS部署实施</h1>
  <ol class="breadcrumb">
    <li><a href="${ctx}"><i class="fa fa-dashboard"></i>主页</a></li>
    <li><a href=""><i class="fa fa-dashboard"></i>dts部署实施</a></li>
  </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info row">
        <div class="box-header with-border">
        </div>
        <form class="form-horizontal" action="" method="post" id="infoFrom" role="form" data-toggle="validator">
          <div class="box-body col-xs-6">
            <div class="form-group">
              <label for="ei" class="col-sm-4 control-label">企业ID</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="ei" placeholder="企业ID(必填)" required>
              </div>
            </div>
            <div class="form-group">
              <label for="dbType" class="col-sm-4 control-label">数据库类型</label>
              <div class="col-sm-4">
                <select id="dbType" name="dbType" class="selectpicker show-tick form-control" title="Nothing selected" data-live-search="false">
                  <option value="mysql">mysql</option>
                  <option value="postgresql">postgresql</option>
                  <option value="sqlserver">sqlserver</option>
                  <option value="oracle_sn">oracle_service_name</option>
                  <option value="oracle_sid">oracle_sid</option>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label for="dbHost" class="col-sm-4 control-label">数据库Host</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="dbHost" placeholder="数据库Host(必填)" required>
              </div>
            </div>
            <div class="form-group">
              <label for="dbPort" class="col-sm-4 control-label">数据库端口</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="dbPort" placeholder="数据库端口(必填)" required>
              </div>
            </div>
            <div class="form-group">
              <label for="dbName" class="col-sm-4 control-label">数据库名 / Oracle serviceName/sid</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="dbName" placeholder="数据库名(必填)" required>
              </div>
            </div>
            <div class="form-group">
              <label for="dbUser" class="col-sm-4 control-label">数据库用户名</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="dbUser" placeholder="数据库用户名(必填)" required>
              </div>
            </div>
            <div class="form-group">
              <label for="dbPwd" class="col-sm-4 control-label">数据库密码</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="dbPwd" placeholder="数据库密码(必填)" required>
              </div>
            </div>
            <div class="form-group">
              <label for="dispatchTime" class="col-sm-4 control-label">同步周期(秒)</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="dispatchTime" value="600" placeholder="同步周期(秒)(必填)" required>
              </div>
            </div>
            <div class="form-group">
              <label for="dateTimeToLong" class="col-sm-4 control-label">日期字段类型</label>
              <div class="col-sm-4">
                <label>
                  <input type="radio" name="dateTimeToLong" value="false">
                  DATE
                </label>
                &nbsp;&nbsp;
                <label>
                  <input type="radio" name="dateTimeToLong" value="true" checked>
                  LONG
                </label>
              </div>
            </div>
            <div class="form-group">
              <label for="tombstone" class="col-sm-4 control-label">逻辑删除</label>
              <div class="col-sm-4">
                <label>
                  <input type="radio" name="tombstone" value="true">
                  是
                </label>
                &nbsp;&nbsp;
                <label>
                  <input type="radio" name="tombstone" value="false" checked>
                  否
                </label>
              </div>
            </div>
            <div class="form-group">
              <label for="syncRelevantTeam" class="col-sm-4 control-label">同步相关团队数据</label>
              <div class="col-sm-4">
                <label>
                  <input type="radio" name="syncRelevantTeam" value="true">
                  是
                </label>
                &nbsp;&nbsp;
                <label>
                  <input type="radio" name="syncRelevantTeam" value="false" checked>
                  否
                </label>
              </div>
            </div>
            <div class="form-group">
              <label for="syncAllCity" class="col-sm-4 control-label">同步全球数据</label>
              <div class="col-sm-4">
                <label>
                  <input type="radio" name="syncAllCity" value="true">
                  是
                </label>
                &nbsp;&nbsp;
                <label>
                  <input type="radio" name="syncAllCity" value="false" checked>
                  否
                </label>
              </div>
            </div>
            <div class="form-group">
              <label for="checkJdbcUrl" class="col-sm-4 control-label">生成数据的时候校验数据库连接</label>
              <div class="col-sm-4">
                <input type="checkbox" style="border-radius:5px;" id="checkJdbcUrl" checked="checked">
              </div>
            </div>
            <div class="col-sm-offset-3">
              <button type="button" id="checkDbUrl" class="btn btn-primary">检测数据库连接</button>
              <button type="button" id="getConfig" class="btn btn-primary">获取安装配置</button>
              <input id="cancel_btn" class="btn" type="button" value="返回" onclick="history.back()"/>
            </div>
          </div>
          <div class="box-body col-xs-6">
            <h4><a href="#" onclick="navigator.clipboard.writeText(document.getElementById('dataInfo').innerText).then(() => alert('已复制到粘贴板')).catch(() => alert('复制失败')); return false;">拷贝到粘贴板 <i class="fa fa-hand-o-down"></i></a></h4>
            <pre style=""><div id="dataInfo"></div></pre>
          </div>
        </form>
      </div>
    </div>

    <div class="col-md-12">
      <div class="box box-info row">
        <div class="box-header with-border">
        </div>
        <form class="form-horizontal" action="" method="post" id="infoFrom" role="form" data-toggle="validator">
          <div class="box-body col-xs-6">
            生成egress脚本
            <div class="form-group">
              <label for="egressEi" class="col-sm-4 control-label">企业ID</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="egressEi" placeholder="企业ID(必填)" required>
              </div>
            </div>
            <div class="form-group">
              <label for="egressPort" class="col-sm-4 control-label">对外端口</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="egressPort" value="80" required>
              </div>
            </div>
            <div class="form-group">
              <label for="egressDbInfo" class="col-sm-4 control-label">数据库信息</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="egressDbInfo" value="" placeholder="数据库 IP:PORT(必填)" required>
              </div>
            </div>

            <div class="col-sm-offset-3">
              <button type="button" id="generateEgressScript" class="btn btn-primary">生成Egress脚本</button>
            </div>
          </div>
          <div class="box-body col-xs-6">
            <h4><a href="#" onclick="navigator.clipboard.writeText(document.getElementById('dataInfo2').innerText).then(() => alert('已复制到粘贴板')).catch(() => alert('复制失败')); return false;">拷贝到粘贴板 <i class="fa fa-hand-o-down"></i></a></h4>
            <pre style=""><div id="dataInfo2"></div></pre>
          </div>
        </form>
      </div>
    </div>

    <div class="col-md-12">
      <div class="box box-info row">
        <div class="box-header with-border">
        </div>
        <form class="form-horizontal" action="" method="post" id="infoFrom" role="form" data-toggle="validator">
          <div class="box-body col-xs-6">
            生成dts topic
            <div class="form-group">
              <label for="topicEi" class="col-sm-4 control-label">企业EI</label>
              <div class="col-sm-6">
                <input type="text" style="border-radius:5px;" class="form-control" id="topicEi" name="topicEi" value="" placeholder="企业EI(必填)" required>
              </div>
            </div>
            <div class="form-group">
              <label for="dtsTopic" class="col-sm-4 control-label">Dts topic</label>
              <div class="col-sm-6">
                <input type="text" style="border-radius:5px;" class="form-control" id="dtsTopic" name="dtsTopic" value="" placeholder="dts topic">
              </div>
            </div>
            <div class="box-footer clearfix">
              <div class="col-sm-offset-3">
                <a id="getDtsTopic" class="btn btn-info">查询Topic</a>
                <input id="cancel_btn" class="btn" type="button" value="返回" onclick="history.back()"/>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

  </div>
</section>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<#--表单验证插件-->
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script>
  $('#checkDbUrl').on('click', function () {
    $('#dataInfo').text("");

    const postBody = {
      "dbType": $('#dbType').val(),
      "dbHost": $('#dbHost').val(),
      "dbPort": $('#dbPort').val(),
      "dbName": $('#dbName').val(),
      "dbUser": $('#dbUser').val(),
      "dbPwd": $('#dbPwd').val()
    };

    $.ajax({
      url: "${CONTEXT_PATH}/dts/check/jdbc",
      contentType: "application/json",
      data: JSON.stringify(postBody),
      dataType: "json",
      type: "POST",
      traditional: true,
      success: function (data) {
        $('#dataInfo').text(data.data)
      }
    });
  });

  $('#getConfig').on('click', function () {
    $('#dataInfo').text("");
    const checkJdbcUrl = $('#checkJdbcUrl').is(':checked') ? '1' : '0'
    const postBody = {
      "ei": $('#ei').val(),
      "dbType": $('#dbType').val(),
      "dbHost": $('#dbHost').val(),
      "dbPort": $('#dbPort').val(),
      "dbName": $('#dbName').val(),
      "dbUser": $('#dbUser').val(),
      "dbPwd": $('#dbPwd').val(),
      "checkJdbcUrl": checkJdbcUrl,
      "dispatchTime": $('#dispatchTime').val(),
      "dateTimeToLong": $('input[name="dateTimeToLong"]:checked').val(),
      "tombstone": $('input[name="tombstone"]:checked').val(),
      "syncRelevantTeam": $('input[name="syncRelevantTeam"]:checked').val(),
      "syncAllCity": $('input[name="syncAllCity"]:checked').val()
    };
    $.ajax({
      url: "${CONTEXT_PATH}/dts/config/data",
      contentType: "application/json",
      data: JSON.stringify(postBody),
      dataType: "json",
      type: "POST",
      traditional: true,
      success: function (data) {
        $('#dataInfo').text(data.data)
      }
    });
  });

  $('#getDtsTopic').on('click', function () {
    $('#dtsTopic').val("");

    const ei = $('#topicEi').val();
    $.ajax({
      url: "${CONTEXT_PATH}/dts/get/topic?ei="+ei,
      type: "get",
      traditional: true,
      success: function (data) {
        $('#dtsTopic').val(data.data)
      }
    });
  });

  $('#generateEgressScript').on('click', function () {

    $('#dataInfo2').text("");
    const postBody = {
      "ei": $('#egressEi').val(),
      "port": $('#egressPort').val(),
      "dbInfo": $('#egressDbInfo').val()
    };
    $.ajax({
      url: "${CONTEXT_PATH}/dts/get/egress/script",
      contentType: "application/json",
      data: JSON.stringify(postBody),
      dataType: "json",
      type: "POST",
      traditional: true,
      success: function (data) {
        $('#dataInfo2').text(data.data)
      }
    });
  });

</script>
</#assign>
<#include "../layout/layout-main.ftl" />
