//package com.fxiaoke.paas.console.entity.workflow
//
//import com.effektif.workflow.api.ext.ExecutionPojo
//import com.facishare.paas.workflow.kernel.entity.ApprovalOpinionEntity
//import com.facishare.paas.workflow.kernel.entity.ApproverModifyLogEntity
//import com.facishare.paas.workflow.kernel.entity.RemindEntity
//import com.facishare.paas.workflow.kernel.entity.WorkflowRuleEntity
//import lombok.Data
//import com.effektif.mongo.TaskFields;
///**
// * Created by yangxw on 2018/3/2.
// */
//import org.bson.types.ObjectId
//import org.mongodb.morphia.annotations.Embedded
//import org.mongodb.morphia.annotations.Id
//import org.mongodb.morphia.annotations.Property
//@Data
//class TaskEntity {
//    @Id
//    private ObjectId id
//    @Property(TaskFields.TENANT_ID)
//    private String tenantId
//    @Property(TaskFields.APP_ID)
//    private String appId
//    @Property(TaskFields.SOURCE_WORKFLOW_ID)
//    private String sourceWorkflowId
//    @Property(TaskFields.WORKFLOW_ID)
//    private String workflowId
//    @Property(TaskFields.WORKFLOW_INSTANCE_ID)
//    private String workflowInstanceId
//    @Property(TaskFields.ENTITY_ID)
//    private String entityId
//    @Property(TaskFields.OBJECT_ID)
//    private String objectId
//    @Property(TaskFields.ACTIVITY_ID)
//    private String activityId
//    @Property(TaskFields.ACTIVITY_INSTANCE_ID)
//    private String activityInstanceId
//    @Property(TaskFields.CREATE_TIME)
//    private Long createTime
//    @Property(TaskFields.MODIFY_TIME)
//    private Long modifyTime
//    @Property(TaskFields.APPLICANT_ID)
//    private String applicantId
//    @Property(TaskFields.STATE)
//    private String state
//    @Property(TaskFields.TASK_TYPE)
//    private String taskType
//    @Property(TaskFields.ACTION_TYPE)
//    private String actionType
//    @Embedded(TaskFields.ASSIGNEE)
//    private Map<String, List<String>> assignee
//    @Embedded(TaskFields.OPINIONS)
//    private List<ApprovalOpinionEntity> opinions
//    @Property(TaskFields.REMIND)
//    private Boolean remind
//    @Property(TaskFields.REMIND_LATENCY)
//    private Integer remindLatency
//    @Property(TaskFields.REMIND_ID)
//    private String remindId
//    @Property(TaskFields.ERR_MSG)
//    private String errMsg
//
//    @Property(TaskFields.CANDIDATE_IDS)
//    private List<String> candidateIds
//    @Property(TaskFields.ASSIGNEE_IDS)
//    private List<String> assigneeIds
//    @Property(TaskFields.COMPLETED)
//    private Boolean completed
//    @Property(TaskFields.CANCELED)
//    private Boolean canceled
//    @Property(TaskFields.WORKFLOW_NAME)
//    private String workflowName
//    @Property(TaskFields.WORKFLOW_DESCRIPTION)
//    private String workflowDescription
//    @Property(TaskFields.NAME)
//    private String name
//    @Property(TaskFields.DESCRIPTION)
//    private String description
//    @Embedded(TaskFields.BPM_EXTENSION)
//    private Map<String, Object> bpmExtension
//
//    @Property(TaskFields.ASSIGNEE_CHANGED)
//    private Boolean assigneeChanged
//    @Property(TaskFields.ASSIGNEE_CHANGE_LOG)
//    private List<List<String>> assigneeChangeLog
//
//    @Embedded(TaskFields.APPROVER_MODIFY_LOG)
//    private List<ApproverModifyLogEntity> approverModifyLog
//
//    @Property(TaskFields.MODIFIER)
//    private String modifier
//    @Embedded(TaskFields.REMINDERS)
//    private List<RemindEntity> reminders
//    @Property(TaskFields.DELETED)
//    private Boolean deleted
//    @Embedded(TaskFields.EXECUTION)
//    private Map<String, List<ExecutionPojo>> execution
//    @Embedded(TaskFields.RULE)
//    private WorkflowRuleEntity rule
//    @Embedded(TaskFields.SOURCE_TRANSITION)
//    private Map<String, String> sourceTransition
//    @Property(TaskFields.CANDIDATE_EDITABLE)
//    private Boolean candidateEditable
//
//    @Property(TaskFields.TASK_EXECUTION_EXECUTED_INDEX)
//    private Integer taskExecutionExecutedIndex
//
//    @Property(TaskFields.TASK_EXECUTE_STATE)
//    private String taskExecuteState
//
//    /**
//     * demandBeyondAssignee值  0：上级审批；1：流程终止；2：指定审批人。
//     */
//    @Property(TaskFields.DEMAND_BEYOND_ASSIGNEE)
//    private Integer demandBeyondAssignee
//
//    /**
//     * 针对demandBeyondAssignee==2的情况
//     */
//
//    @Embedded(TaskFields.BEYOND_ASSIGNEE)
//    private Map<String, List<String>> beyondAssignee
//
//    /**
//     * 会签时：
//     * allPassType = 1 表示所有人会签人员都要进行一次操作，即使第一个人执行了reject，后面的人也要执行
//     * allPassType = 0 如果第一个人执行了reject，则流程就终止，会签的其他人不需要执行。
//     */
//    @Property(TaskFields.ALL_PASS_TYPE)
//    private Integer allPassType
//
//
//    /**
//     * 1:是外部节点
//     * 0或空：不是外部节点
//     */
//
//    @Property(TaskFields.EXTERNAL_APPLY_TASK)
//    private Integer externalApplyTask
//
//
//    /**
//     * 1:表示可以指定下一个审批人
//     * 0或空：不可以
//     */
//
//
//    @Property(TaskFields.ASSIGN_NEXT_TASK)
//    private Integer assignNextTask
//
//
//    /**
//     * 1:表示该节点的审批人由上一节点指定
//     * 0或空：不需要
//     */
//
//
//    @Property(TaskFields.CANDIDATE_BY_PRE_TASK)
//    private Integer candidateByPreTask
//
//}
