package com.fxiaoke.paas.console.util.workflow

import com.facishare.paas.org.pojo.OrgContext
import com.fxiaoke.paas.console.bean.workflow.BatchQueryWorkflowInstanceArg
import com.fxiaoke.paas.console.bean.workflow.OrderArg
import com.fxiaoke.paas.console.bean.workflow.QueryWorkflowArg
import com.fxiaoke.paas.console.exception.workflow.WorkflowAdminException
import com.github.mybatis.datatables.DataColumn
import com.github.mybatis.datatables.DataRequest
import com.github.mybatis.datatables.OrderColumn
import groovy.util.logging.Slf4j
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils

import java.text.SimpleDateFormat

/**
 * Created by yangxw on 2018/3/5.
 */
@Slf4j
class QueryArgUtil {

  private static final String TIME_FORMAT = "yyyy/MM/dd HH:mm:ss";

  /**
   * 根据columnList获取查询流程列表的查询条件，即一个BatchQueryWorkflowArg对象
   * @param req
   * @return
   */
  static QueryWorkflowArg getBatchQueryWorkflowArg(OrgContext context,Map<String, String> param) {

    QueryWorkflowArg arg = new QueryWorkflowArg()
    String value
    int flag = 0
    for (Map.Entry<String, String> entry : param.entrySet()) {
      value = entry.getValue()
      switch (entry.getKey()) {
        case WorkflowFields.NAME:
          flag++
          arg.setName(value)
          break
        case WorkflowFields.DESC:
          flag++
          arg.setDescription(value)
          break
        case WorkflowFields.TENANT_ID:
          flag++
          arg.setTenantId(value)
          break
        case WorkflowFields.APP_ID:
          flag++
          arg.setAppId(this.analyzeFiled(value))
          break
        case WorkflowFields.FLOW_TYPE:
          if ("all".equals(value)) {
            arg.setFlowType(null)
            break
          }
          arg.setFlowType(value)
          break
        case WorkflowFields.ENABLE:
          arg.setEnable(this.analyzeEnable(value))
          break
        case "SRC_IDOrName":
          arg.setSourceWorkflowId(value)
          break
        default:
          break
      }
    }
    if (flag == 0) {
      return null
    }
    arg.setStart(Integer.parseInt(param.get("start")))
    arg.setLength(Integer.parseInt(param.get("length")))
//    arg.setOrderArgList(getOrderArgList(req))
    return arg

  }

  def static getBatchQueryWorkflowInstanceArg(OrgContext context, DataRequest req) {

    BatchQueryWorkflowInstanceArg arg = new BatchQueryWorkflowInstanceArg();
    if (null == req || CollectionUtils.isEmpty(req.getColumns())) {
      return arg;
    }
    String value;
    for (DataColumn dataColumn : req.getColumns()) {
      if (null != dataColumn.getSearch()) {
        value = dataColumn.getSearch().getValue();
        switch (dataColumn.getData()) {
          case WorkflowFields.TENANT_ID:
            arg.tenantId = value;
            break;
          case WorkflowFields.APPLICANT_ID:
            arg.setApplicantId(value);
            break;
          case WorkflowFields.APP_NAME:
            arg.setAppId(this.analyzeFiled(value));
            break;
//                    case WorkflowFields.ENTITY_NAME:
//                        arg.setEntityId(this.analyzeFiled(value));
//                        break;
          case WorkflowFields.STATE:
            arg.setState(this.analyzeFiled(value));
            break;
          case WorkflowFields.WORKFLOW_ID:
            arg.setWorkflowId(value);
            break;
//                    case "applicantAccountAndDept":
//                        arg.setApplicantIdList(dubboServiceUtil.getUserIdListByUserAccount(context, value));
//                        break;
          case "startTime":
            arg.setStartTime(this.timeConvert(value));
            break;
          case "endTime":
            arg.setEndTime(this.timeConvert(value));
            break;
          default:
            break;
        }
      }
    }
    arg.setStart(req.getStart());
    arg.setLength(req.getLength());
    arg.setOrderArgList(this.getOrderArgList(req));
    return arg;

  }


  static getOrderArgList(DataRequest req) {

    List<DataColumn> dataColumnList = req.getColumns()
    List<OrderColumn> orderColumnList = req.getOrders()
    List<OrderArg> orderArgList = new ArrayList<>()
    if (CollectionUtils.isEmpty(orderColumnList) || CollectionUtils.isEmpty(dataColumnList)) {
      return orderArgList
    }
    for (OrderColumn orderColumn : orderColumnList) {
      String field = dataColumnList.get(orderColumn.getColumn()).getData()
      switch (field) {
        case WorkflowFields.APP_NAME:
          field = WorkflowFields.APP_ID
          break
        case WorkflowFields.ENTITY_NAME:
          field = WorkflowFields.ENTITY_ID
          break
        case WorkflowFields.FLOW_TYPE:
          field = WorkflowFields.TYPE
          break
        default:
          break
      }
      String order = "desc".equalsIgnoreCase(orderColumn.getDir()) ? "-" : ""
      orderArgList.add(new OrderArg(WorkflowFields.CREATE_TIME, order))
    }
    return orderArgList
  }

  static analyzeEnable(String value) {

    if ("started".equals(value)) {
      return true
    } else if ("stopped".equals(value)) {
      return false
    } else if ("all".equals(value)) {
      return null
    } else {
      throw new WorkflowAdminException("enable = " + value + "不是一个有效的查询启用、禁用流程的参数")
    }
  }

  static analyzeFiled(String value) {

    if ("all".equals(value)) {
      return null
    } else {
      return value
    }
  }

  static Long timeConvert(String time) {

    if (StringUtils.isBlank(time)) {
      return null;
    }
    String[] array = time.split(":");
    String dateString = array[0] + "/" + array[1] + "/1 0:0:0";
    SimpleDateFormat sdf = new SimpleDateFormat(TIME_FORMAT);
    Date date;
    try {
      date = sdf.parse(dateString);
    } catch (Exception e) {
      log.info("parameter = {} should have the format " + TIME_FORMAT, e);
      throw new WorkflowAdminException("参数 = " + dateString + "应该有如下格式：" + TIME_FORMAT, e);
    }
    return date.getTime();

  }

}
