<#assign title="数据共享缓存查询">
<#assign active_nav="data_permission_share_cache">
<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
<style>
    .doSearch {
        width: 8%;
        margin-left: 20px
    }

    #dataPermission_table {
        margin-top: 10px;
        width: 97%;
        margin-left: 1.5%;
    }

    input {
        margin-left: 10px;
    }

    #dataTable2 th {
        vertical-align: middle;
        align-items: center;
    }

    #dataTable2 td {
        vertical-align: middle;
        align-items: center
    }

    .table > thead:first-child > tr:first-child > th {
        text-align: center;
        vertical-align: middle;
    }

    .table > tbody > tr > td {
        text-align: center;
    }

    input {
        width: 10%;
        height: 34px;
        line-height: 34px;
        box-sizing: border-box;
        border-radius: 4px;
        border: 1px solid #c8cccf;
        color: #6a6f77;
        -web-kit-appearance: none;
        -moz-appearance: none;
        outline: 0;
        text-decoration: none;
    }
</style>
</#assign>
<#assign bodyContent>
    <#assign breadcrumbContent>
    <section class="content-header">
        <h1>数据共享缓存查询</h1>
        <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i>查询</a></li>
        </ol>
    </section>
    </#assign>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title"></h3>
                </div>
                <form class="form-horizontal" action="${ctx}/biz/add" method="post" id="myForm" role="form"
                      data-toggle="validator">
                    <div class="container-fluid">
                        <div id="permission_share_cache">
                            <input placeholder="企业_ID"/>
                            <input placeholder="App_ID"/>
                            <input placeholder="对象实体_ID"/>
                            <input placeholder="共享规则_ID"/>
                            <input placeholder="共享用户_ID"/>
                            <input placeholder="接收方用户_ID"/>
                            <button type="button" class="doSearch btn btn-primary">Send</button>
                        </div>
                        <div id="dataPermission_table">
                            <table class="table table-striped table-bordered table-condensed dataTable no-footer"
                                   id="dataTable2">
                                <thead>
                                <tr>
                                    <th>企业ID</th>
                                    <th>App_ID</th>
                                    <th>对象实体_ID</th>
                                    <th>共享规则ID</th>
                                    <th>共享者ID</th>
                                    <th>共享者姓名</th>
                                    <th>接收者ID</th>
                                    <th>接收者类别</th>
                                    <th>接收者名称</th>
                                    <th>权限</th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
            </div>
            </form>
        </div>
    </div>
    </div>
</section>
</#assign>
<#assign scriptContent>
<script>
    $(document).ready(function () {
        var bootstrapDom = "<'row'<'col-sm-6'l>>" +
                "<'row'<'col-sm-12'tr>>" +
                "<'row'<'col-sm-5'i><'col-sm-7'p>>";
        var table = $("#dataTable2").DataTable({
            "dom": bootstrapDom,
            "processing": false,
            "serverSide": true,
            "search": {
                "regex": true
            },
            "ajax": "${CONTEXT_PATH}/share/cache/query?dataObject=",
            "columnDefs": [
                {"width": "10%", "targets": 0},
                {"width": "10%", "targets": 1},
                {"width": "10%", "targets": 2},
                {"width": "10%", "targets": 3},
                {"width": "10%", "targets": 4},
                {"width": "10%", "targets": 5},
                {"width": "10%", "targets": 6},
                {"width": "10%", "targets": 7},
                {"width": "10%", "targets": 8},
                {"width": "10%", "targets": 9}
            ],
            columns: [
                {data: "tenantId"},
                {data: "appId"},
                {data: "entityId"},
                {
                    data: 'entityShareId', render: function (data, type, row, meta) {
                    if (type == 'display') {
                        return "<a href=\"${CONTEXT_PATH}/workflow/share/query?entityShareId=" + data + "&AppId=" + row.appId + "&tenantId=" + row.tenantId + "\">" + data + "</a>";
                    } else {
                        return data;
                    }
                }
                },
                {data: "shareUser"},
                {data: "shareUserName"},
                {data: "receiveUser"},
                {
                    data: 'receiveType', render: function (data, type, row, meta) {
                    if (data != "用户" && data != "角色") {
                        if (data == "用户组") {
                            return "<a href=\"${CONTEXT_PATH}/workflow/data/permission/group/query?groupId=" + row.receiveUser + "&AppId=" + row.appId + "&tenantId=" + row.tenantId + "\">" + data + "</a>";
                        }
                        if (data == "部门") {
                            return "<a href=\"${CONTEXT_PATH}/workflow/data/permission/dept/query?deptId=" + row.receiveUser + "&AppId=" + row.appId + "&tenantId=" + row.tenantId + "\">" + data + "</a>";
                        }
                    } else {
                        return data;
                    }
                }
                },
                {data: "receiveUserName"},
                {data: "permission"}
            ],
            "iDisplayLength": 25,
            "sPaginationType": "full_numbers",
            "language": {
                "processing": "加载中...",
                "lengthMenu": "显示 _MENU_ 项结果",
                "InfoEmpty": "显示第 0 至 0 项结果，共 0 项",
                "zeroRecords": "没有匹配结果",
                "emptyTable": "没有数据",
                "info": "",
                "infoEmpty": "",
                "infoFiltered": "",
                "infoPostFix": "",
                "search": "搜索:",
                "url": "",
                "paginate": {
                    "first": "首页",
                    "previous": "上页",
                    "next": "下页",
                    "last": "末页"
                }
            }
        });


        $(".doSearch").on("click", function () {
            var tenantId = $("#permission_share_cache input").eq(0).val();
            var AppId = $("#permission_share_cache input").eq(1).val();
            var entityId = $("#permission_share_cache input").eq(2).val();
            var entityShareId = $("#permission_share_cache input").eq(3).val();
            var shareUser = $("#permission_share_cache input").eq(4).val();
            var receiveUser = $("#permission_share_cache input").eq(5).val();
            if (tenantId == "") {
                alert("企业ID不可为空!");
                return false;
            }
            if (AppId == "") {
                alert("AppId不可为空");
                return false;
            }
            if (entityId == "") {
                alert("对象实体_ID不可为空");
                return false;
            }
            var dataObject = {
             "tenantId":tenantId,
             "AppId":AppId,
             "entityId":entityId,
             "entityShareId":entityShareId,
             "shareUser":shareUser,
             "receiveUser":receiveUser,
            }
            table.ajax.url("${CONTEXT_PATH}/share/cache/query?dataObject=" + encodeURIComponent(JSON.stringify(dataObject))).load();
        });
    });
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
