<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
<link type="text/css" href="//static.foneshare.cn/oss/datatables-checkboxes/dataTables.checkboxes.css" rel="stylesheet"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
    <h1>映射规则</h1>
    <ol class="breadcrumb">
        <li><a href="../"><i class="fa fa-dashboard"></i>主页</a></li>
        <li><a href=""><i class="fa fa-dashboard"></i>映射规则查询</a></li>
    </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title"></h3>
                </div>
                <form class="form-horizontal" action="" method="post" id="myForm" role="form"
                      data-toggle="validator">
                    <div class="box-body">
                        <div class="form-group">
                            <label for="biz" class="col-sm-1 control-label">企业ID</label>
                            <div class="col-sm-2" style="padding-right: 3%">
                                <input type="text" style="border-radius:5px;" class="form-control short-input" id="tenantId" name="tenantId"
                                       value="" placeholder="企业ID" required>
                                <div id="nameMsg" class="help-block with-errors"></div>
                            </div>
                            <div class="col-sm-2">
                                <button type="button" id="findSub" class="btn btn-primary" disabled>查询</button>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="box box-info">
                    <div class="box-body">
                        <table id="datatable" class="table table-hover table-bordered" cellpadding="0" width="100%">
                            <thead>
                            <tr>
                                <th>规则名称</th>
                                <th>原始描述名称</th>
                                <th>目标描述名称</th>
                                <th>动作</th>
                                <th>是否删除</th>
                                <th>包</th>
                                <th>规则API名称</th>
                                <th>最后修改时间</th>
                                <th>详情</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<#--规则详情-->
<div class="modal fade" id="ruleInfoModal" tabindex="-1" role="dialog" aria-labelledby="infoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                        class="sr-only">Close</span></button>
                <h4 class="modal-title" id="stopModalLabel">映射规则详情</h4>
            </div>
            <div class="modal-body">
                <pre id="ruleInfo" style="height: 400px">
                </pre>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script>
    $(document).ready(function () {
        var table = $("#datatable").DataTable({
//            "deferRender": true,
            "processing": true,
            "ajax": "",
            "columnDefs": [
                {
                    "targets": 8,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return "<button value='" + data + "' onclick='theRuleInfo(value)' class='btn btn-info btn-xs'>详情</button>";
                        }
                        return data;
                    }
                }
            ],
            "language": {
                "url": "${ctx}/static/js/datatables-zh_CN.json"
            },
            "mark": true,
            "order": [[7, 'desc']]
        });

//        查询
        $('#findSub').on('click', function () {
            var tenantId = $('#tenantId').val();
            if (tenantId !== null && tenantId !== "") {
                table.ajax.url("${ctx}/metadata/rule/find-rule?id=" + tenantId).load();
            }
        });

        /**
         * 检测input变化并改变按钮状态
         */
        $('#tenantId').bind("input propertychange", function () {
            var tenantIdValue = $('#tenantId').val();
            if (tenantIdValue === null || tenantIdValue === "") {
                $('#findSub').attr("disabled", "disabled");
            } else {
                $('#findSub').removeAttr("disabled");
            }
        });

    });

    function theRuleInfo(ruleId) {
        var tenantId = $('#tenantId').val();
        $.getJSON("${CONTEXT_PATH}/metadata/rule/find-info", {
            ruleId: ruleId,
            tenantId: tenantId
        }, function (ruleInfo) {
            $('#ruleInfo').JSONView(ruleInfo.ruleMap, {
                collapsed: false,
                nl2br: true,
                recursive_collapser: true
            });
            $('#ruleInfoModal').modal('show');
        });
    }
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
