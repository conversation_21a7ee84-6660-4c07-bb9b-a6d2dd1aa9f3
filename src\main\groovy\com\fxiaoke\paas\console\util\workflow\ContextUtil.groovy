package com.fxiaoke.paas.console.util.workflow

import com.facishare.paas.org.pojo.OrgContext
import com.facishare.paas.workflow.common.pojo.WorkflowContext
import com.facishare.paas.auth.model.AuthContext;

/**
 * Created by yangxw on 2018/3/5.
 */
class ContextUtil {
    private ContextUtil() {

    }

    def static  convertToOrgContext(WorkflowContext context) {

        OrgContext orgContext = new OrgContext();
        if (null == context) {
            return orgContext;
        }
        orgContext.setAppId(context.getAppId());
        orgContext.setUserId(context.getUserId());
        orgContext.setTenantId(context.getTenantId());
        return orgContext;

    }

    def static  convertToAuthContext(WorkflowContext context) {

        AuthContext authContext = new AuthContext();
        if (null == context) {
            return authContext;
        }
        authContext.setAppId(context.getAppId());
        authContext.setTenantId(context.getTenantId());
        authContext.setUserId(context.getUserId());
        return authContext;

    }
}
