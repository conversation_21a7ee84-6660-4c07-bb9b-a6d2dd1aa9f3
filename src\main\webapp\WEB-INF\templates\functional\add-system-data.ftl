<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>预制功能权限系统库数据</h1>
        <ol class="breadcrumb">
            <li><a href="../"><i class="fa fa-dashboard"></i>任务主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i></a></li>
        </ol>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="alert alert-info" role="alert">
                参考wiki
                <a href="http://wiki.firstshare.cn/pages/viewpage.action?pageId=128628026" target="_blank">http://wiki.firstshare.cn/pages/viewpage.action?pageId=128628026</a>
            </div>
            <div id="warningInfo" class="alert alert-warning hide">
                <span id="closeInfo" href="#" class="close">&times;</span>
                <strong id="hitInfo"></strong>
            </div>
            <div id="successInfo" class="alert alert-success hide alert-dismissible" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div class="col-md-12">
                <div class="box box-info row">
                    <div class="box-header with-border">
                        <h3 class="box-title">预制功能func</h3>
                        <div class="row">
                            <div class="box-body col-sm-6">
                                <label for="func"></label><textarea id="func" name="func" class="form-control text-muted" style="height: 250px" placeholder="输入正确格式的JsonArray"></textarea>
                            </div>
                        </div>
                        <div class="row">
                            <div class="box-footer col-sm-offset-5">
                                <button type="button" id="addFunc" class="btn btn-primary" disabled>添加功能</button>
                            </div>
                        </div>

                        <h3 class="box-title">预制角色功能关系funcAccess</h3>
                        <div class="row">
                            <div class="box-body col-sm-6">
                                <label for="funcAccess"></label><textarea id="funcAccess" name="funcAccess" class="form-control text-muted" style="height: 250px" placeholder="输入正确格式的JsonArray"></textarea>
                            </div>
                        </div>
                        <div class="row">
                            <div class="box-footer col-sm-offset-5">
                                <button type="button" id="addFuncAccess" class="btn btn-primary" disabled>添加角色功能关系</button>
                            </div>
                        </div>

                        <div class="alert alert-info" role="alert">
                            参考wiki
                            <a href="http://wiki.firstshare.cn/pages/viewpage.action?pageId=139309824" target="_blank">http://wiki.firstshare.cn/pages/viewpage.action?pageId=139309824</a>
                        </div>
                        <h3 class="box-title">预制菜单</h3>
                        <div class="row">
                            <div class="box-body col-sm-6">
                                <label for="menu"></label><textarea id="menu" name="menu" class="form-control text-muted" style="height: 250px" placeholder="输入正确格式的JsonArray"></textarea>
                            </div>
                        </div>
                        <div class="row">
                            <div class="box-footer col-sm-offset-5">
                                <button type="button" id="addMenu" class="btn btn-primary" disabled>添加菜单</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#func').bind("input propertychange", function () {
                let func = $('#func').val();
                if (func === null || func === "") {
                    $('#addFunc').attr("disabled", "disabled");
                } else {
                    $('#addFunc').removeAttr("disabled");
                }
            });
            $('#funcAccess').bind("input propertychange", function () {
                let funcAccess = $('#funcAccess').val();
                if (funcAccess === null || funcAccess === "") {
                    $('#addFuncAccess').attr("disabled", "disabled");
                } else {
                    $('#addFuncAccess').removeAttr("disabled");
                }
            });
            $('#menu').bind("input propertychange", function () {
                let menu = $('#menu').val();
                if (menu === null || menu === "") {
                    $('#addMenu').attr("disabled", "disabled");
                } else {
                    $('#addMenu').removeAttr("disabled");
                }
            });

            $('#addFunc').on('click', function () {
                $('#warningInfo').addClass('hide');
                let func = $('#func').val();
                if (func === "" || func === "") {
                    $('#warningInfo').removeClass('hide');
                    $('#hitInfo').html('关键字段不能为空！');
                    return;
                }

                $.ajax({
                    url: "${CONTEXT_PATH}/paas-auth/system/add-func",
                    contentType: "application/json",
                    data: func,
                    dataType: "json",
                    type: "POST",
                    traditional: true,
                    success: function (result) {
                        if (result.code === 200) {
                            $('#successInfo').removeClass('hide')
                            $('#successInfo').html("添加功能成功")
                        } else {
                            $('#warningInfo').removeClass('hide');
                            $('#hitInfo').html(result.error);
                        }
                    }
                });
            });

            $('#addFuncAccess').on('click', function () {
                $('#warningInfo').addClass('hide');
                let funcAccess = $('#funcAccess').val();
                if (funcAccess === "" || funcAccess === "") {
                    $('#warningInfo').removeClass('hide');
                    $('#hitInfo').html('关键字段不能为空！');
                    return;
                }

                $.ajax({
                    url: "${CONTEXT_PATH}/paas-auth/system/add-funcAccess",
                    contentType: "application/json",
                    data: funcAccess,
                    dataType: "json",
                    type: "POST",
                    traditional: true,
                    success: function (result) {
                        if (result.code === 200) {
                            $('#successInfo').removeClass('hide')
                            $('#successInfo').html("添加角色功能关系成功")
                        } else {
                            $('#warningInfo').removeClass('hide');
                            $('#hitInfo').html(result.error);
                        }
                    }
                });
            });

            $('#addMenu').on('click', function () {
                $('#warningInfo').addClass('hide');
                let menu = $('#menu').val();
                if (menu === "" || menu === "") {
                    $('#warningInfo').removeClass('hide');
                    $('#hitInfo').html('关键字段不能为空！');
                    return;
                }
                console.log("dddd")

                $.ajax({
                    url: "${CONTEXT_PATH}/paas-auth/system/add-menu",
                    contentType: "application/json",
                    data: menu,
                    dataType: "json",
                    type: "POST",
                    traditional: true,
                    success: function (result) {
                        if (result.code === 200) {
                            $('#successInfo').removeClass('hide')
                            $('#successInfo').html("添加菜单成功")
                        } else {
                            $('#warningInfo').removeClass('hide');
                            $('#hitInfo').html(result.error);
                        }
                    }
                });
            });
        });
        /**
         * 信息提示栏关闭
         */
        $('#closeInfo').on('click', function () {
            $('#warningInfo').addClass('hide');
        });

    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
