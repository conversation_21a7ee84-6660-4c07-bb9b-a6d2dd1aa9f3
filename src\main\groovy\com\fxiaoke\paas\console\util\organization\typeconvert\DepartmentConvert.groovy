package com.fxiaoke.paas.console.util.organization.typeconvert

import com.fxiaoke.paas.console.bean.organization.object.DepartmentObject
import com.fxiaoke.paas.console.entity.organization.DepartmentEntity

import java.text.SimpleDateFormat

/**
 * Created by wangxing on 2018/03/06
 * 数据转换工具类
 */
class DepartmentConvert {

  static DepartmentObject departmentEntityConvertDepartmentObject(DepartmentEntity departmentEntity) {
    DepartmentObject departmentObject = new DepartmentObject()
    departmentObject.setId(departmentEntity.getId())
    departmentObject.setDeptId(departmentEntity.getDeptId())
    departmentObject.setName(departmentEntity.getName())
    departmentObject.setText(departmentEntity.getName() + "(" + departmentEntity.getDeptId() + ")")
    if (departmentEntity.getManagerId() == null) {
      departmentObject.setManagerId("")
    } else {
      departmentObject.setManagerId(departmentEntity.getManagerId())
    }
    if (departmentEntity.getStatus() != null) {
      if (departmentEntity.getStatus() == 0) {
        departmentObject.setStatus("启用")
      } else if (departmentEntity.getStatus() == 1) {
        departmentObject.setStatus("停用")
      }
    }
    if (departmentEntity.getCreateBy() == null) {
      departmentObject.setCreateBy("")
    } else {
      departmentObject.setCreateBy(departmentEntity.getCreateBy())
    }

    if (departmentEntity.getCreateTime() != null) {
      departmentObject.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(departmentEntity.getCreateTime())))
    } else {
      departmentObject.setCreateTime("")
    }
    departmentObject.setTenantId(departmentEntity.getTenantId())
    departmentObject.setParentId(departmentEntity.getParentId())
    if (departmentEntity.getLastModifiedBy() == null) {
      departmentObject.setLastModifiedBy("")
    } else {
      departmentObject.setLastModifiedBy(departmentEntity.getLastModifiedBy())
    }
    if (departmentEntity.getLastModifiedTime() != null) {
      departmentObject.setLastModifiedTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(departmentEntity.getLastModifiedTime())))
    } else {
      departmentObject.setLastModifiedTime("")
    }
    departmentObject.setIsDeleted(departmentEntity.getIsDeleted())
    departmentObject.setDescription(departmentEntity.getDescription())
    return departmentObject
  }
}
