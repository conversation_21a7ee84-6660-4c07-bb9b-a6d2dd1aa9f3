<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
  <settings>
    <setting name="cacheEnabled" value="false"/>
    <setting name="lazyLoadingEnabled" value="true"/>
    <setting name="multipleResultSetsEnabled" value="true"/>
    <setting name="useColumnLabel" value="true"/>
    <setting name="useGeneratedKeys" value="false"/>
    <setting name="autoMappingBehavior" value="PARTIAL"/>
    <setting name="defaultExecutorType" value="SIMPLE"/>
    <setting name="defaultStatementTimeout" value="25"/>
    <setting name="safeRowBoundsEnabled" value="false"/>
    <setting name="mapUnderscoreToCamelCase" value="true"/>
    <setting name="localCacheScope" value="SESSION"/>
    <setting name="jdbcTypeForNull" value="OTHER"/>
    <setting name="lazyLoadTriggerMethods" value="equals,clone,hashCode,toString"/>
  </settings>
  <typeHandlers>
    <package name="com.github.mybatis.handler.list"/>
  </typeHandlers>
  <!--指定数据库类型-->
  <plugins>
    <plugin interceptor="com.github.mybatis.interceptor.PaginationAutoMapInterceptor">
      <property name="dialect" value="postgresql"/>
    </plugin>
    <plugin interceptor="com.github.mybatis.interceptor.InjectSchemaInterceptor">
      <property name="dialect" value="postgresql"/>
      <property name="tenantPolicyBeanName" value="consoleTenantPolicy"/>
    </plugin>
  </plugins>
</configuration>
