<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
  <h1>清除缓存</h1>
  <ol class="breadcrumb">
    <li><a href="${ctx}"><i class="fa fa-dashboard"></i>主页</a></li>
    <li><a href=""><i class="fa fa-dashboard"></i>清除缓存</a></li>
  </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info row">
        <div class="box-header with-border">
        </div>
        <form class="form-horizontal" action="" method="post" id="infoFrom" role="form" data-toggle="validator">
          <div class="box-body col-xs-6">
            <div class="form-group">
              <label for="tenantId" class="col-sm-4 control-label">tenantIds</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="tenantIds" placeholder="企业ID(必填)" required>
              </div>
            </div>
            <div class="col-sm-offset-3">
              <button type="button" id="purgeCache" class="btn btn-primary" >清理</button>
              <input id="cancel_btn" class="btn" type="button" value="返回" onclick="history.back()"/>
            </div>
          </div>
          <div class="box-body col-xs-6">
            <h4>Result <i class="fa fa-hand-o-down"></i></h4>
            <pre id="dataInfo" style="">
                        </pre>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<#--表单验证插件-->
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script>
  $('#purgeCache').on('click', function () {
    $('#dataInfo').text("");
    $('#purgeCache').attr("disabled", "disabled");

    $.post("${CONTEXT_PATH}/metadata/purge/purgeCache", {
      tenantIds: $('#tenantIds').val()
    }, function (data) {
      if (data !== "") {
        $('#purgeCache').removeAttr("disabled");
        $('#dataInfo').JSONView(data, {
          collapsed: false,
          nl2br: true,
          recursive_collapser: true
        });
      }
    });

  });
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
