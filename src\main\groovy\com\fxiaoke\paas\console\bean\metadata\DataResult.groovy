package com.fxiaoke.paas.console.bean.metadata

import groovy.transform.ToString
import groovy.transform.builder.Builder
import lombok.Getter
import lombok.Setter

/**
 * <AUTHOR>
 * @date 2018/3/19
 */
@ToString
@Getter
@Setter
@Builder
class DataResult {
  /**
   * 数据类型（通表mtData，专表specialData）
   */
  String tableType
  /**
   * 当为专表时表名
   */
  String tableName
  List<Map<String, Object>> dataList
}
