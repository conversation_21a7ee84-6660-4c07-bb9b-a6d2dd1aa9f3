package com.fxiaoke.paas.console.service.hamster;

import com.facishare.hamster.constant.HamsterConstant;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.paas.console.entity.hamster.HamsterTemplate;
import com.fxiaoke.paas.console.entity.hamster.HamsterTemplateItem;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class HamsterTemplateService {

  public final String defaultTemplateId = "template_default_id";

  public final String exclusiveCloudTemplateId = "exclusive_cloud_template_id";

  public final String defaultSchemaToSchemaTemplateId = "template_default_schema_id";

  public final String defaultCloudSchemaToSchemaTemplateId = "template_cloud_default_schema_id";

  public final String defaultTemplateIdWithCHForBi = "default_tmp_id_with_ch_for_bi";
  public List<HamsterTemplate> getTemplates() {
    List<HamsterTemplate> result = Lists.newArrayList();
    result.add(HamsterTemplate.builder().id(defaultTemplateId).name("标准schema迁移").build());
    result.add(HamsterTemplate.builder().id(exclusiveCloudTemplateId).name("专属云schema迁移").build());
    result.add(HamsterTemplate.builder().id(defaultSchemaToSchemaTemplateId).name("纷享云schema到schema迁移").build());
    result.add(HamsterTemplate.builder().id(defaultCloudSchemaToSchemaTemplateId).name("专属云schema到schema迁移").build());
    result.add(HamsterTemplate.builder().id(defaultTemplateIdWithCHForBi).name("标准schema迁移（含CH）").build());

    return result;
  }

  public List<HamsterTemplateItem> getHamsterTemplateIds(String templateId) {
    //TODO:TO BE IMPLEMENTED.
    List<HamsterTemplateItem> result = Lists.newArrayList();

    Integer normal = Integer.valueOf(Integer.toBinaryString(
      HamsterConstant.TaskItemOperation.EXECUTE + HamsterConstant.TaskItemOperation.ROLLBACK + HamsterConstant.TaskItemOperation.PAUSE +
        HamsterConstant.TaskItemOperation.RESUME));
    Integer skip = Integer.valueOf(Integer.toBinaryString(
      HamsterConstant.TaskItemOperation.EXECUTE + HamsterConstant.TaskItemOperation.ROLLBACK + HamsterConstant.TaskItemOperation.PAUSE +
        HamsterConstant.TaskItemOperation.RESUME + HamsterConstant.TaskItemOperation.SKIP));

    getHamsterTemplateItems(result, templateId, normal, skip);
    return result;

  }

  private void getHamsterTemplateItems(List<HamsterTemplateItem> result, String templateId, Integer normal, Integer skip) {
    int order = 0;
    if (defaultSchemaToSchemaTemplateId.equals(templateId)){
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("创建路由资源(回滚但不执行)").action("RouteResourceCreate").templateId(templateId).order(++order).actionType(1).operation(normal).build());
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("刷库平台添加PG(回滚但不执行)").action("PodResourceCreate").templateId(templateId).order(++order).actionType(1).operation(skip).build());
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("停写").action("FsGrayPodStart").templateId(templateId).order(++order).actionType(2).operation(normal).build());
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("切路由").action("RouteSwitch").templateId(templateId).order(++order).actionType(2).operation(normal).build());
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("定义新库产生的oplog发送到哪个topic(回滚但不执行)").action("CmsPaasPgList").templateId(templateId).order(++order).actionType(1).operation(normal).build());
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("新库发出oplog").action("CmsFsPaasPgScanner").templateId(templateId).order(++order).actionType(1).operation(normal).build());
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("恢复写入(回滚但不执行)").action("FsGrayPodEnd").templateId(templateId).order(++order).actionType(1).operation(normal).build());
      return;
    }
    if (defaultCloudSchemaToSchemaTemplateId.equals(templateId)){
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("创建路由资源(回滚但不执行)").action("RouteResourceCreate").templateId(templateId).order(++order).actionType(1).operation(normal).build());
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("定义PAAS，BI新库和老库的影射关系").action("CmsFsPaasCopierUrl").templateId(templateId).order(++order).actionType(1).operation(normal).build());
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("停写").action("FsGrayPodStart").templateId(templateId).order(++order).actionType(2).operation(normal).build());
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("切路由").action("RouteSwitch").templateId(templateId).order(++order).actionType(2).operation(normal).build());
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("定义新库产生的oplog发送到哪个topic(回滚但不执行)").action("CmsPaasPgList").templateId(templateId).order(++order).actionType(1).operation(normal).build());
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("新库发出oplog").action("CmsFsPaasPgScanner").templateId(templateId).order(++order).actionType(1).operation(normal).build());
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("恢复写入(回滚但不执行)").action("FsGrayPodEnd").templateId(templateId).order(++order).actionType(1).operation(normal).build());
      return;
    }
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("创建路由资源(回滚但不执行)").action("RouteResourceCreate").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    if (defaultTemplateId.equals(templateId)) {
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("刷库平台添加PG(回滚但不执行)").action("PodResourceCreate").templateId(templateId).order(++order).actionType(1).operation(skip).build());
    }
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("定义PAAS，BI新库和老库的影射关系").action("CmsFsPaasCopierUrl").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("定义新库产生的oplog发送到哪个topic(回滚但不执行)").action("CmsPaasPgList").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("新库发出oplog").action("CmsFsPaasPgScanner").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("初始化schema").action("SchemaInit").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    if(defaultTemplateIdWithCHForBi.equals(templateId)){
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("创建 CH db表").action("BIInitCHDB").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    }
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("增量灰度").action("CmsFsGrayCopierStart").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    if(defaultTemplateIdWithCHForBi.equals(templateId)){
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("同步pg数据到CH db").action("BICopyPgToCH").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    }
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("开启原始bi库特定表的oplog").action("BITableTriggerEnable").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("全量数据迁移").action("DataMigration").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("开始同步中间表").action("QueryFromMidTableStart").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("数据验证").action("DataValidation").templateId(templateId).order(++order).actionType(1).operation(skip).build());
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("停写").action("FsGrayPodStart").templateId(templateId).order(++order).actionType(2).operation(normal).build());
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("消息积压验证").action("MessageBacklogValidation").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("切路由").action("RouteSwitch").templateId(templateId).order(++order).actionType(2).operation(normal).build());
    if (defaultTemplateId.equals(templateId)) {
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("路由切换验证").action("RouteSwitchValidation").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    } else if (exclusiveCloudTemplateId.equals(templateId)) {
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("路由切换验证").action("RouteSwitchValidation").templateId(templateId).order(++order).actionType(1).operation(skip).build());
    }
    if(defaultTemplateIdWithCHForBi.equals(templateId)){
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("修改CH DB 路由").action("BICHRouteSwitch").templateId(templateId).order(++order).actionType(1).operation(normal).build());
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("重跑统计图，验证计算状态").action("BIReBuildTopology").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    }
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("关闭同步字段灰度(回滚但不执行)").action("CmsFsSupplementColumnEnd").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("恢复写入(回滚但不执行)").action("FsGrayPodEnd").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("禁用原始bi库特定表的oplog").action("BITableTriggerDisable").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("数据验证").action("DataValidation").templateId(templateId).order(++order).actionType(1).operation(skip).build());
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("数据比对").action("PaasBiCopier").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("停止回写(回滚但不执行)").action("CmsFsGrayCopierEnd").templateId(templateId).order(++order).actionType(2).operation(normal).build());
    if(defaultTemplateIdWithCHForBi.equals(templateId)){
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("数据校验").action("BIDataValidation").templateId(templateId).order(++order).actionType(1).operation(normal).build());
      result.add(HamsterTemplateItem.builder().id(IdGenerator.get()).name("删除原CH 历史数据").action("BICHDeleteOldData").templateId(templateId).order(++order).actionType(1).operation(normal).build());
    }
  }

}
