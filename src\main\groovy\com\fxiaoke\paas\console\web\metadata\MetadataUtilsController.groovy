package com.fxiaoke.paas.console.web.metadata

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.paas.console.service.OKHttpService
import com.github.autoconf.ConfigFactory
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.PostConstruct

/**
 * <AUTHOR>
 * @date 2019/9/16 4:34 PM
 */
@Controller
@Slf4j
@RequestMapping("/metadata/utils")
class MetadataUtilsController {
  @Autowired
  OKHttpService okHttpService

  private String metadataStaticUrl

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-console-url", { iConfig ->
      this.metadataStaticUrl = iConfig.get("metadata-static-url")
    })
  }


  @RequestMapping("/")
  String index(ModelMap model) {
    "metadata/utils"
  }


  @PostMapping(path = "/create", produces = "application/json;charset=utf-8")
  @ResponseBody
  def create(String tenantId, String apiName, String dbType) {
    if (StringUtils.isAnyBlank(tenantId, apiName)) {
      return ["code": 200, "info": "{\"message\":\"tenantId和apiName不能为空\"}"]
    }
    String jsonResult;
    if("pg".equals(dbType)) {
      jsonResult = okHttpService.getForm(String.format(this.metadataStaticUrl + "/paas/metadata/describe/getSql?tenantId=%s&describeApiName=%s", tenantId, apiName))
    } else if ("ch".equals(dbType)) {
      jsonResult = okHttpService.getForm(String.format(this.metadataStaticUrl + "/paas/metadata/describe/getSql/ch?tenantId=%s&describeApiName=%s", tenantId, apiName))
    } else {
      return ["code": 400, "info": "参数异常，没有对应 dbType"]
    }
    Map<String, String> map = Maps.newHashMap()
    map.put("sql", JSONObject.parseObject(jsonResult).getString("result"))
    return ["code": 200, "info": map]

  }

}
