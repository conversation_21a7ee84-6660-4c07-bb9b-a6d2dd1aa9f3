package com.fxiaoke.paas.console.service.metadata
import com.alibaba.fastjson.JSONObject
import com.fxiaoke.paas.console.service.OKHttpService
import com.github.autoconf.ConfigFactory
import com.google.common.collect.Sets
import groovy.util.logging.Slf4j
import lombok.NonNull
import okhttp3.Headers
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import java.lang.reflect.UndeclaredThrowableException

@Service
@Slf4j
class PurgeCacheService {
    @Autowired
    private OKHttpService okHttpService
    @Autowired
    private SqlQueryService sqlQueryService;

    private String purgeCacheUrl

    @PostConstruct
    void init() {
        ConfigFactory.getConfig("paas-console-base", { config ->
            purgeCacheUrl = config.get("fs-metadata-purgeCache")
        })
    }

    def purgeCacheByTenantIdS(@NonNull String tenantIds){
        // 隐蔽使用
        if(tenantIds.contains('u')) {
            purgeCacheForMemLimit(tenantIds)
            return ["info":"success"]
        }
        JSONObject params = new JSONObject()
        params.put("tenantIds",tenantIds.split(","))

        Headers headers = new Headers()
        headers.newBuilder().add("Content-Type","application/json")
                            .add("cache-control","no-cache")
                            .build()
        try {
            okHttpService.postJSON2(purgeCacheUrl, params, headers)
        } catch (UndeclaredThrowableException e) {
            if (e.getUndeclaredThrowable() instanceof SocketTimeoutException) {
                log.warn("请求超时")
                ["info":"处理中，请过会儿验证"]
            }
            log.warn("清理缓存异常", e.getUndeclaredThrowable())
            ["code":500,"erroMesage":"清理缓存异常," + e.getUndeclaredThrowable().getMessage()]
        }
    }

    def purgeCacheByFile(@NonNull String filePath){

    }

    def purgeCacheForMemLimit(String userIds) {
        userIds = userIds.replaceAll('u','');
        sqlQueryService.clearLimitRecord(userIds.split(','))
    }
}
