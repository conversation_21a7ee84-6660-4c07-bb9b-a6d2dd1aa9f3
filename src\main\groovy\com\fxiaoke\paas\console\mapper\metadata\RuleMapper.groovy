package com.fxiaoke.paas.console.mapper.metadata

import com.github.mybatis.mapper.IBatchMapper
import com.github.mybatis.mapper.ICrudMapper
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * Created on 2018/3/19.
 */
interface RuleMapper extends ICrudMapper, IBatchMapper, ITenant<RuleMapper> {

  /**
   * 根据企业ID查询映射规则
   * @param tenantId
   * @return
   */
  @Select("SELECT rule_id,rule_name,source_object_describe_api_name,target_object_describe_api_name,action,is_deleted,pkg,rule_api_name,last_modified_time FROM mt_object_mapping_rule WHERE tenant_id = #{tenantId}")
  List<Map<String, Object>> findRuleByTenantId(@Param("tenantId") String tenantId)

  /**
   * 根据id查询规则详情
   * @param ruleId
   * @return
   */
  @Select("SELECT * FROM mt_object_mapping_rule WHERE rule_id = #{ruleId} AND tenant_id = #{tenantId}")
  Map<String, Object> findRuleByRuleId(@Param("ruleId") String ruleId, @Param("tenantId") String tenantId)

}