package com.fxiaoke.paas.console.util.workflow

import com.facishare.paas.auth.model.RolePojo
import com.facishare.paas.org.pojo.DeptInfo
import com.facishare.paas.workflow.kernel.entity.ExpressionEntity
import com.fxiaoke.paas.console.bean.workflow.ExpressionObject
import groovy.util.logging.Slf4j
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired

/**
 * Created by yangxw on 2018/3/8.
 */
@Slf4j
class FieldEntityToFieldObject {


    def static List<ExpressionObject> entityListToExpressionObjectList(List<ExpressionEntity> expressionEntityList) {

        List<ExpressionObject> expressionObjectList = new ArrayList<>();
        if (CollectionUtils.isEmpty(expressionEntityList)) {
            return expressionObjectList;
        }
        for (ExpressionEntity expressionEntity:expressionEntityList) {
            expressionObjectList.add(entityToExpressionObject(expressionEntity));
        }
        return expressionObjectList;

    }

    static ExpressionObject entityToExpressionObject(ExpressionEntity expressionEntity) {

        if (null == expressionEntity) {
            return null;
        }
        ExpressionObject expressionObject = new ExpressionObject();
        expressionObject.setRowNo(expressionEntity.getRowNo());
        expressionObject.setFieldName(expressionEntity.getFieldName());
        expressionObject.setFieldType(expressionEntity.getFieldType());
        expressionObject.setFieldSrc(expressionEntity.getFieldSrc());
        expressionObject.setOperator(ToShowStringUtil.getOperator(expressionEntity.getOperator()));
        expressionObject.setValue(expressionEntity.getValue());
        return expressionObject;

    }
}
