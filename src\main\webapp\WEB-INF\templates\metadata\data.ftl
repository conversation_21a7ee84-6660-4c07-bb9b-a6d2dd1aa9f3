<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
  <h1>数据查询</h1>
  <ol class="breadcrumb">
    <li><a href="${ctx}"><i class="fa fa-dashboard"></i>主页</a></li>
    <li><a href=""><i class="fa fa-dashboard"></i>数据查询</a></li>
  </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info row">
        <div class="box-header with-border">
        </div>
        <form class="form-horizontal" action="" method="post" id="infoFrom" role="form" data-toggle="validator">
          <div class="box-body col-xs-6">
            <div class="form-group">
              <label for="tenantId" class="col-sm-4 control-label">tenantID</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="tenantId" placeholder="企业ID(必填)" required>
              </div>
            </div>
            <div class="form-group">
              <label for="apiName" class="col-sm-4 control-label">describeApiName</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="apiName" placeholder="describe_api_name(必填)">
              </div>
            </div>
            <div class="form-group">
              <label for="name" class="col-sm-4 control-label">name</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="name" placeholder="主属性Name(选填)">
              </div>
            </div>
            <div class="form-group">
              <label for="dataId" class="col-sm-4 control-label">dataID</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" style="border-radius:5px;" id="dataId" placeholder="数据ID(选填)">
              </div>
            </div>
            <div class="col-sm-offset-3">
              <button type="button" id="findData" class="btn btn-primary" disabled>查询</button>
              <input id="cancel_btn" class="btn" type="button" value="返回" onclick="history.back()"/>
            </div>
          </div>
          <div class="box-body col-xs-6">
            <h4>Result <i class="fa fa-hand-o-down"></i></h4>
            <pre id="dataInfo" style="">
                        </pre>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<#--表单验证插件-->
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script>
  $('#findData').on('click', function () {
    $('#dataInfo').text("");

    $.getJSON("${CONTEXT_PATH}/metadata/data/info", {
      tenantId: $('#tenantId').val(),
      apiName: $('#apiName').val(),
      name: $('#name').val(),
      id: $('#dataId').val()
    }, function (data) {
      if (data.dataInfo !== "") {
        $('#dataInfo').JSONView(data.dataInfo, {
          collapsed: false,
          nl2br: true,
          recursive_collapser: true
        });
      }
    });

  <#--$.ajax({-->
  <#--url: "${ctx}/metadata/data/info?tenantId=" + $('#tenantId').val() + "&apiName=" + $('#apiName').val() + "&name=" + $('name').val() + "&id=" + $('#dataId').val(),-->
  <#--contentType: "application/json",-->
  <#--type: "POST",-->
  <#--data: ,-->
  <#--traditional: true,-->
  <#--success: function (data) {-->
  <#--if (data.dataInfo !== "") {-->
  <#--$('#dataInfo').JSONView(data.dataInfo, {-->
  <#--collapsed: false,-->
  <#--nl2br: true,-->
  <#--recursive_collapser: true-->
  <#--});-->
  <#--}-->
  <#--}-->
  <#--});-->
  });

  /**
   * 表单验证
   */
  var required1 = false, required2 = false, required3 = false, required4 = false;

  $('#tenantId').bind("input propertychange", function () {
    var tenantIdValue = $('#tenantId').val();
    if (tenantIdValue === null || tenantIdValue === "") {
      required1 = false;
    } else {
      required1 = true;
    }
  });

  $('#apiName').bind("input propertychange", function () {
    var apiNameValue = $('#apiName').val();
    if (apiNameValue === null || apiNameValue === "") {
      required2 = false;
    } else {
      required2 = true;
    }
  });

  $('#dataId').bind("input propertychange", function () {
    var dataIdValue = $('#dataId').val();
    if (dataIdValue === null || dataIdValue === "") {
      required3 = false;
    } else {
      required3 = true;
    }
  });

  $('#name').bind("input propertychange", function () {
    var name = $('#name').val();
    if (name === null || name === "") {
      required4 = false;
    } else {
      required4 = true;
    }
  });

  $('#tenantId,#apiName,#dataId,#name').bind("input propertychange", function () {
    if (required1 && required2 && (required3 || required4)) {
      $('#findData').removeAttr("disabled");
    } else {
      $('#findData').attr("disabled", "disabled");
    }
  });
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
