package com.fxiaoke.paas.console.mapper.metadata

import com.github.mybatis.mapper.IBatchMapper
import com.github.mybatis.mapper.ICrudMapper
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Update

interface MtRelationMapper extends ICrudMapper, IBatchMapper, ITenant<MtRelationMapper> {

  @Update("update mt_relation set is_deleted = '0' where tenant_id = #{tenantId} and source_api_name = #{sourceApiName} and source_data_id = #{sourceDataId}")
  int updateIsDeletedBySourceApiNameAndSourceDataId(@Param("tenantId") String tenantId, @Param("sourceApiName") String sourceApiName, @Param("sourceDataId") String sourceDataId)

  @Delete("delete from mt_relation where tenant_id=#{tenantId} and relation_id in (select relation_id from mt_relation where tenant_id=#{tenantId} and source_api_name=#{describeApiName} and reference_name in (select api_name from mt_field where tenant_id =#{tenantId} and describe_api_name =#{describeApiName} and api_name not in (\${blackFieldApiNames}) and status='deleted' and type in ('object_reference', 'object_reference_many','master_detail')) limit 100)")
  int recycleRelation(@Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName, @Param("blackFieldApiNames") String blackFieldApiNames);
}