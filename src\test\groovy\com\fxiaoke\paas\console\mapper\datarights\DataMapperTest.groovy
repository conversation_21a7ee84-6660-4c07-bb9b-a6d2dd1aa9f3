package com.fxiaoke.paas.console.mapper.datarights

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.paas.console.entity.datarights.DepartmentUser
import com.fxiaoke.paas.console.entity.datarights.EntityShareGroup
import com.fxiaoke.paas.console.entity.datarights.EntityShareReceive
import com.fxiaoke.paas.console.entity.datarights.GroupUser
import com.fxiaoke.paas.console.entity.datarights.Personnel
import com.fxiaoke.paas.console.entity.datarights.Principal
import com.google.common.collect.Sets
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import java.util.stream.Collectors

/**
 * <AUTHOR>
 * @date 2019/3/29 下午5:08
 *
 */
@ContextConfiguration(locations = ["classpath:mapperContext.xml"])
@Slf4j
class DataMapperTest  extends Specification{
  @Autowired
  private DataRightsDataMapper dataMapper
  @Autowired
  private PersonnelMapper personnelMapper

  def "queryTeamMember"(){
  given:
  List<Principal> principals=dataMapper.setTenantId("71586").queryTeamMember("71586","5d1195f6a5083d3b448b5164",DataRightsDataMapper.PRINCIPAL,0,1)
    log.info(JSONObject.toJSONString(principals))

  }

  def "queryDataOwnDepartment"(){
    given:
    Map<String,String> map=dataMapper.setTenantId("71586").queryDataOwnDepartment("71586","5c6a4b7ba5083d5444d25e5e")
    System.out.println(map)
  }


  def "getEntityShares"() {
    given:
    String tenantId="71586"
    String objectId="5c6a4b7ba5083d5444d25e5e"
    List<Principal> principal = dataMapper.setTenantId(tenantId).queryTeamMember(tenantId, objectId, DataRightsDataMapper.PRINCIPAL)
    Personnel personnel = personnelMapper.setTenantId(tenantId).queryPersonnelByObjectId(tenantId, principal.userId, objectId)
    Set<String> ids = Sets.newHashSet()
    ids.add(personnel.userId)
    ids.add(personnel.superior)
    ids.addAll(personnel.subordinates.stream().map({ s -> s.get("subordinate") }).collect(Collectors.toSet()))
    ids.add(personnel.role)
    ids.add(personnel.deptId)
    ids.add(personnel.groupId)
    ids=ids.stream().filter({ s -> s != null && !StringUtils.isBlank(s) }).collect(Collectors.toSet())
    System.out.println(ids)
    Map<String,Object> maps= dataMapper.setTenantId(tenantId).queryEntityShare(tenantId, objectId, ids)
    System.out.println(maps)
  }


  def "queryGroupUserByGroupId"(){
    given:
    String tenantId='466'
    String groupId='5b203b69319d1932fadd5997'
    GroupUser groupUser=dataMapper.setTenantId(tenantId).queryGroupUserByGroupId(tenantId,groupId)
    System.out.println(groupUser)
  }


  def "queryDepartmentUserByDeptId"(){
    given:
    String tenantId="71586"
    String deptId="1024"
    List<DepartmentUser> departmentUsers=dataMapper.setTenantId(tenantId).queryDepartmentUserByDeptId(tenantId,deptId)
    System.out.println(departmentUsers)
  }

  def "queryEntityShareGroupByApiName"(){
    given:
    String tenantId="466"
    String apiName="object_Ttk0O__c"
    List<EntityShareGroup> entityShareGroupList=dataMapper.setTenantId(tenantId).queryEntityShareGroupByApiName(tenantId,apiName)
    entityShareGroupList.forEach({s->System.out.println(s.fieldValue+"------"+s.sqlSelectFields)})
  }

  def "queryEntityShareReceiveByApiName"(){
    given:
    String tenantId="466"
    String apiName="object_Ttk0O__c"
    List<EntityShareReceive> entityShareReceiveList=dataMapper.setTenantId(tenantId).queryEntityShareReceiveByApiName(tenantId,apiName)
   System.out.println(entityShareReceiveList.size())
  }
}
