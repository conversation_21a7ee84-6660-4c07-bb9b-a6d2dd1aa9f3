package com.fxiaoke.paas.console.license.mapper;

import com.facishare.paas.license.pojo.ProductInfoPojo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
interface ProductInfoMapper {

  @Select("SELECT id FROM product_info WHERE tenant_id = #{tenantId} AND product_version = #{productVersion} AND product_name = #{productName} ")
  String queryProductInfoId(@Param("tenantId") String tenantId, @Param("productVersion") String productVersion, @Param("productName") String productName);

  @Select("SELECT * FROM product_info WHERE tenant_id = #{tenantId} AND del_flag = false ")
  List<ProductInfoPojo> queryProductInfoByTenantId(@Param("tenantId") String tenantId);

  @Select("SELECT product_version FROM product_info WHERE tenant_id = #{tenantId} AND del_flag = false ")
  List<String> queryProductVersionByTenantId(@Param("tenantId") String tenantId);

  @Select("SELECT * FROM product_info WHERE id = #{productId} AND del_flag = false ")
  ProductInfoPojo queryProInfoByProId(@Param("productId") String productId);
}
