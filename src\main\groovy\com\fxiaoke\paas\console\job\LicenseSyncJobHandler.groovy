package com.fxiaoke.paas.console.job

import com.fxiaoke.paas.console.service.license.LicenseSyncJobService
import com.xxl.job.core.biz.model.ReturnT
import com.xxl.job.core.biz.model.TriggerParam
import com.xxl.job.core.handler.IJobHandler
import com.xxl.job.core.handler.annotation.JobHander
import groovy.util.logging.Slf4j
import org.springframework.stereotype.Component

import javax.annotation.Resource

/**
 * 同步所有企业的license信息定时任务
 */
@JobHander(value = "LicenseSyncJobHandler")
@Component
@Slf4j
class LicenseSyncJobHandler extends IJobHandler {
  @Resource
  LicenseSyncJobService licenseSyncJobService

  @Override
  ReturnT execute(TriggerParam triggerParam) throws Exception {
    try {
      log.info("start the job!")
      licenseSyncJobService.sync()
      return new ReturnT(200, "license sync is success!")
    } catch (Exception e) {
      log.error("license sync is error: ", e)
      throw e
    }
  }
}
