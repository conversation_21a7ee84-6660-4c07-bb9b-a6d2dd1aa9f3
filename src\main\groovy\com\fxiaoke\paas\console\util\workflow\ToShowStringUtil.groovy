//package com.fxiaoke.paas.console.util.workflow
//
//import com.effektif.workflow.api.ext.WorkflowConstants
//import com.facishare.paas.workflow.kernel.entity.ExpressionEntity
//import com.fxiaoke.paas.console.exception.workflow.WorkflowAdminException
//import groovy.util.logging.Slf4j
//import org.apache.commons.collections.CollectionUtils
//import org.apache.commons.lang3.StringUtils
//import org.springframework.stereotype.Component
//
//import java.text.SimpleDateFormat
//
///**
// * Created by yangxw on 2018/3/5.
// */
//@Slf4j
//class ToShowStringUtil {
//   static final long ONE_SECOND = 1000
//   static final long ONE_MINUTE = ONE_SECOND * 60
//   static final long ONE_HOUR = ONE_MINUTE * 60
//   static final long ONE_DAY = ONE_HOUR * 24
//   static final String ENABLED = "<a style='color: green'>已启用</a>"
//   static final String NOT_ENABLED = "<a style='color: red'>已停用</a>"
//
//  def static getOperator(String operator) {
//
//    if (StringUtils.isNotBlank(operator)) {
//      switch (operator) {
//        case Constants.Operator.STRING_EQUALS:
//        case Constants.Operator.NUMBER_EQUALS:
//          return "等于"
//        case Constants.Operator.STRING_NOT_EQUALS:
//        case Constants.Operator.NUMBER_NOT_EQUALS:
//          return "不等于"
//        case Constants.Operator.STRING_STARTS_WITH:
//          return "开始于"
//        case Constants.Operator.STRING_ENDS_WITH:
//          return "结束于"
//        case Constants.Operator.STRING_NOT_STARTS_WITH:
//          return "不开始于"
//        case Constants.Operator.STRING_NOT_ENDS_WITH:
//          return "不结束于"
//        case Constants.Operator.STRING_CONTAINS:
//          return "包含"
//        case Constants.Operator.STRING_NOT_CONTAINS:
//          return "不包含"
//        case Constants.Operator.NUMBER_LESS_THAN:
//          return "小于"
//        case Constants.Operator.NUMBER_GREATER_THAN:
//          return "大于"
//        case Constants.Operator.NUMBER_LESS_THAN_OR_EQUAL:
//          return "小于或等于"
//        case Constants.Operator.NUMBER_GREATER_THAN_OR_EQUAL:
//          return "大于或等于"
//        default:
//          return ""
//      }
//    }
//    return ""
//
//  }
//
//  /**
//   * 把流程类型转换成科显示的中文，审批流或工作流
//   */
//  def static getFlowType(String type) {
//
//    if (StringUtils.isNotBlank(type)) {
//      if ("approvalflow".equals(type)) {
//        return "审批流"
//      } else if ("workflow".equals(type)) {
//        return "工作流"
//      }
//      if ("workflow_bpm".equals(type)) {
//        return "BPM"
//      }
//    }
//    return "没有匹配的类型"
//
//  }
//
//  /**
//   * 把实例的状态转换成相应的中文
//   * @param state
//   * @return
//   */
//  def static getInstanceState(String state) {
//
//    String stateShow = ""
//    if (StringUtils.isNotBlank(state)) {
//      switch (state) {
//        case WorkflowConstants.InstanceStatus.IN_PROGRESS:
//          stateShow = "进行中"
//          break
//        case WorkflowConstants.InstanceStatus.PASS:
//          stateShow = "已通过"
//          break
//        case WorkflowConstants.InstanceStatus.REJECT:
//          stateShow = "已拒绝"
//          break
//        case WorkflowConstants.InstanceStatus.CANCEL:
//          stateShow = "已取消"
//          break
//        case WorkflowConstants.InstanceStatus.ERROR:
//          stateShow = "已出错"
//          break
//        default:
//          stateShow = "没有匹配的实例状态"
//          break
//      }
//    }
//    return stateShow
//
//  }
//
//  /**
//   * 把TASK的状态转换成相应的中文
//   * @param state
//   * @return
//   */
//  def static getTaskState(String state) {
//
//    String stateShow = ""
//    if (StringUtils.isNotBlank(state)) {
//      switch (state) {
//        case WorkflowConstants.UserTaskStatus.IN_PROGRESS:
//          return "进行中"
//        case WorkflowConstants.UserTaskStatus.PASS:
//          return "已通过"
//        case WorkflowConstants.UserTaskStatus.AUTO_PASS:
//          return "自动通过"
//        case WorkflowConstants.UserTaskStatus.REJECT:
//          return "已拒绝"
//        case WorkflowConstants.UserTaskStatus.CANCEL:
//          return "已取消"
//        case WorkflowConstants.UserTaskStatus.GO_BACK:
//          return "已驳回"
//        case WorkflowConstants.UserTaskStatus.AUTO_GO_BACK:
//          return "自动驳回"
//        case WorkflowConstants.UserTaskStatus.SCHEDULE:
//          return "待生成"
//        case WorkflowConstants.UserTaskStatus.ERROR:
//          return "已出错"
//        default:
//          return "没有匹配的TASK状态"
//      }
//    }
//    return stateShow
//
//  }
//
//  /**
//   * 把TASK的类型转换成相应的中文
//   * @param taskType
//   * @return
//   */
//  def static getTaskType(String taskType) {
//
//    String typeShow = ""
//    if (StringUtils.isNotBlank(taskType)) {
//      switch (taskType) {
//        case WorkflowConstants.UserTaskType.SINGLE:
//          typeShow = "单人审批"
//          break
//        case WorkflowConstants.UserTaskType.ONE_PASS:
//          typeShow = "一票通过"
//          break
//        case WorkflowConstants.UserTaskType.ALL_PASS:
//          typeShow = "会签"
//          break
//        case WorkflowConstants.UserTaskType.ANYONE:
//          typeShow = "BPM:一票通过"
//          break
//        case WorkflowConstants.UserTaskType.ONE:
//          typeShow = "BPM:单人审批"
//          break
////                case WorkflowConstants.UserTaskType.ONE_BY_ONE:
////                    typeShow = "BPM专用:一票通过"
////                    break
//        default:
//          typeShow = "没有匹配的TASK类型"
//          break
//      }
//    }
//    return typeShow
//
//  }
//
//  /**
//   * 把TASK的审批动作转换成相应的中文
//   * @param actionType
//   * @return
//   */
//  def static getActionType(String actionType) {
//
//    String actionTypeShow = ""
//    if (StringUtils.isNotBlank(actionType)) {
//      switch (actionType) {
//        case WorkflowConstants.Action.AGREE:
//          actionTypeShow = "同意"
//          break
//        case WorkflowConstants.Action.AUTO_AGREE:
//          actionTypeShow = "自动同意"
//          break
//        case WorkflowConstants.Action.CANCEL:
//          actionTypeShow = "取消"
//          break
//        case WorkflowConstants.Action.GO_BACK:
//          actionTypeShow = "驳回"
//          break
//        case WorkflowConstants.Action.AUTO_GO_BACK:
//          actionTypeShow = "自动驳回"
//          break
//        case WorkflowConstants.Action.REJECT:
//          actionTypeShow = "拒绝"
//          break
//        default:
//          actionTypeShow = "没有匹配的审批动作"
//          break
//      }
//    }
//    return actionTypeShow
//
//  }
//
//  /**
//   * 获取流程触发动作
//   * @param actionTypes
//   * @return
//   */
//  def static getTriggerActionTypes(List<Integer> actionTypes) {
//
//    StringBuilder types = new StringBuilder()
//    if (CollectionUtils.isNotEmpty(actionTypes)) {
//      for (Integer type : actionTypes) {
//        switch (type) {
//          case 1:
//            types.append("新增; ")
//            break
//          case 2:
//            types.append("编辑; ")
//            break
//          case 3:
//            types.append("作废; ")
//            break
//          case 4:
//            types.append("删除; ")
//            break
//          default:
//            break
//        }
//      }
//
//    }
//    return types.toString()
//
//  }
//
//  /**
//   * 获取前段展示的executionType
//   * @param executionType
//   * @return
//   */
//  def static getExecutionType(int executionType) {
//    //TODO 以后有确定的映射表时要修改
//    return "实时"
//
//  }
//
//  /**
//   * 把long类型时间转换成年月日时分秒的可显示的时间
//   * @param time
//   * @return
//   */
//  def static getTimeString(Long time) {
//
//    if (null == time) {
//      return ""
//    }
//    return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(time))
//
//  }
//
//  /**
//   * 把long类型时间转换成年月日的可显示的日期
//   * @param time
//   * @return
//   */
//  def static getDateString(Long time) {
//
//    if (null == time) {
//      return ""
//    }
//    return new SimpleDateFormat("yyyy-MM-dd").format(new Date(time))
//
//  }
//
//  def static getInstanceDurationTimeString(Long time) {
//
//    if (null == time) {
//      return ""
//    }
//    long days = time / ONE_DAY
//    long rest = time % ONE_DAY
//    long hours = rest / ONE_HOUR
//    rest %= ONE_HOUR
//    long minutes = rest / ONE_MINUTE
//    rest %= ONE_MINUTE
//    long seconds = rest / ONE_SECOND
//    String duration = ""
//    boolean flag = false
//    if (0 < days) {
//      duration += days + "天"
//      flag = true
//    }
//    if (0 < hours) {
//      duration += hours + "时"
//      flag = true
//    } else if (flag) {
//      duration += "0时"
//    }
//    if (0 < minutes) {
//      duration += minutes + "分"
//      flag = true
//    } else if (flag) {
//      duration += "0分"
//    }
//    if (0 < seconds) {
//      duration += seconds + "秒"
//    } else if (flag) {
//      duration += "0秒"
//    } else {
//      duration = "小于1秒"
//    }
//
//    return duration
//
//  }
//
//  def static getTaskRemindTimeString(Long time) {
//
//    if (null == time) {
//      return ""
//    }
//    long days = time / ONE_DAY
//    long rest = time % ONE_DAY
//    long hours = rest / ONE_HOUR
//    rest %= ONE_HOUR
//    long minutes = rest / ONE_MINUTE
//    rest %= ONE_MINUTE
//    long seconds = rest / ONE_SECOND
//    String remindTime = ""
//    boolean flag = false
//    if (time > 0) {
//      remindTime += "超时后："
//    }
//    if (time < 0) {
//      remindTime += "超时前："
//    }
//    if (0 < days) {
//      remindTime += days + "天"
//      flag = true
//    }
//    if (0 < hours) {
//      remindTime += hours + "时"
//      flag = true
//    } else if (flag) {
//      remindTime += "0时"
//    }
//    if (0 < minutes) {
//      remindTime += minutes + "分"
//      flag = true
//    } else if (flag) {
//      remindTime += "0分"
//    }
//    if (0 < seconds) {
//      remindTime += seconds + "秒"
//    } else if (flag) {
//      remindTime += "0秒"
//    } else {
//      remindTime = "小于1秒"
//    }
//    if (time != 0) {
//      remindTime += " 提醒"
//    }
//    return remindTime
//
//  }
//
//  def static getTaskRemindDataTimeString(Long remindTime, Long createTime, Integer remindLatency) {
//    Long time = null
//    if (remindTime != null) {
//      time += remindTime
//    }
//    if (createTime != null) {
//      time += createTime
//    }
//    if (remindLatency != null) {
//      time += remindLatency * ONE_HOUR
//    }
//    return new SimpleDateFormat("yyyy-MM-dd HH;mm;ss").format(new Date(time))
//  }
//
//
//  def static getTaskDurationTimeString(Long createTime, Long modifyTime) {
//
//    if (null == modifyTime || modifyTime <= createTime) {
//      return ""
//    }
//    Long duration = modifyTime - createTime
//    return getInstanceDurationTimeString(duration)
//
//  }
//
//  //TODO 当enable为空的时候应该怎么显示
//  def static convertEnableByBoolean (Boolean enable) {
//
//    if (null == enable) {
//      return ""
//    } else if (!enable) {
//      return NOT_ENABLED
//    } else {
//      return ENABLED
//    }
//
//  }
//  //TODO 当enable为空的时候应该怎么显示
//  def static convertEnable(String enable) {
//
//    if (StringUtils.isBlank(enable)) {
//      return ""
//    } else if ("false".equalsIgnoreCase(enable)) {
//      return NOT_ENABLED
//    } else if ("true".equalsIgnoreCase(enable)) {
//      return ENABLED
//    } else {
//      log.info("parameter enable can only be true or false, can't be {}", enable)
//      throw new WorkflowAdminException("变量enable只能是true或者false，不能为" + enable)
//    }
//
//  }
//
//  /**
//   *  前端要显示的字符串，如果对象为空，则显示""，否则显示其值
//   * @param object
//   * @return
//   */
//  def static getShowObject(Object object) {
//    if (null == object) {
//      return ""
//    }
//    return object.toString()
//  }
//
//  /**
//   * 把多个对象拼成字符串
//   * @param objects
//   * @param separator
//   * @return
//   */
//  def static jointObjects(String separator, Object... objects) {
//
//    StringBuilder builder = new StringBuilder()
//    for (Object object : objects) {
//      if (null != object) {
//        builder.append(object.toString()).append(separator)
//      }
//    }
//    return builder.substring(0, builder.length() - separator.length())
//
//  }
//
//  def static getTriggerConditionShow(String pattern, List<ExpressionEntity> conditions) {
//
//    if (StringUtils.isBlank(pattern) || CollectionUtils.isEmpty(conditions)) {
//      return ""
//    }
//    String conditionPattern = " " + pattern + " "
//    conditionPattern = conditionPattern.replace("and", " 且 ")
//            .replace("or", " 或 ")
//            .replace("(", " ( ")
//            .replace(")", " ) ")
//    Integer rowNo
//    for (ExpressionEntity condition : conditions) {
//      rowNo = condition.getRowNo()
//      if (null != rowNo) {
//        conditionPattern = conditionPattern.replace(" " + Math.abs(rowNo) + " ", "#" + Math.abs(rowNo))
//      }
//    }
//    for (ExpressionEntity condition : conditions) {
//      rowNo = condition.getRowNo()
//      if (null != rowNo) {
//        conditionPattern = conditionPattern.replace("#" + Math.abs(rowNo), AssembleUtil.assembleExpressionShow(condition))
//      }
//    }
//    return conditionPattern
//
//  }
//}
