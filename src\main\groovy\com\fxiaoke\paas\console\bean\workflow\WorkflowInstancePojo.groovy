package com.fxiaoke.paas.console.bean.workflow

import io.swagger.annotations.ApiModelProperty

/**
 * Created by yangxw on 2018/3/9.
 */
class WorkflowInstancePojo {
    @ApiModelProperty("流程id，唯一标识")
     String id;

    @ApiModelProperty("租户id")
     String tenantId;

    @ApiModelProperty("应用名称")
     String appName;

    @ApiModelProperty("流程实例所属的流程id")
     String workflowId;

    @ApiModelProperty("流程实例绑定的实体id")
     String objectId;

    @ApiModelProperty("实体名称")
     String entityName;

    @ApiModelProperty("流程实例的申请人id")
     String applicantId;

    @ApiModelProperty("流程实例的申请人姓名与部门")
     String applicantAccountAndDept;

//    @ApiModelProperty("申请人所在部门")
//     String dept;

    @ApiModelProperty("流程实例当前状态")
     String state;

    @ApiModelProperty("流程实例开始时间")
     String start;

    @ApiModelProperty("流程实例完成时间")
     String end;

    @ApiModelProperty("展示流程的正在进行中的实例数量和所有的实例数量")
     String inProgressAll;

     String duration;

}
