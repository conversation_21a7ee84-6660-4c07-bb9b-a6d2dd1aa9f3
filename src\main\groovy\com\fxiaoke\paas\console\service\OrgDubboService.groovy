package com.fxiaoke.paas.console.service

import com.effektif.workflow.api.workflow.ExecutableWorkflow
import com.facishare.ibss.em.biz.bo.EnterpriseAccountDetailBO
import com.facishare.ibss.em.service.api.EnterpriseAccountService
import com.facishare.paas.org.pojo.*
import com.facishare.paas.org.service.GroupService
import com.facishare.paas.org.service.OrganizationService
import com.facishare.paas.org.service.UserService
import com.fxiaoke.paas.console.exception.workflow.WorkflowAdminException
import com.fxiaoke.paas.console.util.workflow.IdUtil
import com.fxiaoke.paas.console.util.workflow.WorkflowFields
import com.google.common.collect.Lists
import groovy.util.logging.Slf4j
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * Created by yangxw on 2018/3/12.
 */
@Service("orgDubboService")
@Slf4j
class OrgDubboService {
  @Autowired
  private OrganizationService organizationService;

//    @Autowired
//    private ApprovalFlowKernelService approvalFlowKernelService;

//  @Autowired
//  private EnterpriseAccountService enterpriseAccountService;


  @Autowired
  private GroupService groupService;

  @Autowired
  private UserService userService;

//    @Autowired
//    private defEnterpriseService defEnterpriseService;

  /**
   * 通过用户姓名，来查找符合条件的userId，这是模糊查询
   * @param context 请求者的相关信息
   * @param userAccount
   * @return 返回所有满足条件的UserId和UserAccount一个key ( UserId ) :value(UserAccount)对
   */
  def List<String> getUserIdListByUserAccount(OrgContext context, String userAccount)
          throws WorkflowAdminException {

    if (StringUtils.isBlank(userAccount)) {
      return null;
    }
    List<String> userIds;
    try {
      userIds = organizationService.getUserIdsByName(context, userAccount, 0);
    } catch (Exception e) {
      log.error("get userIds by userAccount = {} error", userAccount, e);
      throw new WorkflowAdminException("通过用户姓名 = \"" + userAccount + "\" 模糊查询用户id出错", e);
    }
    if (CollectionUtils.isEmpty(userIds)) {
      log.info("get null when query userIds by userAccount = {}", userAccount);
    }
    return userIds;

  }

  /**
   * 根据userId查询用户姓名
   * @param context 请求者的相关信息
   * @param userId
   * @return
   * @throws WorkflowAdminException
   */
  def String getUserAccountByUserId(OrgContext context, String userId)
          throws WorkflowAdminException {

    if (StringUtils.isBlank(userId)) {
      log.info("userId = {} is null", userId);
      return null;
    }
    Map<String, String> map = getUserIdAndUserAccountMapByUserIdList(context, Lists.newArrayList(userId));
    return map.get(userId);

  }

  /**
   * 根据userIdList来查找与之匹配的Account，组成一个把结果组成一个KEY(userId) VALUE(account) map
   * @param context 请求者的相关信息
   * @param userIdList
   * @return
   * @throws WorkflowAdminException
   */
  def Map<String, String> getUserIdAndUserAccountMapByUserIdList(OrgContext context, List<String> userIdList)
          throws WorkflowAdminException {

    HashMap<String, String> map = new HashMap<>();
    if (CollectionUtils.isEmpty(userIdList)) {
      log.info("Parameter userIdList is null");
      return map;
    }
    List<UserInfo> userInfoList = this.getUserInfoListByUserIdList(context, userIdList);
    if (CollectionUtils.isEmpty(userInfoList)) {
      log.info("get null when query UserInfo By userIdList = {}", userIdList);
      return map;
    }
    /**
     * 生成KEY(userId) VALUE(account) map
     */
    userInfoList.each { userInfo ->
      map.put(userInfo.getId(), userInfo.getName())
    };
    return map;

  }

  /**
   * 根据用户id列表查询与之对应的主部门信息
   * @param context
   * @param userIdList
   * @return
   */
  def List<MainDeptInfo> getMainDeptInfoListByUserIdList(OrgContext context, List<String> userIdList) {

    List<MainDeptInfo> mainDeptInfoList = new ArrayList<>();
    if (userIdList.size() > 20) {
      List<MainDeptInfo> mainDeptInfoList2 = new ArrayList<>();
      int record1 = 0, record2 = 20;
      int length = userIdList.size() / 20;
      for (int i = 0; i <= length; i++) {
        if (i == length) {
          mainDeptInfoList = organizationService.queryDeptByUserBatch(context, userIdList.subList(record1, userIdList.size()));
          for (int j = 0; j < mainDeptInfoList.size(); j++) {
            mainDeptInfoList2.add(mainDeptInfoList.get(j));
          }
          break;
        }
        mainDeptInfoList = organizationService.queryDeptByUserBatch(context, userIdList.subList(record1, record2));
        for (int j = 0; j < mainDeptInfoList.size(); j++) {
          mainDeptInfoList2.add(mainDeptInfoList.get(j));
        }
        record1 += 20;
        record2 += 20;
      }
      return mainDeptInfoList2;
    }
    if (CollectionUtils.isEmpty(userIdList)) {
      return mainDeptInfoList;
    }
    try {
      mainDeptInfoList = organizationService.queryDeptByUserBatch(context, userIdList);
    } catch (Exception e) {
      log.error("get MainDeptInfo List error by Ids = {}", userIdList, e);
      throw new WorkflowAdminException("根据用户及部门信息列表 = " + userIdList + "查询用户信息失败", e);
    }
    return mainDeptInfoList;

  }

  /**
   * 根据userIdList获取与之对应的主部门信息，并把结果放到一个map里面
   * @param context
   * @param userIdList
   * @return
   */
  def Map<String, MainDeptInfo> getUserIdAndMainDeptInfoMap(OrgContext context, List<String> userIdList) {

    List<MainDeptInfo> mainDeptInfoList = this.getMainDeptInfoListByUserIdList(context, userIdList);
    Map<String, MainDeptInfo> map = new HashMap<>();
    if (CollectionUtils.isNotEmpty(mainDeptInfoList)) {
      for (MainDeptInfo mainDeptInfo : mainDeptInfoList) {
        map.put(mainDeptInfo.getUserId(), mainDeptInfo);
      }
    }
    return map;

  }

  /**
   * 根据userIdList获取与之对应的UserInfo List
   * @param context
   * @param userIdList
   * @return
   */
  def List<UserInfo> getUserInfoListByUserIdList(OrgContext context, List<String> userIdList) {

    List<UserInfo> userInfoList = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(userIdList)) {
      try {
        userInfoList = organizationService.getUserInfoByUserIds(context, userIdList);
      } catch (Exception e) {
        log.error("get UserInfo By UserIds = {} error", userIdList, e);
        throw new WorkflowAdminException("根据 userIds = " + userIdList + "查询用户信息出错", e);
      }
    }
    return userInfoList;

  }

  /**
   * 根据部门id list查询与之对应的部门信息list
   * @param context
   * @param deptList
   * @return
   */
  def List<DeptInfo> getDeptInfoListByDeptIdList(OrgContext context, List<String> deptList) {

    List<DeptInfo> deptInfoList = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(deptList)) {
      try {
        deptInfoList = organizationService.getDeptInfoByDeptIds(context, deptList);
      } catch (Exception e) {
        log.error("get DeptInfo By deptList = {} error", deptList, e);
        throw new WorkflowAdminException("根据部门idList = " + deptList + "查询部门信息出错", e);
      }
    }
    return deptInfoList;

  }

  /**
   * 根据部门id查询与之对应的部门信息，并放入map中
   * @param context
   * @param deptList
   * @return
   */
  def Map<String, DeptInfo> getDeptIdAndDeptInfoMapByDeptIdList(OrgContext context, List<String> deptList) {

    List<DeptInfo> deptInfoList = this.getDeptInfoListByDeptIdList(context, deptList);
    Map<String, DeptInfo> deptMap = new HashMap<>();
    if (CollectionUtils.isNotEmpty(deptInfoList)) {
      for (DeptInfo deptInfo : deptInfoList) {
        deptMap.put(deptInfo.getDeptId(), deptInfo);
      }
    }
    return deptMap;

  }

  /**
   * 根据workflowId查询流程详细信息
   *
   * @param workflowId
   * @return
   * @throws WorkflowAdminException
   */
  ExecutableWorkflow queryExecutableWorkflowById(OrgContext context, String tenantId, String workflowId)
          throws WorkflowAdminException {

    IdUtil.verifyId(workflowId);
    ExecutableWorkflow executableWorkflow;
    try {
//            executableWorkflow = approvalFlowKernelService.findApprovalFlowById(workflowId);
      executableWorkflow = new ExecutableWorkflow()
    } catch (Exception e) {
      log.error("query ExecutableWorkflow By WorkflowId = {} error", workflowId, e);
      throw new WorkflowAdminException("根据workflowId = " + workflowId + " 查询ExecutableWorkflow出错", e);
    }
    if (null == executableWorkflow) {
      log.info("query ExecutableWorkflow By Id = {} get null Object", workflowId);
      return null;
    }
    if (tenantId.equals(executableWorkflow.getProperty(WorkflowFields.TENANT_ID))) {
      executableWorkflow.property(
              "creatorAccount"
              , this.getUserAccountByUserId(
              context
              , executableWorkflow.getCreatorId())
      );
      return executableWorkflow;
    }

    return null;

  }

  /**
   * 根据用户组id列表查询与之对应的用户组信息
   * @param context
   * @param groupIdList
   * @return
   */
  def List<GroupPojo> getGroupByGroupIdList(OrgContext context, List<String> groupIdList) {

    List<GroupPojo> groupPojoList = new ArrayList<>();
    List<GroupPojo> groupPojoList2 = new ArrayList<>();
    if (groupIdList.size() > 20) {
      int record1 = 0, record2 = 20;
      for (int i = 0; i <= groupIdList.size() / 20; i++) {
        if (i == groupIdList.size() / 20) {
          groupPojoList = groupService.queryGroupByIdList(context, false, true, null, groupIdList.subList(record1, groupIdList.size()), null);
          for (int j = 0; j < groupPojoList.size(); j++) {
            groupPojoList2.add(groupPojoList.get(j));
          }
          groupPojoList = groupService.queryGroupByIdList(context, false, false, null, groupIdList.subList(record1, groupIdList.size()), null);
          for (int j = 0; j < groupPojoList.size(); j++) {
            if (groupPojoList2.contains(groupPojoList.get(j))) {
              groupPojoList2.add(groupPojoList.get(j));
            }
          }
          break;
        }
        groupPojoList = groupService.queryGroupByIdList(context, false, true, null, groupIdList.subList(record1, record2), null);
        for (int j = 0; j < groupPojoList.size(); j++) {
          groupPojoList2.add(groupPojoList.get(j));
        }
        groupPojoList = groupService.queryGroupByIdList(context, false, false, null, groupIdList.subList(record1, record2), null);
        for (int j = 0; j < groupPojoList.size(); j++) {
          if (groupPojoList2.contains(groupPojoList.get(j))) {
            groupPojoList2.add(groupPojoList.get(j));
          }
        }
        record1 += 20;
        record2 += 20;
      }
      return groupPojoList2;
    }
    if (CollectionUtils.isEmpty(groupIdList)) {
      return groupPojoList;
    }
    try {
      groupPojoList = groupService.queryGroupByIdList(context, false, true, null, groupIdList, null);
      for (int j = 0; j < groupPojoList.size(); j++) {
        groupPojoList2.add(groupPojoList.get(j));
      }
      groupPojoList = groupService.queryGroupByIdList(context, false, false, null, groupIdList, null);
      for (int j = 0; j < groupPojoList.size(); j++) {
        if (groupPojoList2.contains(groupPojoList.get(j))) {
          groupPojoList2.add(groupPojoList.get(j));
        }
      }
    } catch (Exception e) {
      log.error("get queryGroupByIdList List error by Ids = {}", groupIdList, e);
      throw new WorkflowAdminException("根据用户id列表 = " + groupIdList + "查询用户信息失败", e);
    }
    return groupPojoList2;

  }

  /**
   * 根据部门id查询与之对应的部门信息，并放入map中
   * @param context
   * @param groupIdList
   * @return
   */
  def Map<String, GroupPojo> getGroupMapByGroupIdList(OrgContext context, List<String> groupIdList) {

    List<GroupPojo> groupPojoList = this.getGroupByGroupIdList(context, groupIdList);
    Map<String, GroupPojo> groupMap = new HashMap<>();
    if (CollectionUtils.isNotEmpty(groupPojoList)) {
      for (GroupPojo groupPojo : groupPojoList) {
        groupMap.put(groupPojo.getId(), groupPojo);
      }
    }
    return groupMap;

  }

  /**
   *
   * @param context
   * @param userIds
   * @param userStatus
   * @return
   */
  def List<UserPojo> batchQueryUserPojoByUserId(OrgContext context, Set<String> userIds, Integer userStatus) {
    List<UserPojo> userPojoList = new ArrayList<>();
    try {
      userPojoList = userService.batchQueryUserPojoByUserId(context, userIds, userStatus);
    } catch (Exception e) {
      log.error("get UserPojo List By ids =　{}", userIds, e);
      throw new WorkflowAdminException("根据用户ID列表 = " + userIds + "查询用户信息失败", e);
    }
    return userPojoList;
  }

  def Map<String, UserPojo> batchQueryUserPojoMapByUserId(OrgContext context, Set<String> userIds, Integer userStatus) {
    List<UserPojo> userPojoList;
    Map<String, UserPojo> userPojoMap = new HashMap<>();
    userPojoList = this.batchQueryUserPojoByUserId(context, userIds, userStatus);
    if (CollectionUtils.isNotEmpty(userPojoList)) {
      for (UserPojo userPojo : userPojoList) {
        userPojoMap.put(userPojo.getId(), userPojo);
      }
    }
    return userPojoMap;
  }


}
