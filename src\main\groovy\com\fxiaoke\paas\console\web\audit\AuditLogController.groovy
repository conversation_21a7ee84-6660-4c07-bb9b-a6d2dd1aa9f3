package com.fxiaoke.paas.console.web.audit

import com.fxiaoke.paas.console.entity.log.AuditLog
import com.fxiaoke.paas.console.service.metadata.AuditLogService
import com.fxiaoke.paas.console.util.DateFormatUtil
import com.github.autoconf.ConfigFactory
import com.github.shiro.support.ShiroCasRealm
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.PostConstruct
import javax.annotation.Resource

/**
 * 审计日志
 * <AUTHOR>
 * Created on 2018/3/30.
 */
@Controller
@Slf4j
@RequestMapping("/audit")
class AuditLogController {

  private Integer num

  @Autowired
  AuditLogService auditLogService

  @Resource(name = "casRealm")
  private ShiroCasRealm cas

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("paas-console-base", { config ->
      num = config.get("logNum") as Integer
    })
  }

  /**
   * 跳转操作记录列表
   * @return
   */
  @RequestMapping("/list")
  def list() {
    "audit/operate-record"
  }

  /**
   * 查询审计日志
   * @return
   */
  @RequestMapping("/log")
  @ResponseBody
  def auditLog() {
    try {
      List<AuditLog> logList = auditLogService.findLog(num, cas.getCurrentUser().getDisplayName())

      def json = []
      logList.each { log ->
        json << [log.id, DateFormatUtil.formatLong(log.executeTime.getTime()), log.executor, log.description, log.argument]
      }
      return ["data": json]
    } catch (Exception e) {
      log.error("查询审计日志异常,error:", e)
      return ["data": []]
    }
  }

  /**
   * 查询新增日志条数
   * @param maxAuditExecutorTime
   * @return
   */
  @RequestMapping(value = "/new-num", method = RequestMethod.POST)
  @ResponseBody
  def findNumByTime() {
    def count = auditLogService.findNewAuditLog(cas.getCurrentUser().getDisplayName())
    if (count == 0) {
      ["count": ""]
    } else {
      ["count": String.valueOf(count)]
    }
  }


}
