package com.fxiaoke.paas.console.service.metadata

import com.alibaba.fastjson.JSON
import com.fxiaoke.common.Pair
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.paas.console.mapper.metadata.LayoutMapper
import groovy.util.logging.Slf4j
import lombok.NonNull
import org.springframework.stereotype.Service

import javax.annotation.Resource

/**
 * <AUTHOR>
 * Created on 2018/3/5.
 */
@Service
@Slf4j
class LayoutService {
  @Resource
  private LayoutMapper layoutMapper
  @Resource
  private JdbcService jdbcService

  /**
   * 查询layout列表，有jsonb类型的字段处理成json
   * @param tenantId
   * @param describeApiName
   * @return
   */
  List<Map<String, Object>> layoutList(@NonNull String tenantId, @NonNull String describeApiName) {
    Pair<JdbcConnection, String> pair = jdbcService.conn(tenantId, "SELECT * FROM mt_ui_component WHERE tenant_id= '" + tenantId + "' AND ref_object_api_name = '" + describeApiName + "'")

    List<Map<String, Object>> layoutList = layoutMapper.setTenantId(tenantId).findLayoutList2(pair.second)
    layoutList.forEach { layout ->
      if (layout.containsKey("buttons")) {
        layout.put("buttons", JSON.parse(layout.get("buttons")["value"] as String))
      }
      if (layout.containsKey("components")) {
        layout.put("components", JSON.parse(layout.get("components")["value"] as String))
      }
      if (layout.containsKey("top_info")) {
        layout.put("top_info", JSON.parse(layout.get("top_info")["value"] as String))
      }
      if (layout.containsKey("config")) {
        layout.put("config", JSON.parse(layout.get("config")["value"] as String))
      }
    }
    layoutList
  }
}
