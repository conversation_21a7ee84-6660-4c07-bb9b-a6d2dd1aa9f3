<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="renderer" content="webkit">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="format-detection" content="telephone=no">
  <title>PAAS</title>
  <link href="//static.foneshare.cn/oss/datatables-1.10.15/datatables.min.css" rel="stylesheet" type="text/css"/>
  <link href="//static.foneshare.cn/oss/bootstrap-3.3.6/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
  <link href="//static.foneshare.cn/oss/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet" type="text/css"/>
  <link href="//static.foneshare.cn/oss/ionicons-2.0.1/css/ionicons.min.css" rel="stylesheet" type="text/css"/>
  <link href="//static.foneshare.cn/oss/AdminLTE/dist/css/AdminLTE.min.css" rel="stylesheet" type="text/css"/>
  <link href="//static.foneshare.cn/oss/AdminLTE/dist/css/skins/_all-skins.min.css" rel="stylesheet" type="text/css"/>
  <link href="${ctx}/static/css/global.css" rel="stylesheet" type="text/css"/>
  <link href="${ctx}/static/css/jquery.jsonview.css" rel="stylesheet" type="text/css"/>
${headContent}
</head>

<#if header_nav??>
<body class="hold-transition skin-blue fixed sidebar-collapse  sidebar-mini" data-spy="scroll" data-target="#scrollspy">
<#elseif no_nav??>
<body class="hold-transition skin-blue" data-spy="scroll" data-target="#scrollspy">
<#else >
<body class="hold-transition skin-blue fixed  sidebar-mini" data-spy="scroll" data-target="#scrollspy">
</#if>
<div class="wrapper">
<#if !no_nav??>
    <#include "layout-header.ftl" />
    <#include "layout-sidebar.ftl" />
</#if >
  <div class="content-wrapper" #if(no_nav)style="margin:0px;" #end>
  ${breadcrumbContent}
<#include "layout-note.ftl" />
  ${bodyContent}
  </div>

<#if !header_nav?? && !no_nav??><#include "layout-footer.ftl" /></#if>
<#if !no_nav??><#include "layout-control-sidebar.ftl" /></#if>

</div>

<script src="//static.foneshare.cn/oss/js/jquery-2.1.4.min.js"></script>
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/bootstrap.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables-1.10.15/datatables.min.js"></script>
<script src="//static.foneshare.cn/oss/AdminLTE/plugins/fastclick/fastclick.min.js"></script>
<script src="//static.foneshare.cn/oss/AdminLTE/dist/js/app.js"></script>
<script src="//static.foneshare.cn/oss/AdminLTE/plugins/slimScroll/jquery.slimscroll.min.js"></script>
<script src="${ctx}/static/js/global.js"></script>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="${ctx}/static/js/jquery.jsonview.js"></script>

${scriptContent}

<script>
    <#--控制导航栏的js-->
    $("ul.treeview-menu a").each(function () {
      var url = this.href.split("/");
      var pref_url = [];
      for (var i = 0; i < 6; i++) {
        pref_url.push(url[i])
      }
      if (window.location.href.startsWith(pref_url.join('/'))) {
        $(this).parent("li").addClass("active");
        $(this).parents().parents().parents("li").addClass("active");
      }
    });
    //  控制版本
    $('#versionData').html("Copyright &copy; 2011- " + new Date().getFullYear() + " <a href='https://www.fxiaoke.com/' target='_blank'>纷享销客</a>.");

    //控制提示
    <#--setInterval(function () {-->
      <#--$.post("${ctx}/audit/new-num", {}, function (result) {-->
        <#--console.log(result);-->
        <#--$('#auditLogTag').html(result.count);-->
      <#--});-->
    <#--}, 10000);-->


</script>
</body>
</html>
