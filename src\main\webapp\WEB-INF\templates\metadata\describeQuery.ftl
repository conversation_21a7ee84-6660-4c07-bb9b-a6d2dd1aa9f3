<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
    <h1>对象名 / API_Name: ${names.display_name!} / ${names.describe_api_name!}</h1>
    <ol class="breadcrumb">
        <li><a href="${ctx}"><i class="fa fa-dashboard"></i>任务主页</a></li>
        <li><a href="javascript:history.go(-1)"><i class="fa fa-dashboard"></i>对象查询</a>
        <li><a href=""><i class="fa fa-dashboard"></i>详情</a>
        </li>
    </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-header with-border">
                    <ul class="nav nav-tabs" id="myTab">
                        <li role="presentation" class="active"><a href="#field" data-toggle="tab">字段</a></li>
                        <li role="presentation"><a href="#layout" data-toggle="tab">布局</a></li>
                        <li role="presentation"><a href="#data" data-toggle="tab">数据</a></li>
                    </ul>
                </div>
                <div class="box-body">
                    <div class="tab-content">
                        <div class="tab-pane fade in active" id="field">
                            <div>
                                <button id="toggle-btn-field" type="button" class="btn btn-primary">折叠/展开</button>
                            </div>
                            <pre id="fieldList">
                            </pre>
                        </div>
                        <div class="tab-pane fade" id="layout">
                            <div>
                                <button id="toggle-btn-layout" type="button" class="btn btn-primary">折叠/展开</button>
                            </div>
                            <pre id="layoutList">
                            </pre>
                        </div>
                        <div class="tab-pane fade" id="data">
                            <div>
                                <h3>专表:
                                    <small id="tableTitle"></small>
                                </h3>
                                <table id="specialDataDatatable" class="table table-hover table-bordered" cellspacing="0" width="100%">
                                    <thead>
                                    <tr>
                                        <th>数据ID</th>
                                        <th>企业ID</th>
                                        <th>describe_id</th>
                                        <th>对象名称</th>
                                        <th>name</th>
                                        <th>通表ID</th>
                                        <th>详情</th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <HR style="FILTER: progid:DXImageTransform.Microsoft.Glow(color=#987cb9,strength=10)" width="80%" color=#987cb9 SIZE=1>
                            <div>
                                <h3>通表:
                                    <small id="tableTitle2"></small>
                                </h3>
                                <table id="mtDataDatatable" class="table table-hover table-bordered" cellspacing="0" width="100%">
                                    <thead>
                                    <tr>
                                        <th>数据ID</th>
                                        <th>企业ID</th>
                                        <th>对象ID</th>
                                        <th>对象名称</th>
                                        <th>包</th>
                                        <th>name</th>
                                        <th>记录类型</th>
                                        <th>创建时间</th>
                                        <th>修改时间</th>
                                        <th>详情</th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="box-footer clearfix">
                    <div class="clearfix"></div>
                </div>
            </div>
        </div>
    </div>
</section>
<#--模态框-->
<div class="modal fade" id="infoModal" tabindex="-1" role="dialog" aria-labelledby="infoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                        class="sr-only">Close</span></button>
                <h4 class="modal-title" id="stopModalLabel">数据详情</h4>
            </div>
            <div class="modal-body">
                <pre id="dataInfo" style="height: 400px" readonly>
                </pre>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<#--高亮显示-->
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script>
    $(document).ready(function () {

    });

    //json折叠展开
    $('#toggle-btn-field').on('click', function () {
        $('#fieldList').JSONView('toggle');
    });
    $('#toggle-btn-layout').on('click', function () {
        $('#layoutList').JSONView('toggle');
    });

    //初始化显示哪个标签
    $(function () {
        getFieldList();
        $('#myTab a:first').tab('show');
    });

    //当点击标签时执行
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        if (e.target.text === "字段") {
            getFieldList();
        } else if (e.target.text === "布局") {
            getLayoutList();
        } else if (e.target.text === '数据') {
            getDataList();
        }
    });

    function getFieldList() {
        $.ajax({
            url: "${ctx}/metadata/describe-query/field?tenantId=" + ${names.tenant_id!} +"&describeId=" + '${names.describe_id!}',
            contentType: "application/json",
            type: "GET",
            traditional: true,
            success: function (data) {
                $("#fieldList").JSONView(data.fieldList, {collapsed: false, nl2br: true, recursive_collapser: true});
            }
        });
    };

    function getLayoutList() {
        $.ajax({
            url: "${ctx}/metadata/describe-query/layout?tenantId=" + ${names.tenant_id!} +"&apiName=" + '${names.describe_api_name!}',
            contentType: "application/json",
            type: "GET",
            traditional: true,
            success: function (data) {
                $("#layoutList").JSONView(data.layoutList, {nl2br: true, recursive_collapser: true});
            }
        });
    };

    function getDataList() {
        $.ajax({
            url: "${ctx}/metadata/describe-query/data?tenantId=" + ${names.tenant_id!} +"&describeId=" + '${names.describe_id!}',
            contentType: "application/json",
            type: "GET",
            traditional: true,
            success: function (data) {
                var tableType = data.dataResult.tableType;
                if (tableType === "mtData" && !jQuery.isEmptyObject(data.dataResult.dataList)) {
                    $("#tableTitle2").text(data.dataResult.tableName);
                    $("#mtDataDatatable").dataTable().fnClearTable();
                    $("#mtDataDatatable").dataTable().fnAddData(data.dataResult.dataList);
                } else if (tableType === "special" && !jQuery.isEmptyObject(data.dataResult.dataList)) {
                    $("#tableTitle").text(data.dataResult.tableName);
                    $("#specialDataDatatable").dataTable().fnClearTable();
                    $("#specialDataDatatable").dataTable().fnAddData(data.dataResult.dataList);
                }
            }
        });
    };

    $("#mtDataDatatable").DataTable({
        "deferRender": true,
        "ajax": "",
        "language": {
            "url": "${ctx}/static/js/datatables-zh_CN.json"
        },
        // data: data,
        columns: [
            {data: "id", defaultContent: ""},
            {data: "tenant_id", defaultContent: ""},
            {data: "object_describe_id", defaultContent: ""},
            {data: "object_describe_api_name", defaultContent: ""},
            {data: "package", defaultContent: ""},
            {data: "name", defaultContent: ""},
            {data: "record_type", defaultContent: ""},
            {data: "create_time", defaultContent: ""},
            {data: "last_modified_time", defaultContent: ""},
        ],
        "columnDefs": [
            {
                "targets": [9],
                "data": null,
                "render": function (data, type, row, meta) {
                    if (type === 'display') {
                        return "<button value='" + row.id + "," + row.tenant_id + ",mt_data" + "' onclick='dataInfo(value)' class='btn btn-xs btn-info'>详情信息</button>";
                    }
                    return data;
                }
            }
        ],
        "mark": true,
        "iDisplayLength": 50,
        "bSortClasses": true,
        "order": [[8, 'desc']]
    });

    $("#specialDataDatatable").DataTable({
        "deferRender": true,
        "ajax": "",
        "language": {
            "url": "${ctx}/static/js/datatables-zh_CN.json"
        },
        columns: [
            {data: "id", defaultContent: ""},
            {data: "tenant_id", defaultContent: ""},
            {data: "object_describe_id", defaultContent: ""},
            {data: "object_describe_api_name", defaultContent: ""},
            {data: "name", defaultContent: ""},
            {data: "extend_obj_data_id", defaultContent: ""},
        ],
        "columnDefs": [
            {
                "targets": [6],
                "data": null,
                "render": function (data, type, row, meta) {
                    if (type === 'display') {
                        return "<button value='" + row.id + "," + row.tenant_id + "," + $("#tableTitle").text() + "' onclick='dataInfo(value)' class='btn btn-xs btn-info'>详情信息</button>";
                    }
                    return data;
                }
            }
        ],
        "mark": true,
        "iDisplayLength": 50,
        "bSortClasses": true,
        // "order": [[4, 'desc']]
    });

    //模态框
    function dataInfo(row) {
        $('#dataInfo').text("");
        var rows = row.split(",");
        $.ajax({
            url: "${ctx}/metadata/data/info?tenantId=" + rows[1] + "&apiName=" + '${names.describe_api_name!}' + "&id=" + rows[0],
            contentType: "application/json",
            type: "GET",
            traditional: true,
            success: function (data) {
                if (jQuery.isEmptyObject(data.dataInfo)) {
                    return;
                }
                $('#dataInfo').JSONView(data.dataInfo, {
                    collapsed: false,
                    nl2br: true,
                    recursive_collapser: true
                });
            }
        });
        $('#infoModal').modal('show');
    };

</script>
</#assign>
<#include "../layout/layout-main.ftl" />
