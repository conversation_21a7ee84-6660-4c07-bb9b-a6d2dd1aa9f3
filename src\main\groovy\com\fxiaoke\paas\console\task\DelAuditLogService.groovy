package com.fxiaoke.paas.console.task

import com.fxiaoke.paas.console.service.metadata.AuditLogService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

import java.util.concurrent.ConcurrentMap
/**
 * <AUTHOR>
 * Created on 2018/4/3.
 */
@Slf4j
@Component
class DelAuditLogService {

  @Autowired
  AuditLogService auditLogService

//  @Scheduled(initialDelay = 30000L, fixedDelay = 50000L)
//  每月一日1:00触发
  @Scheduled(cron = "0 0 1 1 * ?")
  void delAuditLog() {
    ConcurrentMap
    try {
      auditLogService.deleteAuditLog()
    } catch (Exception e) {
      log.error("删除审计日志失败,时间={},error:", new Date(), e)
    }
  }
}
