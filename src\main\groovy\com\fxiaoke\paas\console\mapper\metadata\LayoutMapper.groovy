package com.fxiaoke.paas.console.mapper.metadata

import com.github.mybatis.mapper.IBatchMapper
import com.github.mybatis.mapper.ICrudMapper
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * Created on 2018/3/5.
 */
interface LayoutMapper extends ICrudMapper, IBatchMapper, ITenant<LayoutMapper> {

  /**
   * 根据tenantId和describeApiName获取layoutList
   * @param tenantId
   * @param describeApiName
   * @return
   */
  @Select("SELECT * FROM mt_ui_component WHERE tenant_id=#{tenantId} AND ref_object_api_name=#{describeApiName}")
  List<Map<String, Object>> findLayoutList(@Param("tenantId") String tenantId, @Param("describeApiName") String describeApiName)

  @Select("\${sql}")
  List<Map<String, Object>> findLayoutList2(@Param("sql") String sql)
}
