<#assign title="实例查询">
<#assign active_nav="workflow_instance">
<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
<style>
    .doSearch {
        width: 8%;
        margin-left: 20px
    }


    .table > thead:first-child > tr:first-child > th {
        text-align: center;
        vertical-align: middle;
    }

    .table > tbody > tr > td {
        text-align: center;
    }

    input {
        width: 10%;
        height: 34px;
        line-height: 34px;
        box-sizing: border-box;
        border-radius: 4px;
        border: 1px solid #c8cccf;
        color: #6a6f77;
        -web-kit-appearance: none;
        -moz-appearance: none;
        outline: 0;
        text-decoration: none;
        margin-left: 20px;
    }

    .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
        width: 10%;
        margin-left: 20px;
    }

</style>
</#assign>
<#assign bodyContent>
    <#assign breadcrumbContent>
    <section class="content-header">
        <h1>流程实例查询</h1>
        <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i>查询</a></li>
        </ol>
    </section>
    </#assign>
<section class="content">
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title"></h3>
                    </div>
                    <form class="form-horizontal" action="${ctx}/biz/add" method="post" id="myForm" role="form"
                          data-toggle="validator">
                        <div lass="container-fluid">
                            <div style="margin-left: 20px" id="advanced-instance">
                                <span>实例状态</span><select display: inline-block; class="selectpicker">
                                <option value="in_progress">进行中</option>
                                <option value="pass">已通过</option>
                                <option value="reject">已拒绝</option>
                                <option value="cancel">已取消</option>
                                <option value="error">已出错</option>
                                <option value="" selected>全部</option>
                            </select>
                                <input placeholder="租户ID"/>
                                <input placeholder="Object_ID"/>
                                <input placeholder="instances_ID"/>
                                <input placeholder="申请人姓名"/>
                                <button type="button" class="doSearch btn btn-primary">查找</button>
                            </div>
                            <div style="margin-left: 20px; margin-top : 20px">
                                <table class="table table-striped table-bordered table-condensed dataTable no-footer"
                                       id="dataTable2">
                                    <thead>
                                    <tr>
                                        <th>企业ID</th>
                                        <th>实例ID</th>
                                        <th>实例状态</th>
                                        <th>申请人/部门</th>
                                        <th>所属流程ID</th>
                                        <th>TASK数量（进行中／所有）</th>
                                        <th>开始时间</th>
                                        <th>完成时间</th>
                                        <th>持续时间</th>
                                        <th>实例定义</th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                </div>
                </form>
            </div>
        </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script>

        $(function () {
            var bootstrapDom = "<'row'<'col-sm-6'l>>" +
                    "<'row'<'col-sm-12'tr>>" +
                    "<'row'<'col-sm-5'i><'col-sm-7'p>>";
            var table = $("#dataTable2").DataTable({
                "dom": bootstrapDom,
                "processing": false,
                "serverSide": true,
                "search": {
                    "regex": true
                },
                "order": [[6, "desc"]],
                "ajax": "${CONTEXT_PATH}/tenant/flow/version/instances?dataObject="+encodeURIComponent(JSON.stringify(getDataObject())),
                "columnDefs": [
                ],
                columns: [
                    {
                        data: "tenantId"
                    },
                    {
                        data: "id", render: function (data, type, row, meta) {
                        return "<a href=\"${CONTEXT_PATH}/workflow/page/tenant/flow/version/instance/tasks?instanceId=" + data
                                + "&appId=" + row.appId + "&tenantId=" + row.tenantId + "\">" + data + "</a>";
                    }
                    },
                    {
                        data: 'state'
                    },
                    {
                        data: "applicantAccountAndDept"
                    },
                    {
                        data: "workflowId", render: function (data, type, row, meta) {
                        return "<a href=\"${CONTEXT_PATH}/workflow/page/tenant/flow/versions/from/link?workflowId=" + data + "&tenantId=" + row.tenantId + "\">" + data + "</a>";
                    }
                    },
                    {
                        data: "inProgressAll"
                    },
                    {
                        data: "start"
                    },
                    {
                        data: "end"
                    },
                    {
                        data: "duration"
                    },
                    {
                        data: "...", render: function (data, type, row, meta) {
                        return "<a href=\"${CONTEXT_PATH}/workflow/page/instance/show?&instanceId=" + row.id + "&tenantId=" + row.tenantId + "\">" + "实例定义" + "</a>";
                    }
                    }
                ],
                "iDisplayLength": 25,
                "sPaginationType": "full_numbers",
                "language": {
                    "processing": true,
                    "lengthMenu": "显示 _MENU_ 项结果",
                    "zeroRecords": "没有匹配结果",
                    "emptyTable": "没有数据",
                    "info": "",
                    "infoEmpty": "",
                    "infoFiltered": "",
                    "infoPostFix": "",
                    "search": "搜索:",
                    "url": "",
                    "paginate": {
                        "first": "首页",
                        "previous": "上页",
                        "next": "下页",
                        "last": "末页"
                    }
                }
            });

            $("#advanced-instance .doSearch").on("click", function () {
                var state, tenantId, objectId, _id, applicantName;
                state = $("#advanced-instance select").eq(0).val();
                tenantId = $("#advanced-instance input").eq(0).val();
                objectId = $("#advanced-instance input").eq(1).val();
                _id = $("#advanced-instance input").eq(2).val();
                applicantName = $("#advanced-instance input").eq(3).val();
                if (tenantId == "") {
                    alert("tenantId不可为空");
                    return false;
                }
                var dataObject = {
                    "tenantId":tenantId,
                    "state":state,
                    "_id":_id,
                    "objectId":objectId,
                    "tenantId":tenantId
                }
                table.ajax.url('${CONTEXT_PATH}/tenant/flow/version/instances?dataObject=' + encodeURIComponent(JSON.stringify(dataObject))).load();
            });
        });
        function getDataObject() {
            var dataObject = {
                "tenantId":"${(tenantId)!""}",
                "workflowId":"${(workflowId)!""}",
                "_id":"${(instanceId)!""}",
                "objectId":"${(objectId)!""}",
                "state":"${(state)!""}",
                "applicantName":"${(applicantName)!""}",
            }
            return dataObject;
        }
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
