package com.fxiaoke.paas.console.bean.datarights

/**
 * Created by yangxw on 2018/3/15.
 */
class Page {
     static final int DEFAULT_PAGESIZE = 10;  //默认分页大小
     static final int DEFAULT_CURRENTPAGE = 1; //默认当前页
     static final long serialVersionUID = -6281452120105837078L;
     long total; //总记录数
     Integer pageSize; //每页大小
     Integer currentPage; //当前页
     Integer totalPage; //总页数


    def Page() {
        this.pageSize = DEFAULT_PAGESIZE;
        this.currentPage = DEFAULT_CURRENTPAGE;
    }

    def Page(Integer pageSize) {
        this.currentPage = DEFAULT_CURRENTPAGE;
        if (pageSize == null || pageSize.intValue() < 0) {
            this.pageSize = DEFAULT_PAGESIZE;
        } else {
            this.pageSize = pageSize;
        }

    }

    def Page(Integer pageSize, Integer currentPage) {
        if (currentPage == null || currentPage.intValue() < 0) {
            this.currentPage = DEFAULT_CURRENTPAGE;
        } else {
            this.currentPage = currentPage;
        }
        if (pageSize == null || pageSize.intValue() < 0) {
            this.pageSize = DEFAULT_PAGESIZE;
        } else {
            this.pageSize = pageSize;
        }
    }


    def getTotal() {
        return total;
    }

    def setTotal(long total) {
        this.total = total;
    }

    def getPageSize() {
        if (pageSize == null || 0 >= pageSize.intValue()) {
            pageSize = DEFAULT_PAGESIZE;
        }
        return pageSize;
    }

    def setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    def getCurrentPage() {
        if (currentPage == null || 0 >= currentPage.intValue()) {
            currentPage = DEFAULT_CURRENTPAGE;
        }
        return currentPage;
    }

    def setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    def getTotalPage() {
        return totalPage;
    }

    def setTotalPage(Integer totalPage) {
        this.totalPage = totalPage;
    }
}
