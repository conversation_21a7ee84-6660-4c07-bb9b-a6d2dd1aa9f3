<#assign headContent>
    <link href="//static.foneshare.cn/oss/AdminLTE/plugins/datatables/dataTables.bootstrap.css" rel="stylesheet"
          type="text/css"/>
    <link type="text/css" href="//static.foneshare.cn/oss/datatables-checkboxes/dataTables.checkboxes.css"
          rel="stylesheet"/>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
    <style>
        #datatable td {
            text-align: left;
        }

        .modal-content {

            margin-top: -250px
        }
    </style>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>任务列表</h1>
        <ol class="breadcrumb">
            <li><a href="../"><i class="fa fa-dashboard"></i>任务主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>Schema迁移任务查询</a></li>
        </ol>
    </section>
</#assign>

<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div id="warningInfo" class="alert alert-warning hide">
                    <span id="closeInfo" href="#" class="close">&times;</span>
                    <strong id="hitInfo"></strong>
                </div>
                <div class="box box-info" style="margin-bottom: 1px;">
                    <div class="box-body" style="padding: 20px;">
                        <form class="form-inline">
                            <label for="tenantId" class="control-label">企业ID</label>
                            <input type="text" class="form-control" style="border-radius:5px;" id="currentTenantId" name="currentTenantId" value=${currentTenantId} placeholder="必填" readonly>
                            <label for="tenantId" class="control-label">企业EA</label>
                            <input type="text" class="form-control" style="border-radius:5px;" id="currentTenantId" name="currentTenantId" value=${currentTenantIdEa} placeholder="必填" readonly>
                            <#if status == 0>
                                <#if buttonDisable>
                                    <button type="button" id="createTask" onclick="createTaskFunction('${firstAction}','execute','迁移','${currentItemId}')" class="btn btn-primary" disabled>
                                        开始迁移
                                    </button>
                                <#else>
                                    <button type="button" id="createTask" onclick="createTaskFunction('${firstAction}','execute','迁移','${currentItemId}')" class="btn btn-primary">
                                        开始迁移
                                    </button>
                                </#if>
                            </#if>
                            <button type="button" class="btn btn-default" style="float: right" onclick="history.back()">返回</button>
                        </form>
                    </div>
                    <div class="box-footer clearfix" style="padding: 2px;">
                        <div class="clearfix"></div>
                    </div>
                </div>
                <div class="box box-info">
                    <div class="box-body">
                        <table id="datatable" class="table table-hover table-bordered" cellspacing="0" width="100%">
                            <thead>
                            <tr>
                                <th id="taskId" class="col-sm-1" style="table-layout: fixed;">taskId</th>
                                <th>名称</th>
                                <th>行为</th>
                                <th>手动or自动</th>
                                <th>状态</th>
                                <th>顺序</th>
                                <th>itemOperation</th>
                                <th>itemId</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="box-footer clearfix">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="modal fade" id="editTaskItemModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                                class="sr-only">Close</span></button>
                    <h4 class="modal-title" id="stopModalLabel">编辑</h4>
                </div>
                <form class="form-horizontal" action="${ctx}/hamster/edit_task_item" method="post" role="form" data-toggle="validator">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="taskIdEdit" class="col-sm-3 control-label">任务Id</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px;" id="taskIdEdit" name="taskId" value="" placeholder="必填" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="ItemNameEdit" class="col-sm-3 control-label">名字</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px;" id="ItemNameEdit" name="taskItemName" value="" placeholder="必填" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="actionEdit" class="col-sm-3 control-label">行为</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px;" id="actionEdit" name="itemAction" value="" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="itemActionTypeEdit" class="col-sm-3 control-label">ActionType</label>
                            <div class="col-sm-4">
                                <select id="itemActionTypeEdit" name="ActionType" style="border-radius:5px;" class="selectpicker show-tick form-control" title="" data-live-search="false" placeholder="必填" required>
                                    <option id="auto" value="1">自动</option>
                                    <option id="manual" value="2">手动</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="itemStatusEdit" class="col-sm-3 control-label">状态</label>
                            <div class="col-sm-4">
                                <select id="itemStatusEdit" name="itemStatus" style="border-radius:5px;" class="selectpicker show-tick form-control" title="" data-live-search="false" placeholder="必填" required>
                                    <option id="readyItemStatus" value="0">ready</option>
                                    <option id="runningItemStatus" value="1">running</option>
                                    <option id="successItemStatus" value="2">success</option>
                                    <option id="errorItemStatus" value="3">error</option>
                                    <option id="pauseItemStatus" value="4">pause</option>
                                    <option id="execute_waitingItemStatus" value="5">execute_waiting</option>
                                    <option id="pause_waitingItemStatus" value="6">pause_waiting</option>
                                    <option id="resume_waitingItemStatus" value="7">resume_waiting</option>
                                    <option id="rollback_waitingItemStatus" value="8">rollback_waiting</option>
                                    <option id="pausingItemStatus" value="9">pausing</option>
                                    <option id="rolling_backItemStatus" value="10">rolling_back</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="itemOrderEdit" class="col-sm-3 control-label">顺序</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" style="border-radius:5px;" id="itemOrderEdit" name="itemOrder" value="" placeholder="必填" required>
                            </div>
                        </div>

                        <div class="modal-footer">
                            <a type="button" class="btn btn-default" data-dismiss="modal">取消</a>
                            <button type="button" class="btn btn-primary">提交</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="modal fade" id="queryLogModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog" style="display: inline-block; width: auto;">
            <div class="modal-content" style="height:800px; width: 900px">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true"></span><span
                                class="sr-only">Close</span></button>
                    <h4 class="modal-title" id="stopModalLabel">信息提示栏</h4>
                </div>
                <div class="modal-body" style="height:700px; overflow:auto; width: 900px">
                <pre id="errorLog">
                </pre>
                </div>
            </div>
        </div>
    </div>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script type="application/javascript">
        function checkStatus(data) {
            if (data == 0) {
                return "<span class='btn btn-success btn-xs'>ready</span>"
            } else if (data == 1) {
                return "<span class='btn btn-warning btn-xs'>running</span>"
            } else if (data == 2) {
                return "<span class='btn btn-success btn-xs'>success</span>"
            } else if (data == 3) {
                return "<span class='btn btn-danger btn-xs'>error</span>"
            } else if (data == 4) {
                return "<span class='btn btn-warning btn-xs'>pause</span>"
            } else if (data == 5) {
                return "<span class='btn btn-warning btn-xs'>execute_waiting</span>"
            } else if (data == 6) {
                return "<span class='btn btn-warning btn-xs'>pause_waiting</span>"
            } else if (data == 7) {
                return "<span class='btn btn-warning btn-xs'>resume_waiting</span>"
            } else if (data == 8) {
                return "<span class='btn btn-warning btn-xs'>rollback_waiting</span>"
            } else if (data == 9) {
                return "<span class='btn btn-warning btn-xs'>pausing</span>"
            } else if (data == 10) {
                return "<span class='btn btn-warning btn-xs'>rolling_back</span>"
            }
        }

        function checkAction(data) {
            if (data == 1) {
                return "<span class='btn btn-success btn-xs'>自动</span>"
            } else if (data == 2) {
                return "<span class='btn btn-warning btn-xs'>手动</span>"
            }
        }

        function checkStatusV2(taskId, status, action, operation, itemId) {
            var html = "";
            var execute = 'execute'
            var rollback = 'rollback'
            var executeChinese = '迁移'
            var rollbackChinese = '回滚'
            var skipChinese = '跳过'
            var successLogType = 'success'
            var errorLogType = 'error'
            if (${status} != 0) {
                if (status == 0) {
                    html += "<span class='btn btn-success btn-xs' onclick='createTaskFunction(\"" + action + "\"  ,  \"" + execute + "\"  ,  \"" + executeChinese + "\" ,  \"" + itemId + "\")'>迁移</span>"
                } else if (status == 2) {
                    html += "<span class='btn btn-danger btn-xs' onclick='createTaskFunction(\"" + action + "\" , \"" + rollback + "\" ,  \"" + rollbackChinese + "\" ,  \"" + itemId + "\")'>回滚</span>"
                    html += "<span class='btn btn-primary btn-xs' onclick='viewMessages(\"" + taskId + "\" , \"" + action + "\" , \"" + successLogType + "\" )'>查看成功信息</span>"
                } else if (status == 3) {
                    html += "<span class='btn btn-danger btn-xs' onclick='createTaskFunction(\"" + action + "\" , \"" + rollback + "\" ,  \"" + rollbackChinese + "\" ,  \"" + itemId + "\")'>回滚</span>"
                    html += "<span class='btn btn-primary btn-xs' onclick='viewMessages(\"" + taskId + "\" , \"" + action + "\" , \"" + errorLogType + "\" )'>查看错误信息</span>"
                    if (operation == 31) {
                        html += "<span class='btn btn-danger btn-xs' onclick='skipTaskFunction(\"" + action + "\" , \"" + operation + "\" ,  \"" + skipChinese + "\" ,  \"" + itemId + "\")')'>跳过</span>"
                    }
                }

            }
            return html;
        }


        $(document).ready(function () {
            var table = $("#datatable").DataTable({
                "processing": true,
                "ajax": {
                    url: "${ctx}/hamster/query-task-item-list?taskId=${taskId}",
                    type: 'GET'
                },
                "columnDefs": [
                    {
                        "targets": 0,
                        "visible": false
                    },
                    {
                        "targets": 3,
                        "render": function (data, type, row, meta) {
                            return checkAction(data);
                        }
                    },
                    {
                        "targets": 4,
                        "render": function (data, type, row, meta) {
                            return checkStatus(data);
                        }
                    },
                    {
                        "targets": 6,
                        "visible": false
                    },
                    {
                        "targets": 7,
                        "visible": false
                    },
                    {
                        // 定义操作列,######以下是重点########
                        "targets": 8,//操作按钮目标列
                        "render": function (data, type, row, meta) {
                            return checkStatusV2(row[0], row[4], row[2], row[6], row[7]);
                        }
                    }
                ],
                "language": {
                    "url": "${ctx}/static/js/datatables-zh_CN.json"
                },
                "mark": true,
                "displayLength": 25
            });

            //timeouts.push(setTimeout("machKanbanMethod1233()",10000))//注十秒刷新数据

            //定时器
            var timer = setInterval(myrefresh, 5000); //指定 秒刷新一次
            function myrefresh() {
                reload(timer);
            };

            //重新加载
            function reload(timer) {
                if (${status} === 2) {
                    clearInterval(timer);
                }
                var dataTable = $("#datatable").DataTable();
                dataTable.ajax.reload();
            };
        });

        function editHamsterTaskItem(taskId, action) {
            $.getJSON("${CONTEXT_PATH}/hamster/query-task-item", {
                taskId: taskId,
                action: action
            }, function (data) {
                var taskItemPojo = data.data;
                $('#taskIdEdit').val(taskItemPojo.taskId);
                $('#ItemNameEdit').val(taskItemPojo.name);
                $('#actionEdit').val(taskItemPojo.action);
                $('#itemOrderEdit').val(taskItemPojo.order);
                $('#itemActionTypeEdit.selectpicker').selectpicker('val', taskItemPojo.actionType);
                $('#itemStatusEdit.selectpicker').selectpicker('val', taskItemPojo.status);
                $('#editTaskItemModal').modal();
            });
        }

        function viewMessages(taskId, action, type) {
            $.getJSON("${ctx}/hamster/query-hamster-log", {
                taskId: taskId,
                action: action,
                type: type
            }, function (data) {
                $('#errorLog').JSONView(data, {
                    collapsed: false,
                    nl2br: true,
                    recursive_collapser: true
                });
                $("#queryLogModal").modal('show');
            });
        }

        function createTaskFunction(action, operation, chinese, itemId) {
            if (confirm("确定" + chinese + "吗")) {
                $.ajax({
                    url: '${ctx}/hamster/start-migration?taskId=${taskId}&action=' + action + '&operation=' + operation + '&itemId=' + itemId,
                    contentType: "application/json",
                    dataType: 'json',
                    traditional: true,
                    success: function (data) {
                        if (data.code === 200) {
                            alert("开始" + chinese + "！");
                            window.location.reload();
                        } else {
                            alert("执行" + data.info + "!");
                        }
                    }
                })
            }
        }

        function skipTaskFunction(action, operation, chinese, itemId) {
            if (confirm("确定" + chinese + "吗")) {
                $.ajax({
                    url: '${ctx}/hamster/skip-task-item?taskId=${taskId}&action=' + action + '&operation=' + operation + '&itemId=' + itemId,
                    contentType: "application/json",
                    dataType: 'json',
                    traditional: true,
                    success: function (data) {
                        if (data.code === 200) {
                            alert("开始" + chinese + "！");
                            window.location.reload();
                        } else {
                            alert("执行失败！");
                        }
                    }
                })
            }
        }

    </script>
</#assign>
<#include "../layout/layout-main.ftl" />