<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>模拟场景筛选</h1>
        <ol class="breadcrumb">
            <li><a href="${ctx}"><i class="fa fa-dashboard"></i>主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>模拟场景筛选</a></li>
        </ol>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info row">
                    <div class="box-header with-border">
                    </div>
                    <form class="form-horizontal" action="" method="post" id="infoFrom" role="form" data-toggle="validator">
                        <div class="box-body col-xs-6" style="width: 100%">
                            <div class="form-group">
                                <label for="tenantId" class="col-sm-4 control-label">product_id</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" style="border-radius:5px;width: 300px;float: bottom" id="productId" value="${productId}" placeholder="必填" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="userId" class="col-sm-4 control-label">tenant_id</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" style="border-radius:5px;width: 300px" id="tenantId" value="${tenantId}" placeholder="必填" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="describeApiName" class="col-sm-4 control-label">product_name</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" style="border-radius:5px;width: 300px" id="productName" value="${productName}" placeholder="必填" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="isAuth" class="col-sm-4 control-label">product_version</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" style="border-radius:5px;width: 300px" id="productVersion" value="${productVersion}" placeholder="必填" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="isAuth" class="col-sm-4 control-label"></label>
                                <div class="col-sm-4">
                                    <input id="cancel_btn" class="btn" type="button" value="保存"/>
                                    <input id="cancel_btn" class="btn" type="button" value="返回" style="float: right" onclick="history.back()"/>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<#--表单验证插件-->
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script>

    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
