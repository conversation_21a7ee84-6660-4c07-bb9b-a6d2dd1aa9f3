package com.fxiaoke.paas.console.entity.log

import com.github.mybatis.annotation.DynamicTypeHandler
import com.github.mybatis.entity.IdEntity
import groovy.transform.ToString
import groovy.transform.builder.Builder
import lombok.Getter
import lombok.Setter

import javax.persistence.Table
/**
 * <AUTHOR>
 * Created on 2018/4/2.
 */
@Setter
@Getter
@ToString
@Builder
@Table(name = "console_log")
class  AuditLog extends IdEntity {
  //执行人
  String executor
  //执行的操作说明
  String description
  //参数(动态转换Json字符串注解)
  @DynamicTypeHandler("com.github.mybatis.handler2.JSONBStringTypeHandler")
  String argument
  //执行的时间
  Date executeTime
//  String status
}
