<?xml version="1.0" encoding="UTF-8" ?>
  <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fxiaoke.paas.console.mapper.organization.DepartmentMapper">

<resultMap id="departmentEntity" type="com.fxiaoke.paas.console.entity.organization.DepartmentEntity">
  <id property="id" column="id"></id>
  <result property="deptId" column="dept_id"></result>
  <result property="tenantId" column="tenant_id"></result>
  <result property="name" column="name"></result>
  <result property="managerId" column="manager_id"></result>
  <result property="parentId" column="parent_id"></result>
  <result property="status" column="status"></result>
  <result property="description" column="description"></result>
  <result property="createBy" column="create_by"></result>
  <result property="createTime" column="create_time"></result>
  <result property="lastModifiedBy" column="last_modified_by"></result>
  <result property="lastModifiedTime" column="last_modified_time"></result>
  <result property="isDeleted" column="is_deleted"></result>
</resultMap>

<select id="getDepartmentIdByCompanyId" resultType="departmentEntity">
        SELECT dept_id, name FROM org_dept WHERE tenant_id = #{tenantId}
    </select>

<select id="getDepartment" resultType="departmentEntity">
  SELECT * FROM org_dept WHERE tenant_id = #{tenantId}
  <if test="deptIds.size() > 0">
    AND dept_id IN
    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
      #{deptId}
    </foreach>
  </if>
</select>

<select id="getSubordinateDepartment" resultType="departmentEntity">
  SELECT * FROM org_dept WHERE tenant_id = #{tenantId}
  <choose>
    <when test="parentId == null">AND parent_id is NULL</when>
    <when test="parentId != null">AND parent_id = #{parentId} </when>
  </choose>
</select>

<select id="getRelationalDepartment" resultType="departmentEntity">
  SELECT org_dept.* FROM org_dept,org_dept_user WHERE org_dept_user.tenant_id = #{tenantId}
  AND org_dept_user.user_id = #{userId}
  <if test="type != null">
    AND org_dept_user.type = #{type}
  </if>
</select>

<select id="listParentId" resultType="java.lang.String">
  SELECT DISTINCT parent_id FROM org_dept WHERE tenant_id = #{tenantId}AND parent_id IN
  <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
    #{deptId}
  </foreach>
</select>
</mapper>