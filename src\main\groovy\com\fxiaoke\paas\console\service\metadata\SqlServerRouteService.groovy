package com.fxiaoke.paas.console.service.metadata

import com.fxiaoke.common.PasswordUtil
import com.fxiaoke.paas.console.util.metadata.DbUtil
import com.github.autoconf.ConfigFactory
import com.github.autoconf.helper.ConfigHelper
import com.google.common.collect.Maps
import com.google.common.collect.Range
import com.google.common.collect.RangeMap
import com.google.common.collect.TreeRangeMap
import groovy.util.logging.Slf4j
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import java.sql.Connection
import java.sql.ResultSet
import java.sql.SQLException
import java.sql.Statement
import java.text.MessageFormat

/**
 * <AUTHOR>
 * Created on 2018/5/30.
 */
@Slf4j
@Service
class SqlServerRouteService {
  private String user
  private String password
  private String jdbcUrl

  private static final String jdbc_url_template = "jdbc:sqlserver://{0}:1433;DatabaseName={1}"
  private Map<String, String> singleMap
  private RangeMap<Integer, String> rangeMap
  private long expiredTime = 0
  private long refresh_interval = 60000

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("db-paas-console", { config ->
      user = config.get("db.sqlserver.route.user")
      password = config.get("db.sqlserver.route.password")
      boolean dbEncrypt = config.getBool("db.sqlserver.route.password.encrypt", false)
      if (dbEncrypt) {
        password = PasswordUtil.decode(password)
      }
      jdbcUrl = config.get("db.sqlserver.route.url")
    })
  }

  String getUrl(Integer ei) {
    def profile = ConfigHelper.getProcessInfo().getProfile()
    if ("ceshi113" == profile) {
      return "******************************************************"
    }
    if ("fstest" == profile) {
      return "******************************************************"
    }
    refresh()
    def url = singleMap.get(ei)
    if (null == url) {
      url = rangeMap.get(ei)
    }
    return url
  }

  void refresh() {
    if (System.currentTimeMillis() > expiredTime) {
      synchronized (this) {
        long start = System.currentTimeMillis()
        if (System.currentTimeMillis() > expiredTime) {
          singleMap = getSingle()
          rangeMap = getSection()
          expiredTime = System.currentTimeMillis() + refresh_interval
          log.info("load sqlserver route cost ${System.currentTimeMillis() - start} million seconds")
        }
      }
    }
  }

  Map<Integer, String> getSingle() {
    Connection connection
    Statement statement
    ResultSet resultSet
    try {
      connection = getConnection()
      String sql = "SELECT\n" +
              "    single.EnterpriseID AS ei,\n" +
              "    CONCAT('172.17.', CAST(SUBSTRING(tb.RServerName, 5, 3) AS INT), '.', CAST(SUBSTRING(tb.RServerName, 8, 3) AS INT)) AS ip,\n" +
              "    LOWER(single.DbName) AS db_name\n" +
              "FROM\n" +
              "    EnterpriseSingle single\n" +
              "    INNER JOIN RWServerTB tb ON single.dbserverip = tb.RWServerName"
      statement = connection.createStatement()
      resultSet = statement.executeQuery(sql)

      Map<Integer, String> result = Maps.newHashMap()
      while (resultSet.next()) {
        result.put(resultSet.getInt(1), MessageFormat.format(jdbc_url_template, resultSet.getString(2), resultSet.getString(3)))
      }
      return result
    } catch (SQLException e) {
      log.error("route query failed", e)
      return null
    } finally {
      if (resultSet != null) {
        resultSet.close()
      }
      if (connection != null) {
        statement.close()
      }
      if (connection != null) {
        connection.close()
      }
    }

  }

  RangeMap<Integer, String> getSection() {
    Connection connection
    Statement statement
    ResultSet resultSet
    try {
      connection = getConnection()
      String sql = "SELECT\n" +
              "    range.EnterpriseIDBegin AS ei_begin,\n" +
              "    range.EnterpriseIDEnd AS ei_end,\n" +
              "    CONCAT('172.17.', CAST(SUBSTRING(tb.RServerName, 5, 3) AS INT), '.', CAST(SUBSTRING(tb.RServerName, 8, 3) AS INT)) as ip,\n" +
              "    LOWER(range.DbName) AS db_name\n" +
              "FROM\n" +
              "    EnterpriseSection range\n" +
              "    INNER JOIN RWServerTB tb ON range.dbserverip = tb.RWServerName"
      statement = connection.createStatement()
      resultSet = statement.executeQuery(sql)
      TreeRangeMap<Integer, String> rangeMap = TreeRangeMap.create()
      while (resultSet.next()) {
        rangeMap.put(Range.closed(resultSet.getInt(1), resultSet.getInt(2)),
                MessageFormat.format(jdbc_url_template, resultSet.getString(3), resultSet.getString(4)))
      }
      return rangeMap
    } catch (SQLException e) {
      log.error("route query failed", e)
      return null
    } finally {
      if (resultSet != null) {
        resultSet.close()
      }
      if (connection != null) {
        statement.close()
      }
      if (connection != null) {
        connection.close()
      }
    }
  }

  Connection getConnection() {
    return DbUtil.createConnect(jdbcUrl, user, password)
  }
}
