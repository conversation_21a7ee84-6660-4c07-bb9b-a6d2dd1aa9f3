<#assign title="全局搜索查询和刷新">
<#assign active_nav="hubble_query_refresh">
<#assign headContent>
        <style>
          .refresh {
            width: 8%;
            margin-left: 20px
          }

          .testRender {
            width: 8%;
            margin-left: 20px
          }

          .refreshTenantId {
            width: 8%;
            margin-left: 20px
          }

          .testDescribe {
            width: 8%;
            margin-left: 20px
          }

          .query {
            width: 8%;
            margin-left: 20px
          }

          input {
            margin-left: 10px;
          }

          #dataTable2 th {
            vertical-align: middle;
            align-items: center;
          }

          #dataTable2 td {
            vertical-align: middle;
            align-items: center
          }

          .table > thead:first-child > tr:first-child > th {
            text-align: center;
            vertical-align: middle;
          }

          .table > tbody > tr > td {
            text-align: center;
          }

          input {
            width: 10%;
            height: 34px;
            line-height: 34px;
            box-sizing: border-box;
            border-radius: 4px;
            border: 1px solid #c8cccf;
            color: #6a6f77;
            -web-kit-appearance: none;
            -moz-appearance: none;
            outline: 0;
            text-decoration: none;
          }

        </style>
</#assign>
<#assign bodyContent>
    <#assign breadcrumbContent>
    <section class="content-header">
      <h1>全局搜索查询和刷新</h1>
    </section>
    </#assign>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <form class="form-horizontal" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="config_refresh">
              <pre style="font-size:22px;font-weight:bold">刷某个对象的全局搜索索引:</pre>
              <input placeholder="tenantId"/>
              <input placeholder="apiName"/>
              <button type="button" class="refresh btn btn-primary">开始</button>
            </div>
          </div>
        </form>
        <form class="form-horizontal" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="test_render">
              <pre style="font-size:22px;font-weight:bold">Hubble-index生成索引:</pre>
              <input placeholder="tenantId"/>
              <input placeholder="apiName"/>
              <input placeholder="id"/>
              <button type="button" class="testRender btn btn-primary">开始</button>
            </div>
          </div>
        </form>
        <form class="form-horizontal" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="refresh_tenant_id">
              <pre style="font-size:22px;font-weight:bold">刷某个企业的所有对象的全局搜索索引：</pre>
              <input placeholder="tenantId"/>
              <button type="button" class="refreshTenantId btn btn-primary">开始</button>
              </br>
            </div>
          </div>
        </form>
        <form class="form-horizontal" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="hubble_query">
              <pre style="font-size:22px;font-weight:bold">全局搜索：</pre>
              <input placeholder="tenantId" id="tenantId"/>
              <input placeholder="userId" id="userId"/>
              <input placeholder="keyword" id="keyword"/>
              <input placeholder="searchApiNames(,隔开)" id="searchApiNames"/>
              <input placeholder="size" id="size"/>
              <button type="button" class="query btn btn-primary">Search</button>
            </div>
            <br/>
          </div>
        </form>
        <form class="form-horizontal" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="test_describe">
              <pre style="font-size:22px;font-weight:bold">Hubble-index查描述：</pre>
              <input placeholder="tenantId" id="tenantId"/>
              <input placeholder="apiName" id="apiName"/>
              <button type="button" class="testDescribe btn btn-primary">Search</button>
            </div>
            <br/>
          </div>
        </form>
      </div>
    </div>
  </div>
  <div id="json">
  </div>
</section>
</#assign>
<#assign scriptContent>

<script>


  $(".refresh").on("click", function () {
    var tenantId = $("#config_refresh input").eq(0).val();
    var apiName = $("#config_refresh input").eq(1).val();
    if (tenantId == "") {
      alert("企业ID不可为空!");
      return false;
    }
    if (apiName == "") {
      alert("ApiName不可为空");
      return false;
    }
    $.ajax({
      type: 'POST',
      url: '${CONTEXT_PATH}/hubble/config/refresh',
      data: {
        tenantId: tenantId,
        apiName: apiName
      },
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        if (result.errCode == 0) {
          alert("执行成功")
        } else {
          alert(result.errMessage)
        }
      },
      error: function (error) {
        alert('网络异常');
      }
    })
  });


  $(".testRender").on("click", function () {
    var tenantId = $("#test_render input").eq(0).val();
    var apiName = $("#test_render input").eq(1).val();
    var id = $("#test_render input").eq(2).val();
    if (tenantId == "") {
      alert("企业ID不可为空!");
      return false;
    }
    if (apiName == "") {
      alert("apiName不可为空!");
      return false;
    }
    if (id == "") {
      alert("id不可为空!");
      return false;
    }

    $.ajax({
      type: 'POST',
      url: '${CONTEXT_PATH}/hubble/test/render',
      data: {
        tenantId: tenantId,
        apiName: apiName,
        id: id
      },
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        if (result.errCode == 0) {
          $("#json").JSONView(eval(data.result));
        } else {
          alert(result.errMessage)
        }
      },
      error: function (error) {
        alert('网络异常');
      }
    })
  });

  $(".refreshTenantId").on("click", function () {
    var tenantId = $("#refresh_tenant_id input").eq(0).val();
    if (tenantId == "") {
      alert("企业ID不可为空!");
      return false;
    }
    $.ajax({
      type: 'POST',
      url: '${CONTEXT_PATH}/hubble/config/refresh_tenant_id',
      data: {
        tenantId: tenantId
      },
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        if (result.errCode == 0) {
          alert("执行成功")
        } else {
          alert(result.errMessage)
        }
      },
      error: function (error) {
        alert('网络异常');
      }
    })
  });


  $(".testDescribe").on("click", function () {
    var tenantId = $("#test_describe input").eq(0).val();
    var apiName = $("#test_describe input").eq(1).val();
    if (tenantId == "") {
      alert("企业ID不可为空!");
      return false;
    }
    if (apiName == "") {
      alert("ApiName不可为空");
      return false;
    }

    $.ajax({
      type: 'POST',
      url: '${CONTEXT_PATH}/hubble/test/describe',
      data: {
        tenantId: tenantId,
        apiName: apiName
      },
      dataType: 'json',
      async: false,
      success: function (data) {
        if (data.errCode == 0) {
          $("#json").JSONView(eval(data.result));
        } else {
          alert("服务器异常");
        }
      },
      error: function (error) {
        alert('网络异常');
      }

    });
  });

  $(".query").on("click", function () {
    var tenantId = $("#hubble_query input").eq(0).val();
    var userId = $("#hubble_query input").eq(1).val();
    var keyword = $("#hubble_query input").eq(2).val();
    var searchApiNames = $("#hubble_query input").eq(3).val();
    var size = $("#hubble_query input").eq(4).val();
    if (tenantId == "") {
      alert("企业ID不可为空!");
      return false;
    }
    if (userId == "") {
      alert("员工ID不可为空");
      return false;
    }
    if (keyword == "") {
      alert("keyword不可为空");
      return false;
    }
    if (searchApiNames == "") {
      alert("searchApiNames不可为空");
      return false;
    }
    if (size == "") {
      alert("size不可为空");
      return false;
    }
    $.ajax({
      type: 'POST',
      url: '${CONTEXT_PATH}/hubble/query',
      data: {
        tenantId: tenantId,
        userId: userId,
        keyword: keyword,
        searchApiNames: searchApiNames,
        size: size
      },
      dataType: 'json',
      async: false,
      success: function (data) {
        if (data.errCode == 0) {
          $("#json").JSONView(eval(data.result));
        } else {
          alert("服务器异常");
        }
      },
      error: function (error) {
        alert('网络异常');
      }

    });
  });


</script>
</#assign>
<#include "../layout/layout-main.ftl" />
