package com.fxiaoke.paas.console.mapper.datarights

import com.fxiaoke.paas.console.entity.datarights.Personnel
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2019/4/1 上午10:42
 *
 */
@ContextConfiguration(locations = ["classpath:mapperContext.xml"])
class PersonnelMapperTest  extends Specification{
@Autowired
private PersonnelMapper personnelMapper

  def "queryPersonnelByObjectId"() {
    given:
    Personnel personnel = personnelMapper.setTenantId("71586").queryPersonnelByObjectId("71586", "102", "5c6688b6a5083d0a116ae7fa")
    println(personnel.tenantId + " " + personnel.userId + " " + personnel.groupId + " " + personnel.department + " " + personnel.role + " " + personnel.superior + " " + personnel.subordinates + " " + personnel.groupName)
  }
}
