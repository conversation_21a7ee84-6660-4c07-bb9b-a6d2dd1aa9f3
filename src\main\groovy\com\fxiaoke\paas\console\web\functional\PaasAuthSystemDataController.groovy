package com.fxiaoke.paas.console.web.functional

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.service.functional.PaasAuthService
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody

/**
 * <AUTHOR> @date 2020/9/8
 */
@Controller
@Slf4j
@RequestMapping("/paas-auth/system")
class PaasAuthSystemDataController {

  @Autowired
  private PaasAuthService paasAuthService

  @RequestMapping("")
  def paasAuthSystem() {
    return "functional/add-system-data"
  }

  @PostMapping("/add-func")
  @ResponseBody
  @SystemControllerLog(description = "功能权限 -- addFunc")
  def addFunc(@RequestBody String data) {
    try {
      if (StringUtils.isBlank(data)) {
        log.error("关键词为空")
        return ["code": 403, "error": "关键词为空"]
      }
      def funcArray = JSONObject.parseArray(data)
//      for (i in 0..<funcArray.size()) {
//        def appId = funcArray.getJSONObject(i).getString("appId")
//        if ("CRM" != appId) {
//          return ["code": 403, "error": "appId错误"]
//        }
//      }

      paasAuthService.addFunc(funcArray)
      return ["code": 200, "data": ""]
    } catch (Exception e) {
      log.error("添加功能失败,error:", e)
      return ["code": 500, "error": "添加功能失败", "data": []]
    }
  }

  @PostMapping("/add-funcAccess")
  @ResponseBody
  @SystemControllerLog(description = "功能权限 -- addFuncAccess")
  def addFuncAccess(@RequestBody String data) {
    try {
      if (StringUtils.isBlank(data)) {
        log.error("关键词为空")
        return ["code": 403, "error": "关键词为空"]
      }
      def funcAccessArray = JSONObject.parseArray(data)
//      for (i in 0..<funcAccessArray.size()) {
//        def appId = funcAccessArray.getJSONObject(i).getString("appId")
//        if ("CRM" != appId) {
//          return ["code": 403, "error": "appId错误"]
//        }
//      }

      paasAuthService.addFuncAccess(funcAccessArray)
      return ["code": 200, "data": ""]
    } catch (Exception e) {
      log.error("添加角色功能关系失败,error:", e)
      return ["code": 500, "error": "添加角色功能关系失败", "data": []]
    }
  }

  @PostMapping("/add-menu")
  @ResponseBody
  @SystemControllerLog(description = "功能权限 -- addMenu")
  def addMenu(@RequestBody String data) {
    try {
      if (StringUtils.isBlank(data)) {
        log.error("关键词为空")
        return ["code": 403, "error": "关键词为空"]
      }
      def menuArray = JSONObject.parseArray(data)

      paasAuthService.addMenu(menuArray)
      return ["code": 200, "data": ""]
    } catch (Exception e) {
      log.error("添加菜单失败,error:", e)
      return ["code": 500, "error": "添加菜单失败", "data": []]
    }
  }
}
