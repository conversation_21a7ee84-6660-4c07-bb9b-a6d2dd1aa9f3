<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>自定义对象恢复数据</h1>
        <ol class="breadcrumb">
            <li><a href="${ctx}"><i class="fa fa-dashboard"></i>主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>自定义对象恢复数据</a></li>
        </ol>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info row">
                    <div class="box-header with-border">
                    </div>
                    <form class="form-horizontal" action="" method="post" id="infoFrom" role="form" data-toggle="validator">
                        <div class="box-body col-xs-6">
                            <div class="form-group">
                                <label for="tenantId" class="col-sm-4 control-label">tenantId</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" style="border-radius:5px;" id="tenantId" placeholder="企业ID(必填)" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="describeApiName" class="col-sm-4 control-label">describeApiName</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" style="border-radius:5px;" id="describeApiName" placeholder="describeApiName(必填)" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="startTime" class="col-sm-4 control-label">开始时间</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" style="border-radius:5px;" id="startTime" placeholder="时间戳（毫秒）">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="endTime" class="col-sm-4 control-label">结束时间</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" style="border-radius:5px;" id="endTime" placeholder="时间戳（毫秒）">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="dataId" class="col-sm-4 control-label">dataId</label>
                                <div class="col-sm-4">
                                    <textarea id="dataId" rows="5" class="form-control" style="border-radius:5px;" placeholder="数据ID(逗号隔开)"></textarea>
                                </div>
                            </div>
                            <div class="col-sm-offset-3">
                                <button type="button" id="restoreData" class="btn btn-primary">数据ID恢复</button>
                                <button type="button" id="batchRestoreData" class="btn btn-primary">时间范围恢复</button>
                                <input id="cancel_btn" class="btn" type="button" value="返回" onclick="history.back()"/>
                            </div>
                        </div>
                        <div class="box-body col-xs-6">
                            <h4>Result <i class="fa fa-hand-o-down"></i></h4>
                            <pre id="dataInfo" style="">
                        </pre>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<#--表单验证插件-->
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script>
        $('#restoreData').on('click', function () {
            if ($('#tenantId').val() == ""){
                alert("企业ID不可为空!");
                return false;
            }

            if ($('#describeApiName').val() == ""){
                alert("describeApiName不可为空!");
                return false;
            }

            if ($('#dataId').val() == ""){
                alert("数据ID不可为空!");
                return false;
            }

            $('#dataInfo').text("");
            $('#restoreData').attr("disabled", "disabled");

            $.ajax({
                type:"POST",
                url:"${CONTEXT_PATH}/metadata-restore/restoreData",
                data:{
                    tenantId: $('#tenantId').val(),
                    describeApiName: $('#describeApiName').val(),
                    dataId: $('#dataId').val()
                },
                dataType:"json",
                success:function(data){
                    if (data !== "") {
                        $('#restoreData').removeAttr("disabled");
                        $('#dataInfo').JSONView(data, {
                            collapsed: false,
                            nl2br: true,
                            recursive_collapser: true
                        });
                    }
                }
            })

        });

        $('#batchRestoreData').on('click', function () {
            if ($('#tenantId').val() == ""){
                alert("企业ID不可为空!");
                return false;
            }

            if ($('#describeApiName').val() == ""){
                alert("describeApiName不可为空!");
                return false;
            }

            if ($('#startTime').val() == ""){
                alert("开始时间不可为空!");
                return false;
            }

            $('#dataInfo').text("");
            $('#batchRestoreData').attr("disabled", "disabled");

            $.ajax({
                type:"POST",
                url:"${CONTEXT_PATH}/metadata-restore/batchRestoreData",
                data:{
                    tenantId: $('#tenantId').val(),
                    describeApiName: $('#describeApiName').val(),
                    startTime: $('#startTime').val(),
                    endTime: $('#endTime').val()
                },
                dataType:"json",
                success:function(data){
                    if (data !== "") {
                        $('#batchRestoreData').removeAttr("disabled");
                        $('#dataInfo').JSONView(data, {
                            collapsed: false,
                            nl2br: true,
                            recursive_collapser: true
                        });
                    }
                }
            })

        });
    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
