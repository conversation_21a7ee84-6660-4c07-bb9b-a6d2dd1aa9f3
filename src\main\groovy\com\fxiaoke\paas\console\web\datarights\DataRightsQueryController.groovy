package com.fxiaoke.paas.console.web.datarights

import com.fxiaoke.paas.console.service.OKHttpService
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.fxiaoke.common.http.handler.SyncCallback
import com.fxiaoke.common.http.spring.OkHttpSupport
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.service.datarights.DataRightsQueryService
import com.fxiaoke.paas.console.service.datarights.DataRightsStoreTableNameService
import com.fxiaoke.paas.console.util.ApiNameToStoreTableNameUtil
import com.github.autoconf.ConfigFactory
import com.google.common.base.Charsets
import com.google.common.base.Splitter
import groovy.util.logging.Slf4j
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.bind.annotation.RestController

import javax.annotation.PostConstruct
import javax.annotation.Resource

/**
 * <AUTHOR>
 * @date 2019/7/8 下午5:03
 */
@RestController
@Slf4j
@RequestMapping(path = "datarights")
class DataRightsQueryController {
  @Autowired
  private DataRightsQueryService dataRightsQueryService
  @Autowired
  private DataRightsStoreTableNameService dataRightsStoreTableNameService
  @Resource(name = "httpSupport")
  private OkHttpSupport okHttpSupport
  @Autowired
  private OKHttpService okHttpService

  private String paasDescribeWebUrl
  private String workerUrl

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("paas-console-base", { iConfig ->
      this.paasDescribeWebUrl = iConfig.get("paas-describe-web-url")
    })
    ConfigFactory.getConfig("fs-paas-console-datarights", { iConfig ->
      this.workerUrl = iConfig.get("DATA_AUTH_REFRESH_URL")
    })


  }


  @PostMapping(path = "/task/report", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  String taskReport() {
    String url = this.workerUrl + "/data_auth_task/report"
    return    okHttpService.postJSON(url, new JSONObject() )
  }


  @GetMapping(path = "mixBatch", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  @SystemControllerLog(description = "数据权限--数据详情查询")
  String mixBatch(String tenantId, String id, String apiName) {
    JSONArray param = new JSONArray()
    JSONObject params = new JSONObject()
    params.put("id", id)
    params.put("apiKey", new HashMap() {
      {
        put("apiName", apiName)
        put("tenantId", tenantId)
      }
    })
    param.add(params)
    Request request = new Request.Builder()
            .post(RequestBody.create(okhttp3.MediaType.parse("application/json;charset=utf-8"), param.toJSONString().getBytes(Charsets.UTF_8)))
            .url(this.paasDescribeWebUrl + "/data/batch/findMixBatch").build()

    return okHttpSupport.syncExecute(request, new SyncCallback() {
      @Override
      Object response(Response response) throws Exception {
        return new String(response.body().bytes(), Charsets.UTF_8)
      }
    })
  }


  @GetMapping(path = "datarights_query", produces = "application/json;charset=utf-8")
  @ResponseBody
  @SystemControllerLog(description = "数据权限 -- 获取数据权限信息")
  String queryDataRights(String tenantId, String apiName, String objectId) {
    String storeTableName = dataRightsStoreTableNameService.byDescribeApiName(tenantId, apiName)
    String newStoreTableName = ApiNameToStoreTableNameUtil.toNewStoreTableName(storeTableName)

    if (StringUtils.isBlank(newStoreTableName)) {
      newStoreTableName = "mt_data"
    }

    return dataRightsQueryService.getDataAuthMsg(tenantId, objectId, newStoreTableName)
  }

  @GetMapping(path = "datarights_check", produces = "application/json;charset=utf-8")
  @ResponseBody
  @SystemControllerLog(description = "数据权限 -- 新数据权限检查")
  String checkDataRights(String tenantId, String apiName, String objectId) {
    String storeTableName = dataRightsStoreTableNameService.byDescribeApiName(tenantId, apiName)
    String newStoreTableName = ApiNameToStoreTableNameUtil.toNewStoreTableName(storeTableName)

    if (StringUtils.isBlank(newStoreTableName)) {
      newStoreTableName = "mt_data"
    }

    return dataRightsQueryService.checkDataAuth(tenantId, objectId, newStoreTableName, apiName).toJSONString()
  }

  @GetMapping(path = "getDataAuthMsg", produces = "application/json;charset=utf-8")
  @ResponseBody
  String checkDataRights(String tenantId, String userId, String apiName, String objectIds,
                         @RequestParam(required = false) String outerTenantId, @RequestParam(required = false) String outerUserId, @RequestParam(required = false) String outerAppId) {
    return dataRightsQueryService.getDataAuthMsgByTenantIdAndUserIdAndApiNameAndObjectIds(tenantId, userId, apiName, Splitter.on(",").trimResults().omitEmptyStrings().splitToList(objectIds), outerTenantId, outerUserId, outerAppId)

  }

  @GetMapping(path = "queryDataAuth", produces = "application/json;charset=utf-8")
  @ResponseBody
  @SystemControllerLog(description = "数据权限 -- 数据权限查询")
  String queryDataAuth(String tenantId, String userId, String describeApiName, String dataId, @RequestParam(required = false) String outerTenantId) {
    return dataRightsQueryService.queryDataAuth(tenantId, userId, describeApiName, dataId, outerTenantId)
  }

}
