<#assign headContent>
    <link href="//static.foneshare.cn/oss/AdminLTE/plugins/datatables/dataTables.bootstrap.css" rel="stylesheet"
          type="text/css"/>
    <link type="text/css" href="//static.foneshare.cn/oss/datatables-checkboxes/dataTables.checkboxes.css"
          rel="stylesheet"/>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
    <style>
        #datatable td:nth-child(6) {
            text-align: right;
        }

        #datatable td:nth-child(7) {
            text-align: right;
        }
    </style>
</#assign>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>查看配额详情</h1>
        <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i>主页</a></li>
            <li><a href="${ctx}/license/view-moduleDetail?productId=${productId}"><i class="fa fa-dashboard"></i>查看模块详情</a></li>

        </ol>
    </section>
</#assign>

<#assign bodyContent>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div id="warningInfo" class="alert alert-warning hide">
                    <span id="closeInfo" href="#" class="close">&times;</span>
                    <strong id="hitInfo"></strong>
                </div>
                <div class="box box-info" style="margin-bottom: 1px;">
                    <div class="box-body" style="padding: 20px;">
                        <form class="form-inline">
                            <div class="form-group">
                                <label for="tenantId" class="control-label">企业ID</label>
                                <input type="text" style="border-radius:5px;" class="form-control" id="tenantId" name="tenantId" placeholder="企业ID">
                            </div>
                            <button type="button" id="findSub" class="btn btn-primary">查询</button>
                            <a type="button" href="${ctx}/license/add-productInfo" id="newBuild" class="btn btn-primary" style="width: 100px;float: right">新建</a>
                            <input id="cancel_btn" class="btn" type="button" style="float: right" value="返回" onclick="history.back()"/>
                        </form>
                    </div>
                    <div class="box-footer clearfix" style="padding: 2px;">
                        <div class="clearfix"></div>
                    </div>
                </div>
                <div class="box box-info">
                    <div class="box-body">
                        <table id="datatable" class="table table-hover table-bordered" cellspacing="0" width="100%">
                            <thead>
                            <tr>
                                <th>tenantId</th>
                                <th>moduleCode</th>
                                <th>paraKey</th>
                                <th>paraValue</th>
                                <#--<th>操作</th>-->
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="box-footer clearfix">
                    </div>
                </div>
            </div>
        </div>
    </section>
</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script type="application/javascript">
        $(document).ready(function () {
            var table = $("#datatable").DataTable({
//            "deferRender": true,
                "processing": true,
                "ajax": {
                    url: "${ctx}/license/view-paraDetails?productId=${productId}",
                    type:'GET'
                },
                dom: 'Bfrtip',
                buttons: [
                    'copy', 'excel'
                ],
                /*"columnDefs": [ {
                    // 定义操作列,######以下是重点########
                    "targets" : 9,//操作按钮目标列
                    "render" : function(data, type, row, meta) {
                        /!*var productId = row.toString().split(',')[0];
                        var Selecturl = "/license/view-moduleDetail?productId="+productId;
                        var editUrl = "/license/edit-detail?productId="+productId;*!/
                        /!*"+Selecturl+"  "+editUrl+"*!/
                        /!*var html = "<a href='' class='btn btn-primary' > 编辑</a>"
                        html += "<a href='' class='btn btn-primary' > 删除</a>"
                        return html;*!/
                    }
                } ],*/
                "language": {
                    "url": "${ctx}/static/js/datatables-zh_CN.json"
                },
                "mark": true,
                "paging": false
            });

//        提交
            $('#findSub').on('click', function () {
                $('#warningInfo').addClass('hide');
                $("#datatable").dataTable().fnClearTable();
                var tenantId = $('#tenantId').val();
                if (tenantId === "") {
                    $('#warningInfo').removeClass('hide');
                    $('#hitInfo').html('关键字段不能为空！');
                    return;
                }

//            将对象转化为json字符串放在url中传递到后端
                table.ajax.url("${ctx}/license/module-list?tenantId=" + tenantId).load();
            });


        });
        /**
         * 信息提示栏关闭
         */
        $('#closeInfo').on('click', function () {
            $('#warningInfo').addClass('hide');
        });

    </script>
</#assign>
<#include "../layout/layout-main.ftl" />
