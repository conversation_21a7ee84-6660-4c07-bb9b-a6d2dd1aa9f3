package com.fxiaoke.paas.console.entity.organization

import com.fxiaoke.paas.console.util.organization.constant.OrgConstants
import lombok.Data

import javax.persistence.Table

/**
 * 员工信息实体类
 * Created by wangxing on 2018/03/15
 */
@Table(name = OrgConstants.TABLE_EMPLOYEE)
@Data
class EmployeeEntity {

  String userId
  String nickname
  String name
  String phone
  String eamil
  String position
  String tenantId
  String supervisorId
  String title
  String picAddr
  String description
  Integer status
  Long createTime
  String lastModifiedBy
  Long lastModifiedTime
  Integer isDeleted
  String department
  Long version
  String createdBy
  String package_
  String objectDescribeId
  String objectDescribedApiName
  String recordType
  String extendObjDataId
  String id
}
