package com.fxiaoke.paas.console.web.metadata

import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.service.metadata.BtnService
import com.fxiaoke.paas.console.util.DateFormatUtil
import com.github.shiro.support.ShiroCasRealm
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.Resource

/**
 *  按钮，后动作，自定义函数查询
 * <AUTHOR>
 * Created on 2018/3/19.
 */
@Controller
@Slf4j
@RequestMapping("/metadata/btn")
class ButtonController {

  @Resource(name = "casRealm")
  private ShiroCasRealm cas

  @Autowired
  BtnService btnService

  /**
   * 查询页面
   */
  @RequestMapping(value = "/find")
  def find() {
    "metadata/btn"
  }

  /**
   * 按钮查询
   * @param id 企业ID，tenantId
   */
  @RequestMapping(value = "/find-btn")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 按钮查询")
  def findBtn(@RequestParam String id) {
    List<Map<String, Object>> btnList = btnService.findBtnByTenantId(id.trim())
    def json = btnList.collect { btn ->
      [btn.get("api_name"), btn.get("label"), btn.get("describe_api_name"), btn.get("is_active"), btn.get("is_deleted"),
       btn.get("last_modified_time") != null ? DateFormatUtil.formatLong(btn.get("last_modified_time") as Long) : btn.get("last_modified_time"), btn.get("id")]
    }
    ["data": json]
  }

  /**
   * 查找按钮详情
   * @param id
   * @param tenantId
   * @return
   */
  @RequestMapping(value = "/btn-info")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 查找按钮详情")
  def findBtnInfo(@RequestParam String id, @RequestParam String tenantId) {
    Map<String, Object> infoMap = Maps.newHashMap()
    try {
      infoMap = btnService.findBtnByBtnId(tenantId, id)
      return ["infoMap": infoMap]
    } catch (Exception e) {
      log.error("查询按钮详情异常，btnId={},error:", id, e)
      return ["infoMap": infoMap]
    }
  }

  /**
   * 后动作查询
   * @param id 企业ID，tenantId
   */
  @RequestMapping(value = "/find-act")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 后动作查询")
  def findAct(@RequestParam String id) {
    List<Map<String, Object>> actList = btnService.findActByTenantId(id.trim())
    def json = actList.collect { act ->
      [act.get("label"), act.get("describe_api_name"), act.get("action_type"), DateFormatUtil.formatLong(act.get("create_time") as Long), act.get("id")]
    }
    ["data": json]
  }

  /**
   * 查询后动作详情
   * @param id
   * @param tenantId
   * @return
   */
  @RequestMapping(value = "/act-info")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 查询后动作详情")
  def findActInfo(@RequestParam String id, @RequestParam String tenantId) {
    Map<String, Object> infoMap = Maps.newHashMap()
    try {
      infoMap = btnService.findActByActId(tenantId, id)
      return ["infoMap": infoMap]
    } catch (Exception e) {
      log.error("查询按钮详情异常，actId={},error:", id, e)
      return ["infoMap": infoMap]
    }
  }

  /**
   * 自定义函数查询
   * @param id 企业ID，tenantId
   */
  @RequestMapping(value = "/find-fun")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 自定义函数查询")
  def findFun(@RequestParam String id) {
    List<Map<String, Object>> funList = btnService.findFunByTenantId(id.trim())
    def json = funList.collect { fun ->
      [fun.get('api_name'), fun.get("binding_object_api_name"), fun.get("function_name"), DateFormatUtil.formatLong(fun.get("create_time") as Long), fun.get("id")]
    }
    ["data": json]
  }

  /**
   * 根据id查询自定义函数详情
   * @param id
   * @param tenantId
   * @return
   */
  @RequestMapping(value = "/fun-info")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 查询自定义函数详情")
  def findFunInfo(@RequestParam String id, @RequestParam String tenantId) {
    Map<String, Object> infoMap = Maps.newHashMap()
    try {
      infoMap = btnService.findFunByFunId(tenantId.trim(), id.trim())
      return ["infoMap": infoMap]
    } catch (Exception e) {
      log.error("查询按钮详情异常，funId={},error:", id, e)
      return ["infoMap": infoMap]
    }
  }


}
