package com.fxiaoke.paas.console.web.datarights

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.facishare.organization.adapter.api.model.organizationwithouter.BatchGetResponsibleEmployee
import com.facishare.organization.adapter.api.service.OrganizationWithOuterService
import com.fxiaoke.common.Pair
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.bean.datarights.*
import com.fxiaoke.paas.console.service.datarights.DataRightsAdminService
import com.fxiaoke.paas.console.service.OKHttpService
import com.fxiaoke.paas.console.service.datarights.DataRightsDataService
import com.fxiaoke.paas.console.service.metadata.ApiNameTransferService
import com.fxiaoke.paas.console.service.metadata.JdbcService
import com.fxiaoke.paas.console.util.datarights.DataRightsArgUtil
import com.fxiaoke.release.FsGrayRelease
import com.fxiaoke.release.FsGrayReleaseBiz
import com.github.autoconf.ConfigFactory
import com.github.mybatis.datatables.DataResponse
import com.github.shiro.support.ShiroCasRealm
import com.google.common.base.Splitter
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import com.google.gson.JsonObject
import groovy.util.logging.Slf4j
import lombok.Cleanup
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.PostConstruct
import java.sql.ResultSetMetaData
import java.util.stream.Collectors

/**
 * Created by yangxw on 2018/3/15.
 */
@Controller
@Slf4j
class DataRightsController {

  @Autowired
  private ShiroCasRealm cas
  @Autowired
  ApiNameTransferService apiNameTransferService
  @Autowired
  private DataRightsAdminService dataRightsAdminService
  private FsGrayReleaseBiz gray = FsGrayRelease.getInstance("data-auth");
  @Autowired
  private OKHttpService okHttpService
  @Autowired
  private OrganizationWithOuterService organizationWithOuterService
  private String dataAuthServiceUrl
  @Autowired
  private DataRightsDataService dataRightsDataService
  private final String queryFcUserRoleSQL = "select * from fc_user_role  where tenant_id=? and user_id=? and outer_tenant_id =? and is_deleted='t'";
  private String BASIC_INFO = "01.基本信息"
  private String ME = "02.我"
  private String MY_SUB_DEPT = "03.我负责、我观察部门"
  private String MY_SUB_DEPT_EMPLOYEE = "04.我负责、我观察部门下的人员"
  private String OUTER_USER_ROLE = "05.外部角色"
  private String OUTER_DT_TEAM = "06.外部相关团队"
  private String OUTER_DATA_TEMPORARY_RIGHTS = "07.外部临时权限"
  private String OUTER_DT_AUTH = "08.数据的dt_auth"
  private String OUTER_DT_AUTH_TARGET = "09.数据的dt_auth_target"

  @Autowired
  private JdbcService jdbcService

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-console-datarights", { config ->
      this.dataAuthServiceUrl = config.get("dataAuthServiceUrl")
    })

  }
/**
 * 对象权限查询
 * @param request
 * @param param
 * @return
 */
  @RequestMapping(value = "basicDataPermissionQuery")
  @ResponseBody
  @SystemControllerLog(description = "数据权限 -- 对象权限查询")
  def basicQuery(@RequestParam String dataObject, @RequestParam String start, @RequestParam String length, String draw) {
    DataResponse response = new DataResponse()
    if (StringUtils.isBlank(dataObject)) {
      List list = new ArrayList<>()
      response.setData(list)
      return response
    }
    JSONObject jsonObject = JSONObject.parse(dataObject)
    Map param = (Map) jsonObject
    param.put("start", start)
    param.put("length", length)
    BasicQueryArg basicQueryArg = DataRightsArgUtil.getBasicQueryArg(param)
    basicQueryArg.getContext().setUserId(cas.getCurrentUserName())
    response = dataRightsAdminService.basicQuery(basicQueryArg)
    response.setDraw(draw)
    return response
  }

  @RequestMapping(value = "user/leader/cache/query")
  @ResponseBody
  @SystemControllerLog(description = "数据权限 -- 汇报对象缓存查询")
  def leaderQuery(@RequestParam String dataObject, @RequestParam String start, @RequestParam String length, String draw) {
    DataResponse response = new DataResponse()
    if (StringUtils.isBlank(dataObject)) {
      List list = new ArrayList<>()
      response.setData(list)
      return response
    }
    JSONObject jsonObject = JSONObject.parse(dataObject)
    Map param = (Map) jsonObject
    param.put("start", start)
    param.put("length", length)
    String tenantId = (String) param.get("tenantId")
    if (StringUtils.isBlank(tenantId)) {
      response.data = new ArrayList()
      return response
    }
    LeaderArg leaderArg = DataRightsArgUtil.getLeaderArg(param)
    leaderArg.getContext().setUserId(cas.getCurrentUserName())
    response = dataRightsAdminService.leaderQuery(leaderArg)
    response.setDraw(draw)
    return response
  }


  @RequestMapping(value = "user/dept/cache/query")
  @ResponseBody
  @SystemControllerLog(description = "数据权限 -- 部门负责人缓存查询")
  def deptsQuery(@RequestParam String dataObject, @RequestParam String start, @RequestParam String length, String draw) {
    DataResponse response = new DataResponse()
    if (StringUtils.isBlank(dataObject)) {
      List list = new ArrayList<>()
      response.setData(list)
      return response
    }
    JSONObject jsonObject = JSONObject.parse(dataObject)
    Map param = (Map) jsonObject
    param.put("start", start)
    param.put("length", length)
    String tenantId = (String) param.get("tenantId")
    if (StringUtils.isBlank(tenantId)) {
      response.data = new ArrayList()
      response.draw = draw
      return response
    }
    DeptsArg deptsArg = DataRightsArgUtil.getDeptsArg(param)
    deptsArg.getContext().setUserId(cas.getCurrentUserName())
    response = dataRightsAdminService.deptsQuery(deptsArg)
    response.setDraw(draw)
    return response
  }

  @RequestMapping(value = "share/cache/query")
  @ResponseBody
  @SystemControllerLog(description = "数据权限 -- 共享规则缓存查询")
  def shareCacheQuery(@RequestParam String dataObject, @RequestParam String start, @RequestParam String length, String draw) {
    DataResponse response = new DataResponse()
    if (StringUtils.isBlank(dataObject)) {
      List list = new ArrayList<>()
      response.setData(list)
      response.draw = draw
      return response
    }
    JSONObject jsonObject = JSONObject.parse(dataObject)
    Map param = (Map) jsonObject
    param.put("start", start)
    param.put("length", length)
    String tenantId = (String) param.get("tenantId")
    if (StringUtils.isBlank(tenantId)) {
      response.data = new ArrayList()
      response.draw = draw
      return response
    }
    ShareCacheArg shareCacheArg = DataRightsArgUtil.getShareCacheArg(param)
    shareCacheArg.getContext().setUserId(cas.getCurrentUserName())
    response = dataRightsAdminService.shareCacheQuery(shareCacheArg)
    response.setDraw(draw)
    return response
  }

  @RequestMapping(value = "share/query")
  @ResponseBody
  @SystemControllerLog(description = "数据权限 -- 共享规则查询")
  def shareQuery(@RequestParam String dataObject, @RequestParam String start, @RequestParam String length, String draw) {
    DataResponse response = new DataResponse()
    if (StringUtils.isBlank(dataObject)) {
      List list = new ArrayList<>()
      response.setData(list)
      response.draw = draw
      return response
    }
    JSONObject jsonObject = JSONObject.parse(dataObject)
    Map param = (Map) jsonObject
    param.put("start", start)
    param.put("length", length)
    String tenantId = (String) param.get("tenantId")
    if (StringUtils.isBlank(tenantId)) {
      response.data = new ArrayList()
      response.draw = draw
      return response
    }
    ShareArg shareArg = DataRightsArgUtil.getShareArg(param)
    shareArg.getContext().setUserId(cas.getCurrentUserName())
    response = dataRightsAdminService.shareQuery(shareArg)
    response.setDraw(draw)
    return response
  }

  @RequestMapping(value = "datarights_refresh/permission/init")
  @ResponseBody
  @SystemControllerLog(description = "数据权限 -- 缓存初始化")
  def initCache(@RequestParam String dataObject) {
    DataResponse response = new DataResponse()
    if (StringUtils.isBlank(dataObject)) {
      response.data = new ArrayList()
      return response
    }
    JSONObject jsonObject = JSONObject.parse(dataObject)
    Map param = (Map) jsonObject
    String tenantId = (String) param.get("tenantId")
    if (StringUtils.isBlank(tenantId)) {
      response.data = new ArrayList()
      return response
    }
    InitCacheArg initCacheArg = DataRightsArgUtil.getInitCachaArg(param)
    initCacheArg.getContext().setUserId(cas.getCurrentUserName())
    response = dataRightsAdminService.initCache(initCacheArg, (String) param.get("initType"))
    return response
  }

  @RequestMapping(value = "dept/query")
  @ResponseBody
  def deptQuery(@RequestParam Map<String, Object> param) {
    DataResponse<UserPojoObject> response = new DataResponse<>()
    if (StringUtils.isBlank((String) param.get("tenantId"))) {
      return response
    }
    DataRightsContext context = new DataRightsContext()
    context.setTenantId((String) param.get("tenantId"))
    if (StringUtils.isNotBlank((String) param.get("AppId"))) {
      context.setAppId((String) param.get("AppId"))
    }
    context.setUserId(cas.getCurrentUserName())
    List<String> depts = new ArrayList<>()
    if (StringUtils.isNotBlank((String) param.get("deptId"))) {
      depts.add((String) param.get("deptId"))
    }
    response = dataRightsAdminService.batchQueryUserPojoByDeptId(depts, context)

    return response
  }

  @RequestMapping(value = "group/query")
  @ResponseBody
  def groupQuery(@RequestParam Map<String, Object> param) {
    DataResponse<UserPojoObject> response = new DataResponse<>()

    if (StringUtils.isBlank((String) param.get("tenantId"))) {
      return response
    }
    DataRightsContext context = new DataRightsContext()
    context.setTenantId((String) param.get("tenantId"))
    if (StringUtils.isNotBlank((String) param.get("AppId"))) {
      context.setAppId((String) param.get("AppId"))
    }
    context.setUserId(cas.getCurrentUserName())
    String groudId
    if (StringUtils.isNotBlank((String) param.get("groupId"))) {
      groudId = (String) param.get("groupId")
      response = dataRightsAdminService.batchQueryGroupPojo(groudId, context)
    }
    return response
  }

  @RequestMapping(value = "user/circle/query", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, method = RequestMethod.POST)
  @SystemControllerLog(description = "数据权限 -- 查询我的圈子")
  @ResponseBody
  def userCircleQuery(@RequestBody Map<String, String> requestParam) {
    Map<String, Object> treeMap = Maps.newTreeMap()
    String tenantId = requestParam.get("tenantId")
    String userId = requestParam.get("userId")
    String dataIds = requestParam.get("dataIds")
    String describeApiName = requestParam.get("describeApiName")
    String outerTenantId = requestParam.get("outerTenantId")
    boolean check = "true".equals(requestParam.get("check"))
    if (StringUtils.isBlank(outerTenantId)) {
      JSONObject params = new JSONObject()
      JSONObject context = new JSONObject()
      context.put("tenantId", tenantId)
      context.put("userId", userId)
      context.put("appId", "CRM")
      params.put("context", context)
      if (StringUtils.isNotBlank(dataIds)) {
        params.put("dataIds", Splitter.on(",").trimResults().omitEmptyStrings().splitToList(dataIds))
      }
      params.put("describeApiName", describeApiName)
      String json = okHttpService.postJSON(this.dataAuthServiceUrl + "/tool/find_user_relation_with_data", params)
      JSONObject<String, Object> jsonObject = JSONObject.parseObject(json).getJSONObject("result")

      jsonObject.forEach({ k, v -> treeMap.put(k, v) })
      if (check) {
        treeMap.putAll(dataRightsDataService.queryReferenceData(tenantId, describeApiName, Splitter.on(",").trimResults().omitEmptyStrings().splitToList(dataIds)))
      }
    } else {
      Set<String> idsSet = Sets.newHashSet(Splitter.on(",").trimResults().omitEmptyStrings().split(dataIds))
      treeMap.put(BASIC_INFO, getBasicInfo(tenantId))
      //我
      treeMap.put(ME, Lists.newArrayList(userId.trim()))
      //我负责、我观察部门
      treeMap.put(MY_SUB_DEPT, getSubDept(tenantId, userId))
      //我负责、我观察部门下的人员
      treeMap.put(MY_SUB_DEPT_EMPLOYEE, getUserIdsOfMyManagedDepartments(tenantId, userId))
      //外部角色
      treeMap.put(OUTER_USER_ROLE, getOuterUserRole(tenantId, outerTenantId, userId))
      //外部相关团队
      treeMap.put(OUTER_DT_TEAM, queryOutDtTeam(tenantId, describeApiName, userId, idsSet, outerTenantId))
      //外部临时权限
      treeMap.put(OUTER_DATA_TEMPORARY_RIGHTS, getOuterTemporaryRights(tenantId, describeApiName, userId, idsSet, outerTenantId))
      //外部auth表
      treeMap.put(OUTER_DT_AUTH, queryOuterDtAuth(tenantId, describeApiName, userId, idsSet))
      //外部target
      treeMap.put(OUTER_DT_AUTH_TARGET, queryOuterDataAuthTarget(tenantId, describeApiName, userId, outerTenantId))
    }
    return treeMap
  }


  private Map<String, Object> queryOuterDataAuthTarget(String tenantId, String apiName, String userId, String outerTenantId) {
    Map<String, Object> map = Maps.newHashMap()

    try {
      Pair<JdbcConnection, String> pair = jdbcService.conn(tenantId, "select * from dt_auth_out_target where tenant_id='" + tenantId + "' and object_describe_api_name='" + apiName + "' and auth_target='" + outerTenantId + "." + userId + "'; ")
      @Cleanup JdbcConnection jdbcConnection = pair.first
      String sql = pair.second
      jdbcConnection.query(sql, { result ->
        ResultSetMetaData resultSetMetaData = result.getMetaData()
        int index = resultSetMetaData.getColumnCount()
        if (result.next()) {
          for (int i = 1; i <= index; i++) {
            String columnName = resultSetMetaData.getColumnName(i)
            map.put(columnName, result.getObject(columnName).toString())
          }
        }
      })
    } catch (Exception e) {
      log.error("query OuterDataAuthTarget error!  tenantId:{} apiName:{} userId:{} outerTenantId:{} ", tenantId, apiName, userId, outerTenantId, e)
    }
    return map
  }

  private List<Map<String, Object>> queryOuterDtAuth(String tenantId, String apiName, String userId, Set<String> dataIds) {
    if (CollectionUtils.isEmpty(dataIds)) {
      return Lists.newArrayList()
    }
    String storeTableName = apiNameTransferService.getStoreTableName(tenantId, apiName)
    if (StringUtils.isBlank(storeTableName)) {
      storeTableName = "mt_data"
    }
    List<Map<String, Object>> dtAuths = Lists.newArrayList()
    try {
      Pair<JdbcConnection, String> pair = jdbcService.conn(tenantId, String.format("select * from dt_auth_out as a where tenant_id ='%s' and object_describe_api_name='%s' and exists(select id from %s as b where tenant_id='%s' and object_describe_api_name='%s' and a.data_auth_code=b.out_data_auth_code and id =any('{%s}'))", tenantId, apiName, storeTableName, tenantId, apiName, String.join(",", dataIds)))
      @Cleanup JdbcConnection jdbcConnection = pair.first
      String sql = pair.second
      jdbcConnection.query(sql, { result ->
        ResultSetMetaData resultSetMetaData = result.getMetaData()
        int index = resultSetMetaData.getColumnCount()
        while (result.next()) {
          Map<String, Object> map = Maps.newHashMap()
          for (int i = 1; i <= index; i++) {
            String columnName = resultSetMetaData.getColumnName(i)
            map.put(columnName, result.getObject(columnName).toString())
          }
          dtAuths.add(map)
        }

      })
    } catch (Exception e) {
      log.error("query OuterDtAuth error! tenantId:{} userId:{} dataIds:{} ", tenantId, userId, dataIds, e)
    }
    return dtAuths
  }

  private List<Map<String, Object>> getOuterTemporaryRights(String tenantId, String apiName, String userId, Set<String> dataIds, String outerTenantId) {
    if (CollectionUtils.isEmpty(dataIds)) {
      return Lists.newArrayList()
    }
    List<Map<String, Object>> temporaryRights = Lists.newArrayList()
    try {
      Pair<JdbcConnection, String> pair = jdbcService.conn(tenantId, "select * from dt_temporary_rights where entity_id='" + apiName + "' and tenant_id = '" + tenantId + "' and  owner = '" + userId + "' and data_id =any('{" + String.join(",", dataIds) + "}') and outer_tenant_id = '" + outerTenantId + "'")
      @Cleanup JdbcConnection jdbcConnection = pair.first
      String sql = pair.second
      jdbcConnection.query(sql, { result ->
        ResultSetMetaData resultSetMetaData = result.getMetaData()
        int index = resultSetMetaData.getColumnCount()
        while (result.next()) {
          Map<String, Object> map = Maps.newHashMap()
          for (int i = 1; i <= index; i++) {
            String columnName = resultSetMetaData.getColumnName(i)
            map.put(columnName, result.getObject(columnName))
          }
          temporaryRights.add(map)
        }

      })
    } catch (Exception e) {
      log.error("query temporaryRights error! tenantId:{} userId:{} dataIds:{} ", tenantId, userId, dataIds, e)
    }
    return temporaryRights
  }

  private List<Map<String, Object>> getOuterUserRole(String tenantId, String outerTenantId, String userId) {
    List<Map<String, Object>> userRoles = Lists.newArrayList()
    Pair<JdbcConnection, String> pair = jdbcService.conn(tenantId, queryFcUserRoleSQL)
    @Cleanup JdbcConnection jdbcConnection = pair.first
    String sql = pair.second
    jdbcConnection.prepareQuery(sql, { statement ->
      statement.setString(1, tenantId)
      statement.setString(2, userId)
      statement.setString(3, outerTenantId)
    }, { result ->
      ResultSetMetaData resultSetMetaData = result.getMetaData()
      int size = resultSetMetaData.getColumnCount()
      while (result.next()) {
        Map<String, Object> data = Maps.newHashMap()
        for (int i = 1; i <= size; i++) {
          String columnName = resultSetMetaData.getColumnName(i)
          data.put(columnName, result.getObject(columnName))
        }
        userRoles.add(data)
      }
    })
    return userRoles
  }

  private List<String> getBasicInfo(String tenantId) {
    List<String> items = Lists.newArrayList()
    items.add("1.当前企业是否级联：" + isRecursive(tenantId))
    items.add("*共享规则来源方级联，目标方不级联;")
    items.add("*查询我的下属时，依赖此级联开关")
    items.add("*查询我负责的部门时，依赖此级联开关")

    return items
  }
  /**
   * 是否递归部门查找
   */
  boolean isRecursive(String tenantId) {
    return gray.isAllow("cascadeEnabledTenantIds", tenantId)
  }

  Set<String> getSubDept(String tenantId, String userId) {
    com.facishare.organization.adapter.api.model.organizationwithouter.BatchGetResponsibleEmployee.Arg arg = new com.facishare.organization.adapter.api.model.organizationwithouter.BatchGetResponsibleEmployee.Arg()
    arg.setEnterpriseId(tenantId)
    arg.setEmployeeIds(Lists.newArrayList(userId))
    arg.setIncludeLowDepartment(isRecursive(tenantId))
    BatchGetResponsibleEmployee.Result result = organizationWithOuterService.batchGetResponsibleEmployee(arg)
    if (result.getResponsibleEmployeeMap().isEmpty()) {
      return Sets.newHashSet()
    }
    return result.getResponsibleEmployeeMap().stream().map({ x -> x.getDepartmentId() }).collect(Collectors.toSet())
  }

  Set<String> getUserIdsOfMyManagedDepartments(String tenantId, String userId) {
    com.facishare.organization.adapter.api.model.organizationwithouter.BatchGetResponsibleEmployee.Arg arg = new com.facishare.organization.adapter.api.model.organizationwithouter.BatchGetResponsibleEmployee.Arg();
    arg.setEnterpriseId(tenantId);
    arg.setEmployeeIds(Lists.newArrayList(userId));
    arg.setIncludeLowDepartment(isRecursive(tenantId));
    BatchGetResponsibleEmployee.Result result = organizationWithOuterService.batchGetResponsibleEmployee(arg);
    if (result.getResponsibleEmployeeMap().isEmpty()) {
      return Sets.newHashSet()
    }
    return result.getResponsibleEmployeeMap().stream().map({ x -> x.getEmployeeId() }).collect(Collectors.toSet());
  }

  @RequestMapping(path = "/dataright/queryAuthToUser", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  @SystemControllerLog(description = "数据权限--查询人到人数据权限")
  def authToUser(String userId, String tenantId, String apiNames) {
    List<String> apiNameList = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(apiNames)
    Map<String, String> context = Maps.newHashMap()
    context.put("tenantId", tenantId)
    context.put("appId", "CRM")
    context.put("userId", userId)
    JSONObject params = new JSONObject()
    params.put("context", context)
    params.put("userId", userId)
    params.put("objectDescribeApiNames", apiNameList)
    JSONArray jsonArray = JSONObject.parseObject(okHttpService.postJSON(this.dataAuthServiceUrl + "/datarights/get_data_auth_codes_authorized_to_user", params)).getJSONArray("result")
    return ["code": 200, "info": jsonArray.toJavaList(String.class)]
  }


  private List<Map<String, Object>> queryOutDtTeam(String tenantId, String apiName, String outerUser, Set<String> dataIds, String outerTenantId) {
    if (CollectionUtils.isEmpty(dataIds)) {
      return Lists.newArrayList()
    }
    List<Map<String, Object>> dtTeams = Lists.newArrayList()
    try {
      Pair<JdbcConnection, String> pair = jdbcService.conn(tenantId, "select * from dt_team where object_describe_api_name='" + apiName + "' and tenant_id = '" + tenantId + "' and  member_id = '" + outerUser + "' and object_id =any('{" + String.join(",", dataIds) + "}') and out_tenant_id = '" + outerTenantId + "'")
      @Cleanup JdbcConnection jdbcConnection = pair.first
      String sql = pair.second
      jdbcConnection.query(sql, { result ->
        ResultSetMetaData resultSetMetaData = result.getMetaData()
        int index = resultSetMetaData.getColumnCount()
        while (result.next()) {
          Map<String, Object> map = Maps.newHashMap()
          for (int i = 1; i <= index; i++) {
            String columnName = resultSetMetaData.getColumnName(i)
            map.put(columnName, result.getObject(columnName))
          }
          dtTeams.add(map)
        }

      })
    } catch (Exception e) {
      log.error("query dtTeams error! tenantId:{} outerUser:{} dataIds:{} ", tenantId, outerUser, dataIds, e);
    }
    return dtTeams
  }
}