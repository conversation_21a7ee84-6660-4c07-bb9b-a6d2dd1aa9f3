package com.fxiaoke.paas.console.service.metadata

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.facishare.paas.metadata.api.search.IFilter
import com.facishare.paas.metadata.impl.search.DataRightsParameter
import com.facishare.paas.metadata.impl.search.Filter
import com.facishare.paas.metadata.impl.search.Operator
import com.facishare.paas.metadata.impl.search.OrderBy
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.fxiaoke.common.http.handler.SyncCallback
import com.fxiaoke.common.http.spring.OkHttpSupport
import com.fxiaoke.paas.console.bean.metadata.SceneType
import com.fxiaoke.paas.console.bean.metadata.SearchSceneArg
import com.fxiaoke.paas.console.mapper.metadata.FieldMapper
import com.github.autoconf.ConfigFactory
import com.google.common.base.Charsets
import com.google.common.collect.Lists
import okhttp3.MediaType
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import javax.annotation.Resource


@Service
class DataQueryService {

  @Autowired
  private FieldMapper fieldMapper

  @Resource(name = "httpSupport")
  private OkHttpSupport okHttpSupport

  private String searchQueryUrl;

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("paas-console-base", { config ->
      searchQueryUrl = config.get("fs-metadata-search-query")
    })
  }

  List<String> findApiNameList(String tenantId,String describeApiName){
    return fieldMapper.setTenantId(tenantId).findApiNameByDescribeApiName(tenantId,describeApiName)
  }

  Object findDataByScene(SearchSceneArg arg){

    String tenantId = arg.getTenantId()
    String userId= arg.getUserId()
    String describeApiName = arg.getDescribeApiName()
    boolean isAuth = arg.getIsAuth()
    SceneType sceneType = arg.getSceneType()
    String searchSource = arg.getSearchSource()
    String dataName = arg.getDataName()
    boolean detailInfo = arg.getDetailInfo()
    Integer pageSize = arg.getPageSize()

    List<IFilter> filterList = Lists.newArrayList()
    filterList.add(getFilter("object_describe_api_name", stringToList(describeApiName),Operator.EQ))
    filterList.add(getFilter("is_deleted", stringToList("0"),Operator.EQ))
    filterList.add(getFilter("tenant_id", stringToList(tenantId),Operator.EQ))
    if (StringUtils.isNotBlank(dataName)){
      filterList.add(getFilter("name", stringToList(dataName),Operator.LIKE))
    }

//    List<OrderBy> orderList = Lists.newArrayList()
//    orderList.add(getOrder("last_modified_time","md_time",false,false))


    SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery()
    if (pageSize == null || pageSize > 100){
      searchTemplateQuery.setLimit(10)
    }else {
      searchTemplateQuery.setLimit(pageSize)
    }
    searchTemplateQuery.setSearchSource(searchSource)
    searchTemplateQuery.setFilters(filterList)
//    searchTemplateQuery.setOrders(orderList)
    searchTemplateQuery.setPermissionType(isAuth?1:0)
    searchTemplateQuery.setFindExplicitTotalNum(false)
    searchTemplateQuery.setNeedReturnCountNum(true)
    if (isAuth){
      searchTemplateQuery.setDataRightsParameter(getDataRightsParameter(sceneType))
    }


    Request request = new Request.Builder()
            .post(RequestBody.create(MediaType.parse("application/json;charset=utf-8"), getSearchParam(tenantId, userId, describeApiName, searchTemplateQuery.toJsonString()).getBytes(Charsets.UTF_8)))
            .url(this.searchQueryUrl).build()

    JSONObject result = okHttpSupport.syncExecute(request, new SyncCallback() {
      @Override
      Object response(Response response) throws Exception {
        return JSON.parse(new String(response.body().bytes(), Charsets.UTF_8))
      }
    }) as JSONObject

    if (result == null){
      return result
    }

    JSONObject jsonObject = result.getJSONObject("result")

    if (jsonObject == null){
      return result
    }

    JSONArray dataList = jsonObject.getJSONArray("data")

    if (dataList == null){
      return result
    }

    result.put("dataNum",dataList.size())

    for (JSONObject data: (dataList as List<JSONObject>)){
      data.remove("dataField")
      if (!detailInfo){
        if (data.get("containerDocument") != null){
          JSONObject document = data.get("containerDocument") as JSONObject
          List<String> documentKeySet = Lists.newArrayList(document.keySet());
          for (String key : documentKeySet){
            if (document.get(key) == null){
              document.remove(key)
            }
          }
        }
        List<String> keySet = Lists.newArrayList(data.keySet());
        for (String key : keySet){
          if (data.get(key) == null){
            data.remove(key)
          }
        }
      }
    }

    return result
  }


  List<String> stringToList(String value){
    List<String> list = Lists.newArrayList()
    list.add(value)
    return list
  }

  String getSearchParam(String tenantId, String userId, String describeApiName, String templateJson){
    JSONObject jsonObject = new JSONObject()
    jsonObject.put("tenantId",tenantId)
    jsonObject.put("userId",userId)
    jsonObject.put("objectDescribeAPIName",describeApiName)
    jsonObject.put("templateJson",templateJson)
    return jsonObject.toJSONString()
  }

  Filter getFilter(String fieldName, List<String> fieldValues, Operator operator){
    Filter filter = new Filter()
    filter.setFieldName(fieldName)
    filter.setFieldValues(fieldValues)
    filter.setOperator(operator)
    return filter
  }

  OrderBy getOrder(String fieldName, String indexName, boolean isAsc, boolean isReference){
    OrderBy order = new OrderBy()
    order.setFieldName(fieldName)
    order.setIndexName(indexName)
    order.setIsAsc(isAsc)
    order.setReference(isReference)
    return order
  }

  DataRightsParameter getDataRightsParameter(SceneType sceneType){
    DataRightsParameter dataRightsParameter = new DataRightsParameter()
    if (sceneType == SceneType.ALL){
      dataRightsParameter.setRoleType("1")
      dataRightsParameter.setSceneType("all")
      dataRightsParameter.setCascadeDept(true)
      dataRightsParameter.setCascadeSubordinates(true)
      dataRightsParameter.setIsDetailObject(false)
      dataRightsParameter.setLinkAppDataAuthRange(0)
    }else if (sceneType == SceneType.MY_PARTICIPANT){
      dataRightsParameter.setRoleType("4")
      dataRightsParameter.setSceneType("user")
      dataRightsParameter.setCascadeDept(false)
      dataRightsParameter.setCascadeSubordinates(false)
      dataRightsParameter.setIsDetailObject(false)
      dataRightsParameter.setLinkAppDataAuthRange(0)
    }else if (sceneType == SceneType.MY_RESPONSIBLE){
      dataRightsParameter.setRoleType("1")
      dataRightsParameter.setSceneType("user")
      dataRightsParameter.setCascadeDept(false)
      dataRightsParameter.setCascadeSubordinates(false)
      dataRightsParameter.setIsDetailObject(false)
      dataRightsParameter.setLinkAppDataAuthRange(0)
    }else if (sceneType == SceneType.MY_SUBORDINATE_RESPONSIBLE){
      dataRightsParameter.setRoleType("1")
      dataRightsParameter.setSceneType("sub")
      dataRightsParameter.setCascadeDept(true)
      dataRightsParameter.setCascadeSubordinates(true)
      dataRightsParameter.setIsDetailObject(false)
      dataRightsParameter.setLinkAppDataAuthRange(0)
    }else if (sceneType == SceneType.MY_RESPONSIBLE_DEPARTMENT){
      dataRightsParameter.setRoleType("")
      dataRightsParameter.setSceneType("dept")
      dataRightsParameter.setCascadeDept(true)
      dataRightsParameter.setCascadeSubordinates(true)
      dataRightsParameter.setIsDetailObject(false)
      dataRightsParameter.setLinkAppDataAuthRange(0)
    }else if (sceneType == SceneType.MY_SHARED_DATA){
      dataRightsParameter.setRoleType("1")
      dataRightsParameter.setSceneType("share")
      dataRightsParameter.setCascadeDept(false)
      dataRightsParameter.setCascadeSubordinates(false)
      dataRightsParameter.setIsDetailObject(false)
      dataRightsParameter.setLinkAppDataAuthRange(0)
    }else {
      dataRightsParameter.setRoleType("4")
      dataRightsParameter.setSceneType("sub")
      dataRightsParameter.setCascadeDept(true)
      dataRightsParameter.setCascadeSubordinates(true)
      dataRightsParameter.setIsDetailObject(false)
      dataRightsParameter.setLinkAppDataAuthRange(0)
    }
    return dataRightsParameter
  }
}
