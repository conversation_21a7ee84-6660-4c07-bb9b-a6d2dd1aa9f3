package com.fxiaoke.paas.console.web.functional

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.common.http.SimpleHttpClient
import com.fxiaoke.common.http.handler.SyncCallback
import com.fxiaoke.common.http.spring.OkHttpSupport
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.service.functional.FunctionalService
import com.github.autoconf.ConfigFactory
import com.google.common.base.CharMatcher
import com.google.common.base.Charsets
import com.google.common.base.Splitter
import com.google.common.base.Strings
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import okhttp3.MediaType
import okhttp3.Request
import okhttp3.Response
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.PostConstruct
import javax.annotation.Resource
import java.util.concurrent.atomic.AtomicReference

/**
 * 功能权限
 * <AUTHOR>
 * Created on 2018/3/27.
 */
@Controller
@Slf4j
@RequestMapping("/func")
class FunctionalController {

  private List<String> appIdList = Lists.newArrayList()
  private String roleUrl
  private String bizUrl
  private String batchRoleUrl
  private String batchUserUrl
  private String roleListUrl

  @Resource(name = "httpSupport")
  private OkHttpSupport client
  @Autowired
  FunctionalService functionalService

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("paas-console-base", { config ->
      appIdList = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(config.get("appId"))
      roleUrl = config.get("roleUrl")
      bizUrl = config.get("bizUrl")
      batchRoleUrl = config.get("batchRoleUrl")
      batchUserUrl = config.get("batchUserUrl")
      roleListUrl = config.get("roleListUrl")
    })
  }

  /**
   * 角色权限
   * @return
   */
  @RequestMapping("/role")
  def role(ModelMap model) {
    model["appIdList"] = appIdList
    "functional/role-func"
  }

  /**
   * 查询角色功能权限
   * @param dataObject
   */
  @RequestMapping("/role-limit")
  @ResponseBody
  @SystemControllerLog(description = "功能权限 -- 查询角色功能权限")
  def roleLimit(@RequestParam String dataObject) {

    JSONObject jsonObject = JSONObject.parseObject(dataObject)
    def tenantId = jsonObject.getString("tenantId").trim()
    def roleCode = jsonObject.getString("roleCode").trim()
    def userId = jsonObject.getString("userId").trim()
    if (Strings.isNullOrEmpty(tenantId) || Strings.isNullOrEmpty(roleCode) || Strings.isNullOrEmpty(userId)) {
      log.info("关键字段为空.")
      return ["data": []]
    }
    try {

      def authContext = Maps.newHashMap()
      authContext['tenantId'] = tenantId
      authContext['userId'] = userId
      authContext['appId'] = jsonObject.getString("appId").trim()

      def body = Maps.newHashMap()
      body['roleCode'] = roleCode
      body['authContext'] = authContext


      AtomicReference<String> atomicReference=new AtomicReference<>()
      Request request = new Request.Builder().header("x-fs-ei",tenantId).post(okhttp3.RequestBody.create(MediaType.parse("application/json;charset=utf-8"), JSONObject.toJSONString(body))).url(roleUrl).build()
      client.syncExecute(request,new SyncCallback() {
        @Override
        Object response(Response response) throws Exception {
          return atomicReference.set(new String(response.body().bytes(), Charsets.UTF_8))
        }
      })
      JSONObject result = JSONObject.parseObject(atomicReference.get())
      if (!result.get("success")) {
        log.error("查询角色权限失败，error：", result.getString("errMessage"))
        return ["data": []]
      }
      def resultList = result.get("result")
      if ((resultList as List).size() == 0) {
        log.info("查询无结果")
        return ["data": []]
      }
      def json = resultList.collect { it ->
        [it]
      }

      return ["data": json]
    } catch (Exception e) {
      log.error("查询角色权限异常，error:", e)
      return ["data": []]
    }
  }

  /**
   * 业务权限
   * @return
   */
  @RequestMapping("/biz")
  def biz() {
    "functional/biz-func"
  }

  /**
   * 查询业务权限
   * @param dataObject
   */
  @RequestMapping("/biz-func")
  @ResponseBody
  @SystemControllerLog(description = "功能权限 -- 查询业务权限")
  def bizFunc(@RequestBody String dataObject) {

  }

  /**
   * 用户分配的角色
   * @return
   */
  @RequestMapping("/user-role")
  def batchRoleFun(ModelMap model) {
    model["appIdList"] = appIdList
    "functional/user-role"
  }

  /**
   * 批量查询用户分配的角色
   * @param dataObject
   */
  @RequestMapping("/batch-role")
  @ResponseBody
  @SystemControllerLog(description = "功能权限 -- 批量查询用户分配的角色")
  def batchRoleLimit(@RequestParam String tenantId, @RequestParam String appId,
                     @RequestParam String userId, @RequestParam String users) {
    if (Strings.isNullOrEmpty(tenantId) || Strings.isNullOrEmpty(appId) ||
            Strings.isNullOrEmpty(userId) || Strings.isNullOrEmpty(users)) {
      log.error("关键词为空")
      return ["code": 403, "error": "关键词为空"]
    }
    try {
      def userList = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(users.trim())

      def authContext = Maps.newHashMap()
      authContext["tenantId"] = tenantId.trim()
      authContext["appId"] = appId.trim()
      authContext["userId"] = userId.trim()
      def body = Maps.newHashMap()
      body["authContext"] = authContext
      body["users"] = userList


      AtomicReference<String> atomicReference=new AtomicReference<>()
      Request request = new Request.Builder().header("x-fs-ei",tenantId)
              .post(okhttp3.RequestBody.create(MediaType.parse("application/json;charset=utf-8"), JSONObject.toJSONString(body)))
              .url(batchRoleUrl).build()
      client.syncExecute(request,new SyncCallback() {
        @Override
        Object response(Response response) throws Exception {
          return atomicReference.set(new String(response.body().bytes(), Charsets.UTF_8))
        }
      })

      JSONObject result = JSONObject.parseObject(atomicReference.get())
      if (!result.get("success")) {
        log.info("批量查询用户分配的角色失败，error：", result.get("errMessage"))
        return ["code": 500, "error": "批量查询用户分配的角色失败"]
      }

      def resultMap = JSONObject.parseObject(result.get("result").toString(), Map.class)

      def json = []
      resultMap.forEach({ key, value ->
        json << [key, value]
      })

      return ["code": 200, "data": json]
    } catch (Exception e) {
      log.error("批量查询用户分配的角色异常，error：", e)
      return ["code": 500, "error": "批量查询用户分配的角色异常"]
    }
  }

  /**
   * 角色下的用户
   */
  @RequestMapping("/find-user")
  def roleUser(ModelMap model) {
    model["appIdList"] = appIdList
    "functional/role-user"
  }

  /**
   * 批量查询角色下的用户
   * @param dataObject
   */
  @RequestMapping("/batch-user")
  @ResponseBody
  @SystemControllerLog(description = "功能权限 -- 批量查询角色下的用户")
  def batchFindUserByRole(@RequestParam String tenantId, @RequestParam String appId,
                          @RequestParam String userId, @RequestParam String roles) {
    if (Strings.isNullOrEmpty(tenantId) || Strings.isNullOrEmpty(appId) ||
            Strings.isNullOrEmpty(userId) || Strings.isNullOrEmpty(roles)) {
      log.error("关键词为空")
      ["code": 500, "error": "关键词为空"]
    }
    try {

      def rolesList = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(roles)

      def authContext = Maps.newHashMap()
      authContext["tenantId"] = tenantId.trim()
      authContext["appId"] = appId.trim()
      authContext["userId"] = userId.trim()
      def body = Maps.newHashMap()
      body["authContext"] = authContext
      body["roles"] = rolesList
      AtomicReference<String> atomicReference=new AtomicReference<>()
      Request request = new Request.Builder().header("x-fs-ei",tenantId)
              .post(okhttp3.RequestBody.create(MediaType.parse("application/json;charset=utf-8"), JSONObject.toJSONString(body)))
              .url(batchUserUrl).build()

      client.syncExecute(request,new SyncCallback() {
        @Override
        Object response(Response response) throws Exception {
          return atomicReference.set(new String(response.body().bytes(), Charsets.UTF_8))
        }
      })

      JSONObject result = JSONObject.parseObject(atomicReference.get())
      if (!result.get("success")) {
        log.info("批量查询角色下的用户失败，error：", result.get("errMessage"))
        return ["code": 500, "data": []]
      }

      def resultMap = JSONObject.parseObject(result.get("result").toString(), Map.class)
      def json = []
      resultMap.forEach({ key, value ->
        json << [key, value]
      })

      return ["code": 200, "data": json]

    } catch (Exception e) {
      log.error("批量查询角色下的用户异常，error:", e)
      return ["code": 500, "error": "批量查询角色下的用户异常"]
    }
  }

  /**
   * 角色列表
   * @return
   */
  @RequestMapping("/find-roles")
  def roleList(ModelMap model) {
    model["appIdList"] = appIdList
    "functional/role-list"
  }

  /**
   * 查询角色列表
   * @param dataObject
   */
  @RequestMapping("/role-list")
  @ResponseBody
  @SystemControllerLog(description = "功能权限 -- 查询角色列表")
  def findRoleList(@RequestBody String dataObject) {
    try {
      def body = functionalService.checkBody(dataObject)
      AtomicReference<String> atomicReference=new AtomicReference<>()
      Request request = new Request.Builder().header("x-fs-ei",((Map)body.get("authContext")).get("tenantId"))
              .post(okhttp3.RequestBody.create(MediaType.parse("application/json;charset=utf-8"), JSONObject.toJSONString(body)))
              .url(roleListUrl).build()
      client.syncExecute(request,new SyncCallback() {
        @Override
        Object response(Response response) throws Exception {
          return atomicReference.set(new String(response.body().bytes(), Charsets.UTF_8))
        }
      })
      JSONObject result = JSONObject.parseObject(atomicReference.get())
      if (!result.get("success")) {
        log.error("查询角色列表失败")
        return ["code": 500, "error": "查询角色列表失败", "data": []]
      }
      if (result.get("result") == null) {
        return ["code": 203, "warn": "角色为空", "data": []]
      }
      def roles = result.get("result")["roles"] as List
      if (roles.size() == 0) {
        log.info("result下的roles为空")
        return ["code": 203, "error": "角色为空", "data": []]
      }
//      def resultArray = JSONObject.parseObject(result.get("result").toString(), Map.class).get("roles")
      def json = []
      roles.each { it ->
        json << [it["roleCode"], it["roleName"], it["roleType"], it["appId"], it["tenantId"], it["description"], it["delFlag"]]
      }
      return ["code": 200, "data": json]
    } catch (Exception e) {
      log.error("查询角色列表异常,error:", e)
      return ["code": 500, "error": "查询角色列表异常", "data": []]
    }

  }

}
