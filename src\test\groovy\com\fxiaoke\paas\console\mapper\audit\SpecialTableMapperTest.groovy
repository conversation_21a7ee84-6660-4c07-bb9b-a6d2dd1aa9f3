package com.fxiaoke.paas.console.mapper.audit

import com.fxiaoke.paas.console.entity.log.SpecialTable
import com.fxiaoke.paas.console.mapper.log.SpecialTableMapper
import com.google.common.collect.Lists
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * <AUTHOR>
 * Created on 2018/7/24.
 */
@ContextConfiguration(value = ['classpath:mapperContext.xml'])
class SpecialTableMapperTest extends Specification {

  @Autowired
  SpecialTableMapper specialTableMapper

  def "insert"() {
    given:
    def tables = "AdvertisementObj,广告,advertisement#CasesObj,工单,cases#CheckinsObj,高级外勤对象,checkins_data#CheckinsImgObj,外勤图片,checkins_img_obj#CustomerAccountObj,客户账户,customer_account#DeliveryNoteObj,发货单,delivery_note#DeliveryNoteProductObj,发货单产品,delivery_note_product#GoalRuleObj,目标规则配置,goal_rule#GoalRuleApplyCircleObj,目标规则适用部门,goal_rule_apply_circle#GoalRuleDetailObj,子目标规则配置,goal_rule_detail#GoalValueObj,目标值,goal_value#GoodsReceivedNoteObj,入库单,goods_received_note#GoodsReceivedNoteProductObj,入库单产品,goods_received_note_product#MenuObj,CRM菜单,menu#MenuItemObj,CRM菜单项,menu_item#MenuWorkbenchObj,CRM菜单项,menu_workbench#PersonnelObj,人员,org_employee_user#OutboundDeliveryNoteObj,出库单,outbound_delivery_note#OutboundDeliveryNoteProductObj,出库单产品,outbound_delivery_note_product#PartnerObj,合作伙伴,partner#PaymentObj,回款,payment_customer#OrderPaymentObj,回款明细,payment_order#PaymentPlanObj,回款计划,payment_plan#PrepayDetailObj,预存款,prepay_detail#PriceBookObj,价目表,price_book#PriceBookProductObj,价目表明细,price_book_product#PromotionObj,促销,promotion#PromotionProductObj,促销产品,promotion_product#PromotionRuleObj,促销规则,promotion_rule#QuoteObj,报价单,quote#QuoteLinesObj,报价单明细,quote_lines#RebateIncomeDetailObj,返利,rebate_income_detail#RebateOutcomeDetailObj,返利支出,rebate_outcome_detail#RebateUseRuleObj,返利使用规则,rebate_use_rule#RequisitionNoteObj,调拨单,requisition_note#RequisitionNoteProductObj,调拨单产品,requisition_note_product#ReturnedGoodsInvoiceObj,退货单,return_order#ReturnedGoodsInvoiceProductObj,退货单关联的产品,return_order_product#RoleSourceObj,权限和资源的关联表,role_source#sign_in_info,防作弊信息,sign_anti_cheating_log#StockObj,库存,stock#WarehouseObj,仓库,warehouse#ErpStockObj,ERP库存,erp_stock#ErpWarehouseObj,ERP仓库,erp_warehouse#SignTenantCertifyObj,租户认证管理,sign_tenant_certify#SignUserCertifyObj,用户认证管理,sign_user_certify#SignRecordObj,签署记录,sign_record#InternalSignCertifyObj,内部签章认证,internal_sign_certify#ProductPackageLinesObj,产品包明细,product_package_lines#WechatFanObj,微信粉丝,wechat_fan#ProductPackageObj,产品包,product_package#DeviceObj,设备,device_data#DevicePartObj,设备配件规格关系,device_part#CheckRecordObj,设备巡检,device_check_record#org_dept,部门,org_dep"
    List<String> list = tables.split("#")
    List<SpecialTable> specialTableList = Lists.newArrayList()
    list.each { it ->
      def l = it.split(",")
      SpecialTable specialTable = SpecialTable.builder().describeApiName(l[0])
              .label(l[1]).storeTableName(l[2]).createdTime(new Date()).build()
      specialTableList.add(specialTable)
    }
    expect:
    specialTableMapper.batchInsert(specialTableList)
  }

}
