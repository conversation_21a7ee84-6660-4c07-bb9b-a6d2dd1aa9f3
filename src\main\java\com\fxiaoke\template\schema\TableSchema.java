package com.fxiaoke.template.schema;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
public class TableSchema {
    /**
     * 表名
     */
    private final String tableName;
    /**
     * 列数量
     */
    private final int columnCount;
    /**
     * 用来做查询指定列的sql
     */
    private final String columnSelectNames;
    /**
     * 所有列名
     */
    private final List<String> columnNames;
    /**
     * 所有列
     */
    private final List<ColumnSchema> columnList;
    /**
     * list to map
     */
    private Map<String, ColumnSchema> columnMap;

    public TableSchema(String tableName, List<ColumnSchema> columnList) {
        this.tableName = tableName;
        this.columnCount = columnList.size();
        this.columnSelectNames = columnList.stream().map(it -> "\"" + it.getName() + "\"").collect(Collectors.joining(","));
        this.columnNames = columnList.stream().map(it -> it.getName()).collect(Collectors.toList());
        this.columnList = columnList;
        this.columnMap = columnList.stream().collect(Collectors.toMap(ColumnSchema::getName, it -> it));
    }

    @Data
    @Builder
    public static class ColumnSchema {
        private String name;
        private boolean primary;
        private int index;
        private int type;
        private String typeName;
    }
}
