package com.fxiaoke.template.server;

import com.facishare.paas.license.arg.JudgeModuleArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.common.Result;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.JudgeModulePojo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
public class LicenseServer2 {


  @Resource(name = "licenseClient")
  private LicenseClient licenseClient;

  public boolean judgeModule() {
    String moduleCode = "advanced_outwork_app";

    LicenseContext context = new LicenseContext();
    context.setUserId("1000");
    context.setAppId("CRM");
    context.setTenantId("88");

    JudgeModuleArg arg = new JudgeModuleArg();
    arg.setContext(context);
    arg.setModuleCodes(Lists.newArrayList(moduleCode));

    Result<JudgeModulePojo> result = licenseClient.judgeModule(arg);
    if (Objects.nonNull(result) && Objects.nonNull(result.getResult()) && CollectionUtils.isNotEmpty(result.getResult().getModuleFlags())) {
      return result.getResult().getModuleFlags().stream().anyMatch(o -> o.isFlag() && moduleCode.equals(o.getModuleCode())) ? true : false;
    }
    return true;
  }

}
