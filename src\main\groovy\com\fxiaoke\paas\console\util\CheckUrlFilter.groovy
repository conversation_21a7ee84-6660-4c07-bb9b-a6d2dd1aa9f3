package com.fxiaoke.paas.console.util

import groovy.util.logging.Slf4j
import org.apache.shiro.SecurityUtils

import javax.servlet.*
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse
import javax.servlet.http.HttpServletResponseWrapper

/**
 * 角色权限过滤器
 */
@Slf4j
class CheckUrlFilter implements Filter {

  private static final String UNAUTHORIZED = "/paas-console/unauthorized"
  public FilterConfig config

  @Override
  void init(FilterConfig filterConfig) throws ServletException {
    this.config = filterConfig
  }

  @Override
  void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
    HttpServletRequest hrequest = (HttpServletRequest) request
    HttpServletResponseWrapper wrapper = new HttpServletResponseWrapper((HttpServletResponse) response)

    String redirectPath = config.getInitParameter("redirectPath")

    String[] redirectPathList = redirectPath.split(";")
    if (this.isContains(hrequest.getRequestURI(), redirectPathList)) {
      chain.doFilter(request, response)
    } else {
      wrapper.sendRedirect(UNAUTHORIZED)
    }

  }

  /**
   * 判断是否满足对应角色
   * @param url
   * @param regx
   * @return
   */
  private boolean isContains(String url, String[] regx) {
    boolean result = false
    if (regx.contains(url)) {
      if (SecurityUtils.getSubject().hasRole("paas-console-superadmin")) {
        result = true
      }
    } else {
      result = true
    }
    return result
  }

  @Override
  void destroy() {
    this.config = null
  }
}
