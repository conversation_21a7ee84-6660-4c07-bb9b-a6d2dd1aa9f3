package com.fxiaoke.paas.console.util

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alibaba.fastjson.parser.Feature
import com.fxiaoke.common.http.handler.AsyncCallback
import com.fxiaoke.common.http.handler.SyncCallback
import com.fxiaoke.common.http.spring.OkHttpSupport
import com.github.autoconf.ConfigFactory
import groovy.util.logging.Slf4j
import okhttp3.MediaType
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import org.apache.commons.collections.MapUtils

/**
 * Created by yangxw on 2018/3/15.
 */
@Slf4j
class HttpClientUtil {

  private static OkHttpSupport client;

  void setClient(OkHttpSupport client) {
    HttpClientUtil.client = client;
  }

  def static executeCallBack(String url, Map<String, Object> headers) {
    log.info("http get url:{},header:{}", url, headers);
    Request.Builder builder = new Request.Builder();

    if (MapUtils.isNotEmpty(headers)) {
      headers.each { k, v -> builder.addHeader(k, v.toString()) };
    }
    Request request = builder.url(url).build();
    return (JSONObject) client.syncExecute(request, new SyncCallback() {
      @Override
      Object response(Response response) throws Exception {
        if (response != null) {
          if (response.isSuccessful()) {
            String res = response.body().string();
            JSONObject jsonObject = (JSONObject) JSON.parse(res, Feature.config(0, Feature.UseBigDecimal, false));
            log.info("http get url:{},code: {} result:{}", url, response.code(), JSON.toJSONString(jsonObject));
            return jsonObject;
          } else {
            log.info("http get url:{}, code: {}, message:{}", url, response.code(), response.message());
          }
        } else {
          log.info("http get url:{}, response is null", url);
        }
        return new JSONObject();
      }
    });
  }

  def static get(String url, Map<String, Object> headers) {
    try {
      return executeCallBack(url, headers);
    } catch (Exception e) {
      log.error("HttpClientUtil.get() Error, url:{}, headers:{}", url, headers);
      log.error("HttpClientUtil.get() Error", e);
    }
    return new JSONObject();
  }

  def static getException(String url, Map<String, Object> headers) throws Exception {
    return executeCallBack(url, headers);
  }

  def static post(String configName, String key, Map<String, Object> headers, Object entity, String mediaType) {
    String url = null;
    try {
      url = ConfigFactory.getConfig(configName).get(key);
    } catch (Exception e) {
      log.error("configName:{}, key:{}", configName, key);
      log.error("HttpClientUtil getConfig Error", e);
    }
    return post(url, headers, entity, mediaType);
  }


  def static post(String url, Map<String, Object> headers, Object entity, String type) {
    log.info("post url:{}, headers:{}, entity:{}", url, JSON.toJSON(headers), JSON.toJSON(entity));
    try {
      MediaType mediaType = MediaType.parse(type);
      RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(entity));

      Request.Builder builder = new Request.Builder();

      if (MapUtils.isNotEmpty(headers)) {
        headers.each { k, v -> builder.addHeader(k, v.toString()) };
      }
      Request request = builder.url(url).post(body).build();

      return (JSONObject) client.syncExecute(request, new SyncCallback() {
        @Override
        def Object response(Response response) throws Exception {
          if (response != null) {
            if (response.isSuccessful()) {
              String res = response.body().string();
              JSONObject jsonObject = JSONObject.parseObject(res);
              log.info("http post url:{}, code: {}, result:{}", url, response.code(), JSON.toJSONString(jsonObject));
              return jsonObject;
            } else {
              log.info("http post url:{}, code: {}, message:{}", url, response.code(), response.message());
            }
          } else {
            log.info("http post url:{}, response is null", url);
          }
          return new JSONObject();
        }
      });
    } catch (Exception e) {
      log.error("HttpClientUtil.post() Error, url:{}, headers:{}, entity:{}, mediaType:{}", url, headers, JSON.toJSON(entity), type);
      log.error("HttpClientUtil.post() Error", e);
    }
    return new JSONObject();
  }

  /**
   * 异步执行 post请求
   */
  def static asyncPost(String url, Map<String, Object> headers, Object entity, String type) {
    log.info("post url:{}, headers:{}, entity:{}", url, JSON.toJSON(headers), JSON.toJSON(entity));
    try {

      Request.Builder builder = new Request.Builder();
      if (MapUtils.isNotEmpty(headers)) {
        headers.each { k, v -> builder.addHeader(k, v.toString()) };
      }
      RequestBody body = RequestBody.create(MediaType.parse(type), JSONObject.toJSONString(entity));
      Request request = builder.url(url).post(body).build();

      client.asyncExecute(request, new AsyncCallback() {
        @Override
        def void response(Response response) throws IOException {
          if (response != null) {
            if (response.isSuccessful()) {
              String res = response.body().string();
              JSONObject jsonObject = JSONObject.parseObject(res);
              log.info("async http post url:{}, code: {}, result:{}", url, response.code(), JSON.toJSONString(jsonObject));
            } else {
              log.info("async http post url:{}, code: {}, message:{}", url, response.code(), response.message());
            }
          } else {
            log.info("async http post url:{}, response is null", url);
          }
        }
      });
    } catch (Exception e) {
      log.error("HttpClientUtil.post() Error, url:{}, headers:{}, entity:{}, mediaType:{}", url, headers, entity, type);
      log.error("HttpClientUtil.post() Error", e);
    }
  }

  private HttpClientUtil() {
  }

}
