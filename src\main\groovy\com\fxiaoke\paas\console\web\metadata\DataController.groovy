package com.fxiaoke.paas.console.web.metadata

import com.alibaba.fastjson.JSON
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.service.metadata.DataService
import com.fxiaoke.paas.console.util.HttpClientUtil
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.elasticsearch.common.Strings
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.Resource

/**
 * <AUTHOR>
 * Created on 2018/3/5.
 */
@Controller
@Slf4j
@RequestMapping("/metadata/data")
class DataController {
  @Resource
  private DataService dataService

  /**
   * 查询数据
   * @param data
   * @return
   */
  @RequestMapping(value = "")
  def data() {
    return "metadata/data"
  
  }

  /**
   * 查询数据
   * @param data
   * @return
   */
  @RequestMapping(value = "/t2n_page")
  def refreshEsFieldPage() {
    return "metadata/t2n"

  }

  /**
   * 查询数据详情
   * @param id
   * @param tenantId
   * @param tableName
   */
  @RequestMapping(value = "/info")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- 查询数据详情")
  def dataInfo(@RequestParam String tenantId, @RequestParam String apiName, @RequestParam(required = false) String name, @RequestParam String id) {
    if (Strings.isEmpty(name)) {
      Map<String, Object> dataInfo = dataService.dataInfo(tenantId.trim(), apiName.trim(), id.trim())
      ["dataInfo": dataInfo == null ? "" : dataInfo]
    } else {
      List<Map<String, Object>> dataInfo = dataService.getDataByName(tenantId.trim(), apiName.trim(), name.trim())
      ["dataInfo": dataInfo == null ? "" : JSON.toJSONString(dataInfo)]
    }
  }

  /**
   * 查询数据详情
   * @param id
   * @param tenantId
   * @param tableName
   */
  @RequestMapping(value = "/t2n")
  @ResponseBody
  @SystemControllerLog(description = "Es文本字段转编号类字段")
  def fieldText2Ngram(@RequestParam String tenantId, @RequestParam String objectApiName, @RequestParam String fieldApiName) {

    if(StringUtils.isBlank(tenantId) || StringUtils.isBlank(objectApiName) || StringUtils.isBlank(fieldApiName)){
      ["result": "{\"result\":\"params must not empty\"}"]
    }
    dataService.updateEsFieldType(tenantId,objectApiName,fieldApiName)
    ["result": "{\"result\":\"ok\"}"]
  }

}
