package com.fxiaoke.paas.console.mapper.metadata

import com.github.mybatis.mapper.IBatchMapper
import com.github.mybatis.mapper.ICrudMapper
import com.github.mybatis.mapper.ITenant
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Param

interface MtIndexMapper extends ICrudMapper, IBatchMapper, ITenant<MtIndexMapper>  {

  @Delete("delete FROM mt_index where tenant_id=#{tenantId} and index_id in (select index_id from mt_index where tenant_id=#{tenantId} and describe_api_name=#{describeApiName} and field_num in (select field_num from mt_field where tenant_id =#{tenantId} and describe_api_name =#{describeApiName} and api_name not in (\${blackFieldApiNames}) and status='deleted' and field_num is not null and is_index_field=true) limit 100)")
  int recycleMtIndex(@Param("tenantId")String tenantId, @Param("describeApiName")String describeApiName, @Param("blackFieldApiNames")String blackFieldApiNames);
}
