package com.fxiaoke.paas.console.service.metadata

import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData
import com.facishare.uc.api.service.EnterpriseEditionService
import com.fxiaoke.common.Pair
import com.fxiaoke.helper.StringHelper
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.paas.common.service.SchemaHelper
import com.fxiaoke.paas.console.bean.organization.object.EnterpriseObject
import com.fxiaoke.paas.console.entity.metadata.MtDescribe
import com.fxiaoke.paas.console.mapper.metadata.StatMapper
import com.fxiaoke.paas.console.util.DateFormatUtil
import com.fxiaoke.paas.console.util.organization.typeconvert.EnterpriseConvert
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * Created on 2018/3/6.
 */
@Service
@Slf4j
class StatService {

  private static final Integer THREE_MONTH = 3

  @Autowired
  StatMapper statMapper
  @Autowired
  SqlQueryService sqlQueryService
  @Autowired
  EnterpriseEditionService enterpriseEditionService
  @Autowired
  JdbcService jdbcService
  @Autowired
  SchemaHelper schemaHelper

  /**
   * 获取所有对象数量
   * @param tenantId
   * @return
   */
  def findDescribeSumNum(String tenantId) {
    return statMapper.setTenantId(tenantId).findDescribeNumByTenantId(tenantId)
  }

  /**
   * 查询企业ID下的有效describe
   * @param tenantId
   * @param tableNameList 专表白名单
   * @param startDate 起始时间
   * @param endDate 结束时间
   * @return
   */
  def findDescribeByTenantId(String tenantId, String startDate, String endDate) {
    /**
     * 查询全部对象
     */
    def allDescribeList
    Date startTime
    Date endTime
    if (!startDate.isEmpty()) {
//             处理时间
      if (endDate == "now()") {
        Calendar calendar = Calendar.getInstance()
        endTime = calendar.getTime()
      } else {
        endTime = DateFormatUtil.formatString(endDate)
      }
      if (startDate.contains("now()")) {
        Calendar calendar = Calendar.getInstance()
//        判断是否是月数
        if (startDate.contains("m")) {
          def h = startDate.replace("now()-", "").replace("m", "")
          calendar.add(Calendar.MONTH, -Integer.valueOf(h))
//          判断是年份
        } else if (startDate.contains("y")) {
          def h = startDate.replace("now()-", "").replace("y", "")
          calendar.add(Calendar.YEAR, -Integer.valueOf(h))
          //          判断是否是天数
        } else if (startDate.contains("d")) {
          def h = startDate.replace("now()-", "").replace("d", "")
          calendar.add(Calendar.DATE, -Integer.valueOf(h))
        } else {
          def h = startDate.replace("now()-", "").replace("h", "")
          //获取当前时间前h个小时
          calendar.add(Calendar.HOUR, -Integer.valueOf(h))
        }

        startTime = calendar.getTime()
      } else {
        startTime = DateFormatUtil.formatString(startDate)
      }
      /**
       * 查询
       */
      allDescribeList = statMapper.setTenantId(tenantId).findDescribeByTime(tenantId, startTime.getTime(), endTime.getTime())
    } else {
      allDescribeList = statMapper.setTenantId(tenantId).findDescribeByTenantId(tenantId)
    }

    def map = Maps.newLinkedHashMap()
    /**
     * 专表apiName-storeTableName
     */
    List<Map> mapList = Lists.newArrayList()

    /**
     * 通表对象list
     */
    def mtDataDescribeApiNames = Lists.newLinkedList()
    /**
     * 专表对象List
     */
    def specialDescribeApiName = Lists.newLinkedList()

    allDescribeList.each { mtDescribe ->
//      判断专表字段是否为空
      if (!StringHelper.isNullOrBlank(mtDescribe.getStoreTableName())) {
        map["describeApiName"] = mtDescribe.getDescribeApiName()
        map["storeTableName"] = mtDescribe.getStoreTableName()
        mapList.add(map)
        specialDescribeApiName.add(mtDescribe.getDescribeApiName())
      } else {
        /**
         * 通表对象
         */
        mtDataDescribeApiNames.add(mtDescribe.getDescribeApiName())
      }
    }

    /**
     * 通表对象的数据量
     */
    List<Map> onDesNumMapList = Lists.newLinkedList()
    if (!mtDataDescribeApiNames.isEmpty()) {
      mtDataDescribeApiNames.collate(200).each {
        onDesNumMapList.addAll(statMapper.setTenantId(tenantId).batchFindDesNum(tenantId, it as List<String>))
      }

    }
    /**
     * 专表对象在通表中的数据量查询
     */
    List<Map> describeNumList = Lists.newLinkedList()
//        if (!specialDescribeApiName.isEmpty()) {
////            specialDescribeApiName.collate(200).each {
////                describeNumList.addAll(statMapper.setTenantId(tenantId).batchFindDesNum(tenantId, it as List<String>))
////            }
////        }
    /**
     * 专表中数量查询
     */
    mapList.each { m ->
      if (StringHelper.isNullOrBlank(m.get("storeTableName") as String)) {
        return
      }
//            margeMapList(describeNumList, findSpecialTableNum(tenantId, m.get("storeTableName") as String, m.get("describeApiName") as String))
      describeNumList.add(findSpecialTableNum(tenantId, m.get("storeTableName") as String, m.get("describeApiName") as String))
    }
    /**
     * 合并到同一list
     */
    List<Map> dataNumMapList = Lists.newArrayList()
    dataNumMapList.addAll(onDesNumMapList)
    dataNumMapList.addAll(describeNumList)

    log.info(describeNumList as String)
    return dataNumMapList
  }

  /**
   * 获取数据量
   * @param tenantId
   * @param storeTableName
   * @param describeApiName
   * @return
   */
  def findSpecialTableNum(String tenantId, String storeTableName, String describeApiName) {
    def fields = statMapper.setTenantId(tenantId).findFieldByTableName(storeTableName)
    List<String> fieldNames = Lists.newArrayList()
    def isDeletedType
    fields.each { map ->
      fieldNames.add(map.get("column_name") as String)
      if (map.get("column_name") == "is_deleted") {
        isDeletedType = map.get("data_type")
      }
    }
    def map = Maps.newHashMap()
    if (fieldNames.contains("ei")) {
      if ("boolean" == isDeletedType) {
        map["describe_api_name"] = describeApiName
        map["count"] = statMapper.setTenantId(tenantId).findStoreTableNumByEi(storeTableName, tenantId)
        return map
      }
      map["describe_api_name"] = describeApiName
      map["count"] = statMapper.setTenantId(tenantId).findStoreTableNumByEiAndIsDeleted(storeTableName, tenantId)
      return map
    }
    return statMapper.setTenantId(tenantId).findDataNum(storeTableName, tenantId, describeApiName)
  }

  /**
   * 合并存在专表对象的数据量
   * @param list
   * @param map
   * @return
   */
  static def margeMapList(List<Map> list, Map map) {
    list.each { name ->
      if (name.get("object_describe_api_name") == map.get("object_describe_api_name")) {
        name["count"] = name.get("count") + map.get("count")
      }
    }
    return list
  }

  /**
   * 过滤表名白名单
   * @param tableNames 白名单
   * @param tableName 表名
   * @return
   */
  static def checkTableName(List<String> tableNames, String tableName) {
    return tableNames.contains(tableName)
  }

  /**
   * 获取指定tenantId下的describeId
   * @param tenantId
   * @param describeApiNames
   * @return
   */
  def findDescribeIds(String tenantId, List<String> describeApiNames) {
    List<String> describeIds = Lists.newArrayList()

    describeApiNames.collate(100).each {
      describeIds.addAll(statMapper.setTenantId(tenantId).findDescribeIds(tenantId, it as List<String>))
    }
    return describeIds
  }

  /**
   * 获取field数量
   * @param tenantId
   * @param describeIds
   * @return
   */
  def findFieldSum(String tenantId, List<String> describeIds) {
    Integer num = 0
    describeIds.collate(200).each {
      num += statMapper.setTenantId(tenantId).findFieldSum(it)
    }
    return num
  }

  /**
   * 获取当前tenantId下的field列表
   * @param tenantId
   * @param describeApiNames
   * @return
   */
  def findField(String tenantId, List<String> describeApiNames) {
//    先获取describeIds
    List<String> describeIds = statMapper.setTenantId(tenantId).findDescribeIds(tenantId, describeApiNames)
//    获取对应的所有field
    List<Map<String, Object>> fieldMapList = statMapper.setTenantId(tenantId).findField(describeIds)
    return fieldMapList
  }

  /**
   *  获取指定对象下的字段统计图list(暂时没用)
   * @param tenantId
   * @param describeApiName
   * @return
   */
  def findFieldStatList(String tenantId, String describeApiName) {
//      先获取describeids
    List<String> describeIds = statMapper.setTenantId(tenantId).findAppointDescribeIds(tenantId, describeApiName)
//    获取describeId对应的字段数
    List<Integer> fieldNum = statMapper.setTenantId(tenantId).findFieldNumByDescribeIds(describeIds)
    Map<String, Object> map = Maps.newHashMap()
    map["xName"] = describeIds
    map["xNameNum"] = fieldNum
    return map
  }

  /**
   * 统计指定对象的数据量曲线图
   * @param tenantId
   * @param describeApiName
   * @return
   */
  def findDescribeDataLine(String tenantId, String describeApiName) {
    if (schemaHelper.isSpecialSchema(tenantId)) {
      return null
    }
    String findMtDescribeSql = "SELECT * FROM mt_describe WHERE tenant_id = '" + tenantId + "' AND describe_api_name = '" + describeApiName + "' AND is_current = true"
    Pair<JdbcConnection, String> pair = jdbcService.conn(tenantId, findMtDescribeSql)
//    先获取指定的对象
    MtDescribe mtDescribe = statMapper.setTenantId(tenantId).findMtDescribe2(pair.second)
    if (mtDescribe == null) {
      return null
    }
//    获取最近三个月数据量（按last_modify_time降序）
    Calendar calendar = Calendar.getInstance()
    calendar.add(Calendar.MONTH, -Integer.valueOf(THREE_MONTH))
//    只看通表
    String findDataNumSql = "select last_modified_time from mt_data where object_describe_id = '" + mtDescribe.getDescribeId() + "' AND last_modified_time > " + calendar.getTimeInMillis() + " ORDER BY last_modified_time"
    Pair<JdbcConnection, String> pair2 = jdbcService.conn(tenantId, findDataNumSql)
    List<Object> mapList = statMapper.setTenantId(tenantId).findDataNumByDescribeId2(pair2.second)
    return mapList
  }

  /**
   * 根据ea获取tenantId
   * @param ea
   * @return
   */
  String getTenantIdByEa(List eas) {
    BatchGetSimpleEnterpriseDataArg batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg()
    batchGetSimpleEnterpriseDataArg.setEnterpriseAccounts(eas)
    BatchGetSimpleEnterpriseDataResult batchGetSimpleEnterpriseDataResult
    try {
      //调用dubbo接口
      batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
    } catch (Exception e) {
      log.error("查询企业信息异常，error:{}", e)
      return ""
    }
    List<SimpleEnterpriseData> simpleEnterpriseDataList = batchGetSimpleEnterpriseDataResult.getSimpleEnterpriseList()
    for (SimpleEnterpriseData simpleEnterpriseData : simpleEnterpriseDataList) {
      EnterpriseObject enterpriseObject = EnterpriseConvert.enterpriseDataConvertEnterpriseObject(simpleEnterpriseData)
      return enterpriseObject.enterpriseId as String
    }

  }


}
