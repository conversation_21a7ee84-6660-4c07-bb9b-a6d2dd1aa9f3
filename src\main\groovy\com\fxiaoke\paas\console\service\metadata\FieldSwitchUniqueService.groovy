package com.fxiaoke.paas.console.service.metadata

import com.alibaba.fastjson.JSON
import com.facishare.paas.metadata.api.UniqueIndex
import com.facishare.paas.metadata.dao.pg.entity.metadata.Field
import com.facishare.paas.pod.client.DbRouterClient
import com.facishare.paas.pod.dto.RouterInfo
import com.facishare.paas.pod.util.DialectUtil
import com.fxiaoke.api.IdGenerator
import com.fxiaoke.paas.common.service.SchemaHelper
import org.apache.commons.lang3.StringUtils;
import com.fxiaoke.paas.console.entity.metadata.MtDescribe
import com.fxiaoke.paas.console.entity.metadata.MtUnique;
import com.fxiaoke.paas.console.mapper.metadata.DescribeMapper;
import com.fxiaoke.paas.console.mapper.metadata.FieldMapper
import com.fxiaoke.paas.console.mapper.metadata.MtDataMapper;
import com.fxiaoke.paas.console.mapper.metadata.MtUniqueMapper
import com.fxiaoke.release.FsGrayRelease
import com.fxiaoke.release.FsGrayReleaseBiz
import com.github.autoconf.ConfigFactory
import com.github.autoconf.api.IConfig
import com.google.common.base.Joiner
import com.google.common.collect.Maps
import com.google.common.util.concurrent.RateLimiter
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.ObjectUtils
import org.elasticsearch.common.Strings
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional

import javax.annotation.PostConstruct
import javax.annotation.Resource
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/30 11:22
 */
@Service
@Slf4j
class FieldSwitchUniqueService {

  @Autowired
  private FieldMapper fieldMapper

  @Autowired
  private MtUniqueMapper mtUniqueMapper

  @Autowired
  private DescribeMapper describeMapper

  @Autowired
  private MtDataMapper mtDataMapper

  @Resource
  private SchemaHelper schemaHelper

  @Autowired
  private DbRouterClient dbRouterClient

  @Autowired
  private PurgeCacheService purgeCacheService

  /**
   * mt_unique减少写入，对于部分隔离企业按对象灰度
   */
  private static Map<String,List<String>> UNIQUE_IGNORE_NAME = Maps.newHashMap();

  private Map<String, RateLimiter> rateLimiterMap = Maps.newConcurrentMap()

  int maxDealNum

  private double tps = 1000D;

  @PostConstruct
  void init(){
    ConfigFactory.getConfig("fs-gray-metadata", {config ->
      maxDealNum = config.getInt("max.deal.num",10000)
      tps = config.getDouble("field.unique.switch.tps",1000D)

      UNIQUE_IGNORE_NAME = parseMapFromConfig(config, "allow.ignore.name.unique")
    })
  }

  private static <T> Map<String, T> parseMapFromConfig(IConfig config, String key) {
    String data = config.get(key);
    if (Strings.isNullOrEmpty(data)) {
      return Maps.newHashMap();
    }
    try {
      return JSON.parseObject(data, Map.class);
    } catch (Exception e) {
      log.error("config name {} key {} parseMapError", config.getName(), key, e);
    }

    return new HashMap<>();
  }

  Field findField(String tenantId, String describeApiName, String apiName){
    return fieldMapper.setTenantId(tenantId).findFieldByApiName(tenantId, describeApiName, apiName)
  }

  Integer countUnique(String tenantId, String describeApiName, String apiName){
    return mtUniqueMapper.setTenantId(tenantId).countByTenantIdDescribeApiNameAndApiName(tenantId, describeApiName, apiName)
  }

  Integer countUniqueData(String tenantId, String describeApiName, String apiName, String fieldNum){
    fieldNum = fieldNum == null? apiName : "value" + fieldNum;
    String storeTableName = describeMapper.setTenantId(tenantId).findTableNameByTenantIdAndApiName(tenantId,describeApiName)
    if(StringUtils.isEmpty(storeTableName) || (!fieldNum.equals(apiName) && !schemaHelper.isSpecialSchema(tenantId))){
      storeTableName = "mt_data"
    }
    return mtDataMapper.setTenantId(tenantId).countUniqueData(tenantId,storeTableName,describeApiName,fieldNum)
  }

  String switchUnique(String tenantId, Field field, boolean isUnique){
    if(countUniqueData(tenantId,field.getDescribeApiName(), field.getApiName(), field.getFieldNum() as String) > maxDealNum){
      Date date = new Date(System.currentTimeMillis())
//      if(date.getHours() >=6 && date.getHours() <22){
//        return "数据量过大，未到达指定更新时间，更新时间段[0:00 - 5:59],[22:00 - 23:59]"
//      }
    }
    if(isUnique){
      String fieldNum = field.getFieldNum() == null ? field.getApiName() : "value" + field.getFieldNum()
      String storeTableName = describeMapper.setTenantId(tenantId).findTableNameByTenantIdAndApiName(tenantId,field.getDescribeApiName())
      String idName = !schemaHelper.isSpecialSchema(tenantId) && StringUtils.isNotBlank(storeTableName) && storeTableName!="mt_data" && field.getFieldNum() != null ? "udf_obj_id" : "id";
      if(StringUtils.isEmpty(storeTableName) || (field.getFieldNum() != null && !schemaHelper.isSpecialSchema(tenantId))){
        storeTableName = "mt_data"
      }
      fieldMapper.setTenantId(tenantId).updateUniqueById(isUnique, tenantId, field.getFieldId())
      String id = null
      while (true) {
        List<Map<String, String>> dataList = mtDataMapper.setTenantId(tenantId).getUniqueData(tenantId, storeTableName, field.getDescribeApiName(), fieldNum, idName, id)
        if(ObjectUtils.isEmpty(dataList)) {
          break;
        }
          id = dataList.get(dataList.size() - 1).get(idName);
        List<MtUnique> uniqueList = dataList.stream().map {it -> new MtUnique(IdGenerator.get(), tenantId, it.get(idName), field.getDescribeApiName(), field.getApiName(), it.get("value"))}.collect(Collectors.toList())
        if(ObjectUtils.isEmpty(uniqueList)) {
          break;
        }
        uniqueList.forEach({ x -> x.setUniqueId(IdGenerator.get()) })
        mtUniqueMapper.setTenantId(tenantId).batchInsert(uniqueList)
        getRateLimiter(tenantId, uniqueList.size())
      }
    } else {
      fieldMapper.setTenantId(tenantId).updateUniqueById(isUnique, tenantId, field.getFieldId())
      while (true) {
        int affectRow = mtUniqueMapper.setTenantId(tenantId).deleteUnique(tenantId, field.getDescribeApiName(), field.getApiName())
        if(affectRow == 0) {
          break;
        }
        getRateLimiter(tenantId, affectRow)
      }
    }
    purgeCacheService.purgeCacheByTenantIdS(tenantId)
    return null;
  }

  public void switchFieldType(String tenantId, Field field) {
    fieldMapper.setTenantId(tenantId).updateFieldType2AutoIncrement(tenantId, field.getFieldId())
    purgeCacheService.purgeCacheByTenantIdS(tenantId)
  }

  boolean checkUniqueIndex(String tenantId, String describeApiName){
    if (StringUtils.isAnyEmpty( tenantId, describeApiName)) {
      return false;
    }
    if (!schemaHelper.isSpecialSchema(tenantId)) {
      return false;
    }
    String tableName = describeMapper.setTenantId(tenantId).findTableNameByTenantIdAndApiName(tenantId,describeApiName)
    if(StringUtils.isEmpty(tableName)){
      return false;
    }
    List<UniqueIndex> uniqueIndexList = getUniqueIndex(tenantId, tableName);
    if (ObjectUtils.isEmpty(uniqueIndexList)) {
      return false;
    }
    for (UniqueIndex uniqueIndex : uniqueIndexList) {
      if (uniqueIndex.isNameIndex()) {
        return true;
      }
    }
    return false;
  }

  private List<UniqueIndex> getUniqueIndex(String tenantId, String tableName) {
    String schema = schemaHelper.isSpecialSchema(tenantId) ? "sch_" + tenantId : "public";
    List<Map> searchResult = mtDataMapper.setTenantId(tenantId).getUniqueIndex(schema, tableName);
    return searchResult.stream().map({map -> UniqueIndex.of(map)}).collect(Collectors.toList());
  }

  private void getRateLimiter(String tenantId,int count) {
    String jdbcUrl = dbRouterClient.queryRouterInfo(tenantId, "CRM", "application", DialectUtil.POSTGRESQL, false,false).getJdbcUrl();
    rateLimiterMap.computeIfAbsent(jdbcUrl, { k -> RateLimiter.create(tps) }).acquire(count);
  }
}
