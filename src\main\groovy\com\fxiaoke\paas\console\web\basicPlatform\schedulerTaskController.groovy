package com.fxiaoke.paas.console.web.basicPlatform

import com.fxiaoke.paas.console.service.basicPlatform.SchedulerTaskService
import org.apache.kafka.common.protocol.types.Field.Str
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.servlet.ModelAndView

@RestController
@RequestMapping("/basicPlatform/schedulerTask")
class schedulerTaskController {

    @Autowired
    SchedulerTaskService schedulerTaskService;

    @PostMapping(path = "batchCancel")
    def batchCancel(@RequestBody Map arg, String method) {
        try {
            Map result = schedulerTaskService.batchCancel(method, arg)
            return ["code": 200, "data": result]
        } catch (RuntimeException e) {
            return ["code": -100, "message": e.getMessage()]
        }
    }

    @PostMapping(path = "batchToggleStatus")
    def batchToggleStatus(@RequestBody Map arg, String method, int status) {
        try {
            Map result
            if (status == 1) {
                result = schedulerTaskService.batchEnable(method, arg)
            } else {
                result = schedulerTaskService.batchDisable(method, arg)
            }
            return ["code": 200, "data": result]
        } catch (RuntimeException e) {
            return ["code": -100, "message": e.getMessage()]
        }
    }

    @PostMapping(path = "batchDelete")
    def batchDelete(@RequestBody Map arg, String method) {
        try {
            Map result = schedulerTaskService.batchDelete(method, arg)
            return ["code": 200, "data": result]
        } catch (RuntimeException e) {
            return ["code": -100, "message": e.getMessage()]
        }
    }


    @RequestMapping(value = "/page")
    ModelAndView consoleLog() {
        return new ModelAndView("basicPlatform/schedulerTask");
    }


}
