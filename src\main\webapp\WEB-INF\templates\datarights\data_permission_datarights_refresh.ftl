<#assign title="权限重刷">
<#assign active_nav="data_permission_basic">
<#assign headContent>
<style>
  .productRefresh {
    width: 8%;
    margin-left: 20px
  }
  .temporary{
    widows: 8%;
    margin-left: 20px;
  }
  .queryRefreshErrorLog {
    widows: 8%;
    margin-left: 20px;
  }

  .startByPodUrlAndApiName {
    width: 8%;
    margin-left: 20px
  }

  .startByPodUrlAndApiNameAndTenantId {
    width: 8%;
    margin-left: 20px;
  }

  .startByTenant {
    width: 8%;
    margin-left: 20px
  }

  .startByPodUrl {
    width: 8%;
    margin-left: 20px
  }

  .countAmountForGrep {
    width: 8%;
    margin-left: 20px
  }

  .stopRefresh {
    width: 8%;
    margin-left: 20px
  }

  .datarightsRefresh {
    width: 8%;
    margin-left: 20px
  }

  .refreshAccountObj {
    width: 8%;
    margin-left: 20px
  }
.refreshAccountObjCus{
  width: 8%;
  margin-left: 20px
}
  input {
    margin-left: 10px;
  }

  #dataTable2 th {
    vertical-align: middle;
    align-items: center;
  }

  #dataTable2 td {
    vertical-align: middle;
    align-items: center
  }

  .table > thead:first-child > tr:first-child > th {
    text-align: center;
    vertical-align: middle;
  }

  .table > tbody > tr > td {
    text-align: center;
  }

  input {
    width: 10%;
    height: 34px;
    line-height: 34px;
    box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid #c8cccf;
    color: #6a6f77;
    -web-kit-appearance: none;
    -moz-appearance: none;
    outline: 0;
    text-decoration: none;
  }

</style>
</#assign>
<#assign bodyContent>
    <#assign breadcrumbContent>
    <section class="content-header">
      <h1>权限重刷</h1>
      <ol class="breadcrumb">
        <li><a href="#"><i class="fa fa-dashboard"></i>查询</a></li>
      </ol>
    </section>
    </#assign>
<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <div class="box-header with-border">
          <h3 class="box-title"></h3>
        </div>
        <form class="form-horizontal" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="permission_productRefresh">
              <pre style="font-size:22px;font-weight:bold">刷对象权限:</pre>
              <input placeholder="企业_ID"/>
              <input placeholder="ApiName"/>
              <button type="button" class="productRefresh btn btn-primary">开始</button>
            </div>
          </div>
        </form>

        <form class="form-horizontal" method="post" id="myForm" role="form"
              data-toggle="validator">
          <div class="container-fluid">
            <div id="permission_datarightsRefresh">
              <pre style="font-size:22px;font-weight:bold">重算数据权限:</pre>
              <input placeholder="企业_ID"/>
              <input placeholder="ApiName"/>
              <button type="button" class="datarightsRefresh btn btn-primary">开始</button>
              </br>
              <textarea cols="80" rows="5" placeholder="数据_ID（逗号隔开）"></textarea>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>
</#assign>
<#assign scriptContent>
<script>

  $(".queryRefreshErrorLog").on("click", function () {
    var tenantId = $("#queryRefreshErrorLog input").eq(0).val();
    var apiName = $("#queryRefreshErrorLog input").eq(1).val();

    if (tenantId == "") {
      alert("企业ID不可为空!");
      return false;
    }
    $.ajax({
      type: 'Get',
      url: '${CONTEXT_PATH}/datarights_refresh/queryRefreshErrorLog',
      data: {
        tenantIds: tenantId,
        apiName: apiName,
      },
      contentType: "application/json", //必须有
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        alert(result.msg)
      },
      error: function (error) {
        alert('网络异常');
      }

    })

  });
  $(".startByPodUrlAndApiNameAndTenantId").on("click", function () {
    var podUrl = $("#startByPodUrlAndApiNameAndTenantId input").eq(0).val();
    var tenantId = $("#startByPodUrlAndApiNameAndTenantId input").eq(1).val();
    var apiName = $("#startByPodUrlAndApiNameAndTenantId input").eq(2).val();
    if (podUrl == "") {
      alert("podUrl不可为空");
      return false;
    }
    if (apiName == "") {
      alert("podUrl不可为空");
      return false;
    }
    if (tenantId == "") {
      alert("tenantId不可为空");
      return false;
    }
    $.ajax({
      type: 'GET',
      url: '${CONTEXT_PATH}/datarights_refresh/startByPodUrlAndApiNameAndTenantId',
      data: {
        podUrl: podUrl,
        apiName: apiName,
        tenantId: tenantId
      },
      contentType: "application/json", //必须有
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        if (result.status == 200) {
          alert("执行成功")
        } else {
          alert(result.msg)
        }

      },
      error: function (error) {
        alert('网络异常');
      }

    })

  });


  $(".startByPodUrlAndApiName").on("click", function () {
    var podUrl = $("#startByPodUrlAndApiName input").eq(0).val();
    var apiName = $("#startByPodUrlAndApiName input").eq(1).val();
    if (podUrl == "") {
      alert("podUrl不可为空");
      return false;
    }
    if (apiName == "") {
      alert("apiName不可为空");
      return false;
    }
    $.ajax({
      type: 'GET',
      url: '${CONTEXT_PATH}/datarights_refresh/startByPodUrlAndApiName',
      data: {
        podUrl: podUrl,
        apiName: apiName
      },
      contentType: "application/json", //必须有
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        if (result.status == 200) {
          alert("执行成功")
        } else {
          alert(result.msg)
        }

      },
      error: function (error) {
        alert('网络异常');
      }

    })

  });


  $(".stopRefresh").on("click", function () {
    $.ajax({
      type: 'GET',
      url: '${CONTEXT_PATH}/datarights_refresh/stopRefresh',
      data: {},
      contentType: "application/json", //必须有
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        if (result.status == 200) {
          alert("执行成功")
        } else {
          alert(result.msg)
        }

      },
      error: function (error) {
        alert('网络异常');
      }

    })

  });

  $(".startByPodUrl").on("click", function () {
    var podUrl = $("#start_by_podUrl input").eq(0).val();

    if (podUrl == "") {
      alert("podUrl不可为空");
      return false;
    }

    $.ajax({
      type: 'GET',
      url: '${CONTEXT_PATH}/datarights_refresh/startByPod',
      data: {
        podUrl: podUrl,
      },
      contentType: "application/json", //必须有
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        if (result.status == 200) {
          alert("执行成功")
        } else {
          alert(result.msg)
        }

      },
      error: function (error) {
        alert('网络异常');
      }

    })

  });


  $(".startByTenant").on("click", function () {
    var tenantIds = $("#start_by_tenant input").eq(0).val();
    var apiName = $("#start_by_tenant input").eq(1).val();
    if (tenantIds == "") {
      alert("企业ID不可为空!");
      return false;
    }
    $.ajax({
      type: 'GET',
      url: '${CONTEXT_PATH}/datarights_refresh/startByTenant',
      data: {
        tenantIds: tenantIds,
        apiName: apiName
      },
      contentType: "application/json", //必须有
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        if (result.status == 200) {
          alert("执行成功")
        } else {
          alert(result.msg)
        }

      },
      error: function (error) {
        alert('网络异常');
      }

    })

  });

  $(".productRefresh").on("click", function () {
    var tenantId = $("#permission_productRefresh input").eq(0).val();
    var apiName = $("#permission_productRefresh input").eq(1).val();
    if (tenantId == "") {
      alert("企业ID不可为空!");
      return false;
    }
    if (apiName == "") {
      alert("ApiName不可为空");
      return false;
    }
    $.ajax({
      type: 'GET',
      url: '${CONTEXT_PATH}/datarights_refresh/productRefresh',
      data: {
        tenantId: tenantId,
        apiName: apiName
      },
      contentType: "application/json", //必须有
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        if (result.status == 200) {
          alert("执行成功")
        } else {
          alert(result.msg)
        }

      },
      error: function (error) {
        alert('网络异常');
      }

    })

  });



  $(".refreshAccountObj").on("click", function () {
    var podUrl = $("#refreshAccountObj input").eq(0).val();
    if (podUrl == "") {
      alert("podUrl不可为空");
      return false;
    }
    $.ajax({
      type: 'Get',
      url: '${CONTEXT_PATH}/datarights_refresh/startByPodUrlAccountObj',
      data: {
        podUrl: podUrl
      },
      contentType: "application/json", //必须有
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        if (result.status == 200) {
          alert("执行成功")
        } else {
          alert(result.errMessage)
        }

      },
      error: function (error) {
        alert('网络异常');
      }

    })

  });

  $(".datarightsRefresh").on("click", function () {
    var tenantId = $("#permission_datarightsRefresh input").eq(0).val();
    var apiName = $("#permission_datarightsRefresh input").eq(1).val();
    var objectId = $("#permission_datarightsRefresh textarea").eq(0).val();
    if (tenantId == "") {
      alert("企业ID不可为空!");
      return false;
    }
    if (apiName == "") {
      alert("ApiName不可为空");
      return false;
    }
    if (objectId == "") {
      alert("objectId不可为空");
      return false;
    }
    $.ajax({
      type: 'POST',
      url: '${CONTEXT_PATH}/datarights_refresh/datarightsRefresh',
      data: {
        tenantId: tenantId,
        apiName: apiName,
        objectId: objectId
      },
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        if (result.errCode == 0) {
          alert("执行成功")
        } else {
          alert(result.errMessage)
        }

      },
      error: function (error) {
        alert('网络异常');
      }

    })

  });
  $(".countAmountForGrep").on("click", function () {
    var tenantId = $("#countAmountForGrep input").eq(0).val();
    var apiName = $("#countAmountForGrep input").eq(1).val();

    if (tenantId == "") {
      alert("企业ID不可为空!");
      return false;
    }
    $.ajax({
      type: 'Get',
      url: '${CONTEXT_PATH}/datarights_refresh/countAmountForGrep',
      data: {
        tenantIds: tenantId,
        apiName: apiName,
      },
      contentType: "application/json", //必须有
      dataType: 'json',
      async: false,
      success: function (data) {
        var result = eval(data)
        alert(result.msg)
      },
      error: function (error) {
        alert('网络异常');
      }

    })

  });
</script>
</#assign>
<#include "../layout/layout-main.ftl" />
