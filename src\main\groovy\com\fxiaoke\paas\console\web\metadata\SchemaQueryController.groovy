package com.fxiaoke.paas.console.web.metadata

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.fxiaoke.common.PasswordUtil
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.util.DateFormatUtil
import com.github.autoconf.ConfigFactory
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import lombok.Cleanup
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.PostConstruct
import java.sql.ResultSetMetaData
import java.util.stream.Collectors

/**
 * <AUTHOR>
 * @date 2020/2/6 2:29 PM
 */
@Controller
@Slf4j
@RequestMapping(path = "/metadata/sql/schema")
class SchemaQueryController {

  Map<String, Map<String, String>> dbSource

  String password

  String userName

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-copier-url", { iConfig ->
      Map<String, Map<String, String>> dbSource = Maps.newHashMap()
      String configStr = iConfig.getString()
      JSONArray jsonArray = JSONArray.parseArray(configStr)
      jsonArray.forEach({ object ->
        JSONObject jsonObject = (JSONObject) object
        List<String> tenantIds = jsonObject.getObject("tenantIds", List.class)
        String oldUrl = jsonObject.getString("old")
        String newUrl = jsonObject.getString("new")
        String biOldUrl = jsonObject.getString("bi-old")
        String biNewUrl = jsonObject.getString("bi-new")
        Map<String, String> urlMap = Maps.newHashMap()
        urlMap.put("old", oldUrl)
        urlMap.put("new", newUrl)
        urlMap.put("bi-old", biOldUrl)
        urlMap.put("bi-new", biNewUrl)
        tenantIds.forEach({ tenantId ->
          dbSource.put(tenantId, urlMap)
        })
      })
      this.dbSource = dbSource
    })

    ConfigFactory.getConfig("fs-paas-copier-db", { iConfig ->
      this.userName = iConfig.get("schema_username")
      this.password = PasswordUtil.decode(iConfig.get("schema_password"))
    })


  }


  @RequestMapping(path = "/page/schemaQuery")
  def schemaQueryPage(ModelMap model) {
    model.put("moduleList", Lists.newArrayList("CRM", "BI"))
    model.put("resourceTypeList", Lists.newArrayList("postgresql"))
    model.put("tenantIdLists", Lists.newArrayList(dbSource.keySet()).stream().sorted().collect(Collectors.toList()))
    model.put("schemaTypeList", Lists.newArrayList("newSchema", "oldSchema"))
    "metadata/schemaQuery"
  }


  @RequestMapping(path = "/query/schemaQuery")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- Schema隔离企业SQL查询")
  def schemaQueryQuery(@RequestParam String sql,
                       @RequestParam String module,
                       @RequestParam String resourceType,
                       @RequestParam String tenantId,
                       @RequestParam String schemaType) {

    if (sql.isEmpty() || tenantId.isEmpty()) {
      return ["code": 400, "error": "请输入Sql和租户ID"]
    }
    if (!DateFormatUtil.checkSql(sql)) {
      return ["code": 403, "error": "Sql不符合规范，只能分析查询语句！"]
    }
    if (!resourceType.equals("postgresql")) {
      return ["code": 403, "error": "不支持其他类型数据库!"]
    }
    Map<String, String> resourceMap = dbSource.get(tenantId)
    List<Map<String, String>> resultList = null
    String url = null
    if (module.equals("BI")) {
      if (schemaType.equals("newSchema")) {
        url = resourceMap.get("bi-new")
      } else {
        url = resourceMap.get("bi-old")
      }
    } else {
      if (schemaType.equals("newSchema")) {
        url = resourceMap.get("new")
      } else {
        url = resourceMap.get("old")
      }
    }
    try {
      resultList = querySql(url, userName, password, sql)
    } catch (Exception e) {
      log.warn("Sql查询异常，sql={},error:", sql, e)
      return ["code": 500, "error": "查询异常", "errorInfo": e.getMessage()]
    }
    return ["code": 200, "info": JSON.toJSONString(resultList)]

  }

  List<Map<String, String>> querySql(String url, String userName, String password,String sql) {
    List<Map<String, String>> resultList = Lists.newArrayList()
    if (StringUtils.isAnyBlank(url, sql, password, userName)) {
      return resultList
    }
    sql = sql.trim()
    if (sql.toLowerCase().indexOf("limit") == -1) {
      if (sql.endsWith(";")) {
        sql = sql.substring(0, sql.length() - 1)
      }
      sql += " LIMIT 100 ;"
    }
   @Cleanup JdbcConnection jdbcConnection = new JdbcConnection(url, userName, password)
    jdbcConnection.query(sql, { rs ->
      ResultSetMetaData resultSetMetaData = rs.metaData
      int size = resultSetMetaData.columnCount
      while (rs.next()) {
        Map<String, String> result = Maps.newHashMap()
        for (int i = 1; i <= size; i++) {
          String columnName = resultSetMetaData.getColumnName(i)
          Object value=rs.getObject(columnName)
          result.put(columnName, value==null?null:String.valueOf(value))
        }
        resultList.add(result)
      }
    })
    return resultList
  }

}
