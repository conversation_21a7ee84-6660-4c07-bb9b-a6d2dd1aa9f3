package com.fxiaoke.paas.console.mapper.metadata

import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(value = ["classpath:mapperContext.xml"])
class MtDataMapperTest extends Specification {

  @Autowired
  MtDataMapper mapper

  def "gitDataByName"() {
    given:
    def storeTableName = "bpm_task"
    def tenantId = "71557"
    def apiName = "BpmTask"
    def name = "业务活动2(2018-05-14 16:48)"
    expect:
    mapper.setTenantId(tenantId).getDataByName(storeTableName, tenantId, name, apiName)
  }
}
