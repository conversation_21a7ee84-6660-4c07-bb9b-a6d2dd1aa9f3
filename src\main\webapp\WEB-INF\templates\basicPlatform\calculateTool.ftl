<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<style>
    li {
        list-style: none;
    }

    .box {
        background-color: white;
    }

    .btn-box {
        width: 500px;
        display: flex;
        justify-content: space-around;
    }

    #singleObjForm {
        display: flex;
        justify-content: space-around;
    }

    .singleObjCalculate form div {
        height: 50px;
    }

    .content {
        width: 100%;
        padding: 20px;
    }

    label {
        padding: 5px;
    }

    pre {
        width: 100%;
        height: 75%;
    }

    /*.control-group {*/
    /*    display: flex*/
    /*}*/

    .control-group:last-child {
        width: auto;
    }

    .form {
        padding: 30px;
        width: 100%;
    }

    .formBox {
        display: flex;
        justify-content: space-evenly;
        padding: 20px;
    }

    .formBox input {
        line-height: 28px;
    }

    .form button {
        height: 30px;
        width: auto;
    }
</style>
<#assign breadcrumbContent>
    <section class="content-header">
        <h1>计算工具</h1>
        <ol class="breadcrumb">
            <li><a href="${ctx}"><i class="fa fa-dashboard"></i>任务主页</a></li>
            <li><a href=""><i class="fa fa-dashboard"></i>对象查询</a></li>
        </ol>
        <div id="warning" class="alert alert-warning alert-dismissible hide" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
            <strong>警告!</strong> 请输入tenantId.
        </div>
        <div id="warningInfo" class="alert alert-warning hide">
            <span id="closeInfo" href="#" class="close">&times;</span>
            <strong id="ajaxInfo"></strong>
        </div>
    </section>
</#assign>
<#assign bodyContent>
    <section class="content">
        <div class="box">
            <div class="tabbable">
                <ul class="btn-box nav nav-tabs">
                    <li class="active">
                        <a href="#singleObjCalculate" data-toggle="tab">触发单个对象计算</a>
                    </li>
                    <li>
                        <a href="#batchObjCalculate" data-toggle="tab">多个对象触发计算</a>
                    </li>
                    <li>
                        <a href="#searchCalculateLog" data-toggle="tab">查询metadataChange日志</a>
                    </li>
                    <li>
                        <a href="#fieldOplog" data-toggle="tab">查询字段Oplog</a>
                    </li>
                </ul>
            </div>

            <div class="tab-content">
                <#--单个对象触发计算-->
                <div class="singleObjCalculate tab-pane active" id="singleObjCalculate">
                    <div class="form-inline formBox">
                        <div id="radioGroup">
                            计算类型：
                            <input type="radio" id="update" name="isUpdate" value="true"/>
                            <label for="update">计算且更新</label>
                            <input type="radio" id="unUpdate" name="isUpdate" value="false" checked/>
                            <label for="unUpdate">只计算不更新</label>
                        </div>
                        <input type="text" id="inputTenantId" name="tenantId" placeholder="请输入企业ei或ea">
                        <input type="text" id="inputObjectApiNames" name="objApiName" placeholder="请输入对象ApiName">
                        <input type="text" id="inputFieldApiNamesCal" name="fieldApiNames"
                               placeholder="请输入对象字段apiName"></input>
                        <input type="text" id="inputDataIdsCal" name="dataIds"
                               placeholder="请输入要计算的数据id"></input>
                        <textarea type="text" id="inputFieldApiNames" name="fieldApiNames"
                                  placeholder="请输入对象字段apiName,多个字段使用','隔开" hidden="hidden"></textarea>
                        <textarea type="text" id="inputDataIds" name="dataIds"
                                  placeholder="请输入要计算的数据id,多个字段用','隔开" hidden="hidden"></textarea>

                        <button class="btn" id="submitSingleObjForm">提交</button>
                    </div>
                    <div class="content">
                            <pre id="contentInfo">
                            </pre>
                    </div>
                </div>
                <#--批量对象触发计算-->
                <div class="batchObjCalculate tab-pane" id="batchObjCalculate">
                    <div class="batchObjCalculate tab-pane active" id="batchObjCalculate">
                        <div class="form-inline formBox">
                            <div id="radioGroup">
                                计算类型：
                                <input type="radio" id="batchCalculateField" name="batchCalculateType" value="true"/>
                                <label for="update">不指定计算的字段</label>
                                <input type="radio" id="batchCalculateObj" name="batchCalculateType" value="false"
                                       checked/>
                                <label for="unUpdate">指定计算的字段</label>
                            </div>
                            <input type="text" id="batchInputTenantId" name="tenantId" placeholder="请输入企业ei或ea">
                            <textarea id="batchInputCalculateObjects" name="calculateObjects"
                                      placeholder="请输入要计算的对象（多个对象，则指定的字段计算无效，默认为全量计算）"></textarea>
                            <textarea type="text" id="batchInputCalculateFields" name="fields"
                                      placeholder="请输入该对象下需要计算的字段用','隔开，为空则代表该对象下所有计算字段全部计算"></textarea>
                            <button class="btn" id="submitBatchObjForm">提交</button>
                        </div>

                        <div class="content">
                            <pre id="batchContentInfo">
                            </pre>
                        </div>
                    </div>
                </div>
                <div class="batchObjCalculate tab-pane" id="searchCalculateLog">
                    <div class="form-inline formBox">
                        <input type="text" id="metadataLogByTenantId" name="tenantId" placeholder="请输入企业ei或ea">
                        <input type="text" id="metadataLogById" name="metadataLogById" placeholder="请输入数据id">
                        <input type="text" id="metadataLogByFieldApiName" name="metadataLogByFieldApiName"
                               placeholder="请输入字段apiName">
                        <button class="btn" id="submitMetadataLogForm">提交</button>
                    </div>
                    <div class="content">
                            <pre id="metadataLogContentInfo">
                            </pre>
                    </div>
                </div>

                <div class="batchObjCalculate tab-pane" id="fieldOplog">
                    <div class="form-inline formBox">
                        <input type="text" id="fieldOplogByTenantId" name="tenantId" placeholder="请输入企业ei或ea">
                        <input type="text" id="fieldOplogByObjectApiName" name="metadataLogById"
                               placeholder="请输入对象apiName">
                        <input type="text" id="fieldOplogByFieldApiName" name="metadataLogByFieldApiName"
                               placeholder="请输入字段apiName">
                        <button class="btn" id="submitFieldOplogForm">提交</button>
                    </div>
                    <div class="content">
                            <pre id="fieldOplogContentInfo">
                            </pre>
                    </div>
                </div>
            </div>

        </div>
    </section>



</#assign>
<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
    <script src="//static.foneshare.cn/oss/js/highcharts.js"></script>
    <script src="https://img.hcharts.cn/highcharts/modules/data.js"></script>
<#--高亮显示-->
    <script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<#--表单验证插件-->
    <script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
    <script type="application/javascript">
        $(document).ready(function () {
            $('#update ,#unUpdate').on('click', function () {
                var update = $('#radioGroup input[name="isUpdate"]:checked').val();
                console.log(update)
                if (update === "true") {
                    console.log("true:" + update)
                    $('#inputFieldApiNames').show()
                    $('#inputDataIds').show()
                    $('#inputDataIdsCal').hide()
                    $('#inputFieldApiNamesCal').hide()
                } else {
                    console.log("false:" + update)
                    $('#inputFieldApiNames').hide()
                    $('#inputDataIds').hide()
                    $('#inputDataIdsCal').show()
                    $('#inputFieldApiNamesCal').show()
                }
            })
            $('#submitSingleObjForm').on('click', function () {
                var tenantId = $('#inputTenantId').val()
                var objectApiName = $('#inputObjectApiNames').val()
                var updateAndCal = $('#radioGroup input[name="isUpdate"]:checked').val()
                $('#contentInfo').html("数据加载中，请稍等...")
                if (updateAndCal === "true") {
                    var arg = {
                        "objectApiName": objectApiName,
                        "fieldApiNames": $('#inputFieldApiNames').val().split(","),
                        "dataIds": $('#inputDataIds').val().split(","),
                    };
                    $.ajax({
                        url: "${CONTEXT_PATH}/basicPlatform/calculate/triggerCalculateAndUpdate?tenantId=" + tenantId,
                        contentType: "application/json",
                        data: JSON.stringify(arg),
                        dataType: "json",
                        type: "POST",
                        traditional: true,
                        success: function (data) {
                            result = eval(data)
                            if (result != null && result.code === 0) {
                                $('#contentInfo').JSONView(result.data.result, {
                                    collapsed: false,
                                    nl2br: true,
                                    recursive_collapser: true
                                })
                            } else {
                                console.log(result)
                                alert(result.message)
                                $('#contentInfo').html("")
                            }
                        },
                        error: function (err) {
                            alert(err.responseText)
                            $('#contentInfo').html("")
                        }
                    });
                } else {
                    var arg = {
                        "objectApiName": objectApiName,
                        "fieldApiName": $('#inputFieldApiNamesCal').val(),
                        "dataId": $('#inputDataIdsCal').val()
                    };

                    console.log(arg)
                    $.ajax({
                        url: "${CONTEXT_PATH}/basicPlatform/calculate/triggerCalculate?tenantId=" + tenantId,
                        contentType: "application/json",
                        data: JSON.stringify(arg),
                        dataType: "json",
                        type: "POST",
                        traditional: true,
                        success: function (data) {
                            result = eval(data)
                            if (result != null && result.code === 0) {
                                $('#contentInfo').JSONView(result.data.result, {
                                    collapsed: false,
                                    nl2br: true,
                                    recursive_collapser: true
                                })
                            } else {
                                console.log(result)
                                $('#contentInfo').html("")
                                alert(result.message)
                            }
                        },
                        error: function (err) {
                            $('#contentInfo').html("")
                            alert(err.responseText)
                        }
                    });
                }
            })

            $('#batchCalculateField ,#batchCalculateObj').on('click', function () {
                var batchCalculateType = $('#radioGroup input[name="batchCalculateType"]:checked').val();
                if (batchCalculateType === "true") {
                    console.log("true:" + batchCalculateType)
                    $('#batchInputCalculateFields').hide()
                    $('#batchInputCalculateObjects').show()
                } else {
                    console.log("false:" + batchCalculateType)
                    $('#batchInputCalculateFields').show()
                    $('#batchInputCalculateObjects').show()
                }
            })
            $('#submitBatchObjForm').on('click', function () {
                var tenantId = $('#batchInputTenantId').val()
                var objectApiNames = $('#batchInputCalculateObjects').val()
                var fieldApiNames = $('#batchInputCalculateFields').val()
                var arg = {
                    "objectApiNames": objectApiNames.split(",")
                }
                console.log($('#radioGroup input[name="batchCalculateObj"]:checked').val())
                if (fieldApiNames !== "" && !$('#radioGroup input[name="batchCalculateObj"]:checked').val()) {
                    console.log("选字段")
                    arg = {
                        "objectApiNames": objectApiNames.split(","),
                        "fieldApiNames": fieldApiNames.split(",")
                    };
                }
                $('#batchContentInfo').html("数据加载中，请稍等...")
                $.ajax({
                    url: "${CONTEXT_PATH}/basicPlatform/calculate/submitCalculate?tenantId=" + tenantId,
                    contentType: "application/json",
                    data: JSON.stringify(arg),
                    dataType: "json",
                    type: "POST",
                    traditional: true,
                    success: function (data) {
                        result = eval(data)
                        if (result != null && result.code === 200) {
                            console.log(result)
                            $('#batchContentInfo').JSONView(result, {
                                collapsed: false,
                                nl2br: true,
                                recursive_collapser: true
                            })
                        } else {
                            $('#batchContentInfo').html("")
                            alert(result.message)
                        }
                    },
                    error: function (err) {
                        $('#batchContentInfo').html("")
                        alert(err.responseText)
                    }
                });
            })

            $('#submitMetadataLogForm').on('click', function () {
                var tenantId = $('#metadataLogByTenantId').val()
                var id = $('#metadataLogById').val()
                var fieldApiName = $('#metadataLogByFieldApiName').val()
                var arg = {
                    "id": id,
                    "fieldApiName": fieldApiName
                }
                $('#metadataLogContentInfo').html("数据加载中，请稍等...")
                $.ajax({
                    url: "${CONTEXT_PATH}/basicPlatform/calculate/metadataChangeLog?tenantId=" + tenantId,
                    contentType: "application/json",
                    data: JSON.stringify(arg),
                    dataType: "json",
                    type: "POST",
                    traditional: true,
                    success: function (data) {
                        result = eval(data)
                        if (result != null && result.code === 200) {
                            $('#metadataLogContentInfo').JSONView(result.data, {
                                collapsed: false,
                                nl2br: true,
                                recursive_collapser: true
                            })
                        } else {
                            $('#metadataLogContentInfo').html("")
                            alert(result.message)
                        }
                        console.log(result.message)
                    },
                    error: function (err) {
                        $('#metadataLogContentInfo').html("")
                        alert(err.responseText)
                    }
                });
            })

            $('#submitFieldOplogForm').on('click', function () {
                $('#fieldOplogContentInfo').html("数据加载中，请稍等...")
                var tenantId = $('#fieldOplogByTenantId').val()
                var objectApiName = $('#fieldOplogByObjectApiName').val()
                var fieldApiName = $('#fieldOplogByFieldApiName').val()
                var arg = {
                    "objectApiName": objectApiName,
                    "fieldApiName": fieldApiName
                }
                $.ajax({
                    url: "${CONTEXT_PATH}/basicPlatform/calculate/fieldChangeLog?tenantId=" + tenantId,
                    contentType: "application/json",
                    data: JSON.stringify(arg),
                    dataType: "json",
                    type: "POST",
                    traditional: true,
                    success: function (data) {
                        result = eval(data)
                        if (result != null && result.code === 200) {
                            $('#fieldOplogContentInfo').JSONView(result.data, {
                                collapsed: false,
                                nl2br: true,
                                recursive_collapser: true
                            })
                        } else {
                            $('#fieldOplogContentInfo').html("")
                            console.log(result)
                            alert(result.message)
                        }
                    },
                    error: function (err) {
                        $('#fieldOplogContentInfo').html()
                        console.log(err.responseText)
                        alert(err.responseText)
                    }
                });
            })
        });

    </script>
</#assign>
<#include "../layout/layout-main.ftl" />


