package com.fxiaoke.paas.console.util.organization.typeconvert

import com.fxiaoke.paas.console.bean.organization.object.EmployeeObject
import com.fxiaoke.paas.console.entity.organization.EmployeeEntity

import java.text.SimpleDateFormat

/**
 * Created by wangxing on 2018/03/07
 */
class EmployeeConvert {

  static EmployeeObject employeeEntityConvertEmployeeObject(EmployeeEntity employeeEntity) {
    EmployeeObject employeeObject = new EmployeeObject()
    employeeObject.setId(employeeEntity.getId())
    employeeObject.setUserId(employeeEntity.getUserId())
    employeeObject.setNickname(employeeEntity.getNickname())
    employeeObject.setName(employeeEntity.getName())
    employeeObject.setPhone(employeeEntity.getPhone())
    employeeObject.setEmail(employeeEntity.getEamil())
    employeeObject.setPosition(employeeEntity.getPosition())
    employeeObject.setTenantId(employeeEntity.getTenantId())
    employeeObject.setSupervisorId(employeeEntity.getSupervisorId())
    employeeObject.setTitle(employeeEntity.getTitle())
    employeeObject.setPicAddr(employeeEntity.getPicAddr())
    employeeObject.setDescription(employeeEntity.getDescription())
    if (employeeEntity.getStatus() != null) {
      if (employeeEntity.getStatus() == 0) {
        employeeObject.setStatus("启用")
      } else if (employeeEntity.getStatus() == 1) {
        employeeObject.setStatus("停用")
      }
    }
    if (employeeEntity.getCreateTime() != null) {
      employeeObject.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(employeeEntity.getCreateTime())))
    }
    employeeObject.setLastModifiedBy(employeeEntity.getLastModifiedBy())
    if (employeeEntity.getLastModifiedTime() != null) {
      employeeObject.setLastModifiedTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(employeeEntity.getLastModifiedTime())))
    }
    employeeObject.setIsDeleted(employeeEntity.getIsDeleted())
    employeeObject.setDepartment(employeeEntity.getDepartment())
    employeeObject.setVersion(employeeEntity.getVersion())
    employeeObject.setCreatedBy(employeeEntity.getCreatedBy())
    employeeObject.setPackage_(employeeEntity.getPackage_())
    employeeObject.setObjectDescribeId(employeeEntity.getObjectDescribeId())
    employeeObject.setObjectDescribedApiName(employeeEntity.getObjectDescribedApiName())
    employeeObject.setRecordType(employeeEntity.getRecordType())
    employeeObject.setExtendObjDataId(employeeEntity.getExtendObjDataId())
    return employeeObject
  }
}
