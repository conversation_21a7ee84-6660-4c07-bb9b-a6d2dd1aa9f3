package com.fxiaoke.paas.console.web.functional

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.facishare.converter.EIEAConverter
import com.facishare.paas.license.Result.LicenseListResult
import com.facishare.paas.license.arg.*
import com.facishare.paas.license.common.LicenseContext
import com.facishare.paas.license.common.LicenseObjectInfoContext
import com.facishare.paas.license.common.Result
import com.facishare.paas.license.constant.LicenseConstant
import com.facishare.paas.license.pojo.ModuleInfoPojo
import com.facishare.paas.license.pojo.ProductLicensePojo
import com.facishare.paas.license.util.LicenseDoUtil
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData
import com.facishare.uc.api.service.EnterpriseEditionService
import com.fxiaoke.appcenter.restapi.arg.QueryQuotaInfoArg
import com.fxiaoke.appcenter.restapi.arg.QueryUsedQuotaArg
import com.fxiaoke.appcenter.restapi.common.HeaderObj
import com.fxiaoke.appcenter.restapi.service.QuotaService
import com.fxiaoke.helper.StringHelper
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.bean.organization.object.EnterpriseObject
import com.fxiaoke.paas.console.entity.license.LicenseObjectEntity
import com.fxiaoke.paas.console.entity.license.ProductLicenseEntity
import com.fxiaoke.paas.console.service.license.LicenseService
import com.fxiaoke.paas.console.service.license.LicenseSyncJobService
import com.fxiaoke.paas.console.util.DateFormatUtil
import com.fxiaoke.paas.console.util.organization.typeconvert.EnterpriseConvert
import com.github.autoconf.ConfigFactory
import com.github.shiro.support.ShiroCasRealm
import com.google.common.base.CharMatcher
import com.google.common.base.Splitter
import com.google.common.base.Strings
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import com.google.common.util.concurrent.RateLimiter
import groovy.util.logging.Slf4j
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.collections.MapUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.support.RedirectAttributes

import javax.annotation.PostConstruct
import javax.annotation.Resource
import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * <AUTHOR> Created on 2018/3/30.
 */
@Controller
@Slf4j
@RequestMapping("/")
class LicenseController {

  private List<String> appIdList
  private List<String> crmKeys
  private List<String> moduleTypes

  @Resource(name = "casRealm")
  private ShiroCasRealm cas
  @Autowired
  private LicenseService licenseService
  @Autowired
  private LicenseSyncJobService licenseSyncJobService
  @Autowired
  private EnterpriseEditionService enterpriseEditionService
  @Autowired
  private EIEAConverter eieaConverter
  @Autowired
  private QuotaService quotaService


  private static final String token = "248830b6-b611-446e-a086-3aed5f5ba6c9"

  private String tenantIdByOrder
  private int limiterNumber
  private String licenseAppId

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("paas-console-base", { config ->
      appIdList = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(config.get("appId"))
      crmKeys = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(config.get("crmKeys"))
      moduleTypes = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(config.get("moduleTypes"))
      tenantIdByOrder = config.get("tenantIdByOrder")
      limiterNumber = config.get("limiterNumber", 10 as String) as int
      licenseAppId = config.get("licenseAppId", "FSAID_5f5e248")
    })
  }

  /**
   * 跳转license版本
   * @return
   */
  @RequestMapping("/license/lice-version")
  def licenceVersion(ModelMap model) {
    model["appIdList"] = appIdList
    "license/license-version"
  }

  /**
   * 查询licence版本
   * @param authContext
   */
  @RequestMapping("/license/lin-version")
  @ResponseBody
  @SystemControllerLog(description = "分版 -- 查询licence版本")
  def licenceVersion(@RequestParam String dataObject) {
    JSONObject jsonObject = JSONObject.parseObject(dataObject)
    if (Strings.isNullOrEmpty(jsonObject.getString("tenantId"))) {
      log.error("关键字段为空")
      return ["data": []]
    }
    try {
      LicenseContext licenseContext = new LicenseContext()
      licenseContext.setTenantId(jsonObject.getString("tenantId").trim())
      licenseContext.setUserId("1000")
      licenseContext.setAppId(jsonObject.getString("appId").trim())
      QueryProductArg arg = new QueryProductArg()
      arg.setLicenseContext(licenseContext)

      def result = licenseService.queryProductVersion(arg)
      if (CollectionUtils.isEmpty(result.result)) {
        log.warn("license product version is []")
        return ["data": []]
      }
      def json = []
      def fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
      result.result.each { it ->
        json << [it["tenantId"], it["productId"], it["versionName"], it["productType"], it["productName"], it["currentVersion"], fmt.format(it["startTime"]), fmt.format(it["expiredTime"])]
      }
      log.info("licenceVersion size={}", json.size())
      return ["data": json]
    } catch (Exception e) {
      log.error("查询License版本异常,error:", e)
      return ["data": []]
    }
  }

  @RequestMapping("/license/accessLicense")
  @ResponseBody
  @SystemControllerLog(description = "分版 -- 查询licence版本")
  def accessLicense(@RequestParam String dataObject) {
    JSONObject jsonObject = JSONObject.parseObject(dataObject)
    if (Strings.isNullOrEmpty(jsonObject.getString("tenantId"))) {
      log.error("关键字段为空")
      return ["data": []]
    }
    try {
      def arg = LicenseAccessArg.builder().context(LicenseDoUtil.buildLicenseContext(jsonObject.getString("tenantId"))).moduleCode("object_data_count").paraKey("data_count_limit").build();
      def result = licenseService.accessLicense(arg)
      if (Objects.isNull(result)) {
        log.warn("license accessLicense []")
        return ["data": []]
      }
      return ["data": result]
    } catch (Exception e) {
      log.error("查询License版本异常,error:", e)
      return ["data": []]
    }
  }


  @RequestMapping("/license/accessLicense/file")
  @ResponseBody
  @SystemControllerLog(description = "分版 -- 查询licence版本")
  def accessLicenseFile(@RequestParam String dataObject) {
    JSONObject jsonObject = JSONObject.parseObject(dataObject)
    if (Strings.isNullOrEmpty(jsonObject.getString("tenantId"))) {
      log.error("关键字段为空")
      return ["data": []]
    }
    try {
      def arg = LicenseAccessArg.builder().context(LicenseDoUtil.buildLicenseContext(jsonObject.getString("tenantId"))).moduleCode("small_file_storage_app").paraKey("small_file_storage_limit").build();
      def result = licenseService.accessLicense(arg)
      if (Objects.isNull(result)) {
        log.warn("license accessLicense []")
        return ["data": []]
      }
      return ["data": result]
    } catch (Exception e) {
      log.error("查询License版本异常,error:", e)
      return ["data": []]
    }
  }


  /**
   * 跳转license
   * @param model
   * @return
   */
  @RequestMapping("/license/license")
  def licence(ModelMap model) {
    model["appIdList"] = appIdList
    "license/license"
  }

  /**
   * 查询指定license
   * @param tenantId
   * @param productVersion
   */
  @RequestMapping("/license/find-license")
  @ResponseBody
  @SystemControllerLog(description = "分版 -- 查询licence")
  def licence(@RequestParam String tenantId, @RequestParam String productVersion) {
    if (tenantId.isEmpty() || productVersion.isEmpty()) {
      log.error("关键字段为空!")
      return ["data": []]
    }
    try {
      def body = Maps.newHashMap()
      body["productVersion"] = productVersion.trim()

      LicenseContext context = new LicenseContext()
      context.setTenantId(tenantId.trim())
      context.setUserId("1000")
      context.setAppId("CRM")
      QueryDefaultDataArg arg = new QueryDefaultDataArg()
      arg.setContext(context)
      arg.setProductVersion(productVersion.trim())

      def result = licenseService.queryLicense(arg)
      if (CollectionUtils.isEmpty(result.result)) {
        log.warn("query license is []")
        return ["data": []]
      }

      def json = []
      result.result.each { it ->
        json << [it["id"], it["tenantId"], it["licenseType"], it["productId"], it["productType"], it["productVersion"],
                 DateFormatUtil.formatLong(it["startTime"] as Long), DateFormatUtil.formatLong(it["expiredTime"] as Long), it["maxHeadCount"]]
      }
      log.info("licence size={}", json.size())
      return ["data": json]
    } catch (Exception e) {
      log.error("查询licence异常！error:{}", e)
      return ["data": []]
    }
  }

  /**
   * 跳转product查询
   * @param model
   * @return
   */
  @RequestMapping("/license/product")
  def product(ModelMap model) {
    model["appIdList"] = appIdList
    "license/product"
  }

  /**
   * 查询product(包括过期的)
   * @param tenantId
   */
  @RequestMapping("/license/find-product")
  @ResponseBody
  @SystemControllerLog(description = "分版 -- 查询product")
  def product(@RequestParam String tenantId) {
    if (tenantId.isEmpty()) {
      log.error("关键字段为空!")
      return ["data": []]
    }
    try {
      LicenseContext licenseContext = new LicenseContext()
      licenseContext.setTenantId(tenantId.trim())
      licenseContext.setUserId("1000")
      licenseContext.setAppId("CRM")

      QueryProductArg arg = new QueryProductArg()
      arg.setLicenseContext(licenseContext)

      def result = licenseService.queryProduct(arg)
      if (CollectionUtils.isEmpty(result.result)) {
        log.warn("query product is []")
        return ["data": []]
      }

      def json = []
      result.result.each { it ->
        json << [it["id"], it["tenantId"], it["productName"], it["productVersion"], it["unitPrice"]]
      }
      log.info("product size={}", json.size())
      return ["data": json]
    } catch (Exception e) {
      log.error("查询product异常!error:{}", e)
      return ["data": []]
    }
  }

  @RequestMapping("/license/query-para")
  def queryPara() {
    return "license/query-para"
  }

  /**
   * 查询配额
   * @param tenantId
   * @param moduleCode
   * @param paraKeys
   * @return
   */
  @RequestMapping(value = "/license/query-para", method = RequestMethod.POST)
  @ResponseBody
  @SystemControllerLog(description = "分版 -- 查询配额")
  def queryParaValue(@RequestParam String tenantId, @RequestParam String moduleCode, @RequestParam String paraKeys) {
    def paraKeyList = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(paraKeys.trim())
    try {
      LicenseContext context = new LicenseContext()
      context.setTenantId(tenantId.trim())
      context.setUserId("1000")
      context.setAppId("CRM")
      QueryModuleParaArg arg = new QueryModuleParaArg()
      arg.setContext(context)
      arg.setModuleCode(moduleCode.trim())
      arg.setParaKeys(paraKeyList as Set<String>)


      def result = licenseService.queryPara(arg)
      ["code": 200, "result": result.result, "errMessage": result.errMessage]
    } catch (Exception e) {
      log.error("查询配额异常，error:", e)
      ["code": 500, "error": e.getMessage()]
    }
  }


  @RequestMapping("/license-write/update-para")
  def updateParaList() {
    return "license/update-para"
  }

  /**
   * 调整配额
   * @param tenantId 企业id
   * @param paraKey 配额key
   * @param paraValue 配额数
   * @param moduleCode 模版编码
   * @param r
   * @return
   */
  @RequestMapping(value = "/license-write/update-para", method = RequestMethod.POST)
  @SystemControllerLog(description = "分版 -- 调整配额")
  def updatePara(@RequestParam String tenantId, @RequestParam String paraKey, @RequestParam String paraValue, @RequestParam String moduleCode, RedirectAttributes r) {
    try {

      LicenseContext context = new LicenseContext()
      context.setTenantId(tenantId.trim())
      context.setUserId("1000")
      context.setAppId("CRM")
      UpdateModuleParaArg arg = new UpdateModuleParaArg()
      arg.setContext(context)
      arg.setModuleCode(moduleCode.trim())
      arg.setParaKey(paraKey.trim())
      arg.setParaValue(paraValue.trim() as Integer)

      def result = licenseService.updatePara(arg)
      r.addFlashAttribute("info", result.errMessage)
      r.addFlashAttribute("tenantId", tenantId)
      r.addFlashAttribute("paraKey", paraKey)
      r.addFlashAttribute("paraValue", paraValue)
      r.addFlashAttribute("moduleCode", moduleCode)
      return "redirect:/license/update-para"
    } catch (Exception e) {
      log.error("调整配额异常，error：", e)
      r.addFlashAttribute("error", e.getMessage())
      r.addFlashAttribute("tenantId", tenantId)
      r.addFlashAttribute("paraKey", paraKey)
      r.addFlashAttribute("paraValue", paraValue)
      r.addFlashAttribute("moduleCode", moduleCode)
      return "redirect:/license/update-para"
    }
  }

  @RequestMapping("/license/module")
  def findModules() {
    return "license/module-list"
  }

  /**
   * 查询企业下module
   * @param tenantId
   * @param r
   */
  @RequestMapping(value = "/license/module-list")
  @ResponseBody
  @SystemControllerLog(description = "分版 -- 查询企业下module")
  def findModuleList(@RequestParam String tenantId) {

    LicenseContext licenseContext = new LicenseContext()
    licenseContext.setTenantId(tenantId.trim())
    licenseContext.setUserId("1000")
    licenseContext.setAppId("CRM")
    QueryModuleArg arg = new QueryModuleArg()
    arg.setLicenseContext(licenseContext)

    try {
      def result = licenseService.queryModule(arg)
      def json = []
      result.result.each { it ->
        json << [it["id"], it["moduleName"], it["moduleCode"]]
      }
      log.info("product size={}", json.size())
      return ["data": json]
    } catch (Exception e) {
      log.error("/module-list is error:", e)
      return ["data": []]
    }
  }


  @RequestMapping("/license/clear-catch")
  def clearRedies() {
    return "license/clear-catch"
  }

  /**
   * 清缓存
   * @param tenantId
   * @return
   */
  @RequestMapping(value = "/license/clear-catch", method = RequestMethod.POST)
  @SystemControllerLog(description = "分版 -- 删除缓存")
  def clearCatch(@RequestParam String tenantId, RedirectAttributes r) {
    try {
      LicenseContext context = new LicenseContext()
      context.setTenantId(tenantId.trim())
      context.setUserId("1000")
      context.setAppId("CRM")
      def result = licenseService.clearCatch(context)
      r.addFlashAttribute("success", "clear " + tenantId + " catch is " + result.result)
      return "redirect:/license/clear-catch"
    } catch (Exception e) {
      log.error("clear is error:", e)
      r.addFlashAttribute("error", "FAIL")
      return "redirect:/license/clear-catch"
    }
  }

  @RequestMapping("/license-write/gray-price")
  def gray() {
    return "license/gray-price"
  }

/**
 * 添加灰度产品
 * @param tenantId
 * @param r
 * @return
 */
  @RequestMapping(value = "/license-write/gray-price", method = RequestMethod.POST)
  @SystemControllerLog(description = "分版 -- 添加灰度产品")
  def grayPrice(@RequestParam String tenantId, RedirectAttributes r, @RequestParam String startTime, @RequestParam String endTime) {
    try {
      Map<String, Object> postBody = Maps.newHashMap();
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
      String stTime = (String) sdf.parse(startTime).getTime()
      String edTime = (String) sdf.parse(endTime).getTime()
      if ((Long.compare(Long.parseLong(stTime), Long.parseLong(edTime))) >= 0) {
        r.addFlashAttribute("error", "时间错误")
        return "redirect:/license-write/gray-price"
      }
      if (!(this.isDateVail(startTime) && this.isDateVail(endTime))) {
        r.addFlashAttribute("error", "时间格式错误")
        return "redirect:/license-write/gray-price"
      }
      postBody.put("tenantId", tenantId)
      postBody.put("productVersion", "advanced_pricing_app")
      postBody.put("productName", "高级定价")
      postBody.put("productType", "3")
      postBody.put("startTime", sdf.parse(startTime).getTime())
      postBody.put("expiredTime", sdf.parse(endTime).getTime())
      licenseService.postToGrayPrice(postBody)
      LicenseContext licenseContext = new LicenseContext()
      licenseContext.setTenantId(tenantId.trim())
      licenseContext.setUserId("1000")
      licenseContext.setAppId("CRM")
      QueryModuleArg arg = new QueryModuleArg()
      arg.setLicenseContext(licenseContext)
      def result = licenseService.queryModule(arg)
      ArrayList<ModuleInfoPojo> moduleInfoPojoList = result.result
      for (ModuleInfoPojo moduleInfoPojo : moduleInfoPojoList) {
        if ("advanced_pricing_app".equals(moduleInfoPojo.getModuleCode())) {
          r.addFlashAttribute("success", "提交成功")
          return "redirect:/license-write/gray-price"
        }
      }
    } catch (Exception e) {
      log.error("grayPrice error:", e)
      r.addFlashAttribute("error", "FAIL")
      return "redirect:/license-write/gray-price"
    }
  }

  /**
   * 判断字符串是否为正确的时间格式
   * @param date
   * @return
   */
  private Boolean isDateVail(String date) {
    //用于指定 日期/时间 模式
    DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    boolean flag = true;
    try {
      //Java 8 新添API 用于解析日期和时间
      LocalDateTime.parse(date, dtf);
    } catch (Exception e) {
      flag = false;
    }
    return flag;
  }

  @RequestMapping("/license-admin/add-license-object")
  def addLincenseObject() {
    return "license/add-license-object"
  }

  /**
   * 添加主版本产品对象
   * @param productVersions
   * @param apiNames
   * @param r
   * @return
   */
  @RequestMapping(value = "/license-admin/add-CRM-object", method = RequestMethod.POST)
  @SystemControllerLog(description = "分版 -- 添加CRM产品对象")
  @ResponseBody
  def addCRMObject(@RequestBody JSONObject dataObject) {
    try {
      Map<String, Object> postBody = new HashMap()
      String productVersions = dataObject.getString("productVersions")
      String versionC = dataObject.getString("versionC")
      String apiNames = dataObject.getString("apiNames")
      String check = dataObject.getString("check")
      String type = dataObject.getString("type")
      if ("yes".equals(check)) {
        if (!productVersions.equals(versionC)) return "fail"
      }
      postBody.put("codeOrVersion", this.removeEmptyString(productVersions))
      postBody.put("apiNames", this.removeEmptyString(apiNames))
      postBody.put("type", type)
      licenseService.addLincenseObject(postBody)
      return "success"
    } catch (Exception e) {
      log.error("add-CRM-object is error:", e)
      return "redirect:/license-admin/add-license-object"
    }
  }

  @RequestMapping(value = "/license-admin/delete-CRM-object", method = RequestMethod.POST)
  @SystemControllerLog(description = "分版 -- 删除CRM产品对象")
  @ResponseBody
  def deleteCRMObject(@RequestBody JSONObject dataObject) {
    try {
      Map<String, Object> postBody = new HashMap()
      String productVersions = dataObject.getString("productVersions")
      String versionC = dataObject.getString("versionC")
      String check = dataObject.getString("check")
      if ("yes".equals(check)) {
        if (!productVersions.equals(versionC)) return "fail"
      }
      String apiNames = dataObject.getString("apiNames")
      String type = dataObject.getString("type")
      postBody.put("codeOrVersion", this.removeEmptyString(productVersions))
      postBody.put("apiNames", this.removeEmptyString(apiNames))
      postBody.put("type", type)
      licenseService.deleteLincenseObject(postBody)
      return "success"
    } catch (Exception e) {
      log.error("add-CRM-object is error:", e)
      return "fail"
    }
  }

  @RequestMapping("/license-admin/add-application-object")
  def addApplicationObject() {
    return "license/add-application-object"
  }

  /**
   * 添加应用产品对象
   * @param productVersions
   * @param apiNames
   * @param moduleCode
   * @param r
   * @return
   */
  @RequestMapping(value = "/license-admin/add-application-object", method = RequestMethod.POST)
  @SystemControllerLog(description = "分版 -- 添加应用产品对象")
  def addApplicationObject(@RequestParam String apiNames, @RequestParam String moduleCode, RedirectAttributes r) {
    try {
      Map<String, Object> postBody = new HashMap()
      postBody.put("codeOrVersion", this.removeEmptyString(moduleCode))
      postBody.put("apiNames", this.removeEmptyString(apiNames))
      postBody.put("type", LicenseConstant.ProductType.APPLICATION)
      licenseService.addLincenseObject(postBody)
      r.addFlashAttribute("success", "添加成功")
      return "redirect:/license-admin/add-license-object"
    } catch (Exception e) {
      log.error("add-application-object is error:", e)
      r.addFlashAttribute("error", "FAIL")
      return "redirect:/license-admin/add-license-object"
    }
  }

  /**
   * 删除空字符串
   * @param str
   * @return
   */
  private String[] removeEmptyString(String str) {
    String[] strings = str.split(",")
    String st = "";
    List<String> list = new ArrayList();
    for (String s : strings) {
      list.add(s.trim())
    }
    for (String s : list) {
      if (!s.equals("")) {
        st += s + ","
      }
    }
    return st.substring(0, st.length() - 1).split(",")
  }

  @RequestMapping("/license-admin/handle-special-approval")
  def handleSpecialApproval() {
    return "license/handle-special-approval"
  }

  @RequestMapping(value = "/license-admin/handle-special-approvals")
  @SystemControllerLog(description = "分版 -- 根据订单号查询license")
  @ResponseBody
  def handleSpecialApprovals(@RequestParam String dataObject) {
    try {
      JSONObject jsonObject = JSONObject.parseObject(dataObject)
      String order = jsonObject.getString("order")
      String tenantId = jsonObject.getString("tenantId")
      String orderName = jsonObject.getString("orderName")

      CheckOrderNumberArg checkOrderNumberArg = new CheckOrderNumberArg();
      Map<String, Objects> fsProduct
      if (order.contains("销售订单")) {
        Map<String, Objects> idAndApi = licenseService.queryFsSalesOrderInfoByName(tenantIdByOrder, orderName)
        checkOrderNumberArg.setOrderNumbers(Sets.newHashSet(idAndApi.get("id")))
        if (MapUtils.isEmpty(idAndApi)) {
          return ["data": []]
        }
      } else if (order.contains("订单产品")) {
        Map<String, Objects> orderProduct = licenseService.queryFsSalesOrderProductInfoByName(tenantIdByOrder, orderName)
        fsProduct = licenseService.queryFsProductInfoByName(tenantIdByOrder, orderProduct.get("product_id"))
        if (MapUtils.isEmpty(orderProduct) || MapUtils.isEmpty(fsProduct)) {
          return ["data": []]
        }
        checkOrderNumberArg.setOrderNumbers(Sets.newHashSet(orderProduct.get("order_id")))
      }
      LicenseContext context = new LicenseContext();
      context.setTenantId(tenantId)
      context.setUserId("1000")
      context.setAppId("CRM")

      checkOrderNumberArg.setContext(context)

      List<ProductLicensePojo> result = licenseService.queryLicenseByOrderNumber(checkOrderNumberArg) as List<ProductLicensePojo>
      if (Objects.isNull(result)) {
        return ["data": []]
      }

      def json = []
      def fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
      result.each { it ->
        json << [it["id"], it["tenantId"], it["productId"], it["productName"], it["productVersion"], it["productType"], it["orderNumber"], it["maxHeadCount"], fmt.format(Long.parseLong(it["startTime"])), fmt.format(Long.parseLong(it["expiredTime"])), it["moduleCode"], it["paraKey"], it["paraValue"], fsProduct.get("product_code")]
      }
      return ["data": json]
    } catch (Exception e) {
      log.error("handle-special-approvals is error", e)
      return ["data": []]
    }


  }

  @RequestMapping(value = "/license-admin/handle-special-approvals-object")
  @SystemControllerLog(description = "分版 -- 执行处理特殊审批")
  @ResponseBody
  def handleSpecialApprovalsExpiredOrder(@RequestBody String SpecialApprovalObject, RedirectAttributes r) {
    SpecialApprovalArg arg = new SpecialApprovalArg();
    try {
      JSONObject jsonObject = JSONObject.parseObject(SpecialApprovalObject)

      String id = jsonObject.getString("id")
      String idCheck = jsonObject.get("idCheck")
      if (!id.equals(idCheck)) {
        return "fail"
      }
      String type = jsonObject.getString("type")
      String tenantId = jsonObject.getString("tenantId")
      String orderNumber = jsonObject.getString("orderNumber")
      String expiredTime = jsonObject.getString("expiredTime")
      String productId = jsonObject.getString("productId")
      Integer maxCount = jsonObject.getInteger("maxCount")
      Boolean deleteFlag = jsonObject.getBoolean("deleteFlag")
      Integer productType = jsonObject.getInteger("productType")

      Long exTime

      SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

      //如果时间格式正确且不为空 exTime赋值
      if (StringUtils.isNotEmpty(expiredTime) && isDateVail(expiredTime)) {
        exTime = dft.parse(expiredTime).getTime()
      } else if (StringUtils.isNotEmpty(expiredTime) && !isDateVail(expiredTime)) {
        //如果时间格式不正确 且 不为空
        return "fail"
      } else if (productType != 0 && maxCount != null) {
        return "fail"
      }

      arg.setId(id)
      arg.setTenantId(tenantId)
      arg.setProductId(productId)
      arg.setExpiredTime(exTime)
      arg.setToken(token)
      arg.setMaxCount(maxCount)
      arg.setDeleteFlag(deleteFlag)
      arg.setApprovalType(type)
      arg.setOrderNumber(orderNumber)

      Result result = licenseService.handleSpecialApproval(arg)

      if (result.result == "success") {
        return "success"
      }
      return "fail"
    } catch (Exception e) {
      log.error("handle-special-approvals-object is error arg:{},", arg, e)
      return "fail"
    }


  }

  @RequestMapping("/license/master-version")
  def masterVersion() {
    return "license/master-version-management"
  }

  /**
   * 主版本管理
   * @param tenantId
   * @return
   */
  @RequestMapping(value = "/license/master-version-management")
  @ResponseBody
  @SystemControllerLog(description = "分版 -- 主版本管理")
  def masterVersionManagement(@RequestParam String tenantId) {
    try {
      def result = licenseService.queryMasterVersion(tenantId)
      def json = []
      def fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
      result.result.each { it ->
        json << [it["id"], it["tenantId"], it["productName"], it["productVersion"]]
      }
      log.info("product size={}", json.size())
      return ["data": json]
    } catch (Exception e) {
      log.error("/master-version-management is error:", e)
      return ["data": []]
    }
  }

  @RequestMapping("/license/all-version")
  def allVersion() {
    return "license/all-major-license"
  }

  @RequestMapping(value = "/license/all-major-license")
  @ResponseBody
  @SystemControllerLog(description = "分版 -- 主版本详细数据")
  def allMajorLicense(@RequestParam String tenantId) {
    try {
      def result = licenseService.allMajorLicense(tenantId)
      def json = []
      result.each { it ->
        json << [it["productVersion"], it["productName"], it["moduleCode"], it["moduleName"], it["paraKey"], it["keyName"], it["paraValue"]]
      }
      log.info("product size={}", json.size())
      return ["data": json]
    } catch (Exception e) {
      log.error("all-major-license is error:", e)
      return ["data": []]
    }
  }

  @RequestMapping("/license/all-app")
  def allApp() {
    return "license/all-app-license"
  }

  @RequestMapping(value = "/license/all-app-license")
  @ResponseBody
  @SystemControllerLog(description = "分版 -- 应用详细数据")
  def allAppLicense(@RequestParam String tenantId) {
    try {
      def result = licenseService.allAppLicense(tenantId)
      return ["data": result]
    } catch (Exception e) {
      log.error("all-major-license is error:", e)
      return ["data": []]
    }
  }

  @RequestMapping("/license/product-bought")
  def productBought() {
    return "license/product-bought"
  }

  @RequestMapping(value = "/license/product-boughts")
  @ResponseBody
  @SystemControllerLog(description = "分版 -- 购买license的企业")
  def queryProductBought(@RequestParam String dataObject) {
    try {
      JSONObject jsonObject = JSONObject.parseObject(dataObject)
      String productVersion = jsonObject.getString("productVersion")
      String productName = jsonObject.getString("appId")
      Boolean expired = jsonObject.getBoolean("expired")
      if (StringUtils.isEmpty(productVersion) || StringUtils.isEmpty(productName) || expired == null) {
        return ["data": []]
      }
      List<Map> result = Lists.newArrayList();
      ProductBoughtArg arg = new ProductBoughtArg()
      arg.setExpired(expired)
      arg.setProductName(productName)
      arg.setProductVersion(productVersion.trim())
      def tenantIds = licenseService.queryProductBought(arg)
      for (String tenantId : tenantIds) {
        Map<String, Object> map = Maps.newLinkedHashMap()
        if (StringHelper.isNullOrBlank(tenantId)) {
          return JSONObject.toJSONString(map)
        }
        tenantId = tenantId.trim()
        BatchGetSimpleEnterpriseDataArg batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(
                enterpriseIds: null,
                enterpriseAccounts: Arrays.asList(tenantId)
        )
        BatchGetSimpleEnterpriseDataResult batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
        List<SimpleEnterpriseData> enterpriseList = batchGetSimpleEnterpriseDataResult.simpleEnterpriseList
        List<EnterpriseObject> enterpriseObjectList = enterpriseList?.collect { EnterpriseConvert.enterpriseDataConvertEnterpriseObject(it) }
        if (CollectionUtils.isNotEmpty(enterpriseList)) {
          SimpleEnterpriseData simpleEnterpriseData = enterpriseList.get(0)
          if (simpleEnterpriseData != null && simpleEnterpriseData.enterpriseId != 0 && simpleEnterpriseData.enterpriseAccount.equals(tenantId)) {
            tenantId = simpleEnterpriseData.enterpriseId
          }
        } else {
          if ("null".equals(tenantId) || tenantId.contains("fktest")) {
            continue
          }
          batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(
                  enterpriseIds: Arrays.asList(Integer.parseInt(tenantId)),
                  enterpriseAccounts: null
          )
          batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
          enterpriseList = batchGetSimpleEnterpriseDataResult.simpleEnterpriseList
          if (enterpriseList.size() == 0) {
            continue
          }
          enterpriseObjectList = enterpriseList?.collect { EnterpriseConvert.enterpriseDataConvertEnterpriseObject(it) }
        }
        EnterpriseObject enterpriseObject = enterpriseObjectList.get(0);
        map.put("EI", String.valueOf(enterpriseObject.enterpriseId))
        map.put("EA", enterpriseObject.enterpriseAccount)
        map.put("企业名称", enterpriseObject.enterpriseName)
        map.put("状态", enterpriseObject.runStatus)
        result.add(map)
      }

      def json = []
      result.each { it ->
        json << [it["EI"], it["企业名称"], it["EA"], it["状态"]]
      }
      log.info("product size={}", json.size())
      return ["data": json]
    } catch (Exception e) {
      log.error("all-major-license is error:", e)
      return ["data": []]
    }
  }

  @RequestMapping("/license/product-boughtV2")
  def productBoughtV2() {
    return "license/product-boughtV2"
  }

  @RequestMapping(value = "/license/product-boughtsV2")
  @ResponseBody
  @SystemControllerLog(description = "分版 -- 购买license的企业V2")
  def queryProductBoughtV2(@RequestParam String dataObject) {
    try {
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
      JSONObject jsonObject = JSONObject.parseObject(dataObject)
      String productVersion = jsonObject.getString("productVersion")
      String productName = jsonObject.getString("appId")
      Boolean expired = jsonObject.getBoolean("expired")
      if (StringUtils.isEmpty(productVersion) || StringUtils.isEmpty(productName) || expired == null) {
        return ["data": []]
      }
      List<Map> result = Lists.newArrayList();
      ProductBoughtArg arg = new ProductBoughtArg()
      arg.setExpired(expired)
      arg.setProductName(productName)
      arg.setProductVersion(productVersion.trim())
      def productLicenseResult = licenseService.queryProductBoughtV2(arg)
      for (ProductLicenseEntity productLicense : productLicenseResult) {
        String tenantId = productLicense.getTenantId()
        String startTime = productLicense.getStartTime()
        String expiredTime = productLicense.getExpiredTime()
        Map<String, Object> map = Maps.newLinkedHashMap()
        if (StringHelper.isNullOrBlank(tenantId)) {
          return JSONObject.toJSONString(map)
        }
        tenantId = tenantId.trim()
        BatchGetSimpleEnterpriseDataArg batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(
                enterpriseIds: null,
                enterpriseAccounts: Arrays.asList(tenantId)
        )
        BatchGetSimpleEnterpriseDataResult batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
        List<SimpleEnterpriseData> enterpriseList = batchGetSimpleEnterpriseDataResult.simpleEnterpriseList
        List<EnterpriseObject> enterpriseObjectList = enterpriseList?.collect { EnterpriseConvert.enterpriseDataConvertEnterpriseObject(it) }
        if (CollectionUtils.isNotEmpty(enterpriseList)) {
          SimpleEnterpriseData simpleEnterpriseData = enterpriseList.get(0)
          if (simpleEnterpriseData != null && simpleEnterpriseData.enterpriseId != 0 && simpleEnterpriseData.enterpriseAccount.equals(tenantId)) {
            tenantId = simpleEnterpriseData.enterpriseId
          }
        } else {
          if ("null".equals(tenantId) || tenantId.contains("fktest")) {
            continue
          }
          batchGetSimpleEnterpriseDataArg = new BatchGetSimpleEnterpriseDataArg(
                  enterpriseIds: Arrays.asList(Integer.parseInt(tenantId)),
                  enterpriseAccounts: null
          )
          batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(batchGetSimpleEnterpriseDataArg)
          enterpriseList = batchGetSimpleEnterpriseDataResult.simpleEnterpriseList
          if (enterpriseList.size() == 0) {
            continue
          }
          enterpriseObjectList = enterpriseList?.collect { EnterpriseConvert.enterpriseDataConvertEnterpriseObject(it) }
        }
        EnterpriseObject enterpriseObject = enterpriseObjectList.get(0);
        map.put("EI", String.valueOf(enterpriseObject.enterpriseId))
        map.put("EA", enterpriseObject.enterpriseAccount)
        map.put("企业名称", enterpriseObject.enterpriseName)
        map.put("状态", enterpriseObject.runStatus)
        map.put("开始时间", sdf.format(Long.parseLong(startTime)))
        map.put("过期时间", sdf.format(Long.parseLong(expiredTime)))
        result.add(map)
      }

      def json = []
      result.each { it ->
        json << [it["EI"], it["企业名称"], it["EA"], it["状态"], it["开始时间"], it["过期时间"]]
      }
      log.info("product size={}", json.size())
      return ["data": json]
    } catch (Exception e) {
      log.error("all-major-license is error:", e)
      return ["data": []]
    }
  }

  @RequestMapping("/license/query-object-template")
  def queryObjectTemplate() {
    return "license/query-object-template"
  }

  @RequestMapping("/license/query-object-templates")
  @ResponseBody
  @SystemControllerLog(description = "分版 -- 产品与对象绑定关系")
  def queryObjectTemplates(@RequestParam String version) {
    def json = []
    try {
      if (StringUtils.isNotEmpty(version)) {
        List<LicenseObjectEntity> entities = licenseService.queryObjectTemplate(version)
        entities.each { it ->
          json << [it["productVersion"], it["moduleCode"], it["productName"], it["apiName"], it["apiChineseName"]]
        }
      }
      return ["data": json]
    } catch (Exception e) {
      log.error("queryObjectTemplates version is error ", e)
      return ["data": []]
    }
  }

  @RequestMapping("/license/query-object-templateV2")
  def queryObjectTemplateV2() {
    return "license/query-object-templateV2"
  }

  @RequestMapping("/license/query-object-templatesV2")
  @ResponseBody
  @SystemControllerLog(description = "分版 -- 查询对象模版")
  def queryObjectTemplatesV2(@RequestParam String version) {
    def json = []
    try {
      if (StringUtils.isNotEmpty(version)) {
        List<LicenseObjectEntity> entities = licenseService.queryObjectTemplateV2(version)
        entities.each { it ->
          json << [it["productVersion"], it["moduleCode"], it["productName"], it["apiName"], it["apiChineseName"], it["soldOut"]]
        }
      }
      return ["data": json]
    } catch (Exception e) {
      log.error("queryObjectTemplates version is error ", e)
      return ["data": []]
    }
  }


  @RequestMapping("/license/view-moduleDetail")
  def viewModuleDetail(@RequestParam String productId, Model model) {
    model.addAttribute("productId", productId)
    return "license/view-moduleDetails"
  }

  /**
   * 查看
   * @param productId
   * @return
   */
  @RequestMapping(value = "/license/view-moduleDetails")
  @ResponseBody
  @SystemControllerLog(description = "分版 -- 查看模块详情")
  def viewModuleDetails(@RequestParam String productId) {
    try {
      def result = licenseService.queryModuleInfoByProId(productId)
      def json = []
      def fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
      result.result.each { it ->
        json << [it["productId"], it["moduleName"], it["moduleCode"]]
      }
      log.info("product size={}", json.size())
      return ["data": json]
    } catch (Exception e) {
      log.error("/view-details is error:", e)
      return ["data": []]
    }
  }

  @RequestMapping("/license/view-paraDetail")
  def viewParaDetail(@RequestParam String productId, Model model) {
    model.addAttribute("productId", productId)
    return "license/view-paraDetails"
  }

  @RequestMapping(value = "/license/view-paraDetails")
  @ResponseBody
  @SystemControllerLog(description = "分版 -- 查看配额详情")
  def viewParaDetails(@RequestParam String productId) {
    try {
      def result = licenseService.queryModuleParaByProId(productId)
      def json = []
      def fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
      result.result.each { it ->
        json << [it["tenantId"], it["moduleCode"], it["paraKey"], it["paraValue"]]
      }
      log.info("product size={}", json.size())
      return ["data": json]
    } catch (Exception e) {
      log.error("/view-details is error:", e)
      return ["data": []]
    }
  }

  @RequestMapping("/license/view-objectDetail")
  def viewObjectDetail(@RequestParam String productId, @RequestParam String productVersion, Model model) {
    model["crmKeys"] = crmKeys
    model["moduleTypes"] = moduleTypes
    model["productId"] = productId
    model["productVersion"] = productVersion
    //model.addAttribute("productId",productId)
    return "license/view-objectInfoDetails"
  }

  @RequestMapping(value = "/license/view-objectDetails")
  @SystemControllerLog(description = "分版 -- 查看版本对象")
  @ResponseBody
  def viewObjectDetails(@RequestParam String dataObject) {
    try {
      JSONObject jsonObject = JSONObject.parseObject(dataObject)
      String productId = jsonObject.get("productId")
      def result = licenseService.queryObjectByProId(productId)
      def json = []
      result.result.each { it ->
        json << [it["productVersion"], it["apiName"]]
      }
      log.info("product size={}", json.size())
      return ["data": json]
    } catch (Exception e) {
      log.error("/view-objectDetails is error:", e)
      return ["data": []]
    }
  }


  @RequestMapping("/license/lice-relation")
  def licenseRelation() {
    return "license/license-relation"
  }

  @RequestMapping(value = "/license/lice-relations")
  @SystemControllerLog(description = "分版 -- 查看父子版本")
  @ResponseBody
  def licenseRelations(@RequestParam String tenantId) {
    try {
      Result result = licenseService.queryLicenseRelation(tenantId)
      def json = []
      result.result.each { it ->
        json << [it["productVersion"], it["parentProductVersion"]]
      }
      log.info("product size={}", json.size())
      return ["data": json]
    } catch (Exception e) {
      log.error("/license-relation is error:", e)
      return ["data": []]
    }
  }

  @RequestMapping("/license/add-moduleInfo")
  def addModuleInfo() {
    return "license/add-moduleInfo-object"
  }


  @RequestMapping("/license/query/overview")
  def queryOverview(ModelMap model) {
    return "license/query-overview"
  }

  @RequestMapping(value = "/license/query-overview")
  @SystemControllerLog(description = "分版 -- 查询概览")
  @ResponseBody
  def queryOverviewInfo(@RequestParam String tenantId, @RequestParam Boolean totalPara, @RequestParam Boolean needScan) {
    return ["code": 200, "info": JSON.toJSONString(licenseService.queryOverview(tenantId, totalPara == null ? true : totalPara, needScan == null ? false : needScan))]
  }

  @RequestMapping("/license/query/object")
  def queryObject(ModelMap model) {
    model["crmKeys"] = crmKeys
    model["moduleTypes"] = moduleTypes
    return "license/query-object"
  }


  @RequestMapping(value = "/license/query-object")
  @SystemControllerLog(description = "分版 -- 查询下发对象")
  @ResponseBody
  def clearCatch(@RequestParam String dataObject) {
    JSONObject jsonObject = JSONObject.parseObject(dataObject)
    def tenantId = jsonObject.getString("tenantId")
    def crmKey = jsonObject.getString("crmKey")
    def moduleType = jsonObject.getString("moduleType")
    try {
      LicenseContext context = new LicenseContext()
      context.setUserId("1000")
      context.setAppId("CRM")
      context.setTenantId(tenantId)

      LicenseObjectInfoContext infoContext = new LicenseObjectInfoContext()
      infoContext.setContext(context)
      infoContext.setCrmKey(crmKey)
      infoContext.setModuleType(moduleType)

      def result = licenseService.queryObject(infoContext)
      def apiNames = result.result as Set<String>
      if (CollectionUtils.isEmpty(apiNames)) {
        return ["data": []]
      }
      def json = []
      apiNames.forEach({ it ->
        json << [it]
      })

      return ["data": json]
    } catch (Exception e) {
      log.error("query object is error:", e)
      return ["data": []]
    }
  }

  @RequestMapping("/license/sql/query")
  def sqlQuery(ModelMap model) {
    return "license/sql-query"
  }


  @RequestMapping(value = "/license/query-sql")
  @SystemControllerLog(description = "分版 -- sql查询")
  @ResponseBody
  def queryBySql(@RequestParam String tenantId, @RequestParam String querySql) {
    if (!checkSql(querySql)) {
      log.warn("querySql 不规范")
      return ["code": 403, "error": "Sql不符合规范，只能进行查询！"]
    }
    try {
      QuerySqlArg arg = new QuerySqlArg()
      arg.setTenantId(tenantId)
      arg.setQuerySql(querySql)

      def res = licenseService.queryLicenseBySql(arg)
      return ["code": 200, "info": JSON.toJSONString(res)]
    } catch (Exception e) {
      log.error("query-sql is error:", e)
      return ["code": 500, "error": "查询异常：" + e.getMessage()]
    }

  }


  @RequestMapping("/license/query/expire")
  def queryExpire(ModelMap model) {
    return "license/license-expire"
  }

  @RequestMapping(value = "/license/query-expire")
  @SystemControllerLog(description = "分版 -- 查询指定产品到期时间")
  @ResponseBody
  def queryExpire(@RequestParam String tenantId, @RequestParam String productCodes) {
    try {
      LicenseContext context = new LicenseContext()
      context.setUserId("1000")
      context.setAppId("CRM")
      context.setTenantId(tenantId)
      QueryExpireTimeArg arg = new QueryExpireTimeArg()
      arg.setContext(context)
      if (!Strings.isNullOrEmpty(productCodes)) {
        arg.setProductCodes(Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(productCodes))
      }
      def result = licenseService.queryExpire(arg)
      ["code": 200, "result": result.result, "errMessage": result.errMessage]
    } catch (Exception e) {
      return ["code": 500, "error": "查询异常：" + e.getMessage()]
    }
  }

  @RequestMapping("/license-write/send-mq")
  def sendMq(ModelMap model) {
    return "license/send-mq"
  }

  @RequestMapping(value = "/license-write/send-mq", method = RequestMethod.POST)
  @SystemControllerLog(description = "分版 -- 手动发送创建mq")
  def sendCreateLicenseMq(@RequestParam String tenantIds, @RequestParam productCodes, RedirectAttributes r) {
    try {
      List<String> tenantIdList = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(tenantIds)
      LicenseContext context = new LicenseContext()
      context.setUserId("1000")
      context.setAppId("CRM")
      context.setTenantId(tenantIdList.get(0))
      SendMqArg arg = new SendMqArg()
      arg.setContext(context)
      arg.setTenantIds(Sets.newHashSet(tenantIdList))
      if (!Strings.isNullOrEmpty(productCodes)) {
        List<String> codes = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(productCodes);
        arg.setProductCodes(Sets.newHashSet(codes))
      }
      def result = licenseService.sendMq(arg)
      r.addFlashAttribute("success", "发送mq " + result.errMessage)
      return "redirect:/license/send-mq"
    } catch (Exception e) {
      r.addFlashAttribute("error", e.getMessage())
      return "redirect:/license/send-mq"
    }
  }

  @RequestMapping("/license/query-count")
  def queryCount(ModelMap model) {
    return "license/query-count"
  }

  @RequestMapping(value = "/license/query-count", method = RequestMethod.POST)
  @SystemControllerLog(description = "分版 -- 查询企业员工数")
  @ResponseBody
  def queryMaxHeadCount(@RequestBody String data, RedirectAttributes r) {
    try {
      RateLimiter limiter = RateLimiter.create(limiterNumber)
      def json = []
      def object = JSONObject.parseObject(data)
      ArrayList<String> tenantIds = object.get("tenantId").split("[,，]") as List
      if (!tenantIds.any()) {
        return ["data": []]
      }
      def EI, EA, quotaInfo, usedQuota;
      Result result = new Result()
      QueryQuotaInfoArg queryQuotaInfoArg = new QueryQuotaInfoArg()
      QueryUsedQuotaArg queryUsedQuotaArg = new QueryUsedQuotaArg()
      queryQuotaInfoArg.setAppId(licenseAppId)
      queryUsedQuotaArg.setAppId(licenseAppId)
      LicenseContext context = new LicenseContext()
      context.setUserId("1000")
      context.setAppId("CRM")
      tenantIds.each { tenantId ->
        try {
          limiter.acquire(1)
          tenantId = tenantId.trim()
          context.setTenantId(tenantId)
          result = licenseService.queryMaxHeadCount(context)
          if (!result.result.any()) {
            EA = tenantId
            EI = eieaConverter.enterpriseAccountToId(EA)
            context.setTenantId(EI as String)
            result = licenseService.queryMaxHeadCount(context)
          } else {
            EI = tenantId
            EA = eieaConverter.enterpriseIdToAccount(EI as int)
          }
          queryQuotaInfoArg.setFsEa(EA)
          queryUsedQuotaArg.setFsEa(EA)
          quotaInfo = quotaService.queryQuotaInfo(HeaderObj.newInstance(EI as String), queryQuotaInfoArg)
          usedQuota = quotaService.queryUsedQuota(HeaderObj.newInstance(EI as String), queryUsedQuotaArg)
          result.result.each { it ->
            json << [EI, EA, it["produceCode"], it["maxHeadCount"], quotaInfo.result.quota, usedQuota.result, it["productType"], it["productName"]]
          }
        } catch (NullPointerException | IllegalArgumentException e) {
          log.error("This EI/EA is error :{}", tenantId)
        }
      }
      return ["data": json]
    } catch (Exception e) {
      log.error("An error has occurred :{}", e)
      return ["data": []]
    }
  }


  @RequestMapping("/license/judge-module")
  def judgeModule(ModelMap model) {
    return "license/judge-module"
  }


  @RequestMapping(value = "/license/judge-module", method = RequestMethod.POST)
  @SystemControllerLog(description = "分版 -- 校验模块")
  @ResponseBody
  def judgeModule(@RequestParam String tenantId, @RequestParam String moduleCodes, RedirectAttributes r) {
    try {
      LicenseContext context = new LicenseContext()
      context.setUserId("1000")
      context.setAppId("CRM")
      context.setTenantId(tenantId)

      JudgeModuleArg arg = new JudgeModuleArg()
      arg.setContext(context)
      arg.setModuleCodes(Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(moduleCodes))
      def result = licenseService.judgeModule(arg)
      ["code": 200, "result": result.result, "errMessage": result.errMessage]
    } catch (Exception e) {
      return ["code": 500, "error": "查询异常：" + e.getMessage()]
    }
  }

  @RequestMapping("/license/package")
  def queryPackage(ModelMap model) {
    return "license/query-package"
  }

  @RequestMapping(value = "/license/query-package")
  @SystemControllerLog(description = "分版 -- 查询企业资源包")
  @ResponseBody
  def queryPackage2(@RequestParam String tenantId) {
    try {
      LicenseContext context = new LicenseContext()
      context.setUserId("1000")
      context.setAppId("CRM")
      context.setTenantId(tenantId)
      def result = licenseService.queryPackage(context)

      def json = []
      def fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
      result.result.each { it ->
        json << [getLicenseTypeName(it["licenseType"] as String), it["moduleCode"], it["paraKey"], it["paraValue"], fmt.format(it["createTime"]), fmt.format(it["startTime"]), fmt.format(it["expiredTime"])]
      }
      return ["data": json]
    } catch (Exception e) {
      return ["code": 500, "error": "查询异常：" + e.getMessage()]
    }
  }

  @RequestMapping("/license/checkAppValid")
  def checkAppValid(ModelMap model) {
    return "license/check-app-valid"
  }

  @RequestMapping(value = "/license/check-valid", method = RequestMethod.POST)
  @SystemControllerLog(description = "校验指定产品是都有效")
  @ResponseBody
  def checkAppValid(@RequestParam String tenantId, @RequestParam productVersions, @RequestParam String productType) {
    try {
      LicenseContext context = new LicenseContext()
      context.setUserId("1000")
      context.setAppId("CRM")
      context.setTenantId(tenantId)

      CheckValidityArg arg = new CheckValidityArg()
      arg.setContext(context)
      arg.setProductVersions(Sets.newHashSet(Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(productVersions)))
      arg.setProductType(productType)
      def result = licenseService.checkAppValid(arg)
      ["code": 200, "result": result.result, "errMessage": result.errMessage]
    } catch (Exception e) {
      return ["code": 500, "error": "查询异常：" + e.getMessage()]
    }
  }

  @RequestMapping("/license/queryCloudTenant")
  def queryCloudTenantId(ModelMap model) {
    return "license/query-cloud-tenant"
  }


  @RequestMapping("/license/query/cloud/tenant/")
  @SystemControllerLog(description = "根据有效license获取各专属云企业")
  @ResponseBody
  def queryCloudTenantId2() {
    try {
      ["code": "200", "result": licenseSyncJobService.queryCloudTenantScope(), "errorMessage": "成功"]
    } catch (Exception e) {
      return ["code": 500, "error": "查询异常：" + e.getMessage()]
    }
  }


  @RequestMapping("/license/license-cache")
  def licenseCache(ModelMap model) {
    licenseService.loadVipCache()


    model["appIdList"] = appIdList
    "license/license-cache"
  }

  @RequestMapping(value = "/license/add-object")
  @SystemControllerLog(description = "分版 -- 增加新对象")
  @ResponseBody
  def addNewObject(@RequestParam String productVersions, @RequestParam String appCodes, @RequestParam String apiNames) {
    if ((Strings.isNullOrEmpty(productVersions) && Strings.isNullOrEmpty(appCodes)) || Strings.isNullOrEmpty(apiNames)) {
      return ["code": 403, "error": "参数异常"]
    }

  }

  @RequestMapping(value = "/license/add-module")
  @SystemControllerLog(description = "分版 -- 增加新模块")
  @ResponseBody
  def addNewModule(@RequestParam String jsonObject) {
    if (Strings.isNullOrEmpty(jsonObject)) {
      return ["code": 403, "error": "参数异常"]
    }

  }

  /**
   * 检测过滤sql
   */
  private static def checkSql(String sql) {
    sql = sql.trim().toLowerCase()
    String[] str = ["delete ", "truncate ", "update ", "insert ", "drop ", "rename ", "alter "]
    for (int i = 0; i < str.size(); i++) {
      if (sql.indexOf(str[i]) != -1) {
        return false
      }
    }
    return true
  }

  private static def getLicenseTypeName(String licenseType) {
    switch (licenseType) {
      case "0":
        return "临时"
      case "1":
        return "正式"
      default:
        return "赠送"
    }
  }

}
