package com.fxiaoke.paas.console.license.mapper

import com.fxiaoke.paas.console.entity.license.ProductLicenseEntity
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 */
interface ProductGrayMapper {

  @Select("select distinct product_version from product_gray")
  List<String> queryGrayVersions();

  List<String> queryProductBought(@Param("productVersion")String productVersion, @Param("expired")Long currentTimeMillis);

  List<ProductLicenseEntity> queryProductBoughtV2(@Param("productVersion")String productVersion, @Param("expired")Long currentTimeMillis);
}
