package com.fxiaoke.paas.console.entity.log

import com.github.mybatis.entity.IdEntity
import groovy.transform.ToString
import groovy.transform.builder.Builder
import lombok.Getter
import lombok.Setter

import javax.persistence.Table

/**
 * <AUTHOR>
 * Created on 2018/7/24.
 */
@Setter
@Getter
@ToString
@Builder
@Table(name = "paas_special_table")
class SpecialTable extends IdEntity {
  String describeApiName
  String label
  String storeTableName
  String team
  Date createdTime
  Date modifyTime
  String operator
  String status
}
