<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
    <h1>全网数据信息统计</h1>
    <ol class="breadcrumb">
        <li><a href="../"><i class="fa fa-dashboard"></i>任务主页</a></li>
        <li><a href=""><i class="fa fa-dashboard"></i>全网统计</a></li>
    </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-header with-border">
                <#--<a class="btn btn-info" id="recordCount">记录数统计</a>-->
                <#--<a class="btn btn-primary" id="objectCount">对象数统计</a>-->
                    <ul class="nav nav-tabs" id="myTab">
                        <li role="presentation" class="active"><a href="#objectCount" data-toggle="tab">对象数统计</a></li>
                        <li role="presentation"><a href="#recordCount" data-toggle="tab">全网记录数统计</a></li>
                        <li role="presentation"><a href="#recordNum" data-toggle="tab">排除ID和IP获取数据</a></li>
                        <li role="presentation"><a href="" data-toggle="tab"></a></li>
                    </ul>
                </div>
                <div class="box-body">
                    <div class="tab-content">
                        <div class="tab-pane fade in active" id="objectCount">
                            <table id="datatableObject" class="table table-hover table-bordered" cellpadding="0" width="100%">
                                <thead>
                                <tr>
                                    <th>企业ID</th>
                                    <th>对象数</th>
                                    <th>企业账户</th>
                                    <th>企业名称</th>
                                    <th>状态</th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="tab-pane fade" id="recordCount">
                            <table id="datatable" class="table table-hover table-bordered" cellpadding="0" width="100%">
                                <thead>
                                <tr>
                                    <th>企业ID</th>
                                    <th>ApiName</th>
                                    <th>记录数</th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="tab-pane fade" id="recordNum">
                            <form class="form-horizontal" action="" onsubmit="return false;" method="post" id="myForm" role="form" data-toggle="validator">
                                <div class="box-body">
                                    <div class="form-group">
                                        <label for="tenantId" class="col-sm-1 control-label">排除ID</label>
                                        <div class="col-sm-2">
                                            <input type="text" style="border-radius:5px;" class="form-control" id="tenantId" name="tenantId" value="" placeholder="必填" required>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="tenantId" class="col-sm-1 control-label">排除IP</label>
                                        <div class="col-sm-2">
                                            <input type="text" style="border-radius:5px;" class="form-control" id="ip" name="ip" value="" placeholder="必填" required>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-6" style="margin-left: 20%">
                                            <button type="button" id="findSub" class="btn btn-primary">查询</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        <#--块儿总数展示-->
                            <div class="" id="dataShowDiv">
                            <#--<section class="content">-->
                                <section class="content" style="min-height:140px">
                                    <!-- Small boxes (Stat box) -->
                                    <div class="row">
                                        <div class="col-lg-3 col-xs-6">
                                            <!-- small box -->
                                            <div class="small-box bg-aqua">
                                                <div class="inner">
                                                    <h3 id="countToNum"></h3>
                                                    <p>记录数</p>
                                                </div>
                                                <div class="icon">
                                                    <i class="ion ion-bag"></i>
                                                </div>
                                                <span class="small-box-footer">No more info <i class="fa fa-circle-o-notch"></i></span>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-xs-6">
                                            <!-- small box -->
                                            <div class="small-box bg-red">
                                                <div class="inner">
                                                    <h3 id="totalNum"></h3>
                                                    <p>对象数</p>
                                                </div>
                                                <div class="icon">
                                                    <i class="ion ion-pie-graph"></i>
                                                </div>
                                                <span class="small-box-footer">No more info <i class="fa fa-circle-o-notch"></i></span>
                                            </div>
                                        </div>
                                        <!-- ./col &ndash;&gt;-->
                                        <div class="col-lg-3 col-xs-6">
                                            <!-- small box -->
                                            <div class="small-box bg-green">
                                                <div class="inner">
                                                <#--<h3>53<sup style="font-size: 20px">%</sup></h3>-->
                                                    <h3 id="allNum"></h3>
                                                    <p>总记录数</p>
                                                </div>
                                                <div class="icon">
                                                    <i class="ion ion-stats-bars"></i>
                                                </div>
                                                <span class="small-box-footer">No more info <i class="fa fa-circle-o-notch"></i></span>
                                            </div>
                                        </div>
                                        <!-- ./col &ndash;&gt;-->
                                    </div>
                                <#--</section>-->
                            </div>
                            <table id="datatableIdAndIp" class="table table-hover table-bordered" cellpadding="0" width="100%">
                                <thead>
                                <tr>
                                    <th>tenant_id</th>
                                    <th>Sum of count</th>
                                    <th>Count</th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="tab-pane fade" id="">
                        </div>
                    </div>
                </div>
                <div class="box-footer clearfix">
                    <div class="clearfix"></div>
                </div>
            </div>
        </div>
    </div>
</section>
</#assign>
<#assign scriptContent>
<script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
<script src="//static.foneshare.cn/oss/bootstrap-3.3.6/js/validator.min.js"></script>
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script>
    $(document).ready(function () {

        var objectTable = $('#datatableObject').DataTable({
            "processing": true,
            "ajax": "${ctx}/metadata/rpc/object-count",
            "columnDefs": [],
            "language": {
                "url": "${ctx}/static/js/datatables-zh_CN.json"
            },
            "displayLength": 25,
            "mark": true,
            "order": [[1, 'desc']]
        });

        var recordTable = $('#datatable').DataTable({
            "processing": true,
            "ajax": "${ctx}/metadata/rpc/whole-data",
            "columnDefs": [],
            "language": {
                "url": "${ctx}/static/js/datatables-zh_CN.json"
            },
            "displayLength": 25,
            "mark": true,
            "order": [[2, 'desc']]
        });

        var datatableIdAndIp = $('#datatableIdAndIp').DataTable({
            "processing": true,
            "ajax": "",
            "columnDefs": [],
            "language": {
                "url": "${ctx}/static/js/datatables-zh_CN.json"
            },
            "displayLength": 25,
            "mark": true,
            "order": [[0, 'desc']]
        });

        //查询按钮
        $('#findSub').on('click', function () {
            var tenantId = $('#tenantId').val();
            var ip = $('#ip').val();

            $.post("${ctx}/metadata/rpc/record-num", {
                tenantId: tenantId,
                ip: ip
            }, function (result) {
                $('#countToNum').html(result.countNum);
                $('#totalNum').html(result.total);
                $('#allNum').html(result.allNum)
            });
            //top 10
            datatableIdAndIp.ajax.url("${ctx}/metadata/rpc/count-top?tenantId=" + tenantId + "&ip=" + ip).load();
        });

    });

</script>
</#assign>
<#include "../layout/layout-main.ftl" />
