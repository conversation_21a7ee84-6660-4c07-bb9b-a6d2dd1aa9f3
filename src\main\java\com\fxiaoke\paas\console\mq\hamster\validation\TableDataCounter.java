package com.fxiaoke.paas.console.mq.hamster.validation;

import com.fxiaoke.jdbc.JdbcConnection;
import com.fxiaoke.paas.console.proxy.CmsProxy;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * <AUTHOR>
 */
@Slf4j
@Data
public class TableDataCounter implements Callable<List<TableDataReport>> {

  private SchemaDbResource schemaDbResource;
  private JdbcConnection connection;
  private JdbcConnection paasOldConnection;
  private String biz;

  private boolean isStandalone;
  private AtomicInteger mtDataCount;

  private AtomicInteger mtDataLangCount;

  private AtomicInteger mtDataChangeCount;

  private Map<String, Map<String, Long>> countTimeMap = Maps.newHashMap();

  TableDataCounter(SchemaDbResource schemaDbResource, JdbcConnection connection, JdbcConnection paasOldConnection, boolean isStandalone, String biz) {
    this.schemaDbResource = schemaDbResource;
    this.connection = connection;
    this.isStandalone = isStandalone;
    this.mtDataCount = new AtomicInteger(0);
    this.mtDataChangeCount = new AtomicInteger(0);
    this.mtDataLangCount = new AtomicInteger(0);
    this.biz = biz;
    this.paasOldConnection = paasOldConnection;
  }


  @Override
  public List<TableDataReport> call() {

    String tenantId = schemaDbResource.getTenantId();
    if (isStandalone) {
      Thread.currentThread().setName(biz + "_schema_" + tenantId);
    } else {
      Thread.currentThread().setName(biz + "_public_" + tenantId);
    }

    boolean isPublicPaas = (!isStandalone && "PAAS".equals(biz));

    Set<String> supportOpLogTableNames = Sets.newHashSet();
    if (isPublicPaas) {
      supportOpLogTableNames = querySupportOpLogTable(paasOldConnection);
    }

    List<TableDataReport> result = Lists.newArrayList();

    Set<String> allTables = getAllTables();

    String schema = getSchema();
    for (String table : allTables) {
      boolean isSupportOpLog = true;

      if (isPublicPaas && !supportOpLogTableNames.contains(table)) {
        isSupportOpLog = false;
      }

      //public mt_data_lang __c_lang
      if (("mt_data".equals(table) || "object_data".equals(table) || "mt_data_change".equals(table) || "mt_data_lang".equals(table)) &&
        "public".equals(schema)) {
        Set<String> apiNames = queryCustomOpLogApiNames(table);

        int total = 0;
        for (String apiName : apiNames) {
          String sql = "mt_data_lang".equals(table) ?
            String.format("select count(1) from %s.%s where tenant_id=? and describe_api_name=?", schema, table) :
            String.format("select count(1) from %s.%s where tenant_id=? and object_describe_api_name=?", schema, table);
          int count = 0;
          try {
            count = connection.prepareCount(sql, p -> {
              p.setString(1, schemaDbResource.getTenantId());
              p.setString(2, apiName);
            });
          } catch (SQLException e) {
            log.warn("count from {}:{},apiName{}", table, tenantId, apiName, e);
            count = -1;
          }
          total += count;

          String currentTableName = ("mt_data_lang".equals(table) ? apiName.toLowerCase() + "_lang" : apiName.toLowerCase());
          result.add(TableDataReport
                       .builder()
                       .biz(biz)
                       .tableName(currentTableName)
                       .oldCount(count)
                       .isSupportOpLog(isSupportOpLog)
                       .build());

          //记录当前表的统计时间
          countTimeMap.computeIfAbsent(table, x -> Maps.newHashMap()).put(currentTableName, System.currentTimeMillis());

          log.info("counter:{},{},{},{},{}", biz, schemaDbResource.getTenantId(), table, apiName, count);
        }
        result.add(TableDataReport.builder().biz(biz).tableName(table).oldCount(total).isSupportOpLog(isSupportOpLog).build());
        continue;
      }

      //tableName,count; or apiName.tolower(),count;
      Map<String, Integer> tableDataCountMap = getTableDataCountMap(schema, table);
      for (Map.Entry<String, Integer> entry : tableDataCountMap.entrySet()) {
        String apiNameOrTable = entry.getKey();
        Integer dataCount = entry.getValue();
        TableDataReport build = TableDataReport.builder().biz(biz).tableName(apiNameOrTable).isSupportOpLog(isSupportOpLog).build();
        if (isStandalone) {
          build.setNewCount(dataCount);
        } else {
          build.setOldCount(dataCount);
        }
        log.info("counter:{},{},{}", build.getTableName(), build.getOldCount(), build.getNewCount());
        result.add(build);
      }

    }
    if (isStandalone) {
      if ("BI".equals(biz)) {
        result.add(TableDataReport.builder().biz(biz).tableName("object_data").newCount(mtDataCount.get()).build());
      } else {
        result.add(TableDataReport.builder().biz(biz).tableName("mt_data").newCount(mtDataCount.get()).build());
        log.info("new mt_data_change = {}", mtDataChangeCount.get());
        result.add(TableDataReport.builder().biz(biz).tableName("mt_data_change").newCount(mtDataChangeCount.get()).build());
        log.info("new mt_data_lang = {}", mtDataLangCount.get());
        result.add(TableDataReport.builder().biz(biz).tableName("mt_data_lang").newCount(mtDataLangCount.get()).build());
      }
    }
    return result;
  }

  private Set<String> queryCustomOpLogApiNames(String customTableName) {
    Set<String> apiNames;
    if ("mt_data_change".equals(customTableName)) {
      apiNames = getChangeObjApiNames(schemaDbResource.getTenantId());
    } else if ("mt_data_lang".equals(customTableName)) {
      apiNames = getMtDataLangApiNames(schemaDbResource.getTenantId());
    } else {
      apiNames = getApiNames(schemaDbResource.getTenantId());
    }
    return apiNames;
  }

  private Set<String> querySupportOpLogTable(JdbcConnection connectionOldPaas) {
    StringBuilder builder = new StringBuilder();
    builder.append(" SELECT distinct t.relname FROM pg_class t ");
    builder.append(" inner join pg_namespace n on t.relnamespace = n.oid ");
    builder.append(" inner join pg_trigger tg ON t.oid = tg.tgrelid ");
    builder.append(" inner join pg_proc p ON tg.tgfoid = p.oid ");
    builder.append(" where t.relkind = 'r' and n.nspname = 'public' and  p.proname in ('f_change_detail','f_change_summary') ");

    Set<String> tableNames = Sets.newHashSet();

    try {
      connectionOldPaas.prepareQuery(builder.toString(), p -> {
      }, rs -> {
        while (rs.next()) {
          String currentName = rs.getString(1);
          if (StringUtils.isBlank(currentName)) {
            continue;
          }
          tableNames.add(currentName);
        }
      });
    } catch (SQLException e) {
      log.warn("querySupportOpLogTable error", e);
    }
    return tableNames;
  }

  //获取自定义表的查询结果
  private Map<String, Integer> getTableDataCountMap(String schema, String table) {
    Map<String, Integer> result = Maps.newHashMap();
    result.putIfAbsent(table, 0);
    try {
      String tenantColumnName = null;
      ResultSet columns = connection.connection().getMetaData().getColumns(null, schema, table, "%");
      boolean isEi = false;
      while (columns.next()) {
        String columnName = columns.getString("COLUMN_NAME");
        if (Objects.equals(columnName, "tenant_id")) {
          tenantColumnName = "tenant_Id";
          break;
        }
        if (Objects.equals(columnName, "ei")) {
          tenantColumnName = "ei";
          isEi = true;
          break;
        }
      }

      if (tenantColumnName == null) {
        return result;
      }

      if (table.endsWith("__c_lang")) {
        String mtDataLangSql = String.format("select count(1) from %s.%s where %s=? and describe_api_name ilike ?", schema, table, tenantColumnName);
        int langCount = connection.prepareCount(mtDataLangSql, p -> {
          p.setObject(1, schemaDbResource.getTenantId());
          p.setString(2, table.substring(0, table.length() - 5));
        });
        result.put(table.toLowerCase(), langCount);
        countTimeMap.computeIfAbsent(table, x -> Maps.newHashMap()).putIfAbsent(table.toLowerCase(), System.currentTimeMillis());
        log.info("__c_lang " + schema + " " + table + " count = {}", langCount);
        mtDataLangCount.set(mtDataLangCount.get() + langCount);
        return result;
      }

      if (table.endsWith("__c")) {
        String sql = String.format("select count(1) from %s.%s where %s=? and object_describe_api_name ilike ?", schema, table, tenantColumnName);
        int count = connection.prepareCount(sql, p -> {
          p.setObject(1, schemaDbResource.getTenantId());
          p.setString(2, table);
        });
        mtDataCount.set(mtDataCount.get() + count);
      }
      if (table.endsWith("changeobj__c")) {
        log.info("table changeObj__c = {}", table);
        String sql = String.format("select count(1) from %s.%s where %s=? and object_describe_api_name ilike ?", schema, table, tenantColumnName);
        int count = connection.prepareCount(sql, p -> {
          p.setObject(1, schemaDbResource.getTenantId());
          p.setString(2, table);
        });
        log.info("table changeObj__c count = {}", count);
        mtDataChangeCount.set(mtDataChangeCount.get() + count);
      }
      if ("mt_data".equals(table)) {
        Set<String> apiNames = getApiNamesFromMtData();
        for (String apiName : apiNames) {
          String sql = String.format("select count(1) from public.mt_data where tenant_id=? and object_describe_api_name=?");
          int count = connection.prepareCount(sql, p -> {
            p.setObject(1, schemaDbResource.getTenantId());
            p.setString(2, apiName);
          });
          result.put(apiName.toLowerCase(), count);
          countTimeMap.computeIfAbsent(table, x -> Maps.newHashMap()).putIfAbsent(apiName.toLowerCase(), System.currentTimeMillis());
        }
      } else if (table.endsWith("mt_data_lang")) {
        Set<String> apiNames = getMtDataLangApiNames(schemaDbResource.getTenantId());

        for (String apiName : apiNames) {
          String sql = "select count(1) from public.mt_data_lang where tenant_id=? and describe_api_name=?";
          int count = connection.prepareCount(sql, p -> {
            p.setObject(1, schemaDbResource.getTenantId());
            p.setString(2, apiName);
          });
          result.put(apiName.toLowerCase() + "_lang", count);
          countTimeMap.computeIfAbsent(table, x -> Maps.newHashMap()).putIfAbsent(apiName.toLowerCase() + "_lang", System.currentTimeMillis());
        }
      } else {
        String sql = String.format("select count(1) from %s.%s where %s=?", schema, table, tenantColumnName);
        boolean finalIsEi = isEi;
        int i = connection.prepareCount(sql, p -> p.setObject(1, finalIsEi ? Integer.valueOf(schemaDbResource.getTenantId()) : schemaDbResource.getTenantId()));
        result.put(table, i);
        countTimeMap.computeIfAbsent(table, x -> Maps.newHashMap()).putIfAbsent(table, System.currentTimeMillis());
      }
    } catch (SQLException sqlException) {
      log.warn("getDataCount:{}{};", schema, table, sqlException);
      result.putIfAbsent(table, -1);
      return result;
    }
    return result;
  }

  private Set<String> getApiNamesFromMtData() throws SQLException {
    String queryDescribeSql = "select distinct object_describe_api_name from public.mt_data where tenant_id=? ";
    Set<String> apiNames = Sets.newHashSet();
    connection.prepareQuery(queryDescribeSql, p -> {
      p.setObject(1, schemaDbResource.getTenantId());
    }, rs -> {
      while (rs.next()) {
        String apiName = rs.getString(1);
        if (apiName.endsWith("__c")) {
          apiNames.add(apiName);
        }
      }
    });
    return apiNames;
  }

  private Set<String> getApiNames(String tenantId) {
    String sql = String.format("select distinct describe_api_name from public.mt_describe where tenant_id=? and define_type='custom' and is_deleted is false");

    Set<String> apiNames = Sets.newHashSet();
    try {
      paasOldConnection.prepareQuery(sql, p -> {
        p.setString(1, tenantId);
      }, rs -> {
        while (rs.next()) {
          apiNames.add(rs.getString(1));
        }
      });
    } catch (SQLException e) {
      log.warn("getApiNames:", e);
    }
    return apiNames;
  }

  private Set<String> getMtDataLangApiNames(String tenantId) {
    String sql = "select distinct describe_api_name from public.mt_data_lang where tenant_id=? and is_deleted is false";

    Set<String> apiNames = Sets.newHashSet();
    try {
      paasOldConnection.prepareQuery(sql, p -> {
        p.setString(1, tenantId);
      }, rs -> {
        while (rs.next()) {
          String apiName = rs.getString(1);
          if (apiName.endsWith("__c")) {
            apiNames.add(apiName);
          }
        }
      });
    } catch (SQLException e) {
      log.warn("getMtDataLangApiNames getApiNames:", e);
    }
    return apiNames;
  }

  private Set<String> getChangeObjApiNames(String tenantId) {
    String s = "select distinct describe_api_name from public.mt_describe where tenant_id=? and define_type='custom' and is_deleted is false  and describe_api_name like '%s'";
    String sql = String.format(s, "%changeObj__c");

    Set<String> apiNames = Sets.newHashSet();
    try {
      paasOldConnection.prepareQuery(sql, p -> {
        p.setString(1, tenantId);
      }, rs -> {
        while (rs.next()) {
          apiNames.add(rs.getString(1));
        }
      });
    } catch (SQLException e) {
      log.warn("getApiNames:", e);
    }
    return apiNames;
  }

  //获取要检验的表集合
  private Set<String> getAllTables() {

    Set<String> allTables = Sets.newHashSet();
    String sql = "select tablename from pg_tables where schemaname=?";
    String finalSchema = getSchema();
    Set<String> ignoredTables = getIgnoredTables();
    try {
      connection.prepareQuery(sql, p -> p.setString(1, finalSchema), rs -> {
        while (rs.next()) {
          String tableName = rs.getString("tablename");
          if (ignoredTables.contains(tableName)) {
            continue;
          }
          allTables.add(tableName);
        }
      });
    } catch (SQLException e) {
      log.error("getAllTables:", e);
    }
    if (biz.equals("PAAS")) {
      allTables.removeAll(CmsProxy.getNoValidatePaasTable());
    }
    if (biz.equals("BI")) {
      allTables.removeAll(CmsProxy.getNoValidateBiTable());
    }
    return allTables;
  }

  private String getSchema() {
    String schema = "public";
    if (isStandalone) {
      schema = "sch_" + schemaDbResource.getTenantId();
    }
    return schema;
  }

  private Set<String> getIgnoredTables() {

    IChangeableConfig config = ConfigFactory.getConfig("fs-paas-service-describe");

    String s = config.get("describe.api.oldTable");
    List<String> elements = Splitter.on("|").omitEmptyStrings().trimResults().splitToList(s);
    return Sets.newHashSet(elements);
  }
}
