//package com.fxiaoke.paas.console.util.workflow
//
//import com.facishare.paas.org.pojo.MainDeptInfo
//import com.facishare.paas.workflow.kernel.entity.ApprovalOpinionEntity
//import com.facishare.paas.workflow.kernel.entity.ExpressionEntity
//import com.facishare.paas.workflow.kernel.entity.TaskEntity
//import com.facishare.paas.workflow.service.api.ApprovalFlowCommonService;
//import com.facishare.paas.workflow.kernel.entity.WorkflowEntity
//import com.facishare.paas.workflow.kernel.entity.WorkflowInstanceEntity
//import com.fxiaoke.paas.console.service.workflow.WorkflowAdminService
//import org.apache.commons.collections.CollectionUtils
//import org.apache.commons.lang3.StringUtils
//
///**
// * Created by yangxw on 2018/3/5.
// */
//class AssembleUtil {
//
//    private static final String NEW_LINE = "<br />"
//
//   def static assembleExpressionShow(ExpressionEntity expressionEntity) {
//
//        String expressionShow = ""
//        if (null != expressionEntity) {
//            expressionShow += expressionEntity.getFieldName() + " " + expressionEntity.getOperator() + " " + expressionEntity.getValue()
//        }
//        return expressionShow
//    }
//
//    def static  assembleIdAndNumberMap(List<String> idList, Map<String, WorkflowAdminService.Count> mapCount) {
//
//        Map<String, String> map = new HashMap<>();
//        if (CollectionUtils.isEmpty(idList) || null == mapCount) {
//            return map;
//        }
//        for (String id : idList){
//            map.put(id,"0/0")
//        }
//        WorkflowAdminService.Count count;
//        for (String id : idList) {
//            count = mapCount.get(id);
//            map.put(id, null == count ? "" : count.getFirst() + "/" + count.getSecond());
//        }
//        return map;
//
//    }
//
//    /**
//     * 获取WorkflowInstanceEntity list的所有applicantId
//     * @param instanceEntityList
//     * @return
//     */
//    def static  assembleAllUserIdsOfWorkflowInstanceEntityList(List<WorkflowInstanceEntity> instanceEntityList) {
//
//        List<String> userIdList = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(instanceEntityList)) {
//            instanceEntityList.each{instanceEntity ->
//                String applicantId = instanceEntity.getApplicantId();
//                if (StringUtils.isNotBlank(applicantId) && !userIdList.contains(applicantId)) {
//                    userIdList.add(applicantId);
//                }
//            };
//        }
//        return userIdList;
//    }
//
//    /**
//     * 获取WorkflowEntity list的所有creator(Id)
//     * @param workflowEntityList
//     * @return
//     */
//    def static  assembleAllUserIdsOfWorkflowEntityList(List<WorkflowEntity> workflowEntityList) {
//
//        List<String> userIdList = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(workflowEntityList)) {
//            workflowEntityList.each{workflowEntity ->
//                String creatorId = workflowEntity.getCreator();
//                if (StringUtils.isNotBlank(creatorId) && !userIdList.contains(creatorId)) {
//                    userIdList.add(creatorId);
//                }
//                String modifier = workflowEntity.getModifier();
//                if (StringUtils.isNotBlank(modifier) && !userIdList.contains(modifier)) {
//                    userIdList.add(modifier);
//                }
//            };
//        }
//        return userIdList;
//
//    }
//
//    /**
//     * 获取TaskEntity list的所有申请人id和审批人id
//     * @param taskEntityList
//     * @return
//     */
//    def static assembleAllUserIdsOfTaskEntityList(List<TaskEntity> taskEntityList) {
//
//        List<String> userIdList = new ArrayList<>();
//        if (CollectionUtils.isEmpty(taskEntityList)) {
//            return userIdList;
//        }
//        String applicantId;
//        for (TaskEntity task : taskEntityList) {
//            applicantId = task.getApplicantId();
//            if (StringUtils.isNotBlank(applicantId) && !userIdList.contains(applicantId)) {
//                userIdList.add(applicantId);
//            }
//            if (null == task.getAssignee() || CollectionUtils.isEmpty(task.getAssignee().get(ApprovalFlowCommonService.ASSIGNEE_TYPE_PERSON_LIST))) {
//                continue;
//            }
//            List<String> assigneeIdList = task.getAssignee().get(ApprovalFlowCommonService.ASSIGNEE_TYPE_PERSON_LIST);
//            assigneeIdList.each {assigneeId ->
//                if (!userIdList.contains(assigneeId)) {
//                    userIdList.add(assigneeId);
//                }
//            };
//        }
//        return userIdList;
//
//    }
//
//    /**
//     * 把task的审批人全部转换成  审批人名字／审批人所在部门的形式
//     *
//     * @param task
//     * @param assigneeMap
//     * @return
//     */
//    def static  assembleAssigneeInfo(TaskEntity task, Map<String, MainDeptInfo> assigneeMap) {
//
//        StringBuilder assigneeAndDept = new StringBuilder();
//        /**
//         * 当TASK类型是一票通过且状态不处于进行中的时候，审批人／部门只显示审批过的人的信息
//         */
////        if (WorkflowConstants.UserTaskType.ONE_PASS.equals(task.getTaskType())
////                && !WorkflowConstants.UserTaskStatus.IN_PROGRESS.equals(task.getState())) {
////            List<ApprovalOpinionEntity> opinionList = task.getOpinions();
////            if (CollectionUtils.isNotEmpty(opinionList)) {
////                String assigneeId = opinionList.get(0).getUserId();
////                assigneeAndDept.append(assembleAccountAndDept(assigneeMap.get(assigneeId)));
////            }
////        } else {
//        if (null != assigneeMap) {
//            List<String> userIds = null;
//            if (null != task.getAssignee()) {
//                userIds = task.getAssignee().get(ApprovalFlowCommonService.ASSIGNEE_TYPE_PERSON_LIST);
//            }
//            if (null == userIds) {
//                userIds = new ArrayList<>();
//            }
//            for (String id : userIds) {
//                assembleAssigneeInfo(assigneeAndDept, id, assigneeMap.get(id));
//            }
//        }
////        }
//        return assigneeAndDept.toString();
//
//    }
//
//
//    def static  assembleAssigneeOpinion(List<ApprovalOpinionEntity> opinionList, Map<String, MainDeptInfo> assigneeMap) {
//
//        StringBuilder assigneeAndOpinion = new StringBuilder();
//        String account = "";
//        if (null != assigneeMap && CollectionUtils.isNotEmpty(opinionList)) {
//            for (ApprovalOpinionEntity opinion : opinionList) {
//                MainDeptInfo mainDeptInfo = assigneeMap.get(opinion.getUserId());
//                if (null != mainDeptInfo) {
//                    account = mainDeptInfo.getUserName();
//                }
//                String accountAndDept = ToShowStringUtil.jointObjects("/", account, opinion.getOpinion()
//                        , ToShowStringUtil.getTimeString(0 >= opinion.getReplyTime() ? null : opinion.getReplyTime()));
//                assigneeAndOpinion.append(accountAndDept);
//                if (StringUtils.isNotBlank(accountAndDept)) {
//                    assigneeAndOpinion.append(NEW_LINE);
//                }
//            }
//        }
//        return assigneeAndOpinion.toString();
//
//    }
//
//    def static assembleAssigneeInfo(StringBuilder builder, String userId, MainDeptInfo deptInfo) {
//
////        if (StringUtils.isNoneBlank(builder, userId)) {   原错误代码，对builder进行非空判定导致if中的代码永远无法执行
//        if (StringUtils.isNoneBlank(userId)) {
//            builder.append(assembleAccountAndDept(deptInfo));
//            if (StringUtils.isNotBlank(builder)) {
//                builder.append(NEW_LINE);
//            }
//        }
//
//    }
//
//    /**
//     * 通过MainDeptInfo对象获取前端展示用户姓名和用户所在部门的字符串
//     *
//     * @param mainDeptInfo
//     * @return
//     */
//    def static  assembleAccountAndDept(MainDeptInfo mainDeptInfo) {
//
//        if (null != mainDeptInfo) {
//            return ToShowStringUtil.jointObjects("/", mainDeptInfo.getUserName(), mainDeptInfo.getDeptName());
//        }
//        return "";
//
//    }
//}
