package com.fxiaoke.paas.console.web.functional

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.bean.datarights.OuterUserRoleBatchPojo
import com.fxiaoke.paas.console.service.OKHttpService
import com.github.autoconf.ConfigFactory
import com.google.common.base.Splitter
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import groovy.util.logging.Slf4j
import org.apache.commons.collections.CollectionUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.PostConstruct

/**
 * <AUTHOR>
 * @date 2019/8/23 10:15 AM
 */
@Controller
@Slf4j
@RequestMapping("/func")
class OutFunctionalController {

  String paasAuthUrl

  @Autowired
  private OKHttpService okHttpService

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-console-auth", { iConfig -> this.paasAuthUrl = iConfig.get("PAAS_AUTH_URL") })
  }


  @RequestMapping(path = "/role-sql-query", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  @SystemControllerLog(description = "功能权限--角色库SQL查询")
  def roleSqlQuery(String sql, String tenantId) {
    String sqlQueryUrl = this.paasAuthUrl + "/outer/sqlQuery"
    JSONObject jsonObject = new JSONObject()
    jsonObject.put("tenantId", tenantId.trim())
    jsonObject.put("sql", sql.trim())
    String res = okHttpService.postJSON(sqlQueryUrl, jsonObject)
    return ["code": 200, "info": res]
  }


  @RequestMapping(path = "/out-role", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  @SystemControllerLog(description = "外部功能权限---查询人员所在外部角色")
  def outRole(String tenantId, String appId, String userIds, String outerTenantId) {
    String queryOutRoleUrl = this.paasAuthUrl + "/outer/queryRole"
    JSONObject jsonObject = new JSONObject()
    jsonObject.put("authContext", new HashMap() {
      {
        put("tenantId", tenantId)
        put("appId", appId)
        put("outerTenantId", outerTenantId)
      }
    })
    jsonObject.put("userIds", Splitter.on(",").omitEmptyStrings().trimResults().splitToList(userIds))
    String json = okHttpService.postJSON(queryOutRoleUrl, jsonObject)
    JSONObject result = JSONObject.parseObject(json).getJSONObject("result")
    Set<List<String>> returenValue = Sets.newHashSet()
    Set<String> userIdSets = result.keySet()
    if (CollectionUtils.isNotEmpty(userIdSets)) {
      userIdSets.forEach({ k ->
        JSONArray jsonArray = result.getJSONArray(k)
        List<String> roleCodes = jsonArray.toJavaList(String.class)
        if (CollectionUtils.isNotEmpty(roleCodes)) {
          roleCodes.forEach({ roleCode -> returenValue.add(Lists.newArrayList(roleCode, k)) })

        }
      })
    }
    return ["code": 200, "data": returenValue]

  }


  @RequestMapping(path = "/query-old-api-role", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  @SystemControllerLog(description = "外部功能权限--调用老接口查询角色")
  def queryOldApiRole(String tenantId, String appId, String userId) {
    JSONObject jsonObject = new JSONObject()
    jsonObject.put("tenantId", tenantId)
    jsonObject.put("appId", appId)
    jsonObject.put("userId", userId)
    String json = okHttpService.postJSON(this.paasAuthUrl + "/outer/queryRoleCodeListByUserId", jsonObject)
    JSONArray jsonResult = JSONObject.parseObject(json).getJSONArray("result")
    List<String> list = jsonResult.toJavaList(String.class)
    List<List<String>> resultList = Lists.newArrayList()
    if (list != null) {
      list.forEach({ str -> resultList.add(Lists.newArrayList(str, userId)) })
    }
    return ["code": 200, "data": resultList]
  }


  @RequestMapping(path = "/out-batch-user", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  @SystemControllerLog(description = "外部功能权限---查询角色下人员列表")
  def outBatchUser(String tenantId, String appId, String roles, String outerTenantId) {
    String queryUserRoleUrl = this.paasAuthUrl + "/outer/queryUserRoleBatch"
    JSONObject jsonObject = new JSONObject()
    jsonObject.put("authContext", new HashMap() {
      {
        put("tenantId", tenantId)
      }
    })
    jsonObject.put("params", Sets.newHashSet(new HashMap() {
      {
        put("outerTenantId", outerTenantId)
        put("appId", appId)
        put("roleCodes", Splitter.on(",").omitEmptyStrings().trimResults().splitToList(roles))
      }
    }))
    String json = okHttpService.postJSON(queryUserRoleUrl, jsonObject)
    Set<OuterUserRoleBatchPojo> outerUserRoleBatchPojos = JSONObject.parseObject(json).getJSONArray("result").toJavaList(OuterUserRoleBatchPojo.class)
    Set<List<String>> result = Sets.newHashSet()
    if (CollectionUtils.isNotEmpty(outerUserRoleBatchPojos)) {
      outerUserRoleBatchPojos.forEach({ outerUserRoleBatchPojo ->
        if (outerUserRoleBatchPojo != null) {
          Map<String, Set<String>> map = outerUserRoleBatchPojo.getUserRoles()
          map.forEach({ roleCode, userIds ->
            userIds.forEach({ userId ->
              result.add(Lists.newArrayList(roleCode, userId))
            })
          })
        }
      })
    }
    return ["code": 200, "data": result]
  }

  @RequestMapping("/out-role-user")
  def outRoleUser() {
    "functional/out-role-user"
  }

  @RequestMapping("/out-role")
  def outRole() {
    "functional/out-role"
  }

  @RequestMapping("/sql-query")
  def sqlQuery() {
    "functional/sql-query"
  }

  @RequestMapping("/old-api-role")
  def oldApiRole() {
    "functional/old-api-role"
  }

}
