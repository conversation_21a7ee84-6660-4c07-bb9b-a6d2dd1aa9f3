<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fxiaoke.common</groupId>
    <artifactId>fxiaoke-parent-pom</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <artifactId>paas-console</artifactId>
  <packaging>war</packaging>
  <version>1.0-SNAPSHOT</version>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <maven.skip.deploy>true</maven.skip.deploy>
    <fs-metadata-service.version>1.1.0-SNAPSHOT</fs-metadata-service.version>
    <resteasy-version>3.1.4.Final</resteasy-version>
    <servlet-api-version>3.1.0</servlet-api-version>
    <fs.paas.org.version>2.1.0-SNAPSHOT</fs.paas.org.version>
    <mysql.driver.version>5.1.47</mysql.driver.version>
    <fs-pod-client.version>9.0.1-SNAPSHOT</fs-pod-client.version>
    <fs.paas.license.version>1.5.0-SNAPSHOT</fs.paas.license.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-enterprise-id-account-converter</artifactId>
      <version>1.1-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-appcenter-rest-api</artifactId>
      <version>1.1.7-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>hamster-client</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.yaml</groupId>
          <artifactId>snakeyaml</artifactId>
        </exclusion>
      </exclusions>
      <version>2.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>3.3.4</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-stone-sdk</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-metadata-provider</artifactId>
      <version>9.6.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>metrics-oss</artifactId>
          <groupId>com.fxiaoke.common</groupId>
        </exclusion>
        <exclusion>
          <artifactId>okhttp</artifactId>
          <groupId>com.squareup.okhttp</groupId>
        </exclusion>
        <exclusion>
          <artifactId>slf4j-log4j12</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
        <exclusion>
          <groupId>com.fxiaoke.common</groupId>
          <artifactId>metrics-oss</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>fs-fsi-proxy</artifactId>
          <groupId>com.facishare</groupId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.curator</groupId>
          <artifactId>curator-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.curator</groupId>
          <artifactId>curator-framework</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.colin-lee</groupId>
          <artifactId>mybatis-spring-support</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.facishare</groupId>
          <artifactId>fs-paas-app-expression</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.facishare</groupId>
          <artifactId>fs-paas-expression</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>resteasy-client</artifactId>
          <groupId>org.jboss.resteasy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-paas-license-api</artifactId>
          <groupId>com.facishare.paas</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare.paas</groupId>
      <artifactId>fs-paas-org-provider</artifactId>
      <version>${fs.paas.org.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.glassfish.hk2.external</groupId>
          <artifactId>asm-all-repackaged</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.glassfish.hk2</groupId>
          <artifactId>hk2-locator</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.glassfish.hk2.external</groupId>
          <artifactId>cglib</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.glassfish.jersey.core</groupId>
          <artifactId>jersey-server</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.facishare</groupId>
          <artifactId>fs-cep-plugin-document</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.facishare</groupId>
          <artifactId>fs-metadata-provider</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>fs-metadata-api</artifactId>
          <groupId>com.facishare</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jackson-dataformat-yaml</artifactId>
          <groupId>com.fasterxml.jackson.dataformat</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jackson-dataformat-xml</artifactId>
          <groupId>com.fasterxml.jackson.dataformat</groupId>
        </exclusion>
        <exclusion>
          <artifactId>validation-api</artifactId>
          <groupId>javax.validation</groupId>
        </exclusion>
        <exclusion>
          <artifactId>resteasy-jackson2-provider</artifactId>
          <groupId>org.jboss.resteasy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-lang</artifactId>
          <groupId>commons-lang</groupId>
        </exclusion>
        <exclusion>
          <artifactId>swagger-annotations</artifactId>
          <groupId>io.swagger</groupId>
        </exclusion>
        <exclusion>
          <artifactId>httpclient</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jboss-logging</artifactId>
          <groupId>org.jboss.logging</groupId>
        </exclusion>
        <exclusion>
          <groupId>org.elasticsearch.client</groupId>
          <artifactId>transport</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.nlpcn</groupId>
          <artifactId>elasticsearch-sql</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>paas-common</artifactId>
      <version>1.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>jaxb-api</artifactId>
          <groupId>javax.xml.bind</groupId>
        </exclusion>
        <exclusion>
          <artifactId>FastInfoset</artifactId>
          <groupId>com.sun.xml.fastinfoset</groupId>
        </exclusion>
        <exclusion>
          <artifactId>asm</artifactId>
          <groupId>org.ow2.asm</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--option服务-->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-metadata-option-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>java-utils</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>http-spring-support</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.service</groupId>
      <artifactId>datarights-common</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.service</groupId>
      <artifactId>datarights-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.service</groupId>
      <artifactId>group-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>

    <!--mongo-->
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>mongo-spring-support</artifactId>
    </dependency>

    <!--workflow-->
    <dependency>
      <groupId>com.facishare.paas</groupId>
      <artifactId>fs-paas-workflow-kernel-api</artifactId>
      <version>1.0.1-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>javax.validation</groupId>
          <artifactId>validation-api</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>mongo-java-driver</artifactId>
          <groupId>org.mongodb</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-lang</artifactId>
          <groupId>commons-lang</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-collections</artifactId>
          <groupId>commons-collections</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-bpm-utils</artifactId>
          <groupId>com.facishare</groupId>
        </exclusion>
        <exclusion>
          <groupId>com.alibaba.rocketmq</groupId>
          <artifactId>rocketmq-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-bpm-utils</artifactId>
      <version>7.6.0-SNAPSHOT</version>
    </dependency>

    <!--功能权限-->
    <dependency>
      <groupId>com.facishare.paas</groupId>
      <artifactId>fs-paas-auth-api</artifactId>
      <version>2.4.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>mongo-java-driver</artifactId>
          <groupId>org.mongodb</groupId>
        </exclusion>
        <exclusion>
          <artifactId>libthrift</artifactId>
          <groupId>org.apache.thrift</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-lang</artifactId>
          <groupId>commons-lang</groupId>
        </exclusion>
        <exclusion>
          <artifactId>snakeyaml</artifactId>
          <groupId>org.yaml</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!--dubbo-->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>dubbo</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>activation</artifactId>
          <groupId>javax.activation</groupId>
        </exclusion>
        <exclusion>
          <artifactId>httpcore</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
        </exclusion>
        <exclusion>
          <artifactId>resteasy-client</artifactId>
          <groupId>org.jboss.resteasy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>resteasy-jaxrs</artifactId>
          <groupId>org.jboss.resteasy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>ping-monitor</artifactId>
          <groupId>com.fxiaoke.pms</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!--调用组织结构服务-->
    <dependency>
      <groupId>com.facishare.paas</groupId>
      <artifactId>fs-paas-org-api</artifactId>
      <version>${fs.paas.org.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.alibaba</groupId>
          <artifactId>dubbo</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>mongo-java-driver</artifactId>
          <groupId>org.mongodb</groupId>
        </exclusion>
        <exclusion>
          <artifactId>swagger-annotations</artifactId>
          <groupId>io.swagger</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-jackson2-provider</artifactId>
      <version>${resteasy-version}</version>
    </dependency>

    <!--企业信息查询接口-->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>ibss-service-em-api</artifactId>
      <version>1.1.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>validation-api</artifactId>
          <groupId>javax.validation</groupId>
        </exclusion>
        <exclusion>
          <artifactId>javassist-3.14.0-GA</artifactId>
          <groupId>org.ow2.util.bundles</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!--利用这个jar包能够获取审批流的一些常量--><!--1-->
    <dependency>
      <groupId>com.facishare.paas</groupId>
      <artifactId>fs-paas-workflow-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>com.alibaba</groupId>
          <artifactId>dubbo</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>effektif-workflow-api</artifactId>
          <groupId>com.effektif</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-paas-workflow-common</artifactId>
          <groupId>com.facishare.paas</groupId>
        </exclusion>
      </exclusions>
    </dependency>


    <!-- freemarker依赖 -->
    <dependency>
      <groupId>org.freemarker</groupId>
      <artifactId>freemarker</artifactId>
      <version>2.3.23</version>
    </dependency>
    <!--配置中心-->
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>core-filter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>metrics-oss</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>logconfig-core</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>commons-configuration</artifactId>
          <groupId>commons-configuration</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
    </dependency>
    <!--spring-->
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-webmvc</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aop</artifactId>
    </dependency>
    <dependency>
      <groupId>org.aspectj</groupId>
      <artifactId>aspectjrt</artifactId>
    </dependency>
    <dependency>
      <groupId>org.aspectj</groupId>
      <artifactId>aspectjweaver</artifactId>
    </dependency>
    <dependency>
      <groupId>cglib</groupId>
      <artifactId>cglib</artifactId>
    </dependency>
    <!--redis-->
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>jedis-spring-support</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>mybatis-spring-support</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.alibaba</groupId>
          <artifactId>druid</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>shiro-spring-support</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>spring-support</artifactId>
    </dependency>
    <dependency>
      <groupId>javax.validation</groupId>
      <artifactId>validation-api</artifactId>
      <version>2.0.0.Final</version>
    </dependency>

    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
    </dependency>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
    </dependency>

    <!--xxl-job-->
    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>fs-job-core</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <!--license-->
    <dependency>
      <groupId>com.facishare.paas</groupId>
      <artifactId>fs-paas-license-api</artifactId>
      <version>${fs.paas.license.version}</version>
    </dependency>
    <!--sqlServer-->
    <dependency>
      <groupId>com.microsoft.sqlserver</groupId>
      <artifactId>sqljdbc4</artifactId>
      <version>4.0</version>
    </dependency>

    <!--datatables-support-->
    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>datatables-support</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <!-- Apache Commons IO -->
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-jdbc</artifactId>
      <version>${spring.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-tx</artifactId>
      <version>${spring.version}</version>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
    </dependency>
    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator</artifactId>
    </dependency>
    <!--jdbcSupport-->
    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>jdbc-support</artifactId>
      <version>5.0.0-SNAPSHOT</version>
    </dependency>
    <!--mysql-->
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>${mysql.driver.version}</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-common-mds-event</artifactId>
      <version>1.0.4-SNAPSHOT</version>
    </dependency>
    <!--qixin-->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-qixin-common</artifactId>
      <version>0.0.3-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>netty-all</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
        <exclusion>
          <groupId>com.github.sgroschupf</groupId>
          <artifactId>zkclient</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.mongodb</groupId>
          <artifactId>mongo-java-driver</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.facishare</groupId>
          <artifactId>fs-common-mq</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.facishare</groupId>
          <artifactId>fs-fsi-proxy</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.facishare</groupId>
          <artifactId>datapersist</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.facishare</groupId>
          <artifactId>fs-fcp-file</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.facishare</groupId>
          <artifactId>fs-common-util</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>config-core</artifactId>
          <groupId>com.github.colin-lee</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jedis-spring-support</artifactId>
          <groupId>com.github.colin-lee</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-support</artifactId>
          <groupId>com.github.colin-lee</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-core</artifactId>
          <groupId>io.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-api</artifactId>
          <groupId>io.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-runtime</artifactId>
          <groupId>io.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>mongo-java-driver</artifactId>
          <groupId>org.mongodb</groupId>
        </exclusion>
        <exclusion>
          <artifactId>morphia</artifactId>
          <groupId>org.mongodb.morphia</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-text</artifactId>
          <groupId>org.apache.commons</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--groovy-->
    <dependency>
      <groupId>org.codehaus.groovy</groupId>
      <artifactId>groovy-all</artifactId>
    </dependency>
    <!-- Mandatory dependencies for using Spock -->
    <dependency>
      <groupId>org.spockframework</groupId>
      <artifactId>spock-core</artifactId>
      <scope>test</scope>
    </dependency>
    <!--多租户路由-->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-pod-client</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>mongo-java-driver</artifactId>
          <groupId>org.mongodb</groupId>
        </exclusion>
        <exclusion>
          <groupId>org.yaml</groupId>
          <artifactId>snakeyaml</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--查询企业信息调用Dubbo接口-->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-uc-api</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fxiaoke-helper</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <!--es-->

    <!--netty冲突-->
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-all</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
      <version>2.7</version>
    </dependency>
    <dependency>
      <groupId>com.clickhouse</groupId>
      <artifactId>clickhouse-jdbc</artifactId>
      <version>0.3.2-patch11</version>
    </dependency>
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>config-admin-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.k8s</groupId>
      <artifactId>fs-k8s-java-support</artifactId>
    </dependency>
    <!--es end-->
<!--    <dependency>-->
<!--      <groupId>com.facishare</groupId>-->
<!--      <artifactId>fs-table-transfer-client</artifactId>-->
<!--      <version>1.1.0-SNAPSHOT</version>-->
<!--    </dependency>-->
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-war-plugin</artifactId>
      </plugin>
      <plugin>
        <!-- The gmavenplus plugin is used to compile Groovy code. To learn more about this plugin, visit https://github.com/groovy/GMavenPlus/wiki -->
        <groupId>org.codehaus.gmavenplus</groupId>
        <artifactId>gmavenplus-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>
