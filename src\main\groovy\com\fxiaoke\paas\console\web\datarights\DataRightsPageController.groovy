package com.fxiaoke.paas.console.web.datarights

import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam

/**
 * Created by yangxw on 2018/3/15.
 */
@Controller
class DataRightsPageController {

  @RequestMapping("/datarights/task/report")
  def task() {
    "datarights/data_auth_task"
  }

  @RequestMapping("/datarights/mixBatch")
  def mixBatch() {
    "datarights/data_permission_mix_batch"
  }

  @RequestMapping("/datarights_refresh/refresh")
  def refresh() {
    "datarights/data_permission_datarights_refresh"
  }

  @RequestMapping("/datarights/check")
  def check() {
    "datarights/data_permission_datarights_check"
  }


  @RequestMapping("/datarights/authToUser")
  def authToUser() {
    "datarights/data_permission_auth_to_user"
  }


  @RequestMapping("/datarights/query")
  def query() {
    "datarights/data_permission_datarights_query"
  }

  /***
   * 我的圈子
   * @return
   */
  @RequestMapping("/datarights/user/circle/query")
  def userCircleQuery() {
    "datarights/user_circle_query"
  }

  @RequestMapping("/datarights/basic/query")
  def basicQueryPage() {
    "datarights/data_permission_basic"
  }

  @RequestMapping("/datarights/map")
  def mapQueryPage() {
    "datarights/data_permission_map"
  }

  @RequestMapping("/datarights/leader/query")
  def leaderQueryPage() {
    "datarights/data_permission_user_leader_cache"
  }

  @RequestMapping("/datarights/dept/query")
  def userQueryPage() {
    "datarights/data_permission_user_dept_cache"
  }

  @RequestMapping("/datarights/are/cache/query")
  def shareCacheQueryPage() {
    "datarights/data_permission_share_cache"
  }

  @RequestMapping("/datarights/shar/query")
  def shareQueryPage(@RequestParam Map<String, Object> param, Model model) {
    model.addAttribute("tenantId", param.get("tenantId"))
    model.addAttribute("AppId", param.get("AppId"))
    model.addAttribute("entityShareId", param.get("entityShareId"))
    "datarights/data_permission_share"
  }

  @RequestMapping("/datarights_refresh/permission/init/page")
  def dataPermissiomInitPage() {
    "datarights/data_permission_init_cache"
  }

  @RequestMapping("/datarights/data/permission/dept/query")
  def daptQueryPage(@RequestParam Map<String, Object> param, Model model) {
    model.addAttribute("deptId", param.get("deptId"))
    model.addAttribute("tenantId", param.get("tenantId"))
    model.addAttribute("AppId", param.get("AppId"))
    "datarights/data_permission_dept_query"
  }

  @RequestMapping("/datarights/data/permission/group/query")
  def groupQueryPage(@RequestParam Map<String, Object> param, Model model) {
    model.addAttribute("groupId", param.get("groupId"))
    model.addAttribute("tenantId", param.get("tenantId"))
    model.addAttribute("AppId", param.get("AppId"))
    "datarights/data_permission_group_query"
  }
}
