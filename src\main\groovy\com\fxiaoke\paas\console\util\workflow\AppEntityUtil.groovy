package com.fxiaoke.paas.console.util.workflow

import groovy.util.logging.Slf4j
import org.springframework.stereotype.Component

/**
 * Created by yangxw on 2018/3/7.
 */
@Slf4j
class AppEntityUtil {
    private static final String DOES_NOT_EXIST = "不存在";
    private static final String NO_APP_NAME = "appName = {} doesn't exist";
    private static final String NO_ENTITY_NAME = "entityName = {} with appName = {} doesn't exist";

    /**
     * 把业务id转换成业务名称
     * @param appId
     * @return
     */
    def static appIdToAppName(String appId) {

        if (null == appId) {
            return "";
        }
        String appName = "";
        switch (appId) {
            case Constants.APP_ID_CRM:
                return Constants.APP_NAME_CRM;
            case Constants.APP_ID_XT:
                return Constants.APP_NAME_XT;
            case Constants.APP_ID_BPM:
                return Constants.APP_ID_BPM;
            default:
                log.info(NO_APP_NAME, appId);
                return appName;
        }

    }

    /**
     * 实体id转换成实体名称
     * @param appId
     * @param entityId
     * @return
     */
    def static entityIdToName(String appId, String entityId) {

        if (null == appId || null == entityId) {
            return "";
        }
        String entityName = DOES_NOT_EXIST;
        switch (appId) {
            case Constants.APP_ID_XT:
                log.info(NO_ENTITY_NAME, entityId, appId);
                return entityName;
            case Constants.APP_ID_CRM:
                return Constants.ENTITY_ID_CRM_CUSTOMER.equals(entityId) ? Constants.ENTITY_NAME_CRM_CUSTOMER : entityName;
            default:
                log.info(NO_APP_NAME, appId);
                return entityName;
        }
    }
}
