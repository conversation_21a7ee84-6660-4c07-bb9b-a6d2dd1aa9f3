package com.fxiaoke.paas.console.web.metadata

import com.fxiaoke.common.http.spring.OkHttpSupport
import com.fxiaoke.paas.console.bean.metadata.SearchSceneArg
import com.fxiaoke.paas.console.service.metadata.DataQueryService
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.Resource

@Controller
@Slf4j
@RequestMapping("/metadata/dataQuery")
class DataQueryController {

  @Autowired
  private DataQueryService dataQueryService

  @Resource(name = "httpSupport")
  private OkHttpSupport okHttpSupport

  @RequestMapping("/selectByScene")
  def loadScenePage(){
    "metadata/dataQueryByScene"
  }

  @RequestMapping(value = "/findMtField")
  @ResponseBody
  findMtField(@RequestParam String tenantId, @RequestParam String describeApiName) {
    return dataQueryService.findApiNameList(tenantId,describeApiName)
  }

  @RequestMapping(value = "/findDataByScene")
  @ResponseBody
  findDataByScene(@RequestBody SearchSceneArg arg) {

    if (StringUtils.isEmpty(arg.getTenantId()) || StringUtils.isEmpty(arg.getUserId()) || StringUtils.isEmpty(arg.getDescribeApiName())){
      return ["errCode": 0, "errMessage": "查询参数不正确"]
    }

    if (!"db".equals(arg.getSearchSource()) && !"es".equals(arg.getSearchSource())){
      return ["errCode": 0, "errMessage": "查询参数不正确"]
    }

    return dataQueryService.findDataByScene(arg)
  }


}
