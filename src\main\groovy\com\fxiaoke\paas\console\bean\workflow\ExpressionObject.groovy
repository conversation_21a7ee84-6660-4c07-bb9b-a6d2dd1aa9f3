package com.fxiaoke.paas.console.bean.workflow

import groovy.transform.ToString
import io.swagger.annotations.ApiModelProperty

/**
 * Created by yangxw on 2018/3/8.
 */
@ToString
class ExpressionObject {
    @ApiModelProperty("行号")
     Integer rowNo

    @ApiModelProperty("属性名称")
     String fieldName

    @ApiModelProperty("属性类型")
     String fieldType

    @ApiModelProperty("系统字段 ,对象字段")
     String fieldSrc
    
    @ApiModelProperty("操作符")
     String operator

    @ApiModelProperty("逻辑比较的值")
     String value

}
