package com.fxiaoke.paas.console.util.datarights

import com.alibaba.fastjson.JSONObject

/**
 * <AUTHOR>
 * @date 2019/7/1 下午6:30
 */
 final class  ResponseResult {
   final static JSONObject success(String msg){
    JSONObject jsonObject=new JSONObject()
    jsonObject.put("status",200)
    jsonObject.put("msg",msg)
    return jsonObject
  }

   final static JSONObject failed(String msg){
     JSONObject jsonObject=new JSONObject()
     jsonObject.put("status",500)
     jsonObject.put("msg", msg)
     return jsonObject
   }
}
