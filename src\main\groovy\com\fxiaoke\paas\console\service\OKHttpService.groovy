package com.fxiaoke.paas.console.service

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.alibaba.fastjson.serializer.SerializerFeature
import com.facishare.paas.license.exception.LicenseException
import com.facishare.paas.license.exception.PaasMessage
import com.fxiaoke.common.http.handler.SyncCallback
import com.fxiaoke.common.http.spring.OkHttpSupport
import groovy.util.logging.Slf4j
import okhttp3.Headers
import okhttp3.MediaType
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import org.apache.commons.collections.MapUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Service

import javax.annotation.Resource

/**
 * <AUTHOR>
 * @date 2019/4/10 下午5:31
 */
@Service
@Slf4j
class OKHttpService {


    @Resource(name = "httpSupport")
    OkHttpSupport client

    String postJSON(String url, JSONArray jsonArray) {
        String result = null
        if (StringUtils.isNotBlank(url) && jsonArray != null) {
            Request request = new Request.Builder()
                    .post(RequestBody.create(MediaType.parse("application/json;charset=utf-8"), jsonArray.toJSONString())).url(url).build()
            client.syncExecute(request, { response ->
                if (response.isSuccessful()) {
                    result = response.body().string()
                } else {
                    log.error("dataRights refresh error,url:{},param:{}", url, jsonArray.toJSONString())
                }
            }
            )
        }
        return result
    }

    String postJSON(String url, JSONObject jsonObject) {
        String result = null
        if (StringUtils.isNotBlank(url) && jsonObject != null) {
            Request request = new Request.Builder()
                    .post(RequestBody.create(MediaType.parse("application/json;charset=utf-8"), jsonObject.toJSONString())).url(url).build()
            client.syncExecute(request, { response ->
                if (response.isSuccessful()) {
                    result = response.body().string()
                } else {
                    log.error("dataRights refresh error,url:{},param:{}", url, jsonObject.toJSONString())
                }
            }
            )
        }
        return result
    }

    /**
     *
     * @param url
     * @param jsonObject json格式的请求参数
     * @param headers 设置请求头
     * @return
     */
    String postJSON2(String url, JSONObject jsonObject, Headers headers) {
        String result = null
        if (StringUtils.isNotBlank(url) && jsonObject != null) {
            Request request = new Request.Builder()
                    .headers(headers)
                    .post(RequestBody.create(MediaType.parse("application/json;charset=utf-8"), jsonObject.toJSONString())).url(url).build()
            client.syncExecute(request, { response ->
                if (response.isSuccessful()) {
                    result = response.body().string()
                } else {
                    log.error("dataRights refresh error,url:{},param:{}", url, jsonObject.toJSONString())
                }
            }
            )
        }
        return result
    }

    public <T> T post(String url, String body, Class<T> clazz) {
        Request request = new Request.Builder().url(url).post(RequestBody.create(MediaType.parse("application/json; chaset=UTF-8"), body)).build()
        return (T) client.syncExecute(request, new SyncCallback() {
            @Override
            Object response(Response response) throws Exception {
                if (response.isSuccessful()) {
                    return JSON.parseObject(response.body().string(), clazz)
                } else {
                    log.error("call http failed. code:${}, body:{}", response.code(), response.body().string())
                    throw new LicenseException(PaasMessage.SYSTEM_ERROR.getCode(), PaasMessage.SYSTEM_ERROR.getMessage() + " http failed")
                }
            }
        })
    }

    JSONObject post(String url, Map<String, Object> headers, Object entity, String type) {
        log.debug("post url:{}, headers:{}, entity:{}", url, JSON.toJSON(headers), JSONObject.toJSONString(entity, SerializerFeature.WriteMapNullValue));
        try {
            MediaType mediaType = MediaType.parse(type);
            RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(entity, SerializerFeature.WriteMapNullValue));
            Request.Builder builder = new Request.Builder();
            if (MapUtils.isNotEmpty(headers)) {
                headers.each { k, v -> builder.addHeader(k, v.toString()) }
            }
            Request request = builder.url(url).post(body).build();
            return (JSONObject) client.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) throws Exception {
                    if (response != null) {
                        if (response.isSuccessful()) {
                            String res = response.body().string();
                            JSONObject jsonObject = JSONObject.parseObject(res);
                            log.debug("http post url:{}, code: {}, result:{}", url, response.code(), JSON.toJSONString(jsonObject));
                            return jsonObject;
                        } else {
                            log.debug("http post url:{}, code: {}, message:{}", url, response.code(), response.message());
                        }
                    } else {
                        log.debug("http post url:{}, response is null", url);
                    }
                    return new JSONObject();
                }
            });
        } catch (Exception e) {
            log.error("OkHttpClient.post() Error, url:{}, headers:{}, entity:{}, mediaType:{}",
                    url,
                    headers,
                    JSONObject.toJSONString(entity, SerializerFeature.WriteMapNullValue),
                    type);
            log.error("OkHttpClient.post() Error", e);
        }
        return new JSONObject();
    }

    String postForm(String url) {
        if (StringUtils.isNotBlank(url)) {
            String result
            Request request = new Request.Builder().post(RequestBody.create(MediaType.parse("application/x-www-form-urlencoded"), "")).url(url).build()
            client.syncExecute(request, {
                response ->
                    if (response.isSuccessful()) {
                        result = response.body().string()
                    } else {
                        log.error("dataRights refresh error,url:{}", url)
                    }
            })
            return result
        }
    }

    String getForm(String url) {
        if (StringUtils.isNotBlank(url)) {
            String result
            Request request = new Request.Builder().get().url(url).build()
            client.syncExecute(request, {
                response ->
                    if (response.isSuccessful()) {
                        result = response.body().string()
                    } else {
                        log.error("dataRights refresh error,url:{}", url)
                    }
            })
            return result
        }
    }

}
