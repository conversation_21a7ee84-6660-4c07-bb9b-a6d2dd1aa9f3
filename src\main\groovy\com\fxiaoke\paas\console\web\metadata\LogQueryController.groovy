package com.fxiaoke.paas.console.web.metadata

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.github.autoconf.ConfigFactory
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.http.MediaType
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody
import com.fxiaoke.common.PasswordUtil
import javax.annotation.PostConstruct
import java.sql.ResultSetMetaData

/**
 * <AUTHOR>
 * @date 2019/8/5 3:09 PM
 */
@Controller
@Slf4j
@RequestMapping("/metadata/log-query")
class LogQueryController {


  private String masterUrl
  private String userName
  private String password

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-paas-copier-consumer-db", { iConfig ->
      this.masterUrl = iConfig.get("masterUrl")
      this.userName = iConfig.get("username")
      this.password = PasswordUtil.decode(iConfig.get("password"))
    })
  }

  @RequestMapping("/page")
  String index() {
    return "metadata/log-query"
  }

  @PostMapping(path = "/query", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ResponseBody
  @SystemControllerLog(description = "Schema隔离 -- 刷库日志查询")
  JSONObject query(@RequestParam String sql) {
    sql = sql.trim()
    JSONObject result = new JSONObject()
    if (StringUtils.isBlank(masterUrl) || StringUtils.isBlank(password) || StringUtils.isBlank(userName)) {
      result.put("code", 200)
      result.put("info", "{\"msg\":\"DataBase connection error!\"}")
      return result
    }
    if (sql.endsWith(";")) {
      sql = sql.substring(0, sql.length() - 1)
    }
    if (!(sql.contains("limit") || sql.contains("LIMIT"))) {
      sql += " LIMIT 100"
    }
    JdbcConnection jdbcConnection = new JdbcConnection(masterUrl, userName, password)
    List<Map<String, Object>> queryResult = Lists.newArrayList()
    jdbcConnection.query(sql, { rs ->
      ResultSetMetaData metaData = rs.getMetaData()
      int columnCount = metaData.columnCount
      while (rs.next()) {
        Map<String, Object> map = Maps.newHashMap()
        for (int i = 1; i <= columnCount; i++) {
          map.put(metaData.getColumnName(i), rs.getObject(i))
        }
        queryResult.add(map)
      }

    })
    result.put("code", 200)
    result.put("info", queryResult)
    return result
  }

}
