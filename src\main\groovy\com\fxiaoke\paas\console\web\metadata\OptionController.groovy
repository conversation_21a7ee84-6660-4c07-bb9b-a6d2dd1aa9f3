package com.fxiaoke.paas.console.web.metadata

import com.fxiaoke.common.PasswordUtil
import com.fxiaoke.jdbc.JdbcConnection
import com.fxiaoke.metadata.option.api.OptionApiClient
import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.github.autoconf.ConfigFactory
import com.google.common.base.Splitter
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import lombok.Cleanup
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

import javax.annotation.PostConstruct
import javax.annotation.Resource
import java.sql.ResultSetMetaData

/**
 * <AUTHOR> Created on 2018/3/20.
 */
@Controller
@Slf4j
@RequestMapping("/metadata/option")
class OptionController {

  @Resource
  private OptionApiClient optionApiClient

  private String jdbcUrl
  private String userName
  private String password

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-metadata-option-db", { iConfig ->
      this.jdbcUrl = iConfig.get("masterUrl")
      this.userName = iConfig.get("username")
      this.password = iConfig.get("password")
      if (iConfig.getBool('encrypt.pwd',false)) {
        this.password = PasswordUtil.decode(this.password)
      }
    })
  }


  @RequestMapping(value = "/sqlQuery")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- option SQL查询")
  def sqlQuery(String sql) {
    List<Map<String, Object>> result = Lists.newArrayList()
    if (StringUtils.isNotBlank(sql)) {
      String option = Splitter.on(" ").trimResults().omitEmptyStrings().splitToList(sql).get(0)
      if (!"select".equalsIgnoreCase(option)) {
        return ["code": 200, "info": ["message": "不支持该操作"]]
      }
      try {
        @Cleanup JdbcConnection jdbcConnection = new JdbcConnection(jdbcUrl, userName, password)
        jdbcConnection.query(sql, { resultSet ->
          ResultSetMetaData resultSetMetaData = resultSet.getMetaData()
          int index = resultSetMetaData.getColumnCount()

          while (resultSet.next()) {
            Map<String, Object> map = Maps.newHashMap()
            for (int i = 1; i <= index; i++) {
              String columnName = resultSetMetaData.getColumnName(i)
              map.put(columnName, resultSet.getObject(columnName))
            }
            result.add(map)
          }
        })
      } catch (Exception e) {
        log.error("query sql error! sql:{} ", sql, e)
      }
    }
    return ["code": 200, "info": result]
  }

  /**
   * 跳转查询页
   * @param model
   * @return
   */
  @RequestMapping(value = "/query")
  def query(ModelMap model) {
    "metadata/option"
  }

  /**
   * sql查询
   * @param sql
   */
  @GetMapping(value = "/query-result")
  @ResponseBody
  @SystemControllerLog(description = "元数据 -- option查询")
  def queryByOptionId(@RequestParam String optionId) {
    try {
      String option = optionApiClient.findByMd5(optionId)
      return ["code": 200, "info": option]
    } catch (Exception e) {
      log.warn("option查询异常，optionId={},error:", optionId, e)
      return ["code": 500, "error": "查询异常", "errorInfo": e.getMessage()]
    }
  }

}
