package com.fxiaoke.paas.console.service.metadata

import com.alibaba.fastjson.JSONObject
import com.facishare.paas.foundation.persistence.PersistenceConfiguration
import com.facishare.paas.foundation.persistence.SlaveQuerier
import com.fxiaoke.paas.common.service.SchemaHelper
import com.fxiaoke.paas.console.bean.metadata.DataResult
import com.fxiaoke.paas.console.mapper.metadata.DataMapper
import com.fxiaoke.paas.console.mapper.metadata.DescribeMapper
import com.fxiaoke.paas.console.mapper.metadata.FieldMapper
import com.fxiaoke.paas.console.mapper.metadata.MtDataMapper
import com.fxiaoke.paas.console.service.DataMaskingService
import com.fxiaoke.paas.console.util.DateFormatUtil
import com.fxiaoke.paas.console.util.HttpClientUtil
import com.github.autoconf.ConfigFactory
import com.google.common.base.CharMatcher
import com.google.common.base.Splitter
import com.google.common.base.Strings
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import lombok.NonNull
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import javax.annotation.Resource

/**
 * <AUTHOR> Created on 2018/3/5.
 */
@Service
@Slf4j
class DataService {
  @Resource
  private DescribeMapper describeMapper
  @Resource
  private MtDataMapper mtDataMapper
  @Resource
  private DataMapper dataMapper
  @Resource
  private FieldMapper fieldMapper
  @Resource
  private JdbcService jdbcService
  @Resource
  private SchemaHelper schemaHelper

  /**
   * 专表白名单
   */
  private List<String> tableNameList
  private String refreshEsUrl;

  /**
   * 需要数据脱敏的field类型
   */
  private List<String> maskingFieldType

  //数据类型
  private static final String MT_DATA = "mtData"
  private static final String SPECIAL_DATA = "special"

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("paas-console-base", { config ->
      tableNameList = Splitter.on(CharMatcher.anyOf(",\n\t")).trimResults().omitEmptyStrings().splitToList(config.get("tableName"))
      maskingFieldType = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(config.get("maskingFieldType"))
      refreshEsUrl = config.get("fs-metadata-refresh")
    })
  }

  /**
   * 查找数据列表并判断是专表还是通表
   * @param tenantId
   * @param describeId
   * @return
   */
  DataResult dataList(@NonNull String tenantId, @NonNull String describeId) {
    Map<String, String> apiNameAndTable = describeMapper.setTenantId(tenantId).findStoreTableNameAndApiName(tenantId, describeId)
    String tableName = apiNameAndTable["store_table_name"]
    String apiName = apiNameAndTable["describe_api_name"]
    PersistenceConfiguration.DescribeHolder.set(apiName)

    if (Strings.isNullOrEmpty(tableName)) {
      return DataResult.builder().tableType(MT_DATA).tableName("mt_data").dataList(mtData(tenantId, apiName)).build()
    } else if (tableNameList.contains(tableName) || tableName.endsWith("__c")) {
      return DataResult.builder().tableType(SPECIAL_DATA).tableName(tableName).dataList(specialTable(tenantId, tableName, apiName)).build()
    }
    DataResult.builder().tableType(null).dataList(Lists.newArrayList()).build()
  }

  /**
   * 获取专表数据列表
   * @return
   */
  List<Map<String, Object>> specialTable(@NonNull String tenantId, @NonNull String tableName, @NonNull String apiName) {
    return dataMapper.setTenantId(tenantId).findDataList(tableName, tenantId, apiName)
  }

  /**
   * 获取通表数据列表
   * @return
   */
  List<Map<String, Object>> mtData(@NonNull String tenantId, @NonNull String apiName) {
    List<Map<String, Object>> dataList = mtDataMapper.setTenantId(tenantId).findDataList(tenantId, apiName)
    dataList.forEach { data ->
      data.put("create_time", DateFormatUtil.formatLong(data["create_time"] as Long))
      data.put("last_modified_time", DateFormatUtil.formatLong(data["last_modified_time"] as Long))
    }
    dataList
  }

  /**
   * 获取data详情,并把字段名称替换为field对应的api_name
   * @param tenantId
   * @param apiName
   * @param id
   */
  Map<String, Object> dataInfo(@NonNull String tenantId, @NonNull String apiName, @NonNull String id) {
    Map map = describeMapper.setTenantId(tenantId).queryTableNameAndIsBigObject(tenantId, apiName)
    String tableName = map != null ? map.get("tablename") : null;
    PersistenceConfiguration.DescribeHolder.set(apiName)

    if (StringUtils.isBlank(tableName)) {
      Map<String, Object> dataInfo = mtDataMapper.setTenantId(tenantId).dataInfo(id, tenantId, apiName)
      return replaceFieldLabel(tenantId, dataInfo, apiName, MT_DATA)
    } else {
      //获取专表数据详情
      Map<String, Object> dataInfo = dataMapper.setTenantId(tenantId).dataInfo(tableName, id, tenantId, apiName)
      return replaceFieldLabel(tenantId, dataInfo, apiName, SPECIAL_DATA)
    }
  }

  /**
   * 根据主属性name获取数据
   * @param tenantId
   * @param apiName
   * @param name
   * @return
   */
  List<Map<String, Object>> getDataByName(@NonNull String tenantId, String apiName, String name) {
    Map map = describeMapper.setTenantId(tenantId).queryTableNameAndIsBigObject(tenantId, apiName)
    String tableName = map != null ? map.get("tablename") : null

    PersistenceConfiguration.DescribeHolder.set(apiName)
    List<Map<String, Object>> dataInfo
    String flag;
    if (Strings.isNullOrEmpty(tableName)) {
      dataInfo = mtDataMapper.setTenantId(tenantId).getDataByName("mt_data", tenantId, name, apiName)
      flag = MT_DATA;
    } else {
      dataInfo = mtDataMapper.setTenantId(tenantId).getDataByName(tableName, tenantId, name, apiName)
      flag = SPECIAL_DATA;
    }

    if(dataInfo == null || dataInfo.size()==0) {
      return dataInfo;
    }
    return dataInfo.stream().map{it -> replaceFieldLabel(tenantId, it, apiName, flag)}.collect()

  }

  void updateEsFieldType(String tenantId, String objectApiName, String fieldApiName) {
    if (StringUtils.isNotBlank(refreshEsUrl)) {
      String getUrl = String.format(refreshEsUrl, tenantId, objectApiName, fieldApiName)
      JSONObject result = HttpClientUtil.get(getUrl, null)
      log.info("t_2_n refresh result, url {}, result {}", getUrl, result)
    }
  }

  /**
   * 当数据为通表数据或专表数据有扩展字段时把字段名替换为field的api_name
   * @param dataInfo
   */
  Map<String, Object> replaceFieldLabel(@NonNull String tenantId, @NonNull Map<String, Object> dataInfo, @NonNull String describeApiName, @NonNull String type) {
    if(dataInfo == null || dataInfo.size()==0) {
      return Maps.newHashMap()
    }
    PersistenceConfiguration.DescribeHolder.remove()
    List<Map<String, Object>> fieldList = fieldMapper.setTenantId(tenantId).findFieldByDescribeId(dataInfo["object_describe_id"] as String)

    if(fieldList == null || fieldList.size() == 0) {
      fieldList = fieldMapper.setTenantId(tenantId).findFieldByTenantIdAndDescribe(tenantId, dataInfo["object_describe_api_name"] as String)
    }

    if (type == MT_DATA || schemaHelper.isSpecialSchema(tenantId)) {
      fieldList.forEach { field ->
        if (!Strings.isNullOrEmpty(field["field_num"] as String)) {
          if (maskingFieldType.contains(field["type"])) {
            //数据脱敏
            dataInfo.put((field["api_name"] as String) + "/value" + field["field_num"], DataMaskingService.filter(dataInfo["value" + field["field_num"]] as String, field["type"] as String))
          } else {
            dataInfo.put((field["api_name"] as String) + "/value" + field["field_num"], dataInfo["value" + field["field_num"]])
          }
          dataInfo.remove("value" + field["field_num"])
        }
      }
    } else if (type == SPECIAL_DATA) {
      //判断是否有扩展字段
      if (!Strings.isNullOrEmpty(dataInfo["extend_obj_data_id"] as String)) {
        //扩展字段数据
        Map<String, Object> dataInfoMtData = mtDataMapper.setTenantId(tenantId).dataInfo(dataInfo["extend_obj_data_id"] as String, tenantId, describeApiName)

        fieldList.each { field ->
          //判断field是否是扩展字段的field
          if (field["field_num"] == null) {
            if (maskingFieldType.contains(field["type"])) {
              //数据脱敏
              dataInfo.put(field["api_name"] as String, DataMaskingService.filter(dataInfo[field["api_name"] as String] as String, field["type"] as String))
            }
          } else {
            if (maskingFieldType.contains(field["type"])) {
              //数据脱敏
              dataInfo.put((field["api_name"] as String) + "/value" + field["field_num"], DataMaskingService.filter(dataInfoMtData["value" + field["field_num"]] as String, field["type"] as String))
            } else {
              dataInfo.put((field["api_name"] as String) + "/value" + field["field_num"], dataInfoMtData["value" + field["field_num"]])
            }
          }
        }
      } else {
        fieldList.each { field ->
          if (maskingFieldType.contains(field["type"])) {
            //数据脱敏
            dataInfo.put(field["api_name"] as String, DataMaskingService.filter(dataInfo[field["api_name"] as String] as String, field["type"] as String))
          }
        }
      }
    }
    dataInfo
  }

  //todo:国家省市区详细地址的脱敏
  boolean maskingFlag(String type, String apiName) {
    return maskingFieldType.contains(type)
  }
}
