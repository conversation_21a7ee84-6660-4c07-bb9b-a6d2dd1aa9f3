package com.fxiaoke.paas.console.web

import com.fxiaoke.paas.console.service.metadata.AuditLogService
import com.github.autoconf.spring.reloadable.ReloadableProperty
import com.github.shiro.support.ShiroCasRealm
import groovy.util.logging.Slf4j
import org.apache.shiro.SecurityUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.servlet.mvc.support.RedirectAttributes

import javax.annotation.Resource

@Controller
@Slf4j
class HomeController {
  @Resource(name = "casRealm")
  private ShiroCasRealm cas

  @Autowired
  AuditLogService auditLogService

  @ReloadableProperty('casServerUrlPrefix')
  def casServerUrlPrefix = "/cas"

  @RequestMapping("/logout")
  def logout(RedirectAttributes r) {
    SecurityUtils.getSubject().logout()
    r.addFlashAttribute("message", "您已经安全退出")
    "redirect:" + cas.casServerUrlPrefix + "/logout"
  }

  @RequestMapping("/unauthorized")
  def unauthorized() {
    "/home/<USER>"
  }

  @RequestMapping("/error")
  def error() {
    "home/error"
  }

  @RequestMapping(value = ["/home", "/"])
  def home() {
//    "home/month-figure"
    "home/home"
  }
}
