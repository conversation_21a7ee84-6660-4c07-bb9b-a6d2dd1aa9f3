<#assign title="部门信息关系查询">
<#assign headContent>
<link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
<style>
    #dataTable2 th {
        vertical-align: middle;
        align-items: center;
    }

    #dataTable2 td {
        vertical-align: middle;
        align-items: center
    }

    .idObject {
        padding-left: 10px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #ccc
    }
</style>
</#assign>
<#assign breadcrumbContent>
<section class="content-header">
    <h1>部门信息关系查询</h1>
    <ol class="breadcrumb">
        <li><a href="../../"><i class="fa fa-dashboard"></i>任务主页</a></li>
        <li><a href=""><i class="fa fa-dashboard"></i>部门信息关系查询</a></li>
    </ol>
</section>
</#assign>
<#assign bodyContent>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-header with-border">
                    <form class="form-inline" action="${ctx}/describe/list" method="post" id="findForm" role="form" data-toggle="validator">
                        <div class="form-group">
                            <label for="tenantId">企业ID</label>
                            <input type="text" name="companyId" class="idObject" id="companyId" placeholder="企业ID必填" required>
                        </div>
                        <div class="form-group">
                            <label for="apiName">员工ID</label>
                            <input type="text" name="userId" class="idObject" id="userId" placeholder="员工ID必填" required>
                        </div>
                        <div class="form-group">
                            <label for="apiName">部门员工关系类型</label>
                            <select class="selectpicker" id="relationalType">
                                <option value="">所有关系</option>
                                <option value="1">主属关系</option>
                                <option value="0">附属关系</option>
                            </select>
                        </div>
                        <button type="button" class="btn btn-primary" id="doSearch">查询</button>
                    </form>
                </div>

                <div class="box-body">
                    <table class="table table-striped table-bordered table-condensed dataTable no-footer" id="dataTable2">
                        <thead>
                        <tr>
                            <th>企业ID</th>
                            <th>上级部门ID</th>
                            <th>部门ID</th>
                            <th>部门名称</th>
                            <th>负责人ID</th>
                            <th>状态</th>
                            <th>描述</th>
                            <th>创建人</th>
                            <th>创建时间</th>
                            <th>最后更新人</th>
                            <th>最后更新时间</th>
                        </tr>
                        </thead>
                    </table>
                </div>

                <div class="box-footer clearfix">
                </div>
            </div>
        </div>
    </div>
</section>
<#--提示信息模态框-->
<div class="modal fade" id="hintModal" tabindex="-1" role="dialog" aria-labelledby="sqlModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                        class="sr-only">Close</span></button>
                <h4 class="modal-title" id="stopModalLabel">提示</h4>
            </div>
            <div class="modal-body">
                <h4>企业ID和员工ID不能为空！</h4>
            </div>
            <div class="modal-footer">
                <a type="button" class="btn btn-default" data-dismiss="modal">返回</a>
            </div>
        </div>
    </div>
</div>
</#assign>
<#assign scriptContent>
<!--搜索高亮显示-->
<script src="//static.foneshare.cn/oss/mark.js/8.6.0/jquery.mark.min.js"></script>
<script src="//static.foneshare.cn/oss/datatables.mark.js/2.0.0/datatables.mark.min.js"></script>
<script>
    $(document).ready(function () {
        var table = $("#dataTable2").DataTable({
            "searching": true,
            "ajax": "",
            "columnDefs": [
                {"width": "8%", "targets": 0},
                {"width": "9%", "targets": 1},
                {"width": "9%", "targets": 2},
                {"width": "7%", "targets": 3},
                {"width": "9%", "targets": 4},
                {"width": "9%", "targets": 5},
                {"width": "9%", "targets": 6},
                {"width": "9%", "targets": 7},
                {"width": "11%", "targets": 8},
                {"width": "9%", "targets": 9},
                {"width": "11%", "targets": 10}
            ],
            columns: [
                {data: "tenantId"},
                {data: "parentId"},
                {data: "deptId"},
                {data: "name"},
                {data: "managerId"},
                {data: "status"},
                {data: "description"},
                {data: "createBy"},
                {data: "createTime"},
                {data: "lastModifiedBy"},
                {data: "lastModifiedTime"},
            ],
            "iDisplayLength": 10,
            "sPaginationType": "full_numbers",
            "language": {
                "url": "${ctx}/static/js/datatables-zh_CN.json"
            }
        });
        $("#doSearch").click(function () {
            var tenantId = $("#companyId").val();
            var userId = $("#userId").val();
            var relationalType = $("#relationalType").val();
            if (tenantId.length !== 0 && userId.length !== 0) {
                table.ajax.url("${CONTEXT_PATH}/organization/relationalDepartment?tenantId=" + tenantId + "&userId=" + userId + "&type=" + relationalType).load();
            } else {
                $('#hintModal').modal("show");
            }
        });
    });
</script>
</#assign>
<#include "../layout/layout-main.ftl"/>