package com.fxiaoke.paas.console.bean.metadata

enum SceneType {
  ALL("ALL","全部"),
  MY_PARTICIPANT("MY_PARTICIPANT","我参与的"),
  MY_RESPONSIBLE("MY_RESPONSIBLE","我负责的"),
  MY_SUBORDINATE_RESPONSIBLE("MY_SUBORDINATE_RESPONSIBLE","我下属负责的"),
  MY_RESPONSIBLE_DEPARTMENT("MY_RESPONSIBLE_DEPARTMENT","我负责部门的"),
  MY_SHARED_DATA("MY_SHARED_DATA","共享给我的"),
  MY_SUBORDINATE_PARTICIPANT("MY_SUBORDINATE_PARTICIPANT","我下属参与的")

  private String scene
  private String description

  SceneType(String scene, String description) {
    this.scene = scene
    this.description = description
  }

  String getScene() {
    return scene
  }

  void setScene(String scene) {
    this.scene = scene
  }

  String getDescription() {
    return description
  }

  void setDescription(String description) {
    this.description = description
  }
}