package com.fxiaoke.paas.console.bean.workflow

import io.swagger.annotations.ApiModelProperty

/**
 * Created by yangxw on 2018/3/8.
 */
class WorkflowRulePojo {

        @ApiModelProperty("workflowRule id，唯一标识")
         String id

        @ApiModelProperty("租户id")
         String tenantId

        @ApiModelProperty("应用名称")
         String appName

        @ApiModelProperty("关联的对象类型")
         String entityName

        @ApiModelProperty("规则类型")
         String ruleType

        @ApiModelProperty("操作类型，如新增、编辑、删除...")
         String actionTypes

        @ApiModelProperty("规则描述信息")
         String description

        @ApiModelProperty("实时，定时（5.5版本只有实时")
         String executionType

         String conditionPattern;

        @ApiModelProperty("规则表达式数组")
         List<ExpressionObject> conditions

        @ApiModelProperty("把conditionPattern和conditions结合起来")
         String triggerConditions

        @ApiModelProperty("规则对应的工作流srcId")
         String workflowSrcId

        @ApiModelProperty("创建者")
         String creator

        @ApiModelProperty("创建者姓名")
         String creatorAccount

        @ApiModelProperty("更改时间")
         String modifyTime

        @ApiModelProperty("更改者")
         String modifier

        @ApiModelProperty("更改者姓名")
         String modifierAccount
    }

